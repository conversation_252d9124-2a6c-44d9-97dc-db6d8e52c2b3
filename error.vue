<template>
  <div class="p-0 m-0">
    <template v-if="String(error.statusCode) === '404'">
      <div class="h-screen flex items-center justify-center">
        <div class="flex items-center justify-center flex-col">
          <img src="@/assets/images/img-404.png" alt="">
          <p class="text-[rgba(0,0,0,0.55)] text-[24px]">抱歉，您访问的网址丢了。<a-button type="link" @click="handleError"><span class="text-[24px]">返回首页</span></a-button></p>
        </div>
      </div>
    </template>
    <template v-else>
      <a-result :status="error.statusCode">
        <template #extra>
          <div class="p-[10px]">
            <h2 class="text-[18px] my-[10px]">sorry，该页面发生了意料不到的错误</h2>
            <p class="text-[14px] my-[10px]">{{ error.statusCode }}</p>
            <p class="text-[14px] my-[10px]">{{ error.message }}</p>
            <p class="text-[14px] my-[10px]">{{ error.stack }}</p>
            <a-button type="primary" @click="handleError">返回首页</a-button>
          </div>
        </template>
      </a-result>
    </template>
  </div>
</template>

<script setup lang="ts">
const { error } = defineProps<{ error: object }>();

const handleError = () => clearError({
  redirect: '/',
});
</script>
