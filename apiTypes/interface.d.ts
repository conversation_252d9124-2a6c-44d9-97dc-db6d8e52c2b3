import type { PageTab } from '@/layouts/components/interface.d';
// 表格分页入参基础
export interface PageParams {
  pageNum: number;
  pageSize: number;
}

// 表格返回数据
export interface TableData<T = unknown> {
  records: T[];
  current: number; // 当前页码
  total: number; // 总记录数
  size: number;
  pages: number; // 总页数
}

// 用户信息
export interface UserInfo {
  umCode: string;
  umName: string;
  deptList: Array<string>;
}

// 蘑菇云配置菜单结构
export interface MenuItem {
  id: number;
  authCode: string;
  menuName: string;
  menuType: string; // 01目录， 02菜单(可访问路由)
  menuUrl: string; // 路由path
  menuDesc: string; // 用来判断是否是详情页（hidden）
  subMenus?: MenuItem[];
}

export interface PageTabInject {
  pageTabList?: Ref<PageTab[]>;
  deletPageTabListItem?: (path: string) => void;
}
