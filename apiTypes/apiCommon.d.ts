export interface EventResponse<T> {
  code: string;
  msg: string;
  data: T;
}

export interface SelectOptions {
  label: string;
  value: string;
  disabled?: boolean;
  children?: SelectOptions[];
  isLeaf?: boolean;
}

export interface EmployeeOptions {
  employeeName: string;
  employeeCode: string;
}

export interface ChannelSourceDetail {
  channelSourceDetailCode: string;
  channelSourceDetailName: string;
}
export interface ChannelSource {
  channelSourceCode: string;
  channelSourceName: string;
  detailList: ChannelSourceDetail[];
}

export interface BaseConstants {
  projectSourceList: SelectOptions[];
  customerTypeList: SelectOptions[];
  subsidyTypeList: SelectOptions[];
  specialPromiseTypeList: SelectOptions[];
}
