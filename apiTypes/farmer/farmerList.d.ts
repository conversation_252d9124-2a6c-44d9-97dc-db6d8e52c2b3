type Status = '01' | '02' | '03';
export interface FarmerList {
  index: string;
  farmerlistNo: string;
  farmerName: string;
  certificateNo: string;
  certificateType: string;
  executeFlag: boolean;
  idFarmerlistCustomInfo: string;
}
export interface EditFarmerList extends FarmerList {
  riskName: string;
  insuranceNums: string;
  statusList: Status[];
  mobileTelephone: string;
  customCode: string;
}

export interface EditFarmerListReq {
  farmerlistNo: string;
  farmerName: string;
  certificateNo: string;
  mobileTelephone: string;
  plantAddressCodeVillage: string;
  riskCode: string;
  pageSize: number;
  pageNum: number;
  plantAddressCodeProvince?: string;
  plantAddressCodeCity?: string;
  plantAddressCodeCounty?: string;
  plantAddressCodeTown?: string;
}

export interface EditFarmerListResult {
  list: EditFarmerList[];
  current: number;
  size: number;
  total: number;
}

export interface EditFarmerListRes {
  code: string;
  msg: string;
  data: EditFarmerListResult;
}

export interface RiskFarmerList extends FarmerList {
  riskruleContents: string;
}

export interface RiskFarmerListResult {
  list: RiskFarmerList[];
  current: number;
  size: number;
  total: number;
}

export interface RiskFarmerListRes {
  code: string;
  msg: string;
  data: RiskFarmerListResult;
}

export interface ErrorFarmerList extends FarmerList {
  errorruleContents: string;
}

export interface ErrorFarmerListResult {
  list: ErrorFarmerList[];
  current: number;
  size: number;
  total: number;
}

export interface ErrorFarmerListRes {
  code: string;
  msg: string;
  data: ErrorFarmerListResult;
}

export interface GetFarmerListByPageRes {
  code: string;
  msg: string;
  data: {
    current: number;
    size: number;
    total: number;
    records: unknown[];
  };
}

export interface InsuranceFarmerList {
  createdBy: string; // 操作人员
  importDate: string; // 导入日期
  farmerlistNo: string; // 投保清单编号
  farmerlistName: string; // 清单名称
  farmerlistStatusDesc: string; // 清单状态
  farmerlistStatus: string; // 清单状态(code)
  importCount: number; // 导入条数
  riskCount: number; // 风险条数
  errorCount: number; // 错误条数
  idAgranTemplateInfo: string; // 模版id
  oprRoleName: string; // 协保员｜业务专员
  riskAddressCodeCity: string;
  riskAddressCodeCounty: string;
  riskAddressCodeProvince: string;
  riskAddressCodeTown: string;
  riskAddressCodeVillage: string;
  addressName: string;
  departmentCode: string;
  buttonAuthList: string[];
  errorruleContents: string;
}

export interface InsuranceFarmerListRes {
  total: number;
  current: number;
  size: number;
  pages: number;
  records: InsuranceFarmerList[];
}
