import { IconJianguanNormalFont, IconDaohangQingdanguanliFont, MenuReplayDefaultFont, IcMenuDikuaizichanzhongxinFont, IcMenuQingdanguanliFont, IcMenuToubaoguanliFont, IcMenuYanbiaoguanliFont, IcMenuHebaoguanliFont, IcMenuJiaofeiguanliFont, IcMenuPigaiguanliFont, IcMenuXinxichaxunFont, IcMenuJianguanpingtaiFont, IcMenuBaodanguanliFont, IcMenuXitongweihuFont, IcMenuXinxixiugaiFont } from '@pafe/icons-icore-agr-an';
// 左侧菜单图标映射表
// 由于icon只能本地更新，需要跟蘑菇云的配置菜单做对应关系，若要新增icon，请在这里配置
// 配置原则是menuIcon: icon
/**
 * 具体步骤如下
 * 1、让UI老师上传icon到图标库，复制图标库上当前icon的名称
 * 2、将当前icon的名称复制到蘑菇云上的导航栏配置上
 * 3、若本地没有当前icon，需要本地更新@pafe/icons-icore-agr-an包，并且在MENUICON添加映射关系
 */
export const MENUICON: { [name: string]: unknown } = {
  IconJianguanNormalFont, // 监管平台
  IconDaohangQingdanguanliFont, // 清单管理
  MenuReplayDefaultFont, // 投保管理
  IcMenuDikuaizichanzhongxinFont, // 地块资产中心
  IcMenuQingdanguanliFont, // 清单管理
  IcMenuToubaoguanliFont, // 投保管理
  IcMenuYanbiaoguanliFont, // 验标管理
  IcMenuHebaoguanliFont, // 核保管理
  IcMenuJiaofeiguanliFont, // 缴费管理
  IcMenuPigaiguanliFont, // 批改管理
  IcMenuXinxichaxunFont, // 信息查询
  IcMenuXitongweihuFont, // 系统维护
  IcMenuJianguanpingtaiFont, // 监管平台
  IcMenuBaodanguanliFont, // 保单管理
  IcMenuXinxixiugaiFont, // 信息修改
};
