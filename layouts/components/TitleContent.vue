<template>
  <a-tooltip placement="right">
    <template #title>{{ title }}</template>
    <span class="sub-title-content">{{ title }}</span>
  </a-tooltip>
</template>
<script setup lang="ts">
defineProps<{
  title: string;
}>();

</script>
<style lang="less">
.title-content {
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
}
.sub-title-content {
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
}
</style>
