<template>
  <a-popover
    placement="rightTop"
    overlay-class-name="layout-navmenu-menu-popover-dropdown-menu-popover"
  >
    <div :class="(openKeyss || []).includes(item.key) ? 'px-[10px] rounded-[4px] hover:bg-[#F2F3F5] text-[#07c160]' : 'px-[10px] rounded-[4px] hover:bg-[#F2F3F5]'">
      {{ item.label }}
    </div>
    <template #content>
      <VerticalMenu v-model:open-keys="openKeyss" v-model:selected-keys="selectedKeys" class="child-item" :list="item.children" v-bind="$attrs" />
    </template>
  </a-popover>
</template>

<script setup lang="ts">
import VerticalMenu from './VerticalMenu.vue';

type MenuItemType = {
  label: string;
  menuUrl?: string;
  children?: MenuItemType[] | null;
  key: string;
};
defineProps<{
  item: MenuItemType;
}>();

const openKeyss = defineModel('openKeys');
const selectedKeys = defineModel('selectedKeys');
</script>

<style lang="less">
.layout-navmenu-menu-popover-dropdown-menu-popover {
.ant-popover-content {
  .ant-popover-arrow {
    display: none;
  }
  .ant-popover-inner {
    .ant-popover-inner-content {

    }
  }
}
}
</style>
