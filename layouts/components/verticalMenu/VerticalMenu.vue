<template>
  <ul class="vertical-menu shrink-0 flex list-none flex-col">
    <li v-for="(item, index) in list" :key="index" class="cursor-pointer my-[2px]">
      <DropdownMenu v-if="item.children && item.children.length" v-model:open-keys="openKeys" v-model:selected-keys="selectedKeys" :item="item" v-bind="$attrs" :origin="origin" />
      <MenuItem
        v-else
        :id="item.key"
        v-model:selected-keys="selectedKeys"
        v-model:open-keys="openKeys"
        :label="item.label"
        :menu-url="item.menuUrl"
        :children="item.children"
        v-bind="$attrs"
        :origin="origin"
      />
    </li>
  </ul>
</template>

<script setup lang="ts">
import type { MenuItemType } from '../interface.d';
import DropdownMenu from './DropdownMenu.vue';
import MenuItem from './MenuItem.vue';

const selectedKeys = defineModel('selectedKeys');

const openKeys = defineModel('openKeys');

defineProps<{
  list: MenuItemType[];
  origin: MenuItemType[];
}>();
</script>

<style lang="less">
.vertical-menu {
  margin: 0;
  padding: 0;
  li {
    font-size: 12px;
    color: #333333;
    line-height: 34px;
  }
  &.child-item {
    li {
      color: rgba(0,0,0,0.60);
    }
  }
}
</style>
