<template>
  <div :class="(selectedKeys || []).includes(props.id) ? 'px-[10px] rounded-[4px] bg-[#E9FAF1] text-[#07c160]' : 'px-[10px] rounded-[4px] hover:bg-[#F2F3F5]'" @click.stop="redirectTo">
    {{ props.label }}
  </div>
</template>
<script setup lang="ts">


type MenuItemType =  {
  label: string;
  menuUrl: string;
  children?: MenuItemType[] | null;
  id: string;
};

const props = defineProps<{
  label: string;
  menuUrl: string;
  id: number;
  children?: MenuItemType[]
}>()

const attrs = useAttrs()

const selectedKeys = defineModel('selectedKeys')

const openKeyss = defineModel('openKeys')

const findOpenKeysByObj = (obj: MenuItemType, targetValue: number, parentChain: number[]= []): number[] => {
  // 检查当前对象是否包含匹配的children值
  if (Array.isArray(obj.children) && obj.children.find(val => val.key === targetValue)) {
    return [obj.key, ...parentChain]
  }
  if (Array.isArray(obj.children)) {
    for (const item of obj.children) {
      if (Array.isArray(item.children)) {
        const result = findOpenKeysByObj(item, targetValue, [obj.key, ...parentChain])
        if (result) {
          return result
        }
      }
    }
  }
}

const redirectTo = () => {
  if (attrs.onClickItem) {
    selectedKeys.value = [props.id]
    // 重置openkeys
    const curOpenKeys: number[] = findOpenKeysByObj(attrs.origin, props.id);
    openKeyss.value = curOpenKeys ? curOpenKeys : [];
    attrs.onClickItem({item: {
      label: props.label,
      menuUrl: props.menuUrl,
      originItemValue: {
        label: props.label,
        menuUrl: props.menuUrl
      }
    }})
  }
}
</script>
<style lang="less">

</style>