<template>
  <div class="global-tab-container bg-white h-[34px] flex items-end border-box w-full sticky top-0 z-[100]">
    <div class="">
      <Tag
        v-if="pageTabList.length"
        :key="pageTabList[0].path"
        :class="['tag first-tag', activeTabPath === pageTabList[0].path ? 'active' : 'unactive']"
        :bordered="false"
        color="blue"
        @click="handleOnClick(pageTabList[0])"
      >
        <div class="tag-content bg-white flex px-[8px]"><VueIcon class="pre-icon" :icon="MenuHomeDefaultFont" /></div>
      </Tag>
    </div>
    <div v-show="showLeftBtn" class="left-btn w-[16px] h-[16px] flex self-center justify-center mx-[8px] cursor-pointer hover:bg-[rgba(0,0,0,0.05)] rounded-[2px]" @click="clickLeft">
      <VueIcon class="next-icon" :icon="IconChevronLeftFont" />
    </div>
    <div v-if="pageTabList.length" ref="pageTabContent" class="tag-wrapper text-left grow mr-[2px]" @scroll="scrollEvent">
      <template v-for="(item, index) in pageTabList" :key="item.path">
        <Tag
          v-if="index !== 0"
          :class="['tag', activeTabPath === item.path ? 'active' : 'unactive']"
          :bordered="false"
          :color="index === 0 ? 'blue' : ''"
          @click="handleOnClick(item)"
        >
          <div class="tag-content bg-white flex px-[8px]">
            <!-- <span class="bg-hack"></span> -->
            <span v-if="index !== 0"> {{ item.title }}</span>
            <span v-if="index !== 0" class="icon" @click="e => handleOnClose(e, index)">
              <VueIcon class="next-icon" :icon="IconCloseFont" />
            </span>
          </div>
        </Tag>
      </template>
    </div>
    <div
      v-show="showRightBtn"
      class="right-btn w-[16px] h-[16px] w-[16px] h-[16px] flex self-center justify-center mx-[8px] cursor-pointer hover:bg-[rgba(0,0,0,0.05)] rounded-[2px]"
      @click="clickRight"
    >
      <VueIcon class="next-icon" :icon="IconChevronRightFont" />
    </div>
    <div v-show="showBackBtn" class="flex items-center text-center back justify-self-end cursor-pointer" @click="backToOldSystem">
      <VueIcon class="back-icon" :icon="IconFanhuijiuxitongFont" />
      返回旧系统
    </div>
  </div>
</template>

<script setup lang="ts">
import { Tag } from 'ant-design-vue';
import type { RouteLocationNormalizedLoaded } from 'vue-router';
import { IconCloseFont, IconFanhuijiuxitongFont, MenuHomeDefaultFont, IconChevronLeftFont, IconChevronRightFont } from '@pafe/icons-icore-agr-an';
import { storeToRefs } from 'pinia';
import { debounce } from 'lodash-es';
import type { PageTab } from './interface.d';
import { useUserStore } from '@/stores/useUserStore';

const pageTabList = defineModel<PageTab[]>('pageTabList', { default: [] });

const activeTabPath = ref('/home');
const router = useRouter();
const route = useRoute();
const { menuList } = storeToRefs(useUserStore());
const pageTabContent = ref();
const showLeftBtn = ref(false);
const showRightBtn = ref(false);
const showBackBtn = ref(false);

/**
 * 页签功能主要说明
 * 1、页签功能会与浏览器的返回有冲突，默认用户使用页签功能，不使用浏览器返回
 * 2、新打开页面会新增一个页签
 * 3、页签新增与否是跟path有关，非fullpath，path一致的，会复用该页签
 * 4、页签切换时，实则是一个push操作，会自动携带上一次进该页面的query和params
 * 5、页签关闭时，如果是非当前打开的页签，则删除这个页签的数据
 * 6、页签关闭时，如果是当前打开的页签，一方面是删除这个页签的数据，还会push到最右侧的菜单
 * 7、首页是固定的，不可删除的
 */

const findTitle = (path: string) => {
  let title = '';

  const fintSubMenu = (list: MenuItem[]) => {
    for (const item of list) {
      if (item?.menuUrl && item.menuUrl === path) {
        title = item.menuName;
        break;
      }

      if (item?.subMenus?.length) {
        fintSubMenu(item.subMenus);
      }
    }
  };
  for (const item of menuList.value) {
    if (item?.menuUrl && item.menuUrl === path) {
      title = item.menuName;
      break;
    }

    if (item?.subMenus?.length) {
      fintSubMenu(item.subMenus);
    }
  }

  return title;
};
// 创建一个页签对象
const createPageItem = (routeObj: RouteLocationNormalizedLoaded): PageTab => {
  const menuName = findTitle(routeObj.path);
  const obj = {
    path: ['/', '/home'].includes(routeObj.path) ? '/home' : routeObj.path,
    title: menuName ? menuName : (routeObj.meta.title || routeObj.path) as string, // 优化获取左侧菜单名词
    fullPath: routeObj.fullPath,
    query: routeObj.query,
    params: routeObj.params,
  };

  return obj;
};

onMounted(() => {
  // 第一次加载，直接把当前页面的路径塞入到页签数组中
  if (['/', '/home'].includes(route.path)) {
    // 当前访问的是官网
    // pageTabList.value.push({
    //   path: '/home',
    //   title: route.meta.title || route.path,
    //   fullPath: route.fullPath,
    //   query: route.query,
    //   params: route.params,
    // });
    pageTabList.value.push(createPageItem(route));
  } else {
    // pageTabList.value = [{
    //   path: '/home',
    //   title: '',
    //   fullPath: '/home',
    // }, {
    //   path: route.path,
    //   title: route.meta.title || route.path,
    //   fullPath: route.fullPath,
    //   query: route.query,
    //   params: route.params,
    // }];
    pageTabList.value = [{
      path: '/home',
      title: '',
      fullPath: '/home',
    }, createPageItem(route)];
    activeTabPath.value = route.path;
  }
  showBackBtn.value = true;
});

/**
 * 页签手动关闭后，再次打开已关闭的页签页面不需要使用缓存
 * 通过链接参数的t来更新缓存
 */
router.beforeEach((to) => {
  // 服务端不会执行
  // 检查to页面是否在页签中
  const opened = pageTabList.value.find(val => val.path === to.path);

  if (!opened && !to.query.t) {
    // 新打开的页面，并且链接不带t参数，redirect到同页面并且新增一个t参数
    let newQuery = {};
    if (to.query) {
      newQuery = { ...to.query, t: new Date().getTime() };
    } else {
      newQuery = { t: new Date().getTime() };
    }
    return {
      ...to,
      query: newQuery,
    };
  } else if (opened && !to.query.t) {
    // 该页面页签已存在，并且链接不带t参数，默认使用缓存
    if (opened?.query?.t) {
      const newQuery = { ...to.query, t: opened.query.t };
      return {
        ...to,
        query: newQuery,
      };
    }
  }
});

router.afterEach((to) => {
  // 检查要跳转的路由是否已经在页签中
  const activeIndex = pageTabList.value.findIndex(val => val.path === to.path);
  if (activeIndex > -1) {
    // 命中
    activeTabPath.value = to.path;
    // 更新参数
    pageTabList.value.splice(activeIndex, 1, createPageItem(to));
    // 路由跳转时，需要把激活的页签带到可视区内
    setActivePageTabToView();
  } else {
    pageTabList.value.push(createPageItem(to));
    activeTabPath.value = to.path;
  }
});

// 关闭某个页签
const handleOnClose = async (event: MouseEvent, index: number) => {
  event?.stopPropagation();
  // 是否删除当前打开的页签
  const isCloseCurrentPageTab = activeTabPath.value === pageTabList.value[index].path;
  // 删除某个页签
  pageTabList.value.splice(index, 1);
  await nextTick();
  if (isCloseCurrentPageTab) {
    // 寻找最后一个页面的路由，跳过去
    const [lastPageTab] = pageTabList.value.slice(-1);
    // 说明只剩下首页的页签了，直接跳转到首页
    router.push({
      path: lastPageTab.path,
      query: lastPageTab.query || {},
      params: lastPageTab.params || {},
    });
  }
};

// 单独点击某个页签tab
const handleOnClick = (nav: PageTab) => {
  activeTabPath.value = nav.path;
  // 跳转回之前某个页面，需要把参数还原
  router.push({
    path: nav.path,
    query: nav.query || {},
    params: nav.params || {},
  });
};

const setActivePageTabToView = async () => {
  await nextTick();
  const dom: HTMLDivElement = pageTabContent.value;
  if (!dom) return;
  const { left: domLeft, right: domRight } = dom.getBoundingClientRect();
  // 获取选中页签
  const item: HTMLDivElement = pageTabContent.value.querySelector('.tag.active');
  if (item) {
    const { left: itemLeft, right: itemRight } = item.getBoundingClientRect();
    if (itemLeft < domLeft) {
      // 页签在容器左边
      pageTabContent.value.scrollLeft = dom.scrollLeft - (domLeft - itemLeft);
    } else if (itemRight > domRight) {
      // 页签在容器右边
      pageTabContent.value.scrollLeft = dom.scrollLeft + (itemRight - domRight);
    }
  }
};

const resetLeftRightBtn = () => {
  const dom: HTMLDivElement = pageTabContent.value;
  const clientWidth = dom.clientWidth || 0;
  const scrollWidth = dom.scrollWidth || 0;
  const scrollLeft = dom.scrollLeft || 0;

  if (scrollWidth > clientWidth) {
    if (scrollLeft === 0) {
      showLeftBtn.value = false;
      showRightBtn.value = true;
    } else if (scrollWidth - clientWidth === scrollLeft) {
      showLeftBtn.value = true;
      showRightBtn.value = false;
    } else {
      showLeftBtn.value = true;
      showRightBtn.value = true;
    }
  } else {
    showLeftBtn.value = false;
    showRightBtn.value = false;
  }
};

const clickLeft = () => {
  const dom: HTMLDivElement = pageTabContent.value;
  const scrollLeft = dom.scrollLeft;
  const clientWidth = dom.clientWidth - 100;

  if (scrollLeft > 0) {
    pageTabContent.value.scrollLeft = scrollLeft - clientWidth > 0 ? scrollLeft - clientWidth : 0;
  }

  resetLeftRightBtn();
};

const clickRight = () => {
  const dom: HTMLDivElement = pageTabContent.value;
  const scrollLeft = dom.scrollLeft;
  const clientWidth = dom.clientWidth - 100;

  pageTabContent.value.scrollLeft = scrollLeft + clientWidth;

  resetLeftRightBtn();
};

// 支持滚动
const scrollEvent = debounce(() => {
  resetLeftRightBtn();
}, 100);

watchEffect(async () => {
  // 页签新增或删除，计算一次是否需要展示左右标签
  if (pageTabList.value.length && pageTabContent.value) {
    // 展示页签左右icon
    // 把选中的页签展示到可视区内
    setActivePageTabToView();
    // 从新调整左右按钮位置
    resetLeftRightBtn();
  }
});

const backToOldSystem = () => {
  const { oldSystemUrl } = useRuntimeConfig().public || {};
  window.open(oldSystemUrl);
};
</script>

<style lang="less" scoped>
@bgColor: #F2F3F5;

@tabBgColor: #fff;

.global-tab-container {
  white-space: nowrap;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.55);
  letter-spacing: 0;
  flex-wrap: nowrap;
  .tag-wrapper {
    display: flex;
    overflow-y: hidden;
    overflow-x: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  .tag {
    background: @bgColor;
    position: relative;
    display: flex;
    min-width: auto;
    margin: 0;
    border: none;
    cursor: pointer;
    align-items: center;
    color: rgba(0, 0, 0, 0.55);
    letter-spacing: 0;
    height: 28px;
    user-select: none;
    box-shadow: none;
    outline: none;
    padding: 0px;
    border-radius: 0px;
    &:hover {
      &::before {
        display: none;
      }
    }
    .tag-content:hover {
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      background-color: @bgColor;
      position: relative;
      z-index: 3;
    }
    span {
      height: 28px;
      line-height: 28px;
      display: inline-block;
    }
    &::before {
      position: absolute;
      content: ' ';
      border-left-width: 1px;
      border-left-color: #D2D4D9;
      border-left-style: solid;
      height: 12px;
      left: -1px;
      z-index: 1;
    }

    &.first-tag {
      &::before {
        display: none;
      }
      &::after {
      position: absolute;
      content: ' ';
      border-left-width: 1px;
      border-left-color: #D2D4D9;
      border-left-style: solid;
      height: 12px;
      right: -1px;
      z-index: 1;
    }
    }
  }

  span:first-of-type {
    border-top-left-radius: 0;
  }

  .active {
    font-size: 12px;
    color: #06BD5E;
    display: flex;
    position: relative;
    z-index: 3;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    .tag-content {
      background-color: @bgColor;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
    }
    span {
      height: 28px;
      line-height: 28px;
      display: inline-block;
    }

    &::before {
      content: unset;
    }

    &::after {
      content: unset;
    }

    &:last-child {
      &::after {
        content: unset;
      }
    }
  }

  .icon {
    display: inline-block;
    justify-content: center;
    align-items: center;
    width: 16px;
    height: 16px;
    margin-left: 8px;
    z-index: 8;
  }
  .pre-icon {
    transform: translateY(2px);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .btn-icon {
    width: 18px;
    height: 18px;
    display: inline-block;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    margin-right: 2px;
  }
  .next-icon {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .back {
    padding-right: 30px;
    height: 100%;
    .back-icon {
      cursor: pointer;
      font-size: 18px;
    }
  }

}
</style>
