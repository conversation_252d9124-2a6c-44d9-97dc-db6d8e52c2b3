<template>
  <div v-if="visible" class="fixed top-0 z-[1000] browser-tips w-full bg-[rgba(240,62,62,0.2)] h-[46px] flex items-center px-[16px] box-border flex">
    <p class="text-[#F03E3E] p-0 m-0 grow text-center">{{ tips }}</p>
    <a class="ml-[4px] mr-[100px] shrink-0" href="/publicNoCache/browserUpdate.docx" target="_blank">下载帮助文档</a>
  </div>
</template>

<script setup lang="ts">
import { getBrowserInfo } from '@/utils/tools';
import { getHeader } from 'h3';

const visible = ref(false);
const tips = ref('');

const { isServer } = useRuntimeConfig();
const useAgent = useState('useAgent', () => '');
const nuxtApp = useNuxtApp();
if (isServer) {
  // 判断浏览器是否支持由服务端校验，因为客户端校验可能入口文件就已经报错了，这个错误提示无法展示出来
  useAgent.value = getHeader(nuxtApp.ssrContext?.event, 'User-Agent') || '';
}

const info = getBrowserInfo(useAgent.value);
if (info.browser === 'other') {
  tips.value = '尊敬的用户，您当前使用的浏览器不支持访问，建议使用88版本及以上的chrome浏览器进行访问';
  visible.value = true;
} else if ((info.browser === 'chrome' && info.version < 87) || (info.browser === 'firefox' && info.version < 78) || (info.browser === 'safari' && info.version < 14) || (info.browser === 'Edge' && info.version < 87)) {
  tips.value = '尊敬的用户，您当前访问的浏览器版本过低，建议使用88版本及以上的chrome浏览器进行访问';
  visible.value = true;
} else {
  visible.value = false;
}
</script>
