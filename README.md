# 产险安农新农险核心系统 ![Static Badge](https://img.shields.io/badge/Vue3-42B883) ![Static Badge](https://img.shields.io/badge/Vite-8A2BE2)  ![Static Badge](https://img.shields.io/badge/TypeScript-e482c3) ![Static Badge](https://img.shields.io/badge/Pinia-ffd859) ![Static Badge](https://img.shields.io/badge/AntdV-1677ff) ![Static Badge](https://img.shields.io/badge/Pnpm-fc5531) ![Static Badge](https://img.shields.io/badge/VsCode-fff) ![Static Badge](https://img.shields.io/badge/Tailwind-13C2C2) ![Static Badge](https://img.shields.io/badge/Nuxt3-8A2BE2) 

## 准备工作
### 1.代码拉取
- 建立自己的开发分支，更新当前版本代码到开发分支上
- Git
  ```javascript
  <NAME_EMAIL>:git/icore-agr-an-web.git
  git fetch origin
  git checkout xxxx
  git rebase xxxx origin/master
  git push
  ```
### 2.开发工具
- node 版本18以上，可以使用nvm进行切换
- 安装pnpm
```shell
corepack enable
nano ~/.zshrc #编辑.zshrc文件，添加一行命令 echo 'export COREPACK_NPM_REGISTRY=https://maven.paic.com.cn/repository/npm' >> ~/.zshrc # 镜像源换成maven库
# 按下按键control+O，再按enter键，再按control+X键即可保存退出
source ~/.zshrc # 或者重新打开一个终端
corepack prepare pnpm@latest --activate # 安装最新版本，如果是指定版本可以用corepack prepare pnpm@x.x.x --activate
pnpm -v # 如果有打印版本号，则说明安装成功，如果没有，可能﻿corepack没有开启，尝试使用corepack enable，然后再重新安装一次
```
- Pnpm
  ```javascript
  pnpm install
  ```
- VsCode
  `推荐插件安装：`
  1. 打开vscode插件面板
  2. 搜索`@recommended`
  3. 点击面版右侧云下载按钮一键安装
     `启用Volar takeover模式` [点击跳转](https://cn.vuejs.org/guide/typescript/overview.html#volar-takeover-mode)

## 项目启动

  ```javascript
  pnpm dev
  ```

## 项目打包

  ```javascript
  # 测试包
  pnpm build:test
  # 生产包
  pnpm build:prod
  ```

## 快速上手

- [Vue3](https://cn.vuejs.org/guide/introduction.html)
- [AntdV](https://www.antdv.com/components/overview-cn)
- [Pinia](https://pinia.vuejs.org/zh/getting-started.html)
- [Vite](https://cn.vitejs.dev/guide/)
- [Tailwind](https://www.tailwindcss.cn/)

## CODE 实践

### 关于组织代码
1. 请使用SFC格式进行代码编写，避免使用其他方式进行编写。代码禁止编写tsx模版，禁止使用h函数，如若需要，请发起评审。

### 关于组件使用
1. 自定义的组件使用使用驼峰写法（如：DepartmentSearch），UI框架组件使用横杠写法（如：a-input）
2. 组件的属性同样使用驼峰写法

### 关于文件命名
1. 关于命名：组件以大驼峰规则命名，其他目录、文件以小驼峰规则命名。

### 关于自动引入原则
1.已自动引入antdv，vue，vue-router，/components的所有组件和钩子方法，页面无需再次单独import，例如 const route = useRoute(); const t = ref<number[]>();
2.注意pinia的方法并没有自动引入。

### 关于antdv使用原则
1. 禁止使用antd vue的icon图标或本地添加，统一使用产险的在线图标库@pafe/

### 关于UI稿
1. 关于UI稿的font-family不必copy，除非是特殊字体，其他使用默认即可。

### 关于CSS
1. SFC文件编写CSS使用scoped，若要覆盖antdv样式的，可以使用deep。
2. 关于z-index的取值范围，页面只允许取值1～100，避免页面的元素覆盖住面包屑和antdv的组件。
3. 允许使用tailwindcss进行开发，一方面可以避免样式覆盖问题，另一方面提升开发效率。
4. 关于主题色请使用已经定好的css变量或者tailwindcss的类名，切勿自己写死样式。
5. 关于布局，系统不强制使用栅格系统。但是，不建议使用antdv的栅格系统，直接使用浏览器的grid布局即可，taiwindcss有完善的API。
6. 禁止使用important，css可以添加classname增加优先级。

### 关于ts使用原则
1. 关于ts报错信息、eslint报错信息，请修改完成方可提交代码到远程分支。
2. 勿随意使用any类型。
3. 定义api时需定义入参、出参类型，interface以大驼峰规则命名。

### 关于配置文件说明
1. 勿随意修改项目的配置文件（例如：commitlint.config.ts、.config.ts、tsconfig.(app|node|vitest).json、postcss.config.js、vite.config.ts、vitest.config.ts）。
2、修改这些配置文件请发起评审

### 关于页面缓存使用原则
1. 页面默认是使用缓存的，如果不使用缓存，可在app.vue文件中添加exclude。
2. 页签列表中的页面相互跳转时默认使用缓存，当页面关闭后再次打开时，默认不使用缓存。
3. 若跳历史页面需要更新缓存的，可在route.query.t = new Date().getTime()。

### 关于pinia可存储说明
1. 添加存储全局共享属性，是全局使用的才可使用pinia，如果只有一两个场景的，不建议使用。
2. 新增一个全局数据，请发起评审。
3. store目录命名使用usexxx.ts，保持一层目录即可，切勿新增文件夹。

### 关于router说明
1. 勿随意添加子路由，若需要请发起评审
2. routes目录是分模块的，一个模块对应一个文件即可，模块里面的路由注册全部在一个文件中，勿新增目录

### 关于npm包维护原则
1. 勿随意增加npm包，或者添加packages的目录。
2. 若要添加请发起评审，由专门的人进行添加。
3. 会锁定重要包的版本，例如vue,vue-router,antdv等包，定期进行手动更新。

### 关于抽离复用原则
1. 逻辑复用、抽离请使用[hooks](https://cn.vuejs.org/guide/reusability/composables.html)。
2. 公用基础组件（如机构选择器）请勿添加业务逻辑，将业务逻辑抽离至父组件可以避免出现大量的相似组件。
3. 抽离成基础组件原则是被使用的页面比较多，是属于页面组成的基础组件。若不符合，遵循旧近原则，写在当前页面的目录下即可。

### 关于utils目录使用原则
1. utils目录是全局共用的工具类方法集合，非全局使用的勿放进来，按照就近原则直接写到当前页面文件即可，无需过于分清该方法是工具类方法就一定放到utils目录。
2. 页面中的常量、数据类型定义分别抽离到单独的文件中，公共的常量统一放在src/utils/constants.ts。这个同样遵循就近原则。

### 关于lodash使用
1. 禁止使用lodash get的方法。
  
### 关于支持浏览器版本
1. ant-design-vue默认是支持chrome 88 以上，该项目支持最低版本是chrome 60。所以，需要设置样式兼容模式，倘若要覆盖组件样式，请在组件自定义一个classname，然后使用该classname增加css权重，然可能覆盖不了。

### 关于本地联调后端本地接口
由于本地默认走测试接口，倘若需要某个接口需要调后端本地接口，可修改vite.config.ts，在proxy添加一条转发规则
```javascript
proxy: {
  '/gateway/icore-agr-an.compliance/mapingConfig/': {
    target: 'http://**************:9006',
    changeOrigin: true,
    rewrite: (path) => '/mapingConfig/',
  },
}
```

### 关于左侧菜单添加菜单
1. 需要在蘑菇云配置才能看的见
2. 倘若需要给菜单加图标，需要修改@/components/Layout/MenuBar/MenuIconMap.ts配置文件，把蘑菇云的anthcode与icon库的icon关联
3. 更新npm包


## 项目模块

业务页面（src/pages）：本次监管需求的代码统一放在src/pages/compliance；
组件（src/components）：公用的界面抽离组件先统一放src/components；
工具类：统一抽离到src/utils；
hooks：统一抽离到src/hooks；

### 开发经验总结

如果要使用服务端渲染获取数据，只能在setup作用域内用async await请求数据，promise then服务端渲染无效，还是在客户端才调接口；
ant design vue 官方推荐的 message.useMessage不能用，与nuxt不兼容，会白屏；
<br/>

如果需要在通过defineExpose子组件内抛出方法或属性，则不能在defineExpose前面写await，会导致抛不出方法。
错误eg：
const req = await usePost('...')
defineExpose({
  validate: () => {}
});

正确eg:
defineExpose({
  validate: () => {}
});
const req = await usePost('...')