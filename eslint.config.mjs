import withNuxt from './.nuxt/eslint.config.mjs';
import eslintPluginPrettier from 'eslint-plugin-prettier';
import eslintConfigPrettier from 'eslint-config-prettier';
import prettierRecommended from 'eslint-plugin-prettier/recommended';

export default withNuxt([
  {
    files: ['**/*.ts', '**/**/*.vue'],
    plugins: {
      prettier: eslintPluginPrettier,
    },
    ...prettierRecommended,
    ...eslintConfigPrettier,
    rules: {
      'vue/no-multiple-template-root': 'off', // 确保 Vue 组件的模板（template）中只有一个根元素
      'vue/singleline-html-element-content-newline': 'off', // 标签内容不必换行
      'vue/max-attributes-per-line': [2, { singleline: 40 }], // 允许当行最多8个属性
      'prettier/prettier': 'error', // Ensure Prettier rules are enforced
      "vue/html-self-closing": 0,
    },
  },
]);
