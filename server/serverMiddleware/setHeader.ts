export default defineEventHandler((event) => {
  const urlObj = getRequestURL(event);
  console.log(event, '请求原始链接：', getRequestURL(event), new Date().getTime());
  if (/^\/api/.test(urlObj.pathname)) {
    // 是接口才设置请求头
    const cookies = parseCookies(event);
    const { xPortalToken } = useRuntimeConfig().public || {};
    const cookiesKey = Object.keys(cookies) || [];
    const ScEnv = getHeader(event, 'X-Pa-Sc-Env') || ''; // 灰度分发标识

    let cookieContent = cookiesKey.reduce((accumulator, currentValue) => {
      return `${accumulator}${currentValue}=${encodeURIComponent(cookies[currentValue])};`;
    }, '');

    cookieContent = cookieContent.replace(/;$/, '');

    const headers = {
      Accept: 'application/json',
      'X-Portal-Token': xPortalToken,
      Cookie: cookieContent,
      'X-Pa-Sc-Env': ScEnv,
    };

    event.context.headers = headers;
    console.log(event, '中间件setHeader设置请求头成功：', event.context.headers);
  }
  appendResponseHeader(event, 'traceId', event.context.traceId);
});
