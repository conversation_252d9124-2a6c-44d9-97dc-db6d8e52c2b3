import redisDriver from 'unstorage/drivers/redis';
import crypto from 'node:crypto';

const sha1Hash = (data) => {
  return crypto.createHash('sha1').update(data).digest('hex');
};

/** 解密 */
const decrypt = (crypted, key) => {
  // iv偏移
  // const iv = 'pidms20180327!@#';
  const iv = null;
  crypted = Buffer.from(crypted, 'hex').toString('binary');
  const decipher = crypto.createDecipheriv('aes-128-ecb', key, iv);
  let decoded: string = decipher.update(crypted, 'binary', 'utf8');
  decoded += decipher.final('utf8');
  return decoded;
};

export default defineNitroPlugin(async (nitroApp) => {
  const storage = useStorage();
  const { cyberark, redis } = useRuntimeConfig();
  // 清除cache
  // await storage.unmount('cache');
  let psd = '';
  try {
    const res = await $fetch(cyberark.url, {
      method: 'post',
      body: {
        appId: cyberark.appId,
        safe: cyberark.safe,
        folder: cyberark.folder,
        object: cyberark.object,
        reason: 'xxx',
        // encrypt_ver: cyberark.encryptver,
        sign: sha1Hash(`${cyberark.appId}&${cyberark.appkey}`),
      },
    });

    psd = decrypt(res?.password || '', cyberark.appkey);
    nitroApp.configs = { setCd: res.code, setPd: (psd || '').substring(0, 12) };
    const driver = redisDriver({
      base: '{unstorage}',
      cluster: [
        {
          port: redis.port,
          host: redis.host,
        },
      ],
      clusterOptions: {
        clusterRetryStrategy: (times) => {
          // 链接3次都连不上，则放弃连接，需要重新启动才能连接
          if (times > 3) {
            return '100';
          } else {
            return 1000;
          }
        },
        redisOptions: {
          password: psd,
        },
      },
      // host: redis.host,
      // port: redis.port,
      // password: psd,
      reconnectOnError: (err) => {
        console.log('---------reconnectOnError--------------------');
        console.log('err', err);
      },
    });

    storage.mount('redis', driver); // 当使用redis作为key启动存储操作时，将调用其挂载的驱动程序，例如useStorage('redis')，则是使用该driver的驱动程序
  } catch (err) {
    console.log('err', err);
  }
});
