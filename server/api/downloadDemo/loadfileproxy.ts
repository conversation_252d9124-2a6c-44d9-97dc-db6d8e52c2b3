// server从真正的后端获取文档流转发到客户端
export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.compliance;
  const query = getQuery(event)
  try {
    const res = await $fetch(baseUrl + '/reportResult/downloadResultList', {
      method: 'get',
      headers: event.context.headers,
      query,
      onResponse({ response }) {
        // 把后端返回的请求头塞到当前的请求头上
        appendResponseHeaders(event, {
          'content-type': response.headers.get('Content-Type') as string,
          'content-disposition': response.headers.get('Content-Disposition') as string
        })
      }
    })

    return res
  } catch (err){
    console.error(event, err)
    return err
  }
})