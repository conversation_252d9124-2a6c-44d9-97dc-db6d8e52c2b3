// sse https://nitro.unjs.io/guide/websocket#server-sent-events-sse
export default defineEventHandler(async (event) => {
  const eventStream = createEventStream(event);

  let i = 0;

  const interval = setInterval(async () => {
    
    i = i + 1;
    if (i === 10) {
      // 服务端主动关闭
      clearInterval(interval);
      await eventStream.close();
    } else {
      // 只能接受字符串
      await eventStream.push(JSON.stringify({
        times: i
      }));
    }
  }, 1000)

  eventStream.onClosed( async () => {
    // 客户端发起了关闭请求
    console.log(event, '---客户端发起了关闭请求---')
    clearInterval(interval);
    await eventStream.close()
  })

  return eventStream.send()
})