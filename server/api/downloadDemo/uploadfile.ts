import fs from 'node:fs'
import path from 'node:path'
// 接收客户端上传的文件方法demo
export default defineEventHandler(async (event) => {
  // 发起真正的请求
  const formdata = await readMultipartFormData(event)
  try {

    for (let item of formdata) {
      let filePath = path.join(process.cwd(), 'public', item.filename)
      fs.writeFileSync(filePath, item.data)
    }
    
    return { code: '000000', msg: '上传成功'}
  } catch (err){
    console.error(event, err)
    return err
  }
})