// server层接收文件流转发到真正的后端
export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  const baseUrl = host + gateWay + service.compliance;
  const formData = await readFormData(event);
  console.log(event,'---------/pageConfig/interface/uploadTemplate-----------', event.context.headers)
  try {
    const res = await $fetch(baseUrl + '/pageConfig/interface/uploadTemplate', {
      method: 'post',
      headers: {...event.context.headers, 'Content-Disposition': formData}, // 这个头部Content-Disposition是必须的，客户端发起的真正后端请求（非通过BFF转发）也需要该字段
      body: formData
    })
    console.log(event,'-----res-----', res)
    return res
  } catch (err){
    console.error(event, err)
    return err
  }
})