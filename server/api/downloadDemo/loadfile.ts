import fs from 'node:fs'
import path from 'node:path'
// 发生文档流给客户端下载方法demo
export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  const baseUrl = host + gateWay + service.compliance
  const query = getQuery(event)
  console.log(event, '---------/api/download/loadfile-----------')
  try {
    appendResponseHeaders(event, {
      'content-type': 'application/docx',
      'content-disposition': `attachment;filename=${encodeURIComponent('视频')}.docx`
    })
    return sendStream(event, fs.createReadStream(path.join(process.cwd(), 'public/doc/mod.docx')))
  } catch (err) {
    console.error(event, err)
    return err
  }
})



