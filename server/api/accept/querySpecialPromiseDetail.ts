// 特约详情
import type { SpecialDetailRes } from '@/apiTypes/insure';
export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {};
  // 发起真正的请求
  const baseUrl = host + gateWay + service.accept;
  const url = baseUrl + '/specialPromise/querySpecialPromiseDetail';
  // const url = 'http://**************:9009/specialPromise/querySpecialPromiseDetail'
  const query = getQuery(event);
  console.log(event, '接口入参：', query);
  console.log(event, '后端接口：', url);
  try {
    const res: SpecialDetailRes = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
      query,
    });
    console.log(event, '后端返回内容：', res);
    const newRes: SpecialDetailRes = {
      code: res.code,
      msg: res.msg,
      data: {
        idSpecialPromiseDefine: res?.data?.idSpecialPromiseDefine,
        specialPromiseDept: res?.data?.specialPromiseDept,
        departmentName: res?.data?.departmentName,
        specialPromiseCode: res?.data?.specialPromiseCode,
        specialPromiseName: res?.data?.specialPromiseName,
        specialPromiseType: res?.data?.specialPromiseType,
        specialPromiseTypeName: res?.data?.specialPromiseTypeName,
        technicProduct:
          (res?.data?.specialPromiseProRelList || [])
            .map((item) => {
              return item.technicProductCode + item.technicProductName;
            })
            ?.join('，') || '',
        marketProduct:
          (res?.data?.specialPromiseProRelList || [])
            .map((item) => {
              return item.marketProductCode + item.marketProductName;
            })
            ?.join('，') || '',
        adaptDepartment:
          (res?.data?.specialPromiseDeptRelList || [])
            .map((item) => {
              return item.adaptDepartmentName;
            })
            ?.join('，') || '',
        specialPromiseProRelList: (res?.data?.specialPromiseProRelList || []).map((item) => {
          return {
            ...item,
            marketProductName: item.marketProductCode + item.marketProductName,
            technicProductName: item.technicProductCode + item.technicProductName,
          };
        }),
        specialPromiseControlList: res?.data?.specialPromiseControlList || [],
        specialPromiseDeptRelList: res?.data?.specialPromiseDeptRelList || [],
        isShowAudit: res?.data?.isShowAudit,
      },
    };
    console.log(event, '返回前端内容：', newRes);
    return newRes;
  } catch (err) {
    console.error(event, err);
    return err;
  }
});
