// 特约详情
import type { SpecialDetailRes } from '@/apiTypes/insure'
export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.accept;
  const url = baseUrl + '/specialPromise/querySpecialPromiseEditDetail';
  // const url = 'http://**************:9009/specialPromise/querySpecialPromiseEditDetail'
  const body = await readBody(event);
  console.log(event, '接口入参：', body);
  console.log(event, '后端接口：', url);
  try {
    const res: SpecialDetailRes = await $fetch(url, {
      method: 'post',
      headers: event.context.headers,
      body,
    })
    console.log(event, '后端返回内容：', res);
    const newRes: SpecialDetailRes = {
      code: res.code,
      msg: res.msg,
      data: {
        idSpecialPromiseDefine: res?.data?.idSpecialPromiseDefine,
        lockVersion: res?.data?.lockVersion,
        specialPromiseDept: res?.data?.specialPromiseDept,
        specialPromiseCode: res?.data?.specialPromiseCode,
        specialPromiseName: res?.data?.specialPromiseName,
        specialPromiseType: res?.data?.specialPromiseType,
        specialPromiseTypeName: res?.data?.specialPromiseTypeName,
        specialPromiseProRelList: res?.data?.specialPromiseProRelList || [],
        specialPromiseDeptRelList: res?.data?.specialPromiseDeptRelList || [],
        specialPromiseControlList: res?.data?.specialPromiseControlList || []
      }
    }
    console.log(event, '返回前端内容：', newRes);
    return newRes;

  } catch (err){
    console.error(event, err)
    return err
  }
})