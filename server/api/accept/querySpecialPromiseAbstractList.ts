// 特约设置列表
import type { SpecialListRes } from '@/apiTypes/insure'
export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.accept;
  const url = baseUrl + '/specialPromise/querySpecialPromiseAbstractList';
  // const url = 'http://**************:9009/specialPromise/querySpecialPromiseAbstractList'
  const body = await readBody(event);
  console.log(event, '接口入参：', body);
  console.log(event, '后端接口：', url);
  try {
    const res: SpecialListRes = await $fetch(url, {
      method: 'post',
      headers: event.context.headers,
      body,
    })
    console.log(event, '后端返回内容：', res);
    const newRes: SpecialListRes = {
      code: res.code,
      msg: res.msg,
      data: {
        total: res?.data?.total || 0,
        pageSize: res?.data?.size || 10,
        pageNum: res?.data?.current || 1,
        pages: res?.data?.pages || 0,
        list: []
      },
    }
    if (res.code === '000000') {
      newRes.data.list = (res?.data?.records || []).map((item) => {
        return {
          idSpecialPromiseDefine: item.idSpecialPromiseDefine || '',
          departmentCode: item.departmentCode || '',
          departmentName: item.departmentName || '',
          marketProductName: item.marketProductName || '',
          specialPromiseCode: item.specialPromiseCode || '',
          specialPromiseName: item.specialPromiseName || '',
          specialPromiseType: item.specialPromiseType || '',
          specialPromiseTypeName:item.specialPromiseTypeName || '',
          specialPromiseDesc: item.specialPromiseDesc || '',
          updateTime: item.updateTime || '',
          createdBy: item.createdBy || '',
          specialPromiseStatus: item.specialPromiseStatus || '',
          specialPromiseStatusName: item.specialPromiseStatusName || '',
          createdRole: item.createdRole || '',
          disabled: item.disabled || false,
        }
      })
    }
    console.log(event, '返回前端内容：', newRes);
    return newRes;

  } catch (err){
    console.error(event, err)
    return err
  }
})