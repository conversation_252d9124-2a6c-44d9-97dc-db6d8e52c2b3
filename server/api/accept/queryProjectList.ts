// 查询商机来源
import type { EventResponse } from '@/apiTypes/apiCommon'
import type { BusinessProject } from '@/apiTypes/insure'
export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.accept;
  const url = baseUrl + '/saleInfo/queryProjectList';
  // const url = 'http://*************:9009/saleInfo/queryProjectList';
  const body = await readBody(event);
  console.log(event, '接口入参：', body);
  console.log(event, '后端接口：', url);
  try {
    const res: EventResponse<BusinessProject> = await $fetch(url, {
      method: 'post',
      headers: event.context.headers,
      body,
    })
    console.log(event, '后端返回内容：', res);
    if (res.code === '000000' && res.data && res.data.list && Array.isArray(res.data.list)) {
      const data = res.data.list.map(val => {
        return {
          value: val.projectCode || '',
          label: val.projectName || ''
        };
      });
      console.log(event, '返回前端内容：', data);
      res.data = data;
    }

    return res;

  } catch (err){
    console.error(event, err)
    return err
  }
})