// 查询客户简称
import type { EventResponse } from '@/apiTypes/apiCommon'
import type { CustomerInfo } from '@/apiTypes/insure'
export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.accept;
  // const url = baseUrl + '/saleInfo/queryCustomerList';
  const url = 'http://*************:9010/ums/queryUnderwritePage';
  const body = await readBody(event);
  console.log(event, '接口入参：', body);
  console.log(event, '后端接口：', url);
  try {
    const res: EventResponse<CustomerInfo[]> = await $fetch(url, {
      method: 'post',
      headers: event.context.headers,
      body,
    })
    console.log(event, '后端返回内容：', res);
    return res;

  } catch (err){
    console.error(event, err)
    return err
  }
})