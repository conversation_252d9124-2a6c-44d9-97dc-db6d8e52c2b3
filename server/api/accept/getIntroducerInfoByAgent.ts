// 查询主介绍人名称
import type { EventResponse } from '@/apiTypes/apiCommon'
import type { IntroducerInfo } from '@/apiTypes/insure'
export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.accept;
  const query = getQuery(event);
  const url = baseUrl + '/saleInfo/getIntroducerInfoByAgent';
  // const url = 'http://*************:9009/saleInfo/getIntroducerInfoByAgent';
  console.log(event, '接口入参：', query);
  console.log(event, '后端接口：', url);
  try {
    const res: EventResponse<IntroducerInfo> = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
      query,
    })
    console.log(event, '后端返回内容：', res);
    return {
      code: res?.code,
      msg: res?.msg,
      data: {
        agentName: res?.data?.chineseName,
        business: res?.data?.business
      }
    }
  } catch (err){
    console.error(event, err)
    return err
  }
})