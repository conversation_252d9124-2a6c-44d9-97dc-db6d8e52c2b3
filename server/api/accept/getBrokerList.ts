// 查询经纪人列表
import type { EventResponse } from '@/apiTypes/apiCommon'
import type { RiskBrokerInfo } from '@/apiTypes/insure'
export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.accept;
  const query = getQuery(event);
  const url = baseUrl + '/saleInfo/getBrokerList';
  // const url = 'http://*************:9009/saleInfo/getBrokerList';
  console.log(event, '接口入参：', query);
  console.log(event, '后端接口：', url);
  try {
    const res: EventResponse<RiskBrokerInfo[]> = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
      query,
    })
    console.log(event, '后端返回内容：', res);
    if (res.code === '000000' && res.data && Array.isArray(res.data)) {
      const data = res.data.map(val => {
        return {
          label: val.brokerCode + val.chineseName,
          value: val.brokerCode
        };
      });
      console.log(event, '返回前端内容：', data);
      res.data = data;
    }

    return res;

  } catch (err){
    console.error(event, err)
    return err
  }
})