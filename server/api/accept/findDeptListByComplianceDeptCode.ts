// 查询二级机构
import type { SelectOptions } from '@/apiTypes/apiCommon';
interface SecondDeptListBackRes {
  code: string;
  msg: string;
  data: Array<{
    departmentCode: string;
    departmentName: string;
  }>;
}
interface SecondDeptListFrontRes {
  code: string;
  msg: string;
  data: SelectOptions[];
}
export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.accept;
  const url = baseUrl + '/specialPromise/findDeptListByComplianceDeptCode';
  // const url = 'http://**************:9009/specialPromise/findDeptListByComplianceDeptCode'
  const query = getQuery(event);
  console.log(event, '接口入参：', query);
  console.log(event, '后端接口：', url);
  try {
    const res: SecondDeptListBackRes = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
      query,
    })
    console.log(event, '后端返回内容：', res);
    const newRes: SecondDeptListFrontRes = {
      code: res?.code,
      msg: res?.msg,
      data: res?.data?.map((item) => {
        return {
          value: item?.departmentCode || '',
          label: item?.departmentName?.replace('分公司', '') || '',
        }
      }) || [],
    }
    console.log(event, '返回前端内容：', newRes);
    return newRes;
  } catch (err){
    console.error(event, err)
    return err
  }
})