// 技术产品、市场产品
import type { SelectOptions } from '@/apiTypes/apiCommon';
interface MarketProductBackRes {
  code: string;
  msg: string;
  data: Array<{
    technicProductCode: string;
    technicProductName: string;
    marketTargetProductDTOList: Array<{
      marketProductCode: string;
      marketProductName: string;
    }>
  }>;
}
interface MarketProductFrontRes {
  code: string;
  msg: string;
  data: SelectOptions[];
}
export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.accept;
  const url = baseUrl + '/specialPromise/queryTechnicMarketProductList';
  // const url = 'http://**************:9009/specialPromise/queryTechnicMarketProductList'
  const body = await readBody(event);
  console.log(event, '接口入参：', body);
  console.log(event, '后端接口：', url);
  try {
    const res: MarketProductBackRes = await $fetch(url, {
      method: 'post',
      headers: event.context.headers,
      body,
    })
    console.log(event, '后端返回内容：', res);
    const newRes: MarketProductFrontRes = {
      code: res?.code,
      msg: res?.msg,
      data: res?.data?.map((tpItem) => {
        return {
          value: tpItem?.technicProductCode || '',
          label: tpItem.technicProductCode + tpItem.technicProductName,
          children: tpItem?.marketTargetProductDTOList?.map((mpItem) => {
            return {
              value: mpItem?.marketProductCode || '',
              label: mpItem.marketProductCode + mpItem.marketProductName
            }
          }) || []
        }
      }) || [],
    }
    console.log(event, '返回前端内容：', newRes);
    return newRes;
  } catch (err){
    console.error(event, err)
    return err
  }
})