export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  const baseUrl = host + gateWay + service.compliance
  const query = getQuery(event)
  const url = `${baseUrl}/pageConfig/interface/get/${query.id}`;
  console.log(event, '后端接口：', url);
  try {
    const res = await $fetch(url, {
      method: 'get',
      headers: event.context.headers
    })
    console.log(event,'=----res====', res)
    return res
  } catch (err) {
    console.error(event, err)
    return err
  }
})