export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.compliance;
  const urlObj = getRequestURL(event)
  const body = await readBody(event)
  
  try {
    console.log(event, 'url:', baseUrl + '/reportResult/batchUnUpload')
    console.log(event, 'body:', body)
    const res = await $fetch(baseUrl + '/reportResult/batchUnUpload', {
      method: 'post',
      headers: {...event.context.headers},
      body: body
    })
    console.log(event,'res:', res)
    return res
  } catch (err){
    console.error(event, err)
    return err
  }
})