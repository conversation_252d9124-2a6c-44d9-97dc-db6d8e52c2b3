export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.compliance;
  const urlObj = getRequestURL(event)
  const query = getQuery(event)
  const url = baseUrl + `/pageConfig/platform/get/${query.id}`;
  console.log(event, '后端接口', url)
  try {
    const res = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
    })
    console.log(event,'=----res====', res)
    return res
  } catch (err){
    console.error(event, err)
    return err
  }
})