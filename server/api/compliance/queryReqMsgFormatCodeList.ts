export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  const baseUrl = host + gateWay + service.compliance
  const url = baseUrl + '/pageConfig/platform/listReqMsgFormatCodeList'
  console.log(event, '后端接口', url)
  try {
    const res = await $fetch(url, {
      method: 'post',
      headers: event.context.headers
    })
    console.log(event,'=----res====', res)
    return res
  } catch (err){
    console.error(event, err)
    return err
  }
})