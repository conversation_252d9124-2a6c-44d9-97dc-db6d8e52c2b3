export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.compliance;
  const urlObj = getRequestURL(event)
  const body = await readBody(event)
  const url = baseUrl + '/platErrorHandle/addErrorHandle';
  console.log(event, '后端接口：',url)
  try {
    const res = await $fetch(url, {
      method: 'post',
      headers: event.context.headers,
      body: body
    })
    console.log(event,'=----res====', res)
    return res
  } catch (err){
    console.log(event, err)
    return err
  }
})