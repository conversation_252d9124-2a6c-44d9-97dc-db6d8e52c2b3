export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.compliance;
  const urlObj = getRequestURL(event)
  const formData = await readFormData(event);
  const url = baseUrl + '/reportResult/refile';
  console.log(event,'后端接口', url)
  try {
    const res = await $fetch(url, {
      method: 'post',
      headers: { ...event.context.headers, 'Content-Disposition': formData },
      body: formData
    })
    console.log(event, '=----res====', res)
    return res
  } catch (err) {
    console.error(event, err)
    return err
  }
})