import type { GetFarmerListByPageRes, ErrorFarmerListRes } from '@/apiTypes/farmer/farmerList';

export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {};
  // 发起真正的请求
  const baseUrl = host + gateWay + service.farmer;
  const body = await readBody(event);

  const url = baseUrl + '/edrCust/getEdrFarmerErrorRuleList';

  console.log('入参：', body);
  console.log('后端接口：', url);
  console.log('后端接口入参：', body);

  try {
    const res: GetFarmerListByPageRes = await $fetch(url, {
      method: 'post',
      headers: event.context.headers,
      body: body,
    });

    console.log('后端返回：', res);

    const resData: ErrorFarmerListRes = {
      code: res.code,
      msg: res.msg,
      data: {
        current: res?.data?.current || 1,
        size: res?.data?.size || 10,
        total: res?.data?.total || 0,
        list: [],
      },
    };

    if (res.code === '000000') {
      resData.data.list = (res?.data?.records || []).map((val, index) => {
        return {
          accurateBigFarmer: val.accurateBigFarmer,
          index: String(index + 1),
          farmerlistNo: val.farmerlistNo || '', // 清单号
          farmerName: val.farmerName || '', // 分户被保险人
          certificateNo: val.certificateNo || '', // 证件号码
          certificateType: val.certificateType || '', // 证件类型
          ruleContent: val.ruleContent || '', // 错误提示内容
          idFarmerlistCustomInfo: val.idFarmerlistCustomInfo,
        };
      });
    }

    console.log('返回前端内容：', resData);

    return resData;
  } catch (err) {
    console.error(event, err);
    return err;
  }
});
