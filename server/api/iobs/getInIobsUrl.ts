export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.administrate;
  const urlObj = getRequestURL(event)
  const body = await readBody(event)
  console.log(event, '---------iobs/getInIobsUrl-----------')
  try {
    const res = await $fetch(baseUrl + '/iobs/getInIobsUrl', {
      method: 'post',
      headers: event.context.headers,
      body: body
    })
    console.log(event,'=----res====', res)
    return res
  } catch (err){
    console.error(event, err)
    return err
  }
})