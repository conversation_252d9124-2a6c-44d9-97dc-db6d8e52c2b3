export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.administrate;
  const urlObj = getRequestURL(event)
  const query = getQuery(event)
  console.log(event, '---------getInIobsInfo-----------')
  try {
    const res = await $fetch(baseUrl + '/iobs/getInIobsInfo', {
      method: 'get',
      headers: event.context.headers,
      query
    })
    console.log(event,'=----res====', res)
    return res
  } catch (err){
    console.error(event, err)
    return err
  }
})