import type { EventResponse } from '@/apiTypes/apiCommon'

export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 根据清单号获取险种下拉
  const baseUrl = host + gateWay + service.farmer;
  const query = await getQuery(event);
  const url = baseUrl + '/product/getPlanListByFarmerListNo';
  console.log(event, '后端接口', url)
  try {
    const res: EventResponse<{planChineseName: string, planCode: string}[]> = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
      query
    })
    return {
      code: res.code,
      data: res.data.map(({planChineseName, planCode}) => ({
        label: planCode + planChineseName,
        value: planCode
      })),
      msg: res.msg,
    };
  } catch (err) {
    console.error(event, err)
    return err
  }
})