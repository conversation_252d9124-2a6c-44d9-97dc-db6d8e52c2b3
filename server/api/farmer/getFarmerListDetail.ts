import type { EventResponse } from '@/apiTypes/apiCommon'

export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {};
  // 分户清单详情页
  const url = host + gateWay + service.farmer + '/farmerList/getFarmerListDetail';
  const body = await readBody(event);
  console.log(event, '后端接口', url)
  try {
    const res: EventResponse<any> = await $fetch(
      url,
      {
        method: "post",
        headers: event.context.headers,
        body,
      }
    );
    // 过滤掉null值
    const filterNullValues = (obj: Record<string, any>) => {
      return Object.keys(obj).reduce<Record<string, any>>((acc, key) => {
        if (obj[key] !== null) {
          acc[key] = obj[key];
        }
        return acc;
      }, {});
    };
    return {
      code: res.code,
      data: {
        ...res.data,
        farmerList: res.data?.farmerList.map((obj: Record<string, any>) => {
          const filterObj = filterNullValues(obj);
          if(Array.isArray(filterObj.tag)) {
            filterObj.tag = filterObj.tag.map((k) => filterNullValues(k));
          }
          return filterObj;
        }),
      },
      msg: res.msg,
    };
  } catch (err) {
    console.error(event, err);
    return err;
  }
});
