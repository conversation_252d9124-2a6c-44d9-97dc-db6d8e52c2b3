import type { EventResponse } from '@/apiTypes/apiCommon'

export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 获取清单状态选项
  const baseUrl = host + gateWay + service.farmer;
  const url = baseUrl + '/farmerList/listFarmerListStatus';
  console.log(event, '后端接口：', url)
  try {
    const res:EventResponse<{farmerlistStatusDesc: string; farmerlistStatus: string}[]> = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
    })
    return {
      code: res.code,
      data: res.data?.map((k) => ({ label: k.farmerlistStatusDesc, value: k.farmerlistStatus })),
      msg: res.msg
    }
  } catch (err){
    console.error(event, err)
    return err
  }
})