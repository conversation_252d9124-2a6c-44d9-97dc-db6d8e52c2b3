export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 投保批次下拉选择数据
  const baseUrl = host + gateWay + service.farmer;
  const url = baseUrl + '/insureQuantity/getInsureQuantityInfo';
  console.log(event, '后端接口', url)
  try {
    const res = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
    })
    return res;
  } catch (err) {
    console.error(event, err)
    return err
  }
})