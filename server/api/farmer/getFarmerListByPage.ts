import type { EditFarmerListRes, GetFarmerListByPageRes } from '@/apiTypes/farmer/farmerList';

export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {};
  // 发起真正的请求
  const baseUrl = host + gateWay + service.farmer;
  const body = await readBody(event);

  console.log('---入参：----', body);

  const url = baseUrl + '/farmerList/getFarmerListByPage';

  console.log('后端接口：', url);
  console.log('后端接口入参：', body);

  try {
    const res: GetFarmerListByPageRes = await $fetch(url, {
      method: 'post',
      headers: event.context.headers,
      body: body,
    });

    console.log('后端返回内：', res);
    const resData: EditFarmerListRes = {
      code: res.code,
      msg: res.msg,
      data: {
        current: res?.data?.current || 1,
        total: res?.data?.total || 0,
        size: res?.data?.size || 10,
        list: [],
      },
    };
    if (res.code === '000000') {
      resData.data.list = (res?.data?.records || []).map((val, index) => {
        return {
          index: String(index + 1),
          accurateBigFarmer: val.accurateBigFarmer,
          farmerlistNo: val.farmerlistNo || '', // 清单号
          farmerName: val.farmerName || '', // 农户名称
          certificateNo: val.certificateNo || '', // 证件号
          certificateType: val.certificateType || '', // 证件类型
          mobileTelephone: String(val.mobileTelephone || ''), // 手机号
          riskName: val.riskName || '', // 标的名称
          insuranceNums: String(val.insuranceNums || ''), // 保险数量
          statusList: val.statusList || [], // 状态
          executeFlag: val.executeFlag, // 是否执行中
          idFarmerlistCustomInfo: val.idFarmerlistCustomInfo,
          customCode: val.customCode,
        };
      });
    }

    console.log('返回前端内容：', resData);

    return resData;
  } catch (err) {
    console.error(event, err);
    return err;
  }
});
