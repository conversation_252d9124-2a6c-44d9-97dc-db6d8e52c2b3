import type { EventResponse } from '@/apiTypes/apiCommon'

export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {};
  // 投保数量单位下拉选择项
  const url = host + gateWay + service.farmer + "/dictSelect/getSelect";
  const body = await readBody(event);
  console.log(event, "入参", body);
  console.log(event, "后端接口", event, url);

  try {
    const res: EventResponse<any> = await $fetch(
      url,
      {
        method: "post",
        headers: event.context.headers,
        body,
      }
    );
    console.log("后端返回", event, res);
    const { selectMaps = {} } = res.data;
    for (const key in selectMaps) {
      selectMaps[key] = selectMaps[key].map((obj: any) => ({
        label: obj.parameterName,
        value: obj.parameterCode,
      }));
    }
    return {
      code: res.code,
      data: selectMaps,
      msg: res.msg,
    };
  } catch (err) {
    console.error(event, err);
    return err;
  }
});
