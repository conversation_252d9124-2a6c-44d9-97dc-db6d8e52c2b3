interface RecordType {
  farmerlistNo: string;
  farmerlistName: string;
  productName: string;
  farmerlistStatusDesc: string;
  riskAddressNameProvince: string;
  riskAddressNameCity: string;
  riskAddressNameCounty: string;
  riskAddressNameTown: string;
  riskAddressNameVillage: string;
  createdBy: string;
  importCount: string;
  riskCount: string;
  errorCount: string;
  importDate: string;
  oprRoleName: string;
  buttonAuthList: string[];
  insuredNumber: string;
  farmersCount: string;
  farmerlistStatus: string;
  departmentName: string;
  departmentCode: string;
}
interface RawRes {
  code: string;
  msg: string;
  data: {
    current: number;
    size: number;
    total: number;
    records: RecordType[];
  };
}

export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {};
  // 获取清单列表数据，分页
  const url = host + gateWay + service.farmer + '/farmerList/pageFarmerListSummary';
  const body = await readBody(event);

  console.log('入参', event, body);
  console.log('后端接口', event, url);
  try {
    const res: RawRes = await $fetch(url, {
      method: 'post',
      headers: event.context.headers,
      body,
    });
    console.log('后端返回', event, res);
    const resData = {
      code: res.code,
      data: {
        current: res.data?.current || 1,
        size: res.data?.size || 10,
        total: res.data?.total || 0,
        records: res.data?.records?.map(({
          applyPolicyNo,
          farmerlistNo,
          farmerlistName,
          productName,
          farmerlistStatusDesc,
          riskAddressNameProvince,
          riskAddressNameCity,
          riskAddressNameCounty,
          riskAddressNameTown,
          riskAddressNameVillage,
          createdBy,
          importCount,
          riskCount,
          errorCount,
          importDate,
          oprRoleName,
          buttonAuthList,
          insuredNumber,
          farmersCount,
          farmerlistStatus,
          riskAddressCodeVillage,
          riskAddressCodeProvince,
          riskAddressCodeCity,
          riskAddressCodeCounty,
          riskAddressCodeTown,
          riskFarmerRuleInfo,
          errorFarmerRuleInfo,
          updatedDate,
          departmentName,
          departmentCode,
          errorruleContents,
        }) => ({
          applyPolicyNo,
          farmerlistNo,
          farmerlistName,
          productName,
          farmerlistStatusDesc,
          riskFarmerRuleInfo,
          errorFarmerRuleInfo,
          addressName: [
            riskAddressNameProvince,
            riskAddressNameCity,
            riskAddressNameCounty,
            riskAddressNameTown,
            riskAddressNameVillage,
          ].reduce((acc, cur) => cur ? acc + cur : acc, ''),
          createdBy,
          importCount,
          riskCount,
          errorCount,
          updatedDate,
          importDate,
          oprRoleName,
          buttonAuthList,
          insuredNumber,
          farmersCount,
          farmerlistStatus,
          riskAddressCodeVillage,
          riskAddressCodeProvince,
          riskAddressCodeCity,
          riskAddressCodeCounty,
          riskAddressCodeTown,
          departmentName,
          departmentCode,
          errorruleContents,
        })) || [],
      },
      msg: res.msg,
    };
    console.log('前端返回', event, res);
    return resData;
  } catch (err) {
    console.error(event, err);
    return err;
  }
});
