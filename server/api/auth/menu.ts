import { useRedis } from '@/composables/useRedis';

const traverseMenuTree = (nodes) => {
  if (!nodes) return; // 遍历出口;
  for (const node of nodes) {
    // 删减字段，减少redis缓存大小
    delete node.requestCode;
    delete node.parentId;
    delete node.buttonType;
    delete node.buttonLink;
    delete node.menuOrder;
    traverseMenuTree(node.subMenus);
  }
};

const fetchNewMenuData = async (event) => {
  const query = getQuery(event);
  const { host, gateWay, service } = useRuntimeConfig().public || {};
  const baseUrl = host + gateWay + service.supercoder;

  const { code, data, msg } = await $fetch(baseUrl + '/menuAuthCenter/remote/menu', {
    method: 'GET',
    headers: event.context.headers,
    query,
    onResponseError({ response }) {
      console.log(event, 'menuAuthCenter/remote/menu onResponseError', response.status, response.body);
    },
  }).catch((err) => {
    console.log(event, 'menuAuthCenter/remote/menu接口报错', err);
    return err.data;
  });
  console.log(event, '---remote/menu---', code, data, msg);
  return { code, data, msg };
};

const afterUpdateCache = async (event, redisKey) => {
  const { setItemAsync } = useRedis();
  const { code, data, msg } = await fetchNewMenuData(event);
  console.log(event, '后端返回原始数据afterUpdateCache：', code, msg);

  if (code === '000000') {
    // 清除前端无用的字段,例如requestcode字段
    traverseMenuTree(data);
    // 更新缓存
    console.log(event, '开始更新缓存数据afterUpdateCache', { redisKey: redisKey, data: data });
    setItemAsync(redisKey, data, 60 * 60 * 24);
  } else {
    // 如果接口报错，清除缓存
    console.log(event, '开始清除缓存数据afterUpdateCache', { code, data, msg });
    setItemAsync(redisKey, [], 0);
  }
};

export default defineEventHandler(async (event) => {
  const cookies = parseCookies(event);
  const query = getQuery(event);
  const { getItem, setItemAsync } = useRedis();
  console.log(event, '入参:', query);

  const authToken = cookies['SC-AUTH-TOKEN'];
  // 该接口存在数据缓存，需要校验登录态是否存在
  if (!authToken) {
    setResponseStatus(event, 401, '登录超时');
    return { statusCode: '401', msg: '登录超时' };
  }
  const redisKey = `menu:${query.userName}`;
  let defaultCode = '000000';

  let cacheData = [];
  // 优先获取缓存数据
  const redisData = await getItem(redisKey);
  console.log(event, '缓存数据为', redisData, redisKey);
  if (redisData && redisData.length) {
    cacheData = redisData;
    afterUpdateCache(event, redisKey);
    const filterList = (cacheData || []).filter((item) => item.authCode !== 'commonMenu');
    console.log(event, '返回前端数据', filterList);
    return { code: defaultCode, data: filterList, msg: '' };
  } else {
    // 不存在缓存数据，直接调用接口
    const { code, data, msg } = await fetchNewMenuData(event);
    console.log(event, '后端返回原始数据：', code, data, msg);
    if (code === '000000') {
      // 清除前端无用的字段,例如requestcode字段
      traverseMenuTree(data);
      cacheData = data;
      defaultCode = code;
      // 更新缓存
      console.log(event, '开始更新缓存数据', { redisKey: redisKey, data: cacheData });
      setItemAsync(redisKey, cacheData, 60 * 60 * 24);
      const filterList = (cacheData || []).filter((item) => item.authCode !== 'commonMenu');
      console.log(event, '返回前端数据', filterList);
      return { code: defaultCode, data: filterList, msg: '' };
    } else {
      return { code, data, msg };
    }
  }
});
