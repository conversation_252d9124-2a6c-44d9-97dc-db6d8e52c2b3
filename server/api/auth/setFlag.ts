// 设置灰度标识，给登录页使用，需要支持跨域访问
// 同样先执行获取用户信息接口，让用户信息先缓存起来，加快接口调用速度，同时请求菜单接口更新缓存
export default defineEventHandler(async (event) => {
  const cookies = parseCookies(event);
  const { host, gateWay, service } = useRuntimeConfig().public || {};
  const authToken = cookies['SC-AUTH-TOKEN'];
  const baseUrl = host + gateWay + service.administrate;
  const token = getHeader(event, 'Token') || ''; // SC-AUTH-TOKEN 请求头也有SC-AUTH-TOKEN

  console.log(event, '后端接口：', baseUrl + '/web/gray/getGraySign');
  console.log(event, '参数：', authToken || '', token || '');
  // cookie有值，直接调用用户信息接口，触发redis缓存
  if (authToken || token) {
    try {
      console.log(event, '发起用户信息请求', authToken || token);
      const userRes = await $fetch('/api/auth/queryUmUserInfo', {
        method: 'GET',
        headers: { ...event.context.headers, ...{ Cookie: `SC-AUTH-TOKEN=${authToken || token}` } },
      });
      console.log(event, '用户信息请求结束', userRes);
      if (userRes.code === '000000') {
        console.log(event, '发起左侧菜单请求', authToken || token, userRes.data?.umCode);
        await $fetch('/api/auth/menu', {
          method: 'GET',
          headers: { ...event.context.headers, ...{ Cookie: `SC-AUTH-TOKEN=${authToken || token}` } },
          query: { userName: userRes.data?.umCode || '' },
        });
      }
      console.log(event, '发起灰度标识请求', authToken || token);
      const res = await $fetch(baseUrl + '/web/gray/getGraySign', {
        method: 'GET',
        headers: { ...event.context.headers, ...{ Cookie: `SC-AUTH-TOKEN=${authToken || token}` } },
      });
      console.log(event, '发起灰度标识请求接口返回数据：', res);
      if (res.code === '000000' && res.data && res.data.graySignKey) {
        // 存在灰度标识
        appendResponseHeaders(event, {
          'set-cookie': `${res.data.graySignKey}=${res.data.graySign || ''}; Domain=.paic.com.cn; Path=/; HttpOnly; Secure; SameSite=none`,
        });
      }
      return res;
    } catch (err) {
      console.error(event, err);
      return err;
    }
  } else {
    return {
      code: '19878677',
      msg: '缺失sc-auth-token',
    };
  }
});
