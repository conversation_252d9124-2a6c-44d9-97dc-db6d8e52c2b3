import { useRedis } from '@/composables/useRedis';

export default defineEventHandler(async (event) => {
  const cookies = parseCookies(event);
  const { host, gateWay, service } = useRuntimeConfig().public || {};
  // 发起真正的请求
  const baseUrl = host + gateWay + service.administrate;
  const { getItem, setItemAsync } = useRedis();

  console.log(event, '后端接口：', baseUrl + '/user/queryUmUserInfo', useNitroApp().configs);

  try {
    const authToken = cookies['SC-AUTH-TOKEN'];
    // 该接口存在数据缓存，需要校验登录态是否存在
    if (!authToken) {
      setResponseStatus(event, 401, '登录超时');
      return { statusCode: '401', msg: '登录超时' };
    }
    const redisKey = `user:${authToken}`;
    let cacheData = {};
    const redisData = await getItem(redisKey);
    console.log(event, '获取缓存数据为', redisData, redisKey);
    if (redisData && Object.keys(redisData).length) {
      cacheData = redisData;
    } else {
      // 不存在缓存数据，直接调用接口
      const res = await $fetch(baseUrl + '/user/queryUmUserInfo', {
        method: 'GET',
        headers: event.context.headers,
        onResponse({ response }) {
          cacheData.paobsCookies = response.headers.get('set-cookie');
        },
      });
      cacheData.user = res;
      if (res && res.code === '000000') {
        // 更新缓存
        console.log(event, '开始更新缓存数据', { redisKey: redisKey, data: cacheData });
        // 设置缓存时间为1天
        setItemAsync(redisKey, cacheData, 60 * 60 * 24);
      }
    }

    console.log(event, '后端返回原始数据：', cacheData.user);

    if (cacheData.paobsCookies) {
      // 获取用户信息接口，后端会在响应头设置iobs权限链接
      appendResponseHeaders(event, {
        'set-cookie': cacheData.paobsCookies,
      });
    }

    return cacheData.user;
  } catch (err) {
    console.error(event, err);
    return err;
  }
});
