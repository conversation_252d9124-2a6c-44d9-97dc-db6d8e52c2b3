interface RawRes {
  code: string;
  msg: string;
  data: {
    productCode: string;
    productName: string;
    version: string;
    subsidyType: string;
    targetType: string;
    targetTypeCodes: string;
  }[];
}

export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {};
  // 发起真正的请求
  const farmerUrl = host + gateWay + service.farmer + '/product/getProductList'; // 清单
  const acceptUrl = host + gateWay + service.accept + '/saleInfo/getProductList'; // 投保
  const urlMap: { [key: string]: string } = {
    farmer: farmerUrl,
    accept: acceptUrl,
  };
  const query = getQuery(event);
  const url = urlMap[query.module as string || 'farmer'];
  console.log(event, '入参', query);
  console.log(event, '接口', url);
  try {
    const res: RawRes = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
      query,
    });
    console.log(event, '后端返回', res);
    const resData = {
      code: res.code,
      data: res.data?.map(k => ({
        label: k.productCode + k.productName,
        value: k.productCode,
        realLabel: k.productName,
        version: k.version,
        subsidyType: k.subsidyType,
        targetType: k.targetType,
        targetTypeCodes: k.targetTypeCodes,
      })) || [],
      msg: res.msg,
    };
    console.log(event, '前端返回', resData);
    return resData;
  } catch (err) {
    console.error(event, err);
    return err;
  }
});
