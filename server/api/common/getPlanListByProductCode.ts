import type { EventResponse } from '@/apiTypes/apiCommon'

export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const url = host + gateWay + service.farmer + '/product/getPlanListByProductCode'; // 清单

  const query = getQuery(event);
  console.log(event, '入参', query);
  console.log(event, '接口', url);

  try {
    const res: EventResponse<any> = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
      query
    })
    console.log(event, '后端返回', res);
    return res;
  } catch (err){
    console.error(event, err)
    return err
  }
})