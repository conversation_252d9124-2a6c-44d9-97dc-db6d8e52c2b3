interface RawRes {
  code: string;
  msg: string;
  data: {
    departmentCode: string;
    abbrName: string;
    departmentLevel: string;
  }[];
}

export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {};
  // 发起真正的请求
  const url = host + gateWay + service.administrate + '/dept/queryChildDeptList';
  const query = getQuery(event);
  console.log(event, '入参', query);
  console.log(event, '接口', url);
  try {
    const res: RawRes = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
      query,
    });
    console.log(event, '后端返回', res);
    const resData = {
      code: res.code,
      data: res.data?.map(item => ({
        label: item.departmentCode + item.abbrName,
        value: item.departmentCode,
        departmentLevel: item.departmentLevel,
      })) || [],
      msg: res.msg,
    };
    console.log(event, '前端返回', resData);
    return resData;
  } catch (err) {
    console.error(event, err);
    return err;
  }
});
