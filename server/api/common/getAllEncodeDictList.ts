interface RawRes {
  code: string;
  msg: string;
  data: {
    encodeDictLevel1: { encodeKey: string },
    encodeDictLevel2: { encodeKey: string },
    encodeDictLevel3: { encodeKey: string },
    encodeDictLevel4: { encodeKey: string },
    encodeDictLevel5: { encodeKey: string },
    showValues: string;
  }[]
}

export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const url = host + gateWay + service.farmer + '/encodeDict/fuzzyQueryAllEncodeDictList';
  const query = getQuery(event);
  console.log(event,'入参', query);
  console.log(event,'后端接口', url);
  try {
    const res: RawRes = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
      query,
    })
    console.log(event, '后端返回', res);
    const resData = {
      code: res.code,
      data: res.data?.map((item) => ({
        label: item.showValues,
        value: [
          item.encodeDictLevel1.encodeKey,
          item.encodeDictLevel2.encodeKey,
          item.encodeDictLevel3.encodeKey,
          item.encodeDictLevel4.encodeKey,
          item.encodeDictLevel5.encodeKey
        ].join('-'),
      })) || [],
      msg: res.msg,
    }
    console.log(event,'前端返回', res);
    return resData;
  } catch (err){
    console.error(event, err)
    return err
  }
})