// 获取投保常量下拉列表，包括：项目来源、补贴类型、客户类型、特约类型...
import type { BaseConstants, EventResponse, SelectOptions } from '@/apiTypes/apiCommon';

export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {};
  const url = host + gateWay + service.administrate + '/parmBaseConstantConf/getParmBaseConstantConf';
  // const url = 'http://**************:9007/parmBaseConstantConf/getParmBaseConstantConf';
  const body = await readBody(event);
  console.log(event, '接口入参：', body);
  console.log(event, '后端接口：', url);
  try {
    const res: EventResponse<SelectOptions[]> = await $fetch(
      url,
      {
        method: 'post',
        headers: event.context.headers,
        body,
      },
    );
    console.log(event, '后端返回内容：', res);
    const newRes: EventResponse<BaseConstants> = {
      code: res.code,
      data: res.data,
      msg: res.msg,
    };
    if (res.code === '000000' && res.data && Array.isArray(res.data)) {
      const data = res.data.map((item) => {
        return {
          label: item.label || '',
          value: item.value || '',
          children: item.children
            ? item.children.map((subItem) => {
              return {
                label: subItem.label || '',
                value: subItem.value || '',
              };
            })
            : [],
        };
      });
      const projectSourceList = data.find(item => item.value === 'isTenderBusiness')?.children || []; // 项目来源
      const customerTypeList = data.find(item => item.value === 'customerType')?.children || []; // 客户类型
      const subsidyTypeList = data.find(item => item.value === 'subsidyType')?.children || []; // 补贴类型
      const specialPromiseTypeList = data.find(item => item.value === 'specialPromiseType')?.children || []; // 特约类型
      const disputeTypeList = data.find(item => item.value === 'disputeType')?.children || []; // 争议处理方式
      const farmerMainList = data.find(item => item.value === 'farmerMain')?.children || []; // 农业主体标识
      const poorHouseList = data.find(item => item.value === 'poorHouse')?.children || []; // 凭困户标识
      const unitCodeList = data.find(item => item.value === 'farmer_land_unit_code')?.children || []; // 凭困户标识
      // unitCodeList
      newRes.data = {
        projectSourceList,
        customerTypeList,
        subsidyTypeList,
        specialPromiseTypeList,
        disputeTypeList,
        farmerMainList,
        poorHouseList,
        unitCodeList,
      };
    }
    console.log(event, '返回前端内容：', newRes);
    return newRes;
  } catch (err) {
    console.error(event, err);
    return err;
  }
});
