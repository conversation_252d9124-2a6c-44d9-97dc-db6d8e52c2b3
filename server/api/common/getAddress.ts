export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {};
  // 发起真正的请求
  // const url = host + gateWay + service.farmer + '/address';
  // 后端要求更换新接口
  const url = host + gateWay + service.administrate + '/public/queryOldAddress';

  const query = getQuery(event);
  console.log(event, '入参', query);
  console.log(event, '后端接口', url);
  try {
    const res = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
      query,
    });
    console.log(event, '后端返回', res);
    return res;
  } catch (err) {
    console.error(event, err);
    return err;
  }
});
