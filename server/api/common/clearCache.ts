// 清除redis缓存
import { useRedis } from '@/composables/useRedis'

export default defineEventHandler(async (event) => {
  // 发起真正的请求
  const query = getQuery(event);
  const { clear } = useRedis();

  try {
    if (query.redisKey) {
      const res = await clear(String(query.redisKey))
      return res;
    } else {
      return {code: '100000', msg: '请输入rediskey'}
    }
    
  } catch (err){
    console.error(event, err)
    return err
  }
})