import { useRedis } from '@/composables/useRedis'

interface Dept {
  belongBusinessDepartmentNo: string;
  departmentNoAndName: string;
}

interface DeptRes {
  code?: string;
  msg?: string;
  data?: Dept[];
}

 export default defineEventHandler(async (event) => {
    const { host, gateWay, service } = useRuntimeConfig().public || {}
    const { getItem, setItem } = useRedis<DeptRes>();
    // 发起真正的请求
    const baseUrl = host + gateWay + service.compliance;
    const body = await readBody(event)
    const url = baseUrl + '/pageConfig/platform/listDeptCodeName';
    console.log(event, '接口入参：', body);
    console.log(event, '后端接口：', url);
    try {
      const redisKey = `dept:${body.belongBusinessDepartmentNo}`;
      let cacheData: DeptRes | null  = null;

      // 这里被查出一个风险漏洞，没有登录态时也能通过接口获取缓存数据
      // nodejs并没有校验登录态和存储登录态，暂时先不存储
      // cacheData = await getItem(redisKey)
      // console.log(event, '获取缓存数据', cacheData)

      if (!cacheData || cacheData.code !== '000000') {
        const res : DeptRes = await $fetch(url, {
          method: 'post',
          headers: event.context.headers,
          body: body
        })
        console.log(event, '后端返回内容：', res)
        cacheData = res;

        if (cacheData.code === '000000') {
          // 更新缓存
          // console.log(event, '更新缓存')
          // setItem(redisKey, cacheData, 600)
        }
      }
      
      return cacheData
    } catch (err){
      console.error(event, err)
      return err
    }
  })