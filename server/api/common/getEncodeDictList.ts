export default defineEventHandler(async (event) => {
  const { host, gateWay, service } = useRuntimeConfig().public || {}
  // 发起真正的请求
  const baseUrl = host + gateWay + service.farmer;
  const query = getQuery(event); 
  const url = baseUrl + '/encodeDict/getEncodeDictList';
  console.log('后端入参', event, query);
  console.log('后端接口', event, url)

  try {
    const res = await $fetch(url, {
      method: 'get',
      headers: event.context.headers,
      query,
    })
    return res;
  } catch (err){
    console.error(event, err)
    return err
  }
})