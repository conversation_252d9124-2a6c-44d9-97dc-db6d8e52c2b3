<template>
  <a-config-provider :locale="zhCN" :theme="themeStyle">
    <a-extract-style>
      <StyleProvider
        hash-priority="high"
        :transformers="[
          px2remTransformer({
            rootValue: 144,
          }),
          legacyLogicalPropertiesTransformer,
        ]"
      >
        <NuxtLayout>
          <ClientOnly>
            <NuxtPage :page-key="(route) => route.path + route.query.t || ''" />
            <DoctorNong v-model:open="doctorNongOpen" />
          </ClientOnly>
        </NuxtLayout>
      </StyleProvider>
    </a-extract-style>
  </a-config-provider>
</template>

<script setup lang="ts">
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { px2remTransformer, StyleProvider, legacyLogicalPropertiesTransformer } from 'ant-design-vue';
import themeStyle from './theme-style.json';
import 'dayjs/locale/zh-cn';
import Doctor<PERSON><PERSON> from '@/components/ui/<PERSON><PERSON><PERSON>.vue';

useHead({
  title: '农险新核心系统',
});

const doctorNongOpen = ref(false);

provide('doctor<PERSON>ong<PERSON><PERSON>', doctor<PERSON>ong<PERSON><PERSON>); // 其他页面也可以控制农博士的展示

// keepalive缓存的key是route.path + route.query.t。如果要刷新某页面缓存，可以利用参数t进行刷新
</script>

<style lang="less">
@import url('@/assets/css/index.less');
</style>
