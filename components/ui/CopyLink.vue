<template>
  <div class="flex items-center group/item">
    <span class="text-[#576B95] cursor-pointer font-number">{{ text }}</span>
    <span class="cursor-pointer invisible group-hover/item:visible" @click.stop="copyText(text)">
      <VueIcon :icon="IconFuzhiFont" />
    </span>
  </div>
</template>

<script setup lang="ts">
// 支持copy内容的超链接文本组件，多用于表格中展示保单号、投保单号等
import { IconFuzhiFont } from '@pafe/icons-icore-agr-an';
import { copyText } from '@/utils/tools';

const { text } = defineProps<{
  text: string;
}>();
</script>
