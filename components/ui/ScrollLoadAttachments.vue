<template>
  <div ref="containerRef" class="scroll-load-attachments relative" @scroll="handleScroll">
    <slot />
    <!-- 加载状态显示 -->
    <div v-show="loading" class="col-span-full bg-[#f6ffed] text-center">
      <a-spin />
    </div>
    <!-- 无更多数据提示 -->
    <div v-show="showEndingTips" class="col-span-full text-center text-[#999] py-[10px] bg-[#f6ffed]">没有更多数据了</div>
  </div>
</template>

<script lang="ts" setup>
const { threshold = 100 } = defineProps<{
  threshold?: number;
}>();

const currentPageNum = defineModel<number>('currentPageNum', {
  default: 1,
});
const pageSize = defineModel<number>('pageSize', {
  default: 20,
});
const totalNum = defineModel<number>('totalNum', {
  default: 0,
});

const loading = defineModel<boolean>('loading', {
  default: false,
});

const showEndingTips = ref(false);
const lastScrollTop = ref(0); // 记录上一次的滚动位置

const emit = defineEmits(['loadMore']);

// 检查是否需要自动加载更多
const checkNeedAutoLoad = () => {
  if (!containerRef.value) return false;

  const { scrollHeight, clientHeight } = containerRef.value;
  // 如果内容高度小于或等于容器高度，说明没有滚动条
  return scrollHeight <= clientHeight;
};

// 自动加载更多数据
const autoLoadMore = async () => {
  if (loading.value || !hasMore.value) return;
  currentPageNum.value = currentPageNum.value + 1;
  emit('loadMore');
};

// 监听加载状态
watch(loading, async (val) => {
  if (val === false) {
    // 接口已加载完成
    if (totalNum.value - currentPageNum.value * pageSize.value <= 0) {
      showEndingTips.value = true;
      setTimeout(() => {
        showEndingTips.value = false;
      }, 3000);
    }
  }
});

// 监听内容变化
const observer = ref<ResizeObserver | null>(null);

onMounted(() => {
  // 创建 ResizeObserver 实例
  observer.value = new ResizeObserver(async (entries) => {
    console.log('entries=', entries);
    await nextTick();
    if (checkNeedAutoLoad()) {
      autoLoadMore();
    }
  });

  // 开始观察容器尺寸变化
  if (containerRef.value) {
    observer.value.observe(containerRef.value);
  }
});

onUnmounted(() => {
  // 清理 observer
  if (observer.value && containerRef.value) {
    observer.value.unobserve(containerRef.value);
    observer.value.disconnect();
  }
});

const hasMore = computed(() => {
  return totalNum.value - currentPageNum.value * pageSize.value > 0;
});

const scrollTimer = ref<ReturnType<typeof setTimeout> | null>(null);
const containerRef = ref<HTMLElement | null>(null);

// 处理滚动事件
const handleScroll = () => {
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value);
  }

  scrollTimer.value = setTimeout(() => {
    if (!containerRef.value) return;
    const { scrollHeight, scrollTop, clientHeight } = containerRef.value;

    // 判断滚动方向
    const isScrollingDown = scrollTop > lastScrollTop.value;
    lastScrollTop.value = scrollTop; // 更新上一次的滚动位置

    // 如果不是向下滚动，直接返回
    if (!isScrollingDown) {
      return;
    }

    // 计算距离底部的距离
    const bottomDistance = scrollHeight - scrollTop - clientHeight;

    // 如果正在加载、没有更多数据，或者不满足触发距离，则不触发加载
    if (loading.value || !hasMore.value || bottomDistance > threshold) {
      return;
    }

    currentPageNum.value = currentPageNum.value + 1;
    // 触发加载更多事件
    emit('loadMore');
  }, 50);
};

// 组件卸载时清理定时器
onUnmounted(() => {
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value);
  }
});
</script>
