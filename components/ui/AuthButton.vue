<template>
  <a-button v-show="isShow" :="$attr"><slot /></a-button>
</template>

<script lang="ts" setup>
// 权限按钮组件，会根据蘑菇云的权限控制展示与隐藏
import { useUserStore } from '@/stores/useUserStore';

const { authPathBtnObj } = storeToRefs(useUserStore());
const $attr = useAttrs();
const route = useRoute();

const isShow = computed(() => {
  if ($attr.code) {
    const path = route.path;
    if (authPathBtnObj.value[path]) {
      return authPathBtnObj.value[path].includes($attr.code as string);
    } else {
      return false;
    }
  }
  return true;
});
</script>
