<template>
  <div class="fold-box" @click="switchExpand">
    <span class="text-nowrap text-[rgba(0,0,0,0.40)]">{{ expand ? '收起' : '展开' }}</span>
    <VueIcon class="text-[rgba(0,0,0,0.40)]" :icon="expand ? IconChevronUpFont : IconChevronDownFont" />
  </div>
</template>

<script setup lang="ts">
import { IconChevronDownFont, IconChevronUpFont } from '@pafe/icons-icore-agr-an';
/**
 * @description: 表单折叠按钮组件
 * @props
 * {boolean} expand - 是否展开(双向绑定)
 */
// 是否展开
const expand = defineModel<boolean>();

// 翻转状态
const switchExpand = () => {
  expand.value = !expand.value;
};
</script>

<style scoped>
.fold-box {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  font-size: 12px;
  color: #262626;
}
</style>
