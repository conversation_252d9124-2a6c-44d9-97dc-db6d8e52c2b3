// 单元格基本类型
export interface CellData {
  value: string | number | null;
  status?: string;
  statusTips?: string;
  unitValue?: string;
  disable?: boolean;
  defaultValue?: CellValue;
}
/**
 * 编辑器配置接口
 */
export interface EditorConfig {
  [key: string]: unknown;
}

/**
 * 选项接口
 */
export interface Option {
  label: string;
  value: string | number;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  valid: boolean;
  message: string;
}

/**
 * 验证规则接口
 */
export interface ValidationRule {
  validator: (value: CellData) => boolean | ValidationResult;
  message?: string;
}

/**
 * 列配置接口
 */
export interface Column {
  options?: Option[];
  rule?: ValidationRule[];
  field?: string;
  title?: string;
  width?: number | string;
  [key: string]: unknown;
}

/**
 * 位置矩形接口
 */
export interface ReferenceRect {
  height: number;
  width: number;
  x?: number;
  y?: number;
  [key: string]: number | undefined;
}

/**
 * 位置引用接口
 */
export interface ReferencePosition {
  rect: ReferenceRect;
}

/**
 * 启动参数接口
 */
export interface StartParams {
  container: HTMLElement;
  value: CellData;
  referencePosition: ReferencePosition;
  endEdit: () => void;
  col: number;
}

/**
 * 基础编辑器接口
 */
export interface BaseEditor {
  onStart: (params: StartParams) => void;
  getValue: () => CellData;
  validateValue: () => boolean;
  validateValues: () => ValidationResult;
  isEditorElement: (target: HTMLElement) => boolean;
  onEnd: () => void;
  stateColumns: (columns: Column[]) => void;
}
