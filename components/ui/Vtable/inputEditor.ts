import { cloneDeep } from 'lodash-es';
import { createInput } from './vrender';
import type { CellData, EditorConfig, ValidationResult, Column, ReferenceRect, StartParams, BaseEditor } from './vtable.d';

/**
 * 输入框编辑器类
 * 用于处理表格单元格的输入编辑功能
 */
export class FramerInputEditor implements BaseEditor {
  private editorConfig: EditorConfig;
  private container!: HTMLElement;
  private value!: CellData;
  private inputElement!: HTMLInputElement;
  // private errorElement!: HTMLDivElement;
  private col!: number;
  private rect!: ReferenceRect;
  private columns: Column[] = [];

  constructor(editorConfig?: EditorConfig) {
    this.editorConfig = editorConfig || {};
  }

  onStart({ container, value, referencePosition, endEdit, col }: StartParams): void {
    this.container = container;

    // 创建input
    const input = createInput();
    input.style.width = referencePosition.rect.width + 'px';
    input.style.height = referencePosition.rect.height + 'px';
    input.style.position = 'absolute';
    input.style.left = referencePosition.rect.left + 'px';
    input.style.top = referencePosition.rect.top + 'px';
    input.value = value.value?.toString() || '';

    // 创建错误提示容器
    // const errorDiv = createErrorDiv(referencePosition.rect);

    container.appendChild(input);
    input.focus();
    // container.appendChild(errorDiv);

    this.value = value;
    this.inputElement = input;
    // this.errorElement = errorDiv;
    this.col = col;
    this.rect = referencePosition.rect;

    // 记录默认值，用于是否隐藏错误提示
    if (!this.value.defaultValue) {
      this.value.defaultValue = cloneDeep(this.value);
    }

    if (value.disable) {
      // 禁止
      endEdit();
    }
  }

  // 更新表格数据
  getValue(): CellData {
    const res = this.validateValues();
    this.value.value = this.inputElement.value;
    if (res.valid) {
      if (this.inputElement.value !== this.value.defaultValue?.value) {
        // 值发生了改变，隐藏错误提示
        this.value.status = '';
      }
    } else {
      this.value.status = 'error';
      this.value.statusTips = res.message;
    }
    return this.value;
  }

  // 规则校验
  validateValues(): ValidationResult {
    let cross = true;
    let message = '';
    const values = cloneDeep(this.value);
    if (this.columns[this.col]?.rule?.length) {
      for (const item of this.columns[this.col].rule) {
        values.value = this.inputElement.value;
        const isTrue = item.validator(values);
        if (!isTrue || (typeof isTrue === 'object' && isTrue.valid === false)) {
          cross = false;
          message = item.message || (typeof isTrue === 'object' ? isTrue.message : '');
          break;
        }
      }
    }
    return { valid: cross, message };
  }

  // 规则校验
  validateValue(): boolean {
    // vtable规则校验不通过，默认不退出编辑状态
    // 但是，这个编辑状态的input框不随滚动条滚动，当滚动条滚动时会脱离当前单元格
    // 统一不自己生成错误提示,不使用vtable的默认校验方法
    // let cross = true;
    // if (this.columns[this.col].rule && this.columns[this.col].rule.length) {
    //   for (const item of this.columns[this.col].rule) {
    //     const isTrue = item.validator(this.inputElement.value);
    //     if (!isTrue || isTrue.valid === false) {
    //       cross = false;
    //       this.errorElement.innerHTML = item.message || isTrue.message;
    //       this.errorElement.style.display = 'block';
    //       this.inputElement.style.height = (this.rect.height - 14) + 'px';
    //       break;
    //     }
    //   }
    // }
    // if (cross) {
    //   this.errorElement.style.display = 'none';
    //   this.inputElement.style.height = this.rect.height + 'px';
    // }
    // return cross;
    return true;
  }

  isEditorElement(target: HTMLElement): boolean {
    if (target === this.inputElement) {
      return true;
    }
    return false;
  }

  onEnd(): void {
    this.container.removeChild(this.inputElement);
    // this.container.removeChild(this.errorElement);
  }

  // 规则校验放到columns配置中
  stateColumns(columns: Column[]): void {
    this.columns = columns || [];
  }
}
