const getBackgroundColor = (args): string => {
  const { row, table } = args;
  const index = row - table.frozenRowCount;
  if ((index % 2)) {
    return '#F7F8FA';
  }
  return '#FFF';
};

export const theme = {
  headerStyle: {
    bgColor: '#f2f3f5',
    padding: 10,
    color: '#333333',
    fontSize: 14,
    fontFamily: 'MicrosoftYaHei,-apple-system, BlinkMacSystemFont',
    fontWeight: 600,
    borderLineWidth: 0,
  },
  bodyStyle: {
    bgColor: getBackgroundColor,
    padding: 10,
    fontSize: 14,
    color: '#333333',
    fontFamily: 'MicrosoftYaHei,-apple-system, BlinkMacSystemFont',
    fontWeight: 400,
    borderColor: '#E6E8EB',
    borderLineWidth: [1, 1],
    hover: {
      cellBgColor: '#f2f2f2',
      inlineRowBgColor: '#f2f2f2',
    },
  },
  checkboxStyle: {
    defaultFill: '#fff',
    defaultStroke: 'rgba(212,214,217,1)',
    disableFill: 'rgba(212,214,217,1)',
    checkedFill: '#07C160',
    checkedStroke: '#07C160',
    disableCheckedFill: 'rgba(212,214,217,1)',
    disableCheckedStroke: 'rgb(170, 170, 170)',
    size: 14,
  },
};
