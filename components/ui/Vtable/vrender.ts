import { createText, createPath, createRect, createGroup } from '@visactor/vtable/es/vrender';
import { theme } from './theme';

// 红星
export const createRedStar = (boundsPadding: number[]) => {
  return createText({
    text: '*',
    fontSize: 13,
    fontFamily: 'MicrosoftYaHei',
    fill: '#FF5B00',
    boundsPadding: boundsPadding || [0, 4, 0, 0],
  });
};

// 表头文字
export const createHeaderText = (text: string) => {
  return createText({
    text,
    fontSize: theme.headerStyle.fontSize,
    fontFamily: theme.headerStyle.fontFamily,
    fill: theme.headerStyle.color,
    fontWeight: theme.headerStyle.fontWeight,
  });
};

export interface TextConfig {
  text?: string;
  fontSize?: string;
  fontFamily?: string;
  fill?: string;
  boundsPadding?: number[];
  cursor?: string;
}
// 表格按钮
export const createLinkBtn = ({ text, config = {} }: { text: string; config?: TextConfig }) => {
  const param: TextConfig = Object.assign({
    text,
    fontSize: 14,
    fontFamily: 'MicrosoftYaHei',
    fill: '#576B95',
    boundsPadding: [0, 8],
    lineHeight: 14,
    cursor: 'pointer',
    pickable: true,
    disableAutoClipedPoptip: true,
  }, config);

  return createText(param);
};
// 表格单元格文案
export const createBodyText = ({ text, config = {}, disable = false }: { text: string; config?: TextConfig; disable?: boolean }) => {
  const param = Object.assign({
    text,
    fontSize: theme.bodyStyle.fontSize,
    fontFamily: theme.bodyStyle.fontFamily,
    fill: disable ? 'rgba(0,0,0,0.26)' : theme.bodyStyle.color,
    fontWeight: theme.bodyStyle.fontWeight,
  }, config);

  return createText(param);
};

export interface Rect {
  width: number;
  height: number;
  left: number;
  right: number;
  top: number;
  bottom: number;
}
export const createFlexDivContainer = (rect: Rect): HTMLDivElement => {
  const div = document.createElement('div');
  div.style.position = 'absolute';
  div.style.boxSizing = 'border-box';
  div.style.width = rect.width + 'px';
  div.style.height = rect.height + 'px';
  div.style.left = rect.left + 'px';
  div.style.top = rect.top + 'px';
  div.style.display = 'flex';
  return div;
};

export const createInput = (): HTMLInputElement => {
  const input = document.createElement('input');
  input.setAttribute('type', 'text');
  input.style.padding = '4px';
  input.style.boxSizing = 'border-box';
  input.style.border = '1px solid rgb(217, 217, 217)';
  return input;
};

export interface SelectOption {
  label: string;
  value: string;
}
export const createSelect = (options?: SelectOption[]): HTMLSelectElement => {
  const select = document.createElement('select');
  select.style.boxSizing = 'border-box';
  select.style.border = '1px solid rgb(217, 217, 217)';
  // 遍历options数组，为每个选项创建option元素
  if (options && options.length) {
    options.forEach(function (option) {
      const opt = document.createElement('option');
      opt.value = option.value;
      opt.innerHTML = option.label;
      select.appendChild(opt);
    });
  }
  return select;
};

export const createErrorDiv = (rect: Rect) => {
  const errorDiv = document.createElement('div');
  errorDiv.style.color = '#f03e3e';
  errorDiv.style.fontSize = '12px';
  errorDiv.style.lineHeight = '14px';
  errorDiv.style.position = 'absolute';
  errorDiv.style.left = rect.left + 'px';
  errorDiv.style.width = rect.width + 'px';
  errorDiv.style.top = (rect.top + rect.height - 14) + 'px';
  errorDiv.style.display = 'none';
  errorDiv.style.backgroundColor = '#fff';
  return errorDiv;
};

export interface CircleIcon {
  path: string;
  width?: number;
  height?: number;
  fill?: string;
}
export const createCircleIcon = ({ fill = '#576B95', width = 14, height = 14, path }: CircleIcon) => {
  const iconElement = createPath({
    path,
    width,
    height,
    fill,
    cursor: 'pointer',
    // 因为原始 SVG 的 viewBox 是 1024，需要缩放到指定大小
    scaleX: width / 1024,
    scaleY: height / 1024,
    transformOrigin: 'center',
  });

  return iconElement;
};

// 获取勾选的索引
export const getCheckedIndexes = (arr: { [name: string]: boolean }[], field: string) => {
  return arr.reduce((indexes, item, index) => {
    if (item[field] === true) {
      indexes.push(index);
    }
    return indexes;
  }, [] as number[]);
};

export const createCheckbox = ({ checked, disable }: { checked: boolean; disable: boolean }) => {
  const checkboxSize = theme.checkboxStyle.size;
  const width = theme.checkboxStyle.size;
  const height = theme.checkboxStyle.size;
  // 创建容器
  const container = createGroup({
    height,
    width,
  });

  // 添加外框
  const checkbox = createRect({
    width,
    height,
    fill: disable ? theme.checkboxStyle.disableFill : checked ? theme.checkboxStyle.checkedFill : theme.checkboxStyle.defaultFill,
    stroke: disable ? theme.checkboxStyle.defaultStroke : checked ? theme.checkboxStyle.checkedStroke : theme.checkboxStyle.defaultStroke,
    lineWidth: 1,
    cornerRadius: 2,
    cursor: disable ? 'not-allowed' : 'pointer',
  });

  container.add(checkbox);

  // 如果是选中状态，添加勾选符号
  if (checked === true) {
    const x = Math.floor((width - checkboxSize) / 2);
    const y = Math.floor((height - checkboxSize) / 2);
    const checkColor = '#fff';

    const checkMark = createText({
      x: x + (checkboxSize / 2),
      y: y + (checkboxSize / 2),
      text: '✓',
      fontSize: 11,
      fontWeight: 700,
      fill: checkColor,
      textAlign: 'center',
      textBaseline: 'middle',
      cursor: disable ? 'not-allowed' : 'pointer',
    });
    container.add(checkMark);
  }

  return container;
};

export const createCheckAllbox = ({ checked, disable }: { checked: 'true' | 'false' | 'indeterminate'; disable: boolean }) => {
  const checkboxSize = theme.checkboxStyle.size;
  const width = theme.checkboxStyle.size;
  const height = theme.checkboxStyle.size;
  const fillColor = {
    disable: theme.checkboxStyle.disableFill,
    true: theme.checkboxStyle.checkedFill,
    indeterminate: theme.checkboxStyle.defaultFill,
    false: theme.checkboxStyle.defaultFill,
  };

  const strokeColor = {
    disable: theme.checkboxStyle.defaultStroke,
    true: theme.checkboxStyle.checkedStroke,
    indeterminate: theme.checkboxStyle.defaultStroke,
    false: theme.checkboxStyle.defaultStroke,
  };
  // 创建容器
  const container = createGroup({
    height,
    width,
  });

  // 添加外框
  const checkbox = createRect({
    width,
    height,
    fill: fillColor[disable ? 'disable' : checked],
    stroke: strokeColor[disable ? 'disable' : checked],
    lineWidth: 1,
    cornerRadius: 2,
    cursor: disable ? 'not-allowed' : 'pointer',
  });

  container.add(checkbox);

  // 如果是选中状态，添加勾选符号
  if (checked === 'true') {
    const x = Math.floor((width - checkboxSize) / 2);
    const y = Math.floor((height - checkboxSize) / 2);
    const checkColor = '#fff';

    const checkMark = createText({
      x: x + (checkboxSize / 2),
      y: y + (checkboxSize / 2),
      text: '✓',
      fontSize: 11,
      fontWeight: 700,
      fill: checkColor,
      textAlign: 'center',
      textBaseline: 'middle',
      cursor: disable ? 'not-allowed' : 'pointer',
    });
    container.add(checkMark);
  }
  // 中间态
  if (checked === 'indeterminate') {
    const checkMark = createRect({
      x: 4,
      y: 4,
      width: 6,
      height: 6,
      fill: theme.checkboxStyle.checkedFill,
      stroke: theme.checkboxStyle.checkedStroke,
      lineWidth: 1,
      cornerRadius: 0,
      cursor: disable ? 'not-allowed' : 'pointer',
    });
    container.add(checkMark);
  }

  return container;
};
