export interface FileItem {
  documentName?: string; // 文件名称
  documentFormat?: string; // 文件类型 img/pdf/word/excel 4种类型，非后缀名
  uploadPath: string; // iobs的key|原图url
  bucketName: string; // iobs桶，有外网桶和内网桶
  thumbnail?: string; // 缩略图url
  documentGroupItemsId?: string; // 文件id
}
export interface ViewerItem {
  title?: string; // 文件目录名称
  key?: string; // 文件目录唯一key
  fileList?: FileItem[]; // 当前文件目录下的文件列表
  children?: ViewerItem[]; // 当前文件目录下的子目录
}

export interface ViewerImageAndThumbnailItem { // 多文件预览
  uploadPath: string; // iobs的key|原图url
  thumbnail: string; // 缩略图url
  documentFormat?: string; // 文件类型 img/pdf/word/excel 4种类型，非后缀名
  documentName?: string;
  bucketName: string; // iobs桶，有外网桶和内网桶
  documentGroupItemsId?: string; // 附件唯一id
}

export type ViewerImageItem = string; // 原图url，仅支持图片预览

export interface thumbnailImage extends ViewerImageAndThumbnailItem {
  title?: string; // 当前目录名称
}

export interface ImageViewerProps {
  list: ViewerItem[] | ViewerImageAndThumbnailItem[] | ViewerImageItem[]; // 预览文件list，支持嵌套（嵌套需要按照固定格式传参）
  current: string | number; // 当前预览的文件，可以是下标index和文件id
}
