<template>
  <div class="info-group-card" v-bind="$attrs">
    <div class="card-title">
      {{ title }}
      <img class="bg-image" src="assets/images/logo-title.png">
    </div>
    <div class="card-content">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 表单内容中信息块卡片布局组件
 * title: 卡片标题
 */
defineProps<{
  title: string;
}>();
</script>

<style lang="less" scoped>
.info-group-card {
  margin-top: 12px;
  .card-title {
    position: relative;
    z-index: 10;
    border-radius: 4px;
    padding: 0 10px;
    height: 31px;
    line-height: 26px;
    width: fit-content;
    color: #ffffff;
    background-image: linear-gradient(90deg, #029a4b 0%, #017036 100%);
    box-shadow: 0px -1px 2px 0px rgba(0, 55, 25, 0.24);
    .bg-image {
      position: absolute;
      top: 0;
      left: 0;
      height: 26px;
    }
  }
  .card-content {
    position: relative;
    z-index: 99;
    background: #fff;
    margin-top: -5px;
    border-radius: 4px;
    padding: 16px 24px;
  }
}
</style>
