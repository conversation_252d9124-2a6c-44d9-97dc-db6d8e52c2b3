<template>
  <div class="space-x-8px">
    <a-checkbox-group v-model:value="checkedList" :options="options" :disabled="disabled" />
    <a-checkbox v-if="!isHiddenAllCheck" v-model:checked="state.checkAll" :disabled="disabled" :indeterminate="state.indeterminate" @change="onCheckAllChange">全选</a-checkbox>
  </div>
</template>

<script setup lang="ts">
import type { CheckboxChangeEvent, CheckboxValueType } from 'ant-design-vue/es/checkbox/interface';
/**
 * @description: 复选框组（自带多选）
 * @props
 * options: { label: string; value: string }[] - 选项列表
 * checkedList: CheckboxValueType[] - 选中的复选框值，默认为空数组（双向绑定）
 */
const props = defineProps<{
  options: { label: string; value: string | number }[];
  disabled?: boolean;
  isHiddenAllCheck?: boolean; // 是否隐藏全选
}>();
const checkedList = defineModel<CheckboxValueType[]>('checkedList', { default: [] });

// 定义一个名为state的响应式对象，包含了indeterminate和checkAll两个属性
const state = reactive({
  indeterminate: false,
  checkAll: false,
});

// 处理全选复选框状态改变的情况
const onCheckAllChange = (e: CheckboxChangeEvent) => {
  // 获取所有选项的值
  const optionvalues = props.options.map(item => item.value);
  // 如果全选复选框被选中，则将checkedList更新为所有选项的值，否则更新为空数组
  if (e.target.checked) {
    checkedList.value = optionvalues;
  } else {
    checkedList.value = [];
  }
  // 更新state中的checkAll和indeterminate属性
  state.checkAll = e.target.checked;
  state.indeterminate = false;
};

// 监听checkedList的变化，更新state中的indeterminate和checkAll属性
watch(
  checkedList,
  (val) => {
    state.indeterminate = !!val.length && val.length < props.options.length;
    state.checkAll = val.length === props.options.length;
  },
  { immediate: true },
);
</script>
