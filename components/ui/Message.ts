/**
 * antdv的message组件使用hooks调用会引发白屏，而不使用hooks调用导致messag的样式使用了:where选择器引发低版本浏览器样式丢失
 * 综上所述，新建一个全局的message组件
 */
import { createVNode, render } from 'vue';
import Message from './Message.vue';

const defaultDuration = 4000;

export interface MessageConfig {
  duration: number;
}

export type MessageType = 'success' | 'error' | 'info' | 'warning';

export interface MessageListItem {
  content: string;
  type: MessageType;
  duration: number;
  current: number;
}
const createParentDom = () => {
  const div = document.createElement('div');
  div.style.zIndex = '6000';
  div.style.position = 'fixed';
  div.style.left = '50%';
  div.style.transform = 'translateX(-50%)';
  div.style.top = '8px';
  div.id = 'agr_an_pc_message';
  document.body.appendChild(div);
};

let messageList: MessageListItem[] = [];

const renderHtml = () => {
  const vnode = createVNode(Message, { list: [...messageList] });

  render(vnode, document.querySelector('#agr_an_pc_message'));
};

const successMessage = async (content: string, config?: MessageConfig) => {
  const parent = document.querySelector('#agr_an_pc_message');
  if (!parent) {
    createParentDom();
  }
  const msg = content || '';
  messageList.push({
    content: msg,
    type: 'success',
    duration: config?.duration || defaultDuration,
    current: 1000,
  });

  renderHtml();

  if (messageList.length === 1) {
    clearList();
  }
};

const errorMessage = (content: string, config?: MessageConfig) => {
  const parent = document.querySelector('#agr_an_pc_message');
  if (!parent) {
    createParentDom();
  }
  const msg = content || '';
  messageList.push({
    content: msg,
    type: 'error',
    duration: config?.duration || defaultDuration,
    current: 1000,
  });

  renderHtml();

  if (messageList.length === 1) {
    clearList();
  }
};

const infoMessage = (content: string, config?: MessageConfig) => {
  const parent = document.querySelector('#agr_an_pc_message');
  if (!parent) {
    createParentDom();
  }
  const msg = content || '';
  messageList.push({
    content: msg,
    type: 'info',
    duration: config?.duration || defaultDuration,
    current: 1000,
  });

  renderHtml();

  if (messageList.length === 1) {
    clearList();
  }
};

const warningMessage = (content: string, config?: MessageConfig) => {
  const parent = document.querySelector('#agr_an_pc_message');
  if (!parent) {
    createParentDom();
  }
  const msg = content || '';
  messageList.push({
    content: msg,
    type: 'warning',
    duration: config?.duration || defaultDuration,
    current: 1000,
  });

  renderHtml();

  if (messageList.length === 1) {
    clearList();
  }
};

const clearParentDom = () => {
  const parent = document.querySelector('#agr_an_pc_message');
  if (parent) {
    document.body.removeChild(parent);
  }
};

let timer = null;
const clearList = () => {
  clearInterval(timer);

  timer = setInterval(() => {
    if (!messageList.length) {
      clearInterval(timer);
      clearParentDom();
    }
    for (const item of messageList) {
      if (item.duration - item.current > 0) {
        item.current += 100;
      }
    }
    const list = messageList.filter(val => val.duration - val.current > 0);
    if (list.length !== messageList.length) {
      messageList = list;
      renderHtml();
    } else {
      messageList = list;
    }
  }, 100);
};

export const message = {
  success: successMessage,
  error: errorMessage,
  info: infoMessage,
  warning: warningMessage,
};
