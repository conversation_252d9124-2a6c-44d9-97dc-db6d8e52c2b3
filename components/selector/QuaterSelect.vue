<template>
  <a-input-group compact>
    <a-select v-model:value="yearValue" placeholder="请选择" :options="yearOptions" style="width: 40%" allow-clear />
    <a-select v-model:value="quaterValue" placeholder="请选择" :options="quaterOptions" :disabled="quaterDisabled" style="width: 60%" allow-clear />
  </a-input-group>
</template>

<script setup lang="ts">
import { $get } from '@/utils/request';
import { SUCCESS_CODE } from '@/utils/constants';

/**
 * @description: 季度选择组件
 * @props
 * {string} yearValue - 年(eg:2024)
 * {string} quaterValue - 季度(eg:第一季度，第二季度，第三季度，第四季度);
 */
const yearValue = defineModel<number | undefined>('yearValue', {
  default: undefined,
});
const quaterValue = defineModel<string | undefined>('quaterValue', {
  default: undefined,
});

const quaterDisabled = ref(false);

const yearOptions = ref<{ label: string; value: string }[]>([]);
const quaterOptions = ref<{ label: string; value: string }[]>([]);
// 获取投保批次筛选项
try {
  const res = await $get('/api/farmer/getInsureQuantityInfo');

  if (res && res.code === SUCCESS_CODE) {
    const { policyYearList = [], quarterMap = {} } = res.data;
    yearOptions.value = policyYearList.map((year: string) => ({
      label: year,
      value: year,
    }));
    quaterOptions.value = Object.keys(quarterMap).map((key) => ({
      label: quarterMap[key],
      value: key,
    }));
  }
} catch {
  yearOptions.value = [];
  quaterOptions.value = [];
}

watch(yearValue, (value) => {
  if (value) {
    quaterDisabled.value = false;
  } else {
    quaterValue.value = undefined;
    quaterDisabled.value = true;
  }
});
</script>
