<template>
  <a-select
    v-model:value="selectValue"
    placeholder="请选择"
    :options="options"
    allow-clear
    show-search
    :default-active-first-option="false"
    :loading="loading"
    option-filter-prop="label"
    :style="{ width: '100%' }"
    @change="(value, option) => emit('productChange', value, option)"
  />
</template>

<script setup lang="ts">
import { ref, defineModel, watch } from 'vue';
import { debounce } from 'lodash-es';
import { useGet } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

// 定义props的默认值
const props = withDefaults(defineProps<{
  departmentCode: string;// 机构编码
  encodeKey?: string;// 标的值
  defualtOptions?: Array<{ label: string; value: string }>;
  module?: string; // 模块名
  agentAgreementNo?: string; // 代理协议
  channelSourceCode?: string; // 渠道来源
  showTips?: boolean; // 产品筛选器禁用时 代理协议和渠道来源改变是否不清空产品信息并且提示用户信息
}>(), {
  departmentCode: '',
  encodeKey: '',
  defualtOptions: () => [],
  module: '',
  agentAgreementNo: '',
  channelSourceCode: '',
  showTips: false,
});

const emit = defineEmits(['productChange']);

const clearOption = () => {
  options.value = [];
};
defineExpose({
  clearOption,
});

// 定义双向绑定的select值
const selectValue = defineModel<string | undefined>('value', { default: undefined });
const options = ref<{ label: string; value: string }[]>(props.defualtOptions || []); // 初始化select选项
const loading = ref(false); // 初始化加载状态为false

const { fetchData: getProductList } = await useGet('/api/common/getProductList');
const firstInit = ref(true);
// 初始化产品列表
const fetchProductList = (departmentCode: string, encodeKey?: string, module?: string) => {
  loading.value = true;
  getProductList({
    departmentCode,
    targetType: encodeKey,
    module,
    conferNo: props.agentAgreementNo,
    channelCode: props.channelSourceCode,
  }).then((res) => {
    if (res && res.code === SUCCESS_CODE && res.data) { // 判断请求结果是否成功
      options.value = res.data;
      if (!res.data.some((item: { value: string }) => item.value === selectValue.value)) {
        if (props?.showTips) {
          message.warning('当前产品不支持该代理协议或该渠道出单，请修改！');
        } else {
          selectValue.value = undefined;
        }
      }
      if (firstInit.value) {
        // 初始化事触发change事件
        if (selectValue.value) {
          const option = options.value.find((item: { value: string; label: string }) => item.value === selectValue.value);
          emit('productChange', selectValue.value, option);
        }
        firstInit.value = false;
      }
    } else {
      options.value = [];
    }
  }).finally(() => {
    loading.value = false; // 无论请求成功或失败，加载状态都应该设置为false
  });
};

// 防抖查询产品下拉列表
const debounceFetchProductList = debounce(fetchProductList, 500);

// 监听props的变化，一旦departmentCode或encodeKey有变化就触发initProductList方法
watch([() => props.departmentCode, () => props.encodeKey, () => props.module, () => props.agentAgreementNo, () => props.channelSourceCode], ([newCode, newKey, newModule]) => {
  // 触发初始化产品列表
  if (newCode) {
    debounceFetchProductList(newCode, newKey, newModule);
  }
}, {
  immediate: true,
});
</script>
