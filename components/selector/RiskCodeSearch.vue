<template>
  <div class="flex w-full">
    <div class="shrink-0 flex gap-x-[4px] mr-8px" :style="{ width: showSearch ? '70%' : '100%' }">
      <template v-for="level in 5" :key="level">
        <a-select v-if="level >= startLevel" v-model:value="arrayValue[level - 1]" placeholder="请选择" allow-clear :disabled="disabledList[level - 1]" :style="{ width: '100%' }" :field-names="{ label: 'encodeValue', value: 'encodeKey' }" :options="levelOptions[level - 1]" @change="(value) => handleChange(value as string, level)" />
      </template>
    </div>
    <div v-if="showSearch" class="w-[30%]">
      <a-select v-model:value="searchValue" :disabled="disabled" style="width: 100%" :options="fiveDictOptions" placeholder="请搜索" :default-active-first-option="false" :filter-option="false" :not-found-content="null" show-search allow-clear :loading="searchLoading" @search="debounceSearch" @change="handleFiveDictChange" @input-key-down="inputKeyDownChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';
import { debounce } from 'lodash-es';
import { $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { nextTick } from 'vue';
import { Form } from 'ant-design-vue';
import { $get } from '@/utils/request';

const { gateWay, service } = useRuntimeConfig().public || {};
const emit = defineEmits(['changeValue', 'levelChange']);
const riskCode = defineModel<string | undefined>('value', {
  default: undefined,
});

interface RiskCodeSelectProps {
  departmentCode: string;
  defaultValue?: string;
  disabled?: boolean;
  showSearch?: boolean; // 是否展示搜索
  startLevel?: number;
  pageSceneCode?: string;
}

const props = withDefaults(defineProps<RiskCodeSelectProps>(), {
  departmentCode: '',
  defaultValue: '',
  disabled: false,
  showSearch: true,
  startLevel: 1,
  pageSceneCode: '',
});

const levelOptions = ref<DefaultOptionType[][]>([[], [], [], [], []]);
const arrayValue = ref<Array<undefined | string>>([undefined, undefined, undefined, undefined, undefined]);
const disabledList = ref([false, false, false, false, false]);
const searchValue = ref<string | undefined>();
const searchLoading = ref(false);
const fiveDictOptions = ref<{ value: string; label: string }[]>([]);
const currentLevel = ref(0); // 当前选中的标的级别

// Reset functions
const resetLevels = (startLevel: number) => {
  for (let i = startLevel; i < 5; i++) {
    arrayValue.value[i] = undefined;
    levelOptions.value[i] = [];
  }
};

const resetData = () => {
  arrayValue.value[0] = undefined;
  resetLevels(1);
  currentLevel.value = 0; // 重置当前级别
  emit('levelChange', 0); // 通知父组件级别已重置
};

defineExpose({
  valueList: arrayValue,
  disabledList,
  resetData,
  currentLevel, // 暴露当前级别给父组件
});

// API calls
// const { fetchData } = await useGet('/gateway/icore-agr-an.administrate/encodeDict/getEncodeDictList');

const fetchLevelOptions = async (level: number, encodeKey?: string) => {
  if (level > 5) return; // 目前只支持5级标的

  try {
    const res = await $get('/gateway/icore-agr-an.administrate/encodeDict/getEncodeDictList', {
      departmentCode: props.departmentCode,
      level,
      encodeKey,
      pageSceneCode: props.pageSceneCode,
    });

    if (res?.code === SUCCESS_CODE) {
      const index = level - 1; // 数据存储位置与选择器位置转换
      levelOptions.value[index] = res.data;
    }
  } catch (error) {
    console.error('Failed to fetch level options:', error);
  }
};

// Event handlers
const handleChange = (val: string, level: number) => {
  if (val) {
    // 正常选择标的
    riskCode.value = val;
    currentLevel.value = level; // 更新当前级别
    emit('changeValue', val, level);
    emit('levelChange', level); // 通知父组件当前级别
  } else {
    // 清除标的，value设置为上一级
    if (level > 1) {
      riskCode.value = arrayValue.value[level - 2];
      currentLevel.value = level - 1;
      emit('changeValue', arrayValue.value[level - 2], level - 1);
      emit('levelChange', level - 1); // 通知父组件当前级别
    } else {
      riskCode.value = undefined;
      currentLevel.value = 0;
      emit('changeValue', undefined, 0);
      emit('levelChange', 0); // 通知父组件当前级别重置为0
    }
  }

  resetLevels(level);
  if (val) {
    fetchLevelOptions(level + 1, val);
  }
};

const handleSearch = async (keywords: string) => {
  if (!keywords) return;

  searchLoading.value = true;
  try {
    const res = await $getOnClient('/api/common/getAllEncodeDictList', {
      departmentCode: props.departmentCode,
      keywords,
    });
    fiveDictOptions.value = res?.data || [];
  } catch (error) {
    console.error('Search failed:', error);
  } finally {
    searchLoading.value = false;
  }
};

const debounceSearch = debounce(handleSearch, 500);

const handleFiveDictChange = (value: SelectValue) => {
  if (value) {
    asyncInitValue(value.toString().split('-'));
  }
};

const inputKeyDownChange = (e: Event & { code: string }) => {
  if (e.code === 'Enter' && fiveDictOptions.value?.[0]?.value) {
    asyncInitValue(fiveDictOptions.value[0].value.toString().split('-'));
  }
};

// Initialize with default value
const asyncInitValue = async (values: string[]) => {
  // 重置所有选择器的值和选项
  resetLevels(0);

  // 依次调用接口给每个选择器options赋值
  for (let i = 0; i <= values.length; i++) {
    const encodeKey = values[i];
    const preEncodeKey = i > 0 ? values[i - 1] : '';

    if (!encodeKey) continue;

    await fetchLevelOptions(i + 1, preEncodeKey);

    const options = levelOptions.value[i];
    const option = options.find((opt) => opt.encodeKey === encodeKey);

    if (option) {
      arrayValue.value[i] = encodeKey;
      riskCode.value = encodeKey;
      currentLevel.value = i + 1; // 更新当前级别
      emit('changeValue', encodeKey, i + 1, option);
      emit('levelChange', i + 1); // 通知父组件当前级别
    } else {
      // 如果当前项数据不对，重置后面所有的选择器为空
      resetLevels(i);
      break;
    }
  }
  // 如果默认值没有五级，则需要请求最后一个下一级的option
  if (values.length < 5) {
    await fetchLevelOptions(values.length + 1, values.slice(-1)[0]);
  }
};

// 初始化默认值
const initDefaultValue = async () => {
  if (!props.defaultValue) {
    resetData();
    return;
  }

  try {
    const res = await $getOnClient(`${gateWay}${service.farmer}/encodeDict/findUpperRiskByRiskCode`, { riskCode: props.defaultValue });

    if (res?.code === SUCCESS_CODE) {
      const { riskCode1 = '', riskCode2 = '', riskCode3 = '', riskCode4 = '', riskCode5 = '' } = res.data;
      const riskCodes = [riskCode1, riskCode2, riskCode3, riskCode4, riskCode5].filter(Boolean);
      // 重置所有选择器的值和选项
      resetData();

      // 依次初始化每一级
      for (let i = 0; i < riskCodes.length; i++) {
        const level = i + 1;
        const parentCode = i > 0 ? riskCodes[i - 1] : '';

        // 获取当前级别的选项
        await fetchLevelOptions(level, parentCode);

        // 设置当前级别的值
        arrayValue.value[i] = riskCodes[i];
        // 如果是最后一级，设置为最终值
        if (i === riskCodes.length - 1) {
          riskCode.value = riskCodes[i];
          currentLevel.value = level; // 更新当前级别
          emit('changeValue', riskCodes[i], level);
          emit('levelChange', level); // 通知父组件当前级别
        }

        // 获取下一级选项（如果不是最后一级）
        if (i < riskCodes.length - 1) {
          await fetchLevelOptions(level + 1, riskCodes[i]);
        }
      }

      // 如果有值但不是5级，加载下一级的选项
      if (riskCodes.length > 0 && riskCodes.length < 5) {
        await fetchLevelOptions(riskCodes.length + 1, riskCodes[riskCodes.length - 1]);
      }
    }
  } catch (error) {
    console.error('Failed to fetch risk codes:', error);
  }
};

// Watchers
watchEffect(() => {
  disabledList.value = props.disabled ? [true, true, true, true, true] : [false, false, false, false, false];
});

const { onFieldChange } = Form.useInjectFormItemContext();
// Add watcher for riskCode to trigger form validation
watch(
  () => riskCode.value,
  () => {
    // Use nextTick to ensure DOM is updated before triggering validation
    nextTick(() => {
      onFieldChange();
    });
  },
);

watch(
  () => props.pageSceneCode,
  () => {
    fetchLevelOptions(1, '');
  },
  { immediate: true },
);

watch(
  () => props.defaultValue,
  () => {
    initDefaultValue();
  },
  { immediate: true, once: true },
);

watch(
  () => props.departmentCode,
  () => {
    // 部门变化时，重新初始化
    resetData();
    fetchLevelOptions(1, '');
    if (props.defaultValue) {
      initDefaultValue();
    }
  },
);

// 监听重置操作
watchEffect(() => {
  if (!riskCode.value) {
    arrayValue.value = [undefined, undefined, undefined, undefined, undefined];
    searchValue.value = undefined;
    currentLevel.value = 0; // 重置当前级别
    emit('levelChange', 0); // 通知父组件级别已重置
  }
});

// Initialize first level options
fetchLevelOptions(1, '');
</script>
