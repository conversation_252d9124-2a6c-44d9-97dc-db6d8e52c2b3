@bgColor: #f2f3f5;
@lightBgColor: #f7f8fa;
@primaryColor: #00bf8f;

// 表格样式覆盖
.ant-table-wrapper {
  .ant-table {
    .ant-table-thead
      > tr
      > th:not(:last-child):not(.ant-table-selection-column):not(
        .ant-table-row-expand-icon-cell
      ):not([colspan])::before {
      display: none;
    }

    .ant-table-thead > tr > th {
      background: @bgColor;
      border-bottom: none;
      padding: 9px 10px;
    }

    .ant-table-tbody > tr > td {
      padding: 6px 10px;
    }

    .ant-table-tbody > tr.table-striped td {
      background: @lightBgColor;
    }
  }

  .ant-pagination {
    .ant-pagination-item,
    .ant-pagination-prev,
    .ant-pagination-next {
      border: 1px solid #d4d6d9;
    }

    .ant-pagination-item-active {
      border: 1px solid @primaryColor;
    }
  }

  .ant-pagination,
  .ant-pagination-mini {
    .ant-pagination-prev,
    .ant-pagination-next {
      margin: 0 6px !important;
    }
  }
}

// 抽屉样式覆盖
.ant-drawer-content-wrapper .ant-drawer-content .ant-drawer-wrapper-body {
  .ant-drawer-header {
    padding: 12px 20px;
    background: @bgColor;
    border-bottom: none;

    .ant-drawer-header-title {
      flex-direction: row-reverse;

      .ant-drawer-close {
        margin-inline-end: 0px;
      }
    }
  }

  .ant-drawer-body {
    padding: 34px 20px;
  }
}

// 弹窗样式覆盖
.ant-modal-root .ant-modal-wrap .ant-modal {
  .ant-modal-close {
    top: 12px;
    right: 20px;
    transform: scale(0.9);
  }

  .ant-modal-content {
    padding: 0;

    .ant-modal-header {
      padding: 12px 20px;
      margin-bottom: 0;
      background: @bgColor;
      font-size: 16px;
    }

    .ant-modal-body {
      padding: 16px 20px;
      font-size: 14px;
      max-height: 500px;
      overflow-y: auto;

      .ant-modal-confirm-body {
        > .anticon {
          font-size: 21px;
        }
      }
    }

    .ant-modal-footer {
      margin-top: 0;
      padding: 16px 20px;
    }
  }
}

// 有背景图的提示弹窗
.bell-bg {
  .ant-modal-content {
    background: #ffffff url("@/assets/images/modalBG.png") top left no-repeat;
    background-size: 92px 150px;
  }
}

// 表单下面时间选择器撑满布局
.ant-form-item-control-input-content {
  .ant-picker {
    width: 100%;
  }
}

// 侧边栏样式覆盖
.ant-layout-sider {
  z-index: 1;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);
}

// 气泡卡片样式覆盖
.ant-tooltip .ant-tooltip-arrow,
.ant-popover .ant-popover-arrow {
  width: 16px !important;
  height: 16px !important;

  &::before {
    width: 16px !important;
    height: 8px !important;
  }

  &::after {
    box-shadow: unset;
  }
}

// 表格-操作列-第一个按钮去除左边距
.ant-table-cell .ant-btn.ant-btn-sm:first-of-type,
.ant-table-cell .ant-btn:first-of-type {
  padding-left: 0;
}

.ant-timeline .ant-timeline-item .ant-timeline-item-tail {
  border-width: 2px;
  border-color: #d4d6d9;
  transform: scale(1, 0.8);
}

.no-arrow-popover {
  .ant-popover-content {
    .ant-popover-arrow {
      display: none;
    }
  }
}

.ant-menu-light .ant-menu-item-selected .ant-menu-title-content .sub-title-content {
  color: #07C160;
}

.layout-sider-menu-bar.ant-menu li .ant-menu li .ant-menu-title-content {
  font-weight: 400;
  margin-left: 9px;
}

.ant-menu-submenu-open >.ant-menu-submenu-title >.expand-icon .txdicon {
  transform: rotate(-180deg);
}

// radio样式覆盖
.nuxt-agr {
  .ant-radio-wrapper.ant-radio-wrapper-checked.ant-radio-wrapper-in-form-item {
    color: rgba(0,0,0,0.90);
  }
  .ant-radio-wrapper.ant-radio-wrapper-in-form-item {
    margin-right: 24px;
    &:last-of-type {
      margin-right: 0px;
    }
  }
}

// checkbox样式覆盖
.nuxt-agr {
  .ant-checkbox-wrapper.ant-checkbox-wrapper-checked {
    color: rgba(0,0,0,0.90);
  }
}

.table-box .ant-table-content {
  max-height: 505px;
  overflow-y: auto!important;
}
.table-box .ant-table-thead {
  position: sticky;
  top: 0;
  z-index: 999;
}
.table-box .ant-table-empty .ant-table-content {
  overflow: hidden!important;
}