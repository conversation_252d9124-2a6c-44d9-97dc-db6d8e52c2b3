<template>
  <a-modal :open="visible" title="查看收付详情" :width="pxToRem(700)" @cancel="handleOk">
    <a-table :data-source="dataSource" :columns="columns" :loading="loading" :pagination="false" :bordered="false" :row-class-name="(_record: unknown, index: number) => (index % 2 === 1 ? 'table-striped' : null)" :scroll="{ x: 'max-content' }">
      <template #bodyCell="{ column, record, text }">
        <template v-if="['errorReason'].includes(column.dataIndex as string)">
          <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
            <div class="max-w-[180px] truncate">{{ text }}</div>
          </a-tooltip>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <div class="flex">
            <a-button v-if="!record.needCollPayFlag" type="link" size="small" @click="supplementaryFn(record.idEvntUpldThrdPtyPlatFinalResult, record.idEvntUpldThrdPtyPlatPremiumFinalResult)">
              补传
            </a-button>
            <a-button type="link" size="small" @click="openLogDrawer(record.insuranceBusinessNo, record.insuranceBusinessNoTypeCode, record.thirdPartyPlatformInterfaceNo, record.idEvntUpldThrdPtyPlatPremiumFinalResult)">
              日志查看
            </a-button>
            <a-button type="link" size="small" @click="noNeedToDeal(record.idEvntUpldThrdPtyPlatFinalResult, record.idEvntUpldThrdPtyPlatPremiumFinalResult)">
              无需处理
            </a-button>
          </div>
        </template>
      </template>
    </a-table>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleOk">返回</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import type {
  UploadedCollPayListRes,
  UploadedDocListParams,
} from '@/apiTypes/compliance';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import { usePost } from '@/composables/request';
import { message } from '@/components/ui/Message';

const { fetchData: fetchList } = await usePost<
  UploadedCollPayListRes[],
  UploadedDocListParams
>('api/compliance/getSuccessUploadedCollPayList');
const batchSupplementaryReq = await usePost(
  '/api/compliance/batchSupplementary',
);

const props = defineProps<{
  visible: boolean;
  insuranceBusinessNo: string;
  thirdPartyPlatformInterfaceNo: string;
  idEvntUpldThrdPtyPlatFinalResult: string;
}>();
const emit = defineEmits(['update:visible', 'openDrawer', 'noNeedToDeal']);

const dataSource = ref<UploadedCollPayListRes[]>([]);
const loading = ref(false);

const fetchData = () => {
  dataSource.value = [];
  loading.value = true;
  fetchList({
    insuranceBusinessNo: props.insuranceBusinessNo,
    thirdPartyPlatformInterfaceNo: props.thirdPartyPlatformInterfaceNo,
  })
    .then((res) => {
      const { code, data } = res || {};
      if (code === SUCCESS_CODE && Array.isArray(data)) {
        dataSource.value = data.map(item => ({
          premiumTerm: item.premiumTerm || '',
          dockStatusName: item.dockStatusName || '',
          idEvntUpldThrdPtyPlatFinalResult:
            props.idEvntUpldThrdPtyPlatFinalResult || '',
          idEvntUpldThrdPtyPlatPremiumFinalResult:
            item.idEvntUpldThrdPtyPlatPremiumFinalResult || '',
          insuranceBusinessNo: item.insuranceBusinessNo || '',
          thirdPartyPlatformInterfaceNo:
            item.thirdPartyPlatformInterfaceNo || '',
          insuranceBusinessNoTypeCode: item.insuranceBusinessNoTypeCode || '',
          updatedDate: item.updatedDate || '',
          errorReason: item.errorReason || '',
        }));
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const handleOk = () => {
  emit('update:visible', false);
};

const columns = [
  {
    title: '期次',
    dataIndex: 'premiumTerm',
  },
  {
    title: '更新时间',
    dataIndex: 'updatedDate',
  },
  {
    title: '状态',
    dataIndex: 'dockStatusName',
  },
  {
    title: '错误描述',
    dataIndex: 'errorReason',
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
];

watch(
  () => props.visible,
  (val) => {
    if (val) {
      fetchData();
    }
  },
  {
    immediate: true,
  },
);

// 补传
const supplementaryFn = (idString: string, premiumTerm: string) => {
  batchSupplementaryReq
    .fetchData({ pkIdList: idString, premiumFinalId: premiumTerm })
    .then((res) => {
      if (res?.code === SUCCESS_CODE) {
        message.success('补传成功');
        fetchData();
      } else {
        message.error(res?.msg || '');
      }
    });
};

const openLogDrawer = (
  insuranceBusinessNo: string,
  insuranceBusinessNoTypeCode: string,
  thirdPartyPlatformInterfaceNo: string,
  idEvntUpldThrdPtyPlatPremiumFinalResult: string,
) => {
  emit(
    'openDrawer',
    insuranceBusinessNo,
    insuranceBusinessNoTypeCode,
    thirdPartyPlatformInterfaceNo,
    idEvntUpldThrdPtyPlatPremiumFinalResult,
  );
};
const noNeedToDeal = (id: string, premiumResultId: string) => {
  emit('noNeedToDeal', id, premiumResultId);
};
</script>
