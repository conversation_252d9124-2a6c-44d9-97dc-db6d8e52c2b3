<template>
  <div class="w-full h-[450px] relative">
    <div id="uploadInfoManageTableContainer" class="w-full h-full" />
    <div v-show="loading" class="w-full h-full z-[1000] absolute top-0 left-0 flex justify-center items-center bg-[rgba(0,0,0,0.25)]"><a-spin size="large" /></div>
  </div>
</template>

<script setup lang="ts">
import { ListTable } from '@visactor/vtable';
import { createGroup, createText } from '@visactor/vtable/es/vrender';
import { onActivated } from 'vue';
import { theme } from '@/components/ui/Vtable/theme';
import kong from '@/assets/images/table/ic-ziliao-kong.svg';
import type { GetReportData } from '@/apiTypes/compliance';
import { copyText } from '@/utils/tools';

const isListen = ref(false);
const vTableInstance = shallowRef(null);
const selectKeys = defineModel<string[]>('selectKeys');
const loading = defineModel<boolean>('loading', {
  default: false, // 默认值
});
const { dataSource } = defineProps<{ dataSource: GetReportData[] }>();
const emit = defineEmits(['supplementaryFn', 'openLogDrawer', 'handleBlockOfLand', 'queryDocList', 'queryPayList', 'noNeedToDeal']);
const columns = [
  {
    field: 'isCheck',
    title: '',
    headerType: 'checkbox',
    cellType: 'checkbox',
    maxWidth: 40,
    disableSelect: true,
  },
  {
    field: 'order',
    title: '序号',
    maxWidth: 50,
    disableSelect: true,
  },
  {
    field: 'deptName',
    title: '出单机构',
    minWidth: 130,
  },
  {
    field: 'insuranceBusinessNo',
    title: '单证号',
    minWidth: 186,
    style: {
      fontFamily: 'D-DIN-PRO-Regular',
    },
  },
  {
    field: 'thirdPartyPlatformInterfaceName',
    title: '接口名称',
    minWidth: 170,
  },
  {
    field: 'dockStatusName',
    title: '状态',
    maxWidth: 100,
  },
  {
    field: 'failureTypeName',
    title: '失败类型',
    maxWidth: 100,
  },
  {
    field: 'failureTypeSubName',
    title: '失败类型细分',
    minWidth: 130,
  },
  {
    field: 'handleProposalName',
    title: '处理方案',
    minWidth: 130,
  },
  {
    field: 'platErrorReason',
    title: '错误描述',
    minWidth: 180,
  },
  {
    field: 'updatedDate',
    title: '响应时间',
    minWidth: 160,
  },
  {
    field: 'insuranceBusinessNoTypeName',
    title: '单证类型',
    maxWidth: 90,
  },
  {
    field: 'operation',
    title: '操作',
    width: 'auto',
    disableSelect: true,
    customLayout: (args) => {
      const { table, row, col, rect } = args;
      const { height, width } = rect || table.getCellRect(col, row);
      const record = table.getRecordByRowCol(col, row);

      const container = createGroup({
        height,
        width,
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'nowrap',
        alignItems: 'center',
      });

      if (!record.needCollPayFlag) {
        const supplementaryBtn = createText({
          text: '补传',
          fontSize: 13,
          fontFamily: 'MicrosoftYaHei',
          fill: '#576B95',
          boundsPadding: [0, 8],
          cursor: 'pointer',
        });

        supplementaryBtn.on('click', () => {
          emit('supplementaryFn', record.idEvntUpldThrdPtyPlatFinalResult);
        });

        const openLogDrawerBtn = createText({
          text: '日志查看',
          fontSize: 13,
          fontFamily: 'MicrosoftYaHei',
          fill: '#576B95',
          boundsPadding: [0, 8],
          cursor: 'pointer',
        });

        openLogDrawerBtn.on('click', () => {
          emit('openLogDrawer', record.insuranceBusinessNo, record.insuranceBusinessNoTypeCode, record.thirdPartyPlatformInterfaceNo);
        });

        container.add(supplementaryBtn);
        container.add(openLogDrawerBtn);
      }

      if (record.needBlockOfLand) {
        const handleBlockBtn = createText({
          text: '按原始地块上传',
          fontSize: 13,
          fontFamily: 'MicrosoftYaHei',
          fill: '#576B95',
          boundsPadding: [0, 8],
          cursor: 'pointer',
        });
        handleBlockBtn.on('click', () => {
          emit('handleBlockOfLand', record.idEvntAsynchronousTaskHandle);
        });
        container.add(handleBlockBtn);
      }

      if (record.needDocWindFlg) {
        const docWindBtn = createText({
          text: '查看影像列表',
          fontSize: 13,
          fontFamily: 'MicrosoftYaHei',
          fill: '#576B95',
          boundsPadding: [0, 8],
          cursor: 'pointer',
        });
        docWindBtn.on('click', () => {
          emit('queryDocList', record.insuranceBusinessNo, record.thirdPartyPlatformInterfaceNo);
        });
        container.add(docWindBtn);
      }

      if (record.needCollPayFlag) {
        const collPayBtn = createText({
          text: '查看收付',
          fontSize: 13,
          fontFamily: 'MicrosoftYaHei',
          fill: '#576B95',
          boundsPadding: [0, 8],
          cursor: 'pointer',
        });
        collPayBtn.on('click', () => {
          emit('queryPayList', record.insuranceBusinessNo, record.thirdPartyPlatformInterfaceNo, record.idEvntUpldThrdPtyPlatFinalResult);
        });
        container.add(collPayBtn);
      }

      if (record.noNeedToDeal) {
        const noNeedToBtn = createText({
          text: '无需处理',
          fontSize: 13,
          fontFamily: 'MicrosoftYaHei',
          fill: '#576B95',
          boundsPadding: [0, 8],
          cursor: 'pointer',
        });
        noNeedToBtn.on('click', () => {
          emit('noNeedToDeal', record.idEvntUpldThrdPtyPlatFinalResult);
        });
        container.add(noNeedToBtn);
      }

      return {
        rootContainer: container,
        renderDefault: false,
      };
    },
  },
];

const options = {
  rightFrozenColCount: 1, // 右侧冻结列
  tooltip: {
    isShowOverflowTextTooltip: true,
  },
  theme: theme,
  hover: { // 与inlineRowBgColor一起使用才有效果
    highlightMode: 'row',
  },
  keyboardOptions: { // 启动复制快捷键功能
    copySelected: true,
  },
  menu: {
    contextMenuItems: ['复制'],
  },
  widthMode: 'standard',
  autoFillWidth: true, // 列数不够，自动撑宽列宽，使得表格占满整个容器
  emptyTip: {
    text: '暂无数据',
    textStyle: {
      color: 'rgba(0,0,0,0.60)',
    },
    icon: {
      width: 86,
      height: 86,
      image: kong,
    },
  },
  columns,
  records: [],
};

// 清楚勾选
const clearCheckboxSelected = () => {
  const infos = vTableInstance.value.getCheckboxState('isCheck') || [];
  const length = infos.length;
  if (length) {
    for (let i = 0; i < length; i++) {
      vTableInstance.value.setCellCheckboxState(0, i, false);
    }
  }
};

// 获取勾选
const getCheckboxSelected = () => {
  const infos = vTableInstance.value.getCheckboxState('isCheck') || [];
  const length = infos.length;
  const idList = [];
  if (length) {
    for (let i = 0; i < length; i++) {
      if (infos[i]) {
        idList.push(dataSource[i].idEvntUpldThrdPtyPlatFinalResult);
      }
    }
  }
  return idList;
};

defineExpose({
  clearCheckboxSelected,
});

const ctrlc = (event) => {
  const isCtrlPressed = event.ctrlKey || event.metaKey;
  if (isCtrlPressed && event.key === 'c' && vTableInstance.value) {
    const info = vTableInstance.value.getSelectedCellInfos();
    if (info.length === 1 && info[0].length === 1) {
      // 仅支持复制一个单元格
      copyText(info[0][0].value || '');
    }
  }
};

watchEffect(() => {
  if (dataSource && vTableInstance.value) {
    vTableInstance.value.setRecords(dataSource);
  }
});
onMounted(() => {
  vTableInstance.value = new ListTable(document.getElementById('uploadInfoManageTableContainer'), options);

  // 监听右侧菜单
  vTableInstance.value.on('dropdown_menu_click', (args) => {
    if (args.menuKey === '复制') {
      const text = vTableInstance.value.getCellValue(args.col, args.row);
      copyText(text);
    }
  });

  // 监听checkbox选中变化
  vTableInstance.value.on('checkbox_state_change', () => {
    const idList = getCheckboxSelected();
    selectKeys.value = idList;
  });
  if (!isListen.value) {
    isListen.value = true;
    document.addEventListener('keydown', ctrlc); // keyboardOptions配置无效，原因未知，手动监听
  }
});

onActivated(() => {
  if (!isListen.value) {
    isListen.value = true;
    document.addEventListener('keydown', ctrlc); // keyboardOptions配置无效，原因未知，手动监听
  }
});

onDeactivated(() => {
  if (isListen.value) {
    isListen.value = false;
    document.removeEventListener('keydown', ctrlc);
  }
});
</script>
