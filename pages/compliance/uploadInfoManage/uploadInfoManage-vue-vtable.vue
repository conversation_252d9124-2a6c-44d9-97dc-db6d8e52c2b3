<template>
  <div class="upload-manage">
    <div class="upload-manage-head">
      <div class="flex">
        <a-form class="grow" :label-col="{ style: { width: pxToRem(100) } }" :colon="false">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="机构" name="belongBusinessDepartmentNo" v-bind="validateInfos.belongBusinessDepartmentNo">
                <DeptListSelect :dept-code="modelRef.belongBusinessDepartmentNo" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="平台名称" name="thirdPartyPlatformNo" v-bind="validateInfos.thirdPartyPlatformNo">
                <a-select
                  v-model:value="modelRef['thirdPartyPlatformNo']"
                  :allow-clear="true"
                  :show-search="true"
                  :options="platformList"
                  :filter-option="(val: string, options: PlatformCodeName) => options.platformCodeName.includes(val)"
                  :field-names="{
                    label: 'platformCodeName',
                    value: 'thirdPartyPlatformNo',
                  }"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="接口名称" name="thirdPartyPlatformInterfaceNo" v-bind="validateInfos.thirdPartyPlatformInterfaceNo">
                <a-select
                  v-model:value="modelRef['thirdPartyPlatformInterfaceNo']"
                  :allow-clear="true"
                  :show-search="true"
                  :options="interfaceList"
                  :filter-option="(val: string, options: InterfaceCodeName) => options.thirdPartyPlatformInterfaceName.includes(val)"
                  :field-names="{
                    label: 'thirdPartyPlatformInterfaceName',
                    value: 'thirdPartyPlatformInterfaceNo',
                  }"
                  placeholder="请选择"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-if="showExpandStatus">
            <a-col :span="24">
              <a-form-item label="单证号" name="insuranceBusinessNo" v-bind="validateInfos.insuranceBusinessNo">
                <!-- <DocGroupSelect v-model:docType="modelRef['insuranceBusinessNoTypeCode']" v-model:docNo="modelRef['insuranceBusinessNo']" /> -->
                <a-input-group>
                  <a-row :gutter="8">
                    <a-col :span="5">
                      <a-select v-model:value="modelRef['insuranceBusinessNoTypeCode']" :options="typeList" :style="{ width: '100%' }" allow-clear placeholder="请选择" />
                    </a-col>
                    <a-col :span="19">
                      <a-input v-model:value="modelRef['insuranceBusinessNo']" placeholder="请输入单号，支持输入同类型的多个单号，用英文逗号隔开" />
                    </a-col>
                  </a-row>
                </a-input-group>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-if="showExpandStatus" :gutter="24">
            <a-col :span="8">
              <a-form-item label="响应时间" name="dateTime" v-bind="validateInfos.dateTime">
                <RangePicker v-model:value="modelRef['dateTime']" value-format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="失败类型" name="failureType" v-bind="validateInfos.failureType">
                <a-select
                  v-model:value="modelRef['failureType']"
                  :show-search="true"
                  :allow-clear="true"
                  :options="failure_type_list"
                  :field-names="{
                    label: 'parameterName',
                    value: 'parameterCode',
                  }"
                  placeholder="请选择"
                  :filter-option="filterOption"
                  @change="queryByClassNoAndIdParent"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="失败类型细分" name="failureTypeSubdivision" v-bind="validateInfos.failureTypeSubdivision">
                <a-select
                  v-model:value="modelRef['failureTypeSubdivision']"
                  :disabled="!modelRef.failureType"
                  :allow-clear="true"
                  :show-search="true"
                  :options="failureList"
                  :field-names="{
                    label: 'parameterName',
                    value: 'parameterCode',
                  }"
                  placeholder="请选择"
                  :filter-option="filterOption"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="处理方案" name="handleProposal" v-bind="validateInfos.handleProposal">
                <a-select
                  v-model:value="modelRef['handleProposal']"
                  :allow-clear="true"
                  :show-search="true"
                  :options="handle_proposal_list"
                  :field-names="{
                    label: 'parameterName',
                    value: 'parameterCode',
                  }"
                  placeholder="请选择"
                  :filter-option="filterOption"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-if="showExpandStatus" :gutter="24">
            <a-col :span="24">
              <a-form-item label="状态" name="dockStatusCode" v-bind="validateInfos.dockStatusCode">
                <RadioGroup v-model:value="modelRef['dockStatusCode']" :options="docStatusOptions" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="expand-area" @click="switchExpand">
          <span>{{ showExpandStatus ? '收起' : '展开' }}</span>
          <VueIcon :icon="showExpandStatus ? IconChevronUpFont : IconChevronDownFont" />
        </div>
      </div>
      <div class="flex justify-center">
        <a-button class="mr-[12px]" @click="reset">重置</a-button>
        <a-button type="primary" ghost @click="submit">查询</a-button>
      </div>
    </div>
    <div class="upload-manage-content">
      <div class="upload-manage-content-title">
        <div class="flex items-center space-x-[12px]">
          <span class="text-[16px] text-[rgba(0,0,0,0.8)] font-semibold">上报情况清单</span>
          <span v-if="selectLength > 0" class="text-[rgba(0,0,0,0.55)] text-[14px]">已选择{{ selectLength }}条</span>
        </div>
        <div class="flex space-x-[8px]">
          <a-button :loading="batchSupplementaryLoading" @click="batchSupplementaryFn">
            批量补传
          </a-button>
          <a-button type="primary" :loading="batchExportLoading" @click="batchExportFn">
            导出
          </a-button>
        </div>
      </div>
      <div class="W-full h-[450px]">
        <ClientOnly>
          <ListTable ref="listTable" :options="tableOptions" :records="tableOptionsRecords" @on-checkbox-state-change="checkboxStateChange" @on-drop-down-menu-click="onDropDownMenuClick">
            <ListColumn field="isCheck" title="" header-type="checkbox" cell-type="checkbox" :max-width="40" :disable-select="true" />
            <ListColumn field="order" title="序号" :max-width="50" />
            <ListColumn field="deptName" title="出单机构" :min-width="130" />
            <ListColumn field="insuranceBusinessNo" title="单证号" :min-width="186" :style="{ fontFamily: 'D-DIN-PRO-Regular' }" />
            <ListColumn field="thirdPartyPlatformInterfaceName" title="接口名称" :min-width="170" />
            <ListColumn field="dockStatusName" title="状态" :max-width="100" />
            <ListColumn field="failureTypeName" title="失败类型" :max-width="100" />
            <ListColumn field="failureTypeSubName" title="失败类型细分" :min-width="130" />
            <ListColumn field="handleProposalName" title="处理方案" :min-width="130" />
            <ListColumn field="platErrorReason" title="错误描述" :min-width="180" />
            <ListColumn field="updatedDate" title="响应时间" :min-width="160" />
            <ListColumn field="insuranceBusinessNoTypeName" title="单证类型" :max-width="90" />
            <ListColumn field="operation" title="操作" width="auto" :disable-select="true">
              <template #customLayout="{ record, height }">
                <Group display="flex" align-items="center" :height="height">
                  <Text v-if="!record.needCollPayFlag" cursor="pointer" fill="#576B95" :font-size="13" font-family="MicrosoftYaHei" :bounds-padding="[0, 8]" text="补传" @click="supplementaryFn(record.idEvntUpldThrdPtyPlatFinalResult)" />
                  <Text v-if="!record.needCollPayFlag" cursor="pointer" fill="#576B95" :font-size="13" font-family="MicrosoftYaHei" text="日志查看" @click="openLogDrawer(record.insuranceBusinessNo, record.insuranceBusinessNoTypeCode, record.thirdPartyPlatformInterfaceNo)" />
                  <Text v-if="record.needBlockOfLand" cursor="pointer" :bounds-padding="[0, 8]" fill="#576B95" :font-size="13" font-family="MicrosoftYaHei" text="按原始地块上传" @click="handleBlockOfLand(record.idEvntAsynchronousTaskHandle)" />
                  <Text v-if="record.needDocWindFlg" cursor="pointer" :bounds-padding="[0, 8]" fill="#576B95" :font-size="13" font-family="MicrosoftYaHei" text="查看影像列表" @click="queryDocList(record.insuranceBusinessNo, record.thirdPartyPlatformInterfaceNo)" />
                  <Text v-if="record.needCollPayFlag" cursor="pointer" :bounds-padding="[0, 8]" fill="#576B95" :font-size="13" font-family="MicrosoftYaHei" text="查看收付" @click="queryPayList(record.insuranceBusinessNo, record.thirdPartyPlatformInterfaceNo, record.idEvntUpldThrdPtyPlatFinalResult)" />
                  <Text v-if="record.noNeedToDeal" cursor="pointer" :bounds-padding="[0, 8]" fill="#576B95" :font-size="13" font-family="MicrosoftYaHei" text="无需处理" @click="noNeedToDeal(record.idEvntUpldThrdPtyPlatFinalResult, record.idEvntUpldThrdPtyPlatPremiumFinalResult)" />
                </Group>
              </template>
            </ListColumn>
          </ListTable>
        </ClientOnly>
      </div>
      <div class="mt-[20px] pb-[20px] flex justify-end"><a-pagination v-bind="pagination" /></div>

      <!-- <a-table
        :data-source="dataSource"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        :bordered="false"
        :row-selection="{
          selectedRowKeys: selectRowKeys,
          onChange: onSelectRowKeysChange,
        }"
        :row-class-name="(_record: unknown, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
        :scroll="{ x: 'max-content' }"
        row-key="idEvntUpldThrdPtyPlatFinalResult"
      >
        <template #bodyCell="{ column, text, record, index }">
          <template v-if="column.dataIndex === 'order'">
            {{ index + 1 }}
          </template>
          <template v-if="['errorCategoryName', 'platErrorReason', 'handleProposalName'].includes(column.dataIndex as string)">
            <a-popover>
              <template #title>
                <div class="flex justify-between">
                  <span>{{ column.title }}</span>
                  <div style="cursor: pointer" class="flex items-center" @click="copyText(text)">
                    <VueIcon :icon="IconFuzhiFont" />
                    <span class="text-[12px]" style="font-weight: 400">复制</span>
                  </div>
                </div>
              </template>
              <template #content>
                <div class="max-w-[200px]">{{ text }}</div>
              </template>
              <div class="max-w-[200px] truncate">{{ text }}</div>
            </a-popover>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <div class="flex">
              <a-button v-if="!record.needCollPayFlag" type="link" size="small" @click="supplementaryFn(record.idEvntUpldThrdPtyPlatFinalResult)">
                补传
              </a-button>
              <a-button v-if="!record.needCollPayFlag" type="link" size="small" @click="openLogDrawer(record.insuranceBusinessNo, record.insuranceBusinessNoTypeCode, record.thirdPartyPlatformInterfaceNo)">
                日志查看
              </a-button>
              <a-button v-if="record.needBlockOfLand" type="link" size="small" @click="handleBlockOfLand(record.idEvntAsynchronousTaskHandle)">
                按原始地块上传
              </a-button>
              <a-button v-if="record.needDocWindFlg" type="link" size="small" @click="queryDocList(record.insuranceBusinessNo, record.thirdPartyPlatformInterfaceNo)">
                查看影像列表
              </a-button>
              <a-button v-if="record.needCollPayFlag" type="link" size="small" @click="queryPayList(record.insuranceBusinessNo, record.thirdPartyPlatformInterfaceNo, record.idEvntUpldThrdPtyPlatFinalResult)">
                查看收付
              </a-button>
              <a-button v-if="record.noNeedToDeal" type="link" size="small" @click="noNeedToDeal(record.idEvntUpldThrdPtyPlatFinalResult)">
                无需处理
              </a-button>
            </div>
          </template>
        </template>
      </a-table> -->
    </div>
  </div>
  <!-- 日志查看 -->
  <a-drawer v-if="openDrawer" v-model:open="openDrawer" title="日志查看页面" width="50%" placement="right">
    <UploadLogTable :insurance-business-no="selectInsuranceBusinessNo" :insurance-business-no-type-code="selectInsuranceBusinessNoTypeCode" :third-party-platform-interface-no="selectThirdPartyPlatformInterfaceNo" :premium-term="premiumTermNo" />
  </a-drawer>
  <!-- 附件类型补传 -->
  <upload-attach-type v-model:visible="docVisible" :insurance-business-no="selectInsuranceBusinessNo" :third-party-platform-interface-no="selectThirdPartyPlatformInterfaceNo" />
  <upload-pay-list v-model:visible="payListVisible" :insurance-business-no="selectInsuranceBusinessNo" :third-party-platform-interface-no="selectThirdPartyPlatformInterfaceNo" :id-evnt-upld-thrd-pty-plat-final-result="idEvntUpldThrdPtyPlatFinalResultNo" @open-drawer="openLogsDrawer" @no-need-to-deal="noNeedToDeal" />
</template>

<script setup lang="ts">
import { Form, RangePicker, RadioGroup } from 'ant-design-vue';
import {
  IconChevronDownFont,
  IconChevronUpFont,
} from '@pafe/icons-icore-agr-an';
import { Group, ListColumn, ListTable, Text } from '@visactor/vue-vtable';
import UploadLogTable from './uploadLogTable.vue';
// import DocGroupSelect from "@/components/DocGroupSelect/DocGroupSelect.vue";
import UploadAttachType from './uploadAttachType.vue';
import UploadPayList from './uploadPayList.vue';
import DeptListSelect from '@/pages/compliance/components/DeptListSelect.vue';
import type {
  InterfaceCodeName,
  PlatformCodeName,
  GetReportParams,
  GetReportData,
  DepartmentNoType,
  GetResultParams,
} from '@/apiTypes/compliance';
import type { TableData } from '@/apiTypes/interface';
import { SUCCESS_CODE } from '@/utils/constants';
import { downloadBlob, pxToRem } from '@/utils/tools';
import { usePagination } from '@/composables/usePagination';
import { usePost, $postOnClient, $post } from '@/composables/request';
import { message } from '@/components/ui/Message';
import { theme } from '@/components/ui/Vtable/theme';
import kong from '@/assets/images/table/ic-ziliao-kong.svg';

// 单证类型可选项
const typeList = ref<{ label: string; value: string }[]>([]);
const { gateWay, service } = useRuntimeConfig().public || {};
const listTable = ref(null);

try {
  const { data } = await $post<{ label: string; value: string }[]>('/api/compliance/getBusinessTypeList') || {};
  typeList.value = data || [];
} catch (err) {
  console.log(err);
  typeList.value = [];
}
// 模糊搜索
const filterOption = (input: string, option: Record<string, string>) => {
  return option.parameterName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const interFaceListInterfaceCodeNameReq = await usePost<
  Array<InterfaceCodeName>,
  { idParmThrdPtyPlatBase: string }
>('/api/compliance/listInterfaceCodeName');
const getReportListReq = await usePost<TableData<GetReportData>>(
  '/api/compliance/getReportResultList',
);
const batchSupplementaryReq = await usePost(
  '/api/compliance/batchSupplementary',
);
const getPlatformCodeNameListReq = await usePost<
  PlatformCodeName[],
  DepartmentNoType
>('/api/compliance/listPlatformCodeName');
const useOriginalBlockReq = await usePost<{ id: string }>(
  '/api/compliance/useOriginalBlock',
);
const batchUnUploadReq = await usePost('/api/compliance/batchUnUpload');

const changeDeptCode = (val: string) => {
  modelRef.belongBusinessDepartmentNo = val;
};

const docStatusOptions = [
  { label: '成功', value: '2' },
  { label: '失败', value: '-1' },
  { label: '所有', value: '-2' },
  { label: '等待执行', value: '0' },
  { label: '执行中', value: '1' },
  { label: '无需处理', value: '4' },
  { label: '其他', value: '99' },
];

const modelRef = reactive<{
  belongBusinessDepartmentNo?: string;
  thirdPartyPlatformNo: string;
  thirdPartyPlatformInterfaceNo?: string;
  insuranceBusinessNoTypeCode?: string;
  insuranceBusinessNo: string;
  dateTime: [string, string];
  errorCategoryNo?: string;
  dockStatusCode: string;
  failureType: string | undefined;
  failureTypeSubdivision: string | undefined;
  handleProposal: string | undefined;
}>({
  belongBusinessDepartmentNo: undefined, // 归属机构
  thirdPartyPlatformNo: '', // 平台编码
  thirdPartyPlatformInterfaceNo: undefined, // 接口编码
  insuranceBusinessNoTypeCode: undefined, // 单证号类型
  insuranceBusinessNo: '', // 单证号
  dateTime: ['', ''], // 响应时间
  errorCategoryNo: undefined, // 错误分类代码
  dockStatusCode: '-1', // 状态代码
  failureType: undefined,
  failureTypeSubdivision: undefined,
  handleProposal: undefined,
});

const rulesRef = reactive({
  belongBusinessDepartmentNo: [
    {
      required: true,
      message: '请选择机构',
    },
  ],
  thirdPartyPlatformNo: [
    {
      required: true,
      message: '请选择平台名称',
    },
  ],
});

const { resetFields, validate, validateInfos } = Form.useForm(
  modelRef,
  rulesRef,
);

// 是否展开额外筛选器
const showExpandStatus = ref(true);
// 切换展开状态
const switchExpand = () => {
  showExpandStatus.value = !showExpandStatus.value;
};

const selectInsuranceBusinessNo = ref('');
const selectInsuranceBusinessNoTypeCode = ref('');
const selectThirdPartyPlatformInterfaceNo = ref('');
const premiumTermNo = ref('');
const idEvntUpldThrdPtyPlatFinalResultNo = ref('');

const selectLength = ref<number>(0);
// 为了实现翻页后保存之前页选中的项

const clearSelect = () => {
  const infos = listTable.value.vTableInstance.getCheckboxState('isCheck') || [];
  const length = infos.length;
  if (length) {
    for (let i = 0; i < length; i++) {
      listTable.value.vTableInstance.setCellCheckboxState(0, i, false);
    }
  }
};

// 平台名称可选项
const platformList = ref<PlatformCodeName[]>([]);
// 接口名称可选项
const interfaceList = ref<InterfaceCodeName[]>([]);
// 失败原因可选项
const errorList = ref<
  { errorCategoryNo: string; errorCategoryNoName: string }[]
>([]);

const dataSource = ref<GetReportData[]>([]);
const loading = ref(false);
const reset = () => {
  resetFields();
};
const refresh = () => {
  const params: GetReportParams = {
    belongBusinessDepartmentNo: modelRef.belongBusinessDepartmentNo || '',
    dockStatusCode: modelRef.dockStatusCode,
    errorCategoryNo: modelRef.errorCategoryNo || '',
    insuranceBusinessNo: modelRef.insuranceBusinessNo,
    insuranceBusinessNoTypeCode: modelRef.insuranceBusinessNoTypeCode || '',
    thirdPartyPlatformInterfaceNo: modelRef.thirdPartyPlatformInterfaceNo || '',
    thirdPartyPlatformNo: modelRef.thirdPartyPlatformNo,
    updatedDateEnd: modelRef.dateTime?.[1],
    updatedDateStart: modelRef.dateTime?.[0],
    pageSize: pagination.pageSize!,
    pageNum: pagination.current!,
    failureType: modelRef.failureType,
    failureTypeSubdivision: modelRef.failureTypeSubdivision,
    handleProposal: modelRef.handleProposal,
  };
  validate().then(async () => {
    loading.value = true;
    getReportListReq
      .fetchData(params)
      .then((res) => {
        if (res?.code === SUCCESS_CODE) {
          const {
            records,
            total = 0,
            current = 1,
            size = 10,
          } = res.data;
          // dataSource.value = records;
          dataSource.value = records.map(record => ({
            belongBusinessDepartmentName: record.belongBusinessDepartmentName,
            deptCode: record.deptCode || '', // 出单机构
            deptName: record.deptName || '', // 出单机构
            dockStatusName: record.dockStatusName,
            handleProposal: record.handleProposal,
            idEvntUpldThrdPtyPlatFinalResult:
              record.idEvntUpldThrdPtyPlatFinalResult,
            insuranceBusinessNo: record.insuranceBusinessNo,
            insuranceBusinessNoTypeCode: record.insuranceBusinessNoTypeCode,
            insuranceBusinessNoTypeName: record.insuranceBusinessNoTypeName,
            platErrorReason: record.platErrorReason,
            thirdPartyPlatformInterfaceNo: record.thirdPartyPlatformInterfaceNo,
            thirdPartyPlatformInterfaceName:
              record.thirdPartyPlatformInterfaceName,
            // thirdPartyPlatformName: record.thirdPartyPlatformName,
            updatedDate: record.updatedDate,
            needDocWindFlg: record.needDocWindFlg,
            needCollPayFlag: record.needCollPayFlag || false, // 支付接口需要展示详情入口
            needBlockOfLand: record.needBlockOfLand, // 按原始地块上传
            noNeedToDeal: record.noNeedToDeal || false, // 无需处理按钮
            idEvntAsynchronousTaskHandle: record.idEvntAsynchronousTaskHandle,
            errorCategoryName: record.errorCategoryName || '',
            failureTypeSubName: record.failureTypeSubName || '',
            failureTypeName: record.failureTypeName || '',
            handleProposalName: record.handleProposalName || '',
          }));
          pagination.total = total;
          pagination.current = current;
          pagination.pageSize = size;
        } else {
          dataSource.value = [];
          pagination.total = 0;
          pagination.current = 1;
          pagination.pageSize = 10;
        }
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

// 是否补传
const isSupplementary = ref(false);
const submit = () => {
  // 补传不重置分页，其他的需要
  if (isSupplementary.value) {
    isSupplementary.value = false;
  } else {
    pagination.current = 1;
  }
  clearSelect();
  refresh();
};
// 页面初始化标识
const initFlag = ref(false);

const { pagination } = usePagination(refresh, ['200', '400', '800', '1000', '2000', '3000', '5000']);

// 补传
const supplementaryFn = (idString: string) => {
  const confirmBack = () => {
    batchSupplementaryReq
      .fetchData({ pkIdList: idString })
      .then((res) => {
        if (res?.code === SUCCESS_CODE) {
          message.success('补传成功');
          isSupplementary.value = true;
          submit();
        } else {
          message.error(res?.msg || '');
          clearSelect();
        }
      })
      .finally(() => {
        batchSupplementaryLoading.value = false;
      });
  };

  if (modelRef.dockStatusCode === '2') {
    Modal.confirm({
      title: '温馨提示',
      content: '已上传的单无需重复上传，除非修正上传，请确认是否需要修正上传！',
      okText: '确认',
      cancelText: '取消',
      centered: true,
      class: 'bell-bg',
      onOk() {
        confirmBack();
      },
    });
  } else {
    confirmBack();
  }
};

// 批量补传
const batchSupplementaryLoading = ref(false);
const batchSupplementaryFn = () => {
  const infos = listTable.value.vTableInstance.getCheckboxState('isCheck') || [];
  const length = infos.length;
  const idList = [];
  if (length) {
    batchSupplementaryLoading.value = true;
    for (let i = 0; i < length; i++) {
      if (infos[i]) {
        idList.push(dataSource.value[i].idEvntUpldThrdPtyPlatFinalResult);
      }
    }
    supplementaryFn(idList.join(','));
  } else {
    message.warning('请选择至少一条记录操作');
  }
};

// 导出
const batchExportLoading = ref(false);
const batchExportFn = async () => {
  batchExportLoading.value = true;
  const params: GetResultParams = {
    belongBusinessDepartmentNo: modelRef.belongBusinessDepartmentNo || '',
    dockStatusCode: modelRef.dockStatusCode,
    errorCategoryNo: modelRef.errorCategoryNo || '',
    insuranceBusinessNo: modelRef.insuranceBusinessNo,
    insuranceBusinessNoTypeCode: modelRef.insuranceBusinessNoTypeCode || '',
    thirdPartyPlatformInterfaceNo: modelRef.thirdPartyPlatformInterfaceNo || '',
    thirdPartyPlatformNo: modelRef.thirdPartyPlatformNo,
    updatedDateEnd: modelRef.dateTime?.[1] ?? '',
    updatedDateStart: modelRef.dateTime?.[0] ?? '',
    failureType: modelRef.failureType,
    failureTypeSubdivision: modelRef.failureTypeSubdivision,
    handleProposal: modelRef.handleProposal,
  };
  await $postOnClient(
    '/gateway/icore-agr-an.compliance/reportResult/downloadResultList',
    params,
    {
      async onResponse({ response }) {
        let fileName = '';
        const contentDisposition = response.headers.get('Content-Disposition');
        const fileData = response._data;
        const fileType
          = response.headers.get('Content-Type') || 'application/octet-stream';
        if (contentDisposition) {
          const match = contentDisposition.match(/filename=(.+)/);
          if (match) {
            fileName = decodeURI(match[1]);
          }
        }
        downloadBlob(fileData, fileName, fileType);
        batchExportLoading.value = false;
      },
    },
  );
};

// 无需上传
// const unUploadFn = (pkId: string) => {
//   batchUnUpload({ pkIdList: pkId }).then((res) => {
//     if (res.code === SUCCESS_CODE) {
//       message.success('无需上传操作成功');
//       onSearch();
//     } else {
//       message.error(res.msg);
//     }
//   });
// };

// 批量无需上传
// const batchUnUploadFn = () => {
//   const keyList = selectRows.value.map((item) => item.idEvntUpldThrdPtyPlatFinalResult)?.join(',');
//   unUploadFn(keyList);
// };

watch(
  () => modelRef.belongBusinessDepartmentNo,
  async () => {
    platformList.value = [];
    modelRef.thirdPartyPlatformNo = '';
    if (modelRef.belongBusinessDepartmentNo) {
      getPlatformCodeNameListReq
        .fetchData({
          belongBusinessDepartmentNo: modelRef.belongBusinessDepartmentNo,
          headDeptFlag: true,
        })
        .then((res) => {
          const { code, data } = res || {};
          if (code === SUCCESS_CODE) {
            platformList.value = data || [];
            if (Array.isArray(data) && data[0]) {
              modelRef.thirdPartyPlatformNo = data[0].thirdPartyPlatformNo;
            }
          }
        });
    }
  },
  {
    immediate: true,
  },
);

watchEffect(() => {
  interfaceList.value = [];
  modelRef.thirdPartyPlatformInterfaceNo = undefined;
  errorList.value = [];
  modelRef.errorCategoryNo = undefined;
  if (modelRef.thirdPartyPlatformNo) {
    const currentPlatform = platformList.value.find(
      item => item.thirdPartyPlatformNo === modelRef.thirdPartyPlatformNo,
    );
    interFaceListInterfaceCodeNameReq
      .fetchData({
        idParmThrdPtyPlatBase: currentPlatform?.idParmThrdPtyPlatBase || '',
      })
      .then((res) => {
        const { code, data } = res || {};
        if (code === SUCCESS_CODE) {
          interfaceList.value = data || [];
        }
      });
  }
});

watchEffect(() => {
  // 初始化一次
  if (modelRef.thirdPartyPlatformNo && !initFlag.value) {
    submit();
    initFlag.value = true;
  }
});

// const columns = [
//   {
//     title: '序号',
//     key: 'order',
//     dataIndex: 'order',
//   },
//   {
//     title: '出单机构',
//     key: 'deptName',
//     dataIndex: 'deptName',
//   },
//   {
//     title: '单证号',
//     key: 'insuranceBusinessNo',
//     dataIndex: 'insuranceBusinessNo',
//   },
//   {
//     title: '接口名称',
//     key: 'thirdPartyPlatformInterfaceName',
//     dataIndex: 'thirdPartyPlatformInterfaceName',
//   },
//   {
//     title: '状态',
//     key: 'dockStatusName',
//     dataIndex: 'dockStatusName',
//   },
//   {
//     title: '失败类型',
//     key: 'failureTypeName',
//     dataIndex: 'failureTypeName',
//   },
//   {
//     title: '失败类型细分',
//     key: 'failureTypeSubName',
//     dataIndex: 'failureTypeSubName',
//   },
//   {
//     title: '处理方案',
//     key: 'handleProposalName',
//     dataIndex: 'handleProposalName',
//   },
//   {
//     title: '错误描述',
//     key: 'platErrorReason',
//     dataIndex: 'platErrorReason',
//   },
//   {
//     title: '响应时间',
//     key: 'updatedDate',
//     dataIndex: 'updatedDate',
//   },
//   // {
//   //   title: '平台名称',
//   //   key: 'thirdPartyPlatformName',
//   //   dataIndex: 'thirdPartyPlatformName',
//   // },
//   {
//     title: '单证类型',
//     key: 'insuranceBusinessNoTypeName',
//     dataIndex: 'insuranceBusinessNoTypeName',
//   },
//   {
//     title: '操作',
//     fixed: 'right',
//     key: 'operation',
//     dataIndex: 'operation',
//   },
// ];

const openDrawer = ref(false);
// 打开日志抽屉
const openLogDrawer = (
  insuranceBusinessNo: string,
  insuranceBusinessNoTypeCode: string,
  thirdPartyPlatformInterfaceNo: string,
) => {
  selectInsuranceBusinessNo.value = insuranceBusinessNo;
  selectInsuranceBusinessNoTypeCode.value = insuranceBusinessNoTypeCode;
  selectThirdPartyPlatformInterfaceNo.value = thirdPartyPlatformInterfaceNo;
  premiumTermNo.value = '';
  openDrawer.value = true;
};

const openLogsDrawer = (
  insuranceBusinessNo: string,
  insuranceBusinessNoTypeCode: string,
  thirdPartyPlatformInterfaceNo: string,
  premiumTerm: string,
) => {
  selectInsuranceBusinessNo.value = insuranceBusinessNo;
  selectInsuranceBusinessNoTypeCode.value = insuranceBusinessNoTypeCode;
  selectThirdPartyPlatformInterfaceNo.value = thirdPartyPlatformInterfaceNo;
  premiumTermNo.value = premiumTerm;
  openDrawer.value = true;
};

// 查看附件列表
const docVisible = ref(false);
const queryDocList = async (
  insuranceBusinessNo: string,
  thirdPartyPlatformInterfaceNo: string,
) => {
  selectInsuranceBusinessNo.value = insuranceBusinessNo;
  selectInsuranceBusinessNoTypeCode.value = '';
  selectThirdPartyPlatformInterfaceNo.value = thirdPartyPlatformInterfaceNo;
  docVisible.value = true;
};

// 查看收付详情
const payListVisible = ref(false);
const queryPayList = (
  insuranceBusinessNo: string,
  thirdPartyPlatformInterfaceNo: string,
  idEvntUpldThrdPtyPlatFinalResult: string,
) => {
  selectInsuranceBusinessNo.value = insuranceBusinessNo;
  selectInsuranceBusinessNoTypeCode.value = '';
  selectThirdPartyPlatformInterfaceNo.value = thirdPartyPlatformInterfaceNo;
  idEvntUpldThrdPtyPlatFinalResultNo.value = idEvntUpldThrdPtyPlatFinalResult;
  payListVisible.value = true;
};

const handleBlockOfLand = (handleId: string) => {
  useOriginalBlockReq.fetchData({ id: handleId }).then((res) => {
    const { code } = res || {};
    if (code === SUCCESS_CODE) {
      message.success('操作成功');
    } else {
      message.error('操作失败');
    }
  });
};

const noNeedToDeal = (idString: string, premiumResultId: string) => {
  batchUnUploadReq
    .fetchData({ id: idString, premiumResultId })
    .then((res) => {
      if (res?.code === SUCCESS_CODE) {
        message.success('处理成功');
        payListVisible.value = false;
        isSupplementary.value = true;
        submit();
      } else {
        message.error(res?.msg || '');
      }
    })
    .finally(() => {
      batchSupplementaryLoading.value = false;
    });
};
type SelectMapsType = {
  selectMaps?: OptionType;
};
type ParameterType = {
  parameterCode: string;
  parameterName: string;
  idParmComnParm: number;
};
type OptionType = {
  failure_type?: ParameterType[];
  handle_proposal?: ParameterType[];
};
// 查询失败类型
const failure_type_list = ref<ParameterType[]>([]); // 失败类型
const handle_proposal_list = ref<ParameterType[]>([]); // 处理方案
try {
  const res = await $post(`${gateWay}${service.administrate}/parmCommonParm/querySelect`, ['failure_type', 'handle_proposal'], { key: 'farmer_breed_select' });
  if (res && res.code === SUCCESS_CODE) {
    const selectMap = (res?.data as SelectMapsType)?.selectMaps as OptionType;
    failure_type_list.value = selectMap?.failure_type || [];
    handle_proposal_list.value = selectMap?.handle_proposal || [];
  } else {
    failure_type_list.value = [];
    handle_proposal_list.value = [];
  }
} catch {
  failure_type_list.value = [];
  handle_proposal_list.value = [];
}
// 查询失败类型细分
const failureList = ref<ParameterType[]>([]); // 失败类型细分
const queryByClassNoAndIdParent = async () => {
  if (!modelRef.failureType) {
    modelRef.failureTypeSubdivision = '';
  }
  const idParentParmComnParm = failure_type_list.value?.filter(item => item.parameterCode === modelRef.failureType)?.[0]?.idParmComnParm;
  try {
    const res = await $postOnClient(`${gateWay}${service.administrate}/parmCommonParm/queryByClassNoAndIdParent`, { parameterClassNo: 'failure_type_subdivision', idParentParmComnParm });
    const { code, data } = res || {};
    if (code === SUCCESS_CODE && Array.isArray(data)) {
      failureList.value = data || [];
      const _index = failureList.value.findIndex(item => item.parameterCode === modelRef.failureTypeSubdivision);
      if (_index === -1) {
        modelRef.failureTypeSubdivision = '';
      }
    }
  } catch (error) {
    console.log(error);
  }
};

const tableOptions = {
  rightFrozenColCount: 1, // 右侧冻结列
  tooltip: {
    isShowOverflowTextTooltip: true,
    overflowTextTooltipDisappearDelay: 0,
  },
  theme: theme,
  hover: { // 与inlineRowBgColor一起使用才有效果
    highlightMode: 'row',
  },
  keyboardOptions: { // 启动复制快捷键功能
    copySelected: true,
  },
  menu: {
    contextMenuItems: ['复制'],
  },
  widthMode: 'standard',
  autoFillWidth: true, // 列数不够，自动撑宽列宽，使得表格占满整个容器
  emptyTip: {
    text: '暂无数据',
    textStyle: {
      color: 'rgba(0,0,0,0.60)',
    },
    icon: {
      width: 86,
      height: 86,
      image: kong,
    },
  },
  // records: []
};

const tableOptionsRecords = ref([]);

watch(dataSource, (val) => {
  tableOptionsRecords.value = val.map((val, index) => { val.order = index + 1; return val; });
});

const checkboxStateChange = () => {
  const infos = listTable.value.vTableInstance.getCheckboxState('isCheck') || [];
  selectLength.value = infos.filter(val => !!val).length;
};

const onDropDownMenuClick = () => {
  console.log('11');
};
</script>

<style lang="less">
@bgColor: #ffffff;
.upload-manage {
  &-head {
    margin: 14px;
    padding: 16px 14px;
    background: @bgColor;
    border-radius: 6px;
    overflow: hidden;
    .expand-area {
      cursor: pointer;
      display: flex;
      align-items: center;
      height: 32px;
      font-size: 12px;
      margin-left: 24px;
      color: #262626;
    }
  }
  &-content {
    margin: 14px;
    padding: 0 16px;
    background: @bgColor;
    border-radius: 6px;
    &-title {
      height: 64px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
