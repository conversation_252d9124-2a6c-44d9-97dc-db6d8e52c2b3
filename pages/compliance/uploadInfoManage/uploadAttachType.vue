<template>
  <a-modal :open="visible" title="查看影像列表" :width="pxToRem(900)" @cancel="handleOk">
    <a-table
      :data-source="dataSource"
      :columns="columns"
      :loading="loading"
      :pagination="false"
      :bordered="false"
      :row-class-name="(_record: unknown, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
      :scroll="{ x: 'max-content' }"
    >
      <template #bodyCell="{ column, text }">
        <template v-if="column.dataIndex === 'uploadPath'">
          <a-popover>
            <template #title>
              <div class="flex justify-between">
                <span>{{ column.title }}</span>
                <div style="cursor: pointer" class="flex items-center" @click="copyText(text)">
                  <VueIcon :icon="IconFuzhiFont" />
                  <span class="text-[12px]" style="font-weight: 400">复制</span>
                </div>
              </div>
            </template>
            <template #content>
              <div class="max-w-[180px] break-all">{{ text }}</div>
            </template>
            <div class="max-w-[180px] truncate">{{ text }}</div>
          </a-popover>
        </template>
        <template v-if="column.dataIndex === 'errorMessage'">
          <a-popover>
            <template #title>
              <div class="flex justify-between">
                <span>{{ column.title }}</span>
                <div style="cursor: pointer" class="flex items-center" @click="copyText(text)">
                  <VueIcon :icon="IconFuzhiFont" />
                  <span class="text-[12px]" style="font-weight: 400">复制</span>
                </div>
              </div>
            </template>
            <template #content>
              <div class="max-w-[180px] break-all">{{ text }}</div>
            </template>
            <div class="max-w-[180px] truncate">{{ text }}</div>
          </a-popover>
        </template>
      </template>
    </a-table>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleOk">返回</a-button>
      </a-space>
    </template>
    <div class="text-right mt-[6px]">
      <a-pagination v-model:current="pageNum" v-model:page-size="pageSize" size="small" :total="totals" :show-total="total => `总共 ${totals} 条`" show-size-changer show-quick-jumper @change="handleChangePage" />
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { IconFuzhiFont } from '@pafe/icons-icore-agr-an';
import type {
  UploadedDocListRes,
  UploadedDocListParams,
} from '@/apiTypes/compliance';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem, copyText } from '@/utils/tools';
import { usePost } from '@/composables/request';

const getSuccessUploadedDocListReq = await usePost<
  UploadedDocListRes[],
  UploadedDocListParams
>('api/compliance/getSuccessUploadedDocList');

const props = defineProps<{
  visible: boolean;
  insuranceBusinessNo: string;
  thirdPartyPlatformInterfaceNo: string;
}>();
const emit = defineEmits(['update:visible']);

const dataSource = ref<UploadedDocListRes[]>([]);
const loading = ref(false);
const totals = ref<number>(0);
const pageSize = ref<number>(20);
const pageNum = ref<number>(1);

const fetchData = () => {
  dataSource.value = [];
  loading.value = true;
  getSuccessUploadedDocListReq
    .fetchData({
      insuranceBusinessNo: props.insuranceBusinessNo,
      thirdPartyPlatformInterfaceNo: props.thirdPartyPlatformInterfaceNo,
      pageSize: pageSize.value,
      pageNum: pageNum.value,
    })
    .then((res) => {
      const { code, data } = res || {};
      if (code === SUCCESS_CODE && Array.isArray(data?.records)) {
        dataSource.value = data?.records.map(item => ({
          policyNo: item.policyNo,
          documentTypeName: item.documentTypeName,
          uploadPath: item.uploadPath,
          status: item.status,
          errorMessage: item.errorMessage,
        }));
        totals.value = res?.data?.total;
        pageSize.value = res?.data?.size;
        pageNum.value = res?.data?.current;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const handleOk = () => {
  emit('update:visible', false);
};
// 分页
const handleChangePage = (page: number) => {
  pageNum.value = page;
  fetchData();
};

const columns = [
  {
    title: '保单号',
    dataIndex: 'policyNo',
  },
  {
    title: '附件类型',
    dataIndex: 'documentTypeName',
  },
  {
    title: '附件ID',
    dataIndex: 'uploadPath',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '错误描述',
    dataIndex: 'errorMessage',
  },
];

watchEffect(() => {
  if (props.visible) {
    fetchData();
  } else {
    pageNum.value = 1;
  }
});
</script>
