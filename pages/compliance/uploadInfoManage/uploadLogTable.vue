<template>
  <a-table :data-source="dataSource" :columns="columns" :loading="loading" :pagination="pagination" :scroll="{ x: 'max-content' }" :row-class-name="(_record: unknown, index: number) => (index % 2 === 1 ? 'table-striped' : undefined)">
    <template #bodyCell="{ column, record, index }">
      <template v-if="column.dataIndex === 'order'">
        {{ index + 1 }}
      </template>
      <template v-if="column.dataIndex === 'requestText'">
        <template v-if="record.uploadMessage === '1'">
          <AuthButton code="downloadComplianceUploadMessage" type="link" size="small" :loading="record.uploadLoading" @click="handleModalOpen(record as LogReport, 1)"> 查看报文 </AuthButton>
          <!-- <AuthButton v-show="record.uploadLoading" code="downloadComplianceUploadMessage" type="link" size="small"  /> -->
        </template>
      </template>
      <template v-if="column.dataIndex === 'responseText'">
        <template v-if="record.returnMessage === '1'">
          <a-button type="link" size="small" :loading="record.updownLoading" @click="handleModalOpen(record as LogReport, 2)"> 查看报文 </a-button>
        </template>
      </template>
    </template>
  </a-table>
  <a-modal v-model:open="modalOpen" centered title="查看报文" width="55%">
    <div class="max-h-[391px] p-[16px] bg-[#f8f8f8] rounded-[4px] text-[rgba(0,0,0, 0.55)] overflow-y-auto">
      <span v-if="showMessage">{{ showMessage }}</span>
      <a-empty v-else :image="simpleImage" />
    </div>
    <template #footer>
      <a-button type="primary" @click="modalOpen = false">返回</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { Empty } from 'ant-design-vue';
import type { LogReport, GetReportRecordParams } from '@/apiTypes/compliance';
import { usePagination } from '@/composables/usePagination';
import { usePost, useGet, $getOnClient } from '@/composables/request';
import type { TableData } from '@/apiTypes/interface';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { downloadBlob } from '@/utils/tools';
import AuthButton from '@/components/ui/AuthButton.vue';

const { fetchData: fetchList } = await usePost<TableData<LogReport>, GetReportRecordParams>('/api/compliance/getReportRecordList');

const { gateWay, service } = useRuntimeConfig().public;

const { fetchData: fetchReport } = await useGet<{ message: string }>(`${gateWay}${service.compliance}/reportResult/getReportRecordMessage`);

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const props = defineProps<{
  insuranceBusinessNo: string;
  insuranceBusinessNoTypeCode: string;
  thirdPartyPlatformInterfaceNo: string;
  premiumTerm: string;
}>();

const dataSource = ref<LogReport[]>([]);
const loading = ref(false);

const fetchData = () => {
  loading.value = true;
  fetchList({
    pageNum: pagination.current!,
    pageSize: pagination.pageSize!,
    insuranceBusinessNo: props.insuranceBusinessNo,
    insuranceBusinessNoTypeCode: props.insuranceBusinessNoTypeCode,
    thirdPartyPlatformInterfaceNo: props.thirdPartyPlatformInterfaceNo,
    premiumFinalId: props.premiumTerm || '',
  })
    .then((res) => {
      const { data } = res || {};
      dataSource.value =
        data?.records?.map((record) => ({
          insuranceBusinessNo: record.insuranceBusinessNo,
          insuranceBusinessNoTypeCode: record.insuranceBusinessNoTypeCode,
          requestTime: record.requestTime,
          returnMessage: record.returnMessage,
          returnStatusName: record.returnStatusName,
          updatedDate: record.updatedDate,
          uploadMessage: record.uploadMessage,
          idEvntUpldThrdPtyPlatRec: record.idEvntUpldThrdPtyPlatRec || '',
          ifDownUploadMessage: record.ifDownUploadMessage || false,
          uploadLoading: false,
          updownLoading: false,
        })) || [];
      pagination.total = data?.total;
      // pagination.pages = data.pages;
    })
    .finally(() => {
      loading.value = false;
    });
};

const { pagination } = usePagination(fetchData);

const showMessage = ref('');

const columns = [
  {
    title: '序号',
    dataIndex: 'order',
    width: '10%',
  },
  {
    title: '上报时间',
    dataIndex: 'requestTime',
  },
  {
    title: '响应时间',
    dataIndex: 'updatedDate',
  },
  {
    title: '状态',
    dataIndex: 'returnStatusName',
  },
  {
    title: '请求报文',
    dataIndex: 'requestText',
  },
  {
    title: '返回报文',
    dataIndex: 'responseText',
  },
];

const modalOpen = ref(false);

const handleModalOpen = async (record: LogReport, messageType: number) => {
  if (messageType === 1) {
    record.uploadLoading = true;
  } else {
    record.updownLoading = true;
  }
  if (record.ifDownUploadMessage && messageType === 1) {
    // 仅支持下载
    try {
      await $getOnClient(
        `${gateWay}${service.compliance}/reportResult/downloadUploadMessage`,
        { idEvntUpldThrdPtyPlatRec: record.idEvntUpldThrdPtyPlatRec },
        {
          async onResponse({ response }) {
            let fileName = '';
            const contentDisposition = response.headers.get('Content-Disposition');
            const fileData = response._data;
            const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
            if (contentDisposition) {
              const match = contentDisposition.match(/filename=(.+)/);
              if (match) {
                fileName = decodeURI(match[1]);
              }
            }
            downloadBlob(fileData, fileName, fileType);
          },
        },
      );
      record.uploadLoading = false;
    } catch {
      record.uploadLoading = false;
    }
  } else {
    try {
      const { code, data, msg } = (await fetchReport({ idEvntUpldThrdPtyPlatRec: record.idEvntUpldThrdPtyPlatRec, messageType })) || {};
      if (messageType === 1) {
        record.uploadLoading = false;
      } else {
        record.updownLoading = false;
      }
      if (code === SUCCESS_CODE) {
        showMessage.value = data?.message || '';
        modalOpen.value = true;
      } else {
        message.error(msg || '请求有误，请稍后重试');
      }
    } catch {
      if (messageType === 1) {
        record.uploadLoading = false;
      } else {
        record.updownLoading = false;
      }
    }
  }
};

onMounted(() => {
  fetchData();
});
</script>
