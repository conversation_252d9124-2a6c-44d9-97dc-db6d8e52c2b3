<template>
  <a-modal v-model:open="visible" wrap-class-name="look-model-component" title="查看修改历史" :width="pxToRem(697)" @ok="handleOk">
    <div class="mb-[13px]">生效配置</div>
    <a-table :data-source="dataSource" :columns="columns" :loading="loading" :pagination="false" :scroll="{ x: 'max-content' }" :row-class-name="(_record: unknown, index: number) => (index % 2 === 1 ? 'table-striped' : null)">
      <template #bodyCell="{ column, index }">
        <template v-if="column.dataIndex === 'order'">{{ index + 1 }}</template>
      </template>
    </a-table>
    <div class="mt-[30px] mb-[16px]">修改记录</div>
    <div class="pl-[16px]">
      <a-timeline>
        <a-timeline-item v-for="(v, i) in historyData" :key="i" :color="v.active ? '#19DB85' : '#D4D6D9'">
          <div class="text-[14px]">{{ v.updateTime }}</div>
          <div class="text-[12px] text-[#0000008c]">{{ v.updateRecord }}</div>
        </a-timeline-item>
      </a-timeline>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleOk">返回</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import type { MappingConfigHisType, MappingConfigChangeHisType, QueryMapingConfigHisParams } from '@/apiTypes/compliance';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import { $postOnClient } from '@/composables/request';

const dataSource = ref<MappingConfigHisType[]>([]);
const loading = ref(false);

onMounted(() => {
  $postOnClient('/gateway/icore-agr-an.compliance/pageConfig/platform/listPlatformCodeName', {
    belongBusinessDepartmentNo: '2',
  })
    .then(() => {
      // const { code, data } = res;
    }).catch(() => {
    });
});

const fetchData = () => {
  loading.value = true;
  dataSource.value = [];
  $postOnClient<MappingConfigHisType, QueryMapingConfigHisParams>('/api/compliance/queryMapingConfigHis', {
    belongBusinessDepartmentNo: props.belongBusinessDepartmentNo,
    parameterClassNo: props.parameterClassNo,
    thirdPartyPlatformNo: props.thirdPartyPlatformNo,
  })
    .then((res) => {
      const { code, data } = res || {};
      if (code === SUCCESS_CODE && Array.isArray(data)) {
        dataSource.value = data.map(item => ({
          belongBusinessDepartmentName: item.belongBusinessDepartmentName,
          thirdPartyPlatformName: item.thirdPartyPlatformName,
          parameterClassNo: item.parameterClassNo,
          pinganDataCodeName: item.pinganDataCodeName,
          platformDataCodeName: item.platformDataCodeName,
          effectiveTime: item.effectiveTime,
        }));
      }
    }).finally(() => {
      loading.value = false;
    });
};

const props = withDefaults(defineProps<{
  belongBusinessDepartmentNo: string;
  parameterClassNo: string;
  thirdPartyPlatformNo: string;
}>(), {
  belongBusinessDepartmentNo: '',
  parameterClassNo: '',
  thirdPartyPlatformNo: '',
});

const historyData = ref<MappingConfigChangeHisType[]>([]);

const getHistoryData = () => {
  historyData.value = [];
  $postOnClient<MappingConfigChangeHisType[], QueryMapingConfigHisParams>('/api/compliance/queryMapingConfigChangeHis', {
    belongBusinessDepartmentNo: props.belongBusinessDepartmentNo,
    parameterClassNo: props.parameterClassNo,
    thirdPartyPlatformNo: props.thirdPartyPlatformNo,
  }).then((res) => {
    const { code, data } = res || {};
    if (code === SUCCESS_CODE && Array.isArray(data)) {
      historyData.value = data.map(item => ({
        active: item.active,
        updateRecord: item.updateRecord,
        updateTime: item.updateTime,
      }));
    }
  });
};

const visible = ref(false);

const handleOk = (): void => {
  visible.value = false;
};

const show = (): void => {
  visible.value = true;
};

defineExpose({
  show,
});

const columns = [
  {
    title: '序号',
    dataIndex: 'order',
  },
  {
    title: '归属机构',
    dataIndex: 'belongBusinessDepartmentName',
  },
  {
    title: '平台名称',
    dataIndex: 'thirdPartyPlatformName',
  },
  {
    title: '代码类型',
    dataIndex: 'parameterClassNo',
  },
  {
    title: '我司类型名称',
    dataIndex: 'pinganDataCodeName',
  },
  // 平台
  {
    title: '平台类型名称',
    dataIndex: 'platformDataCodeName',
  },
  {
    title: '生效日期',
    dataIndex: 'effectiveTime',
  },
];

watch(visible, (newV: boolean) => {
  if (newV) {
    // 获取列表
    fetchData();
    // 获取修改记录
    getHistoryData();
  }
});
</script>

<style lang="less">
</style>
