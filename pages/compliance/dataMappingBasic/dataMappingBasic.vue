<template>
  <div class="data-mapping">
    <div class="data-mapping-head">
      <a-form :colon="false">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="机构" name="belongBusinessDepartmentNo" v-bind="validateInfos.belongBusinessDepartmentNo">
              <DeptListSelect :dept-code="modelRef.belongBusinessDepartmentNo" @change-dept-code="changeDeptCode" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="平台名称" name="thirdPartyPlatformNo" v-bind="validateInfos.thirdPartyPlatformNo">
              <a-select
                v-model:value="modelRef['thirdPartyPlatformNo']"
                placeholder="请选择"
                :allow-clear="true"
                :show-search="true"
                :options="platformNameList"
                :filter-option="(val: string, options: PlatformCodeName) => options.platformCodeName.includes(val)"
                :field-names="{ label: 'platformCodeName', value: 'thirdPartyPlatformNo' }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="类型名称" name="parameterClassNo" v-bind="validateInfos.parameterClassNo">
              <a-select v-model:value="modelRef['parameterClassNo']" placeholder="请选择" :allow-clear="true" :show-search="true" :options="typeOptions" :field-names="{ label: 'parameterClassName', value: 'parameterClassNo' }" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex justify-center">
        <a-button class="mr-[12px]" @click="reset">重置</a-button>
        <a-button type="primary" ghost @click="submit">查询</a-button>
      </div>
    </div>
    <div class="data-mapping-content">
      <div class="data-mapping-content-title">
        基础数据映射清单
      </div>
      <a-table
        :data-source="dataSource"
        :columns="columns"
        :loading="loading"
        :bordered="false"
        :scroll="{ y: '50vh' }"
        :row-class-name="(_record: unknown, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
        :pagination="pagination"
      >
        <template #bodyCell="{ column, record, index, text }">
          <template v-if="column.dataIndex === 'order'">{{ index + 1 }}</template>
          <template v-if="['parameterClassNo'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] truncate">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.key === 'operation'">
            <a-button type="link" size="small" @click="handleCtrl(record as MappingConfigColumn)">明细映射配置</a-button>
            <a-button type="link" size="small" @click="handleLook(record.belongBusinessDepartmentNo, record.parameterClassNo, record.thirdPartyPlatformNo)">查看修改历史</a-button>
          </template>
        </template>
      </a-table>
    </div>
    <LookModel ref="lookRef" :belong-business-department-no="selectBelongBusinessDepartmentNo" :parameter-class-no="selectParameterClassNo" :third-party-platform-no="selectThirdPartyPlatformNo" />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { Form } from 'ant-design-vue';
import LookModel from './lookModel.vue';
import type { MappingConfigColumn, PlatformBaseParams, ParameterType, PlatformCodeName } from '@/apiTypes/compliance';
import type { TableData } from '@/apiTypes/interface';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePagination } from '@/composables/usePagination';
import { pxToRem } from '@/utils/tools';
import { usePost, useGet } from '@/composables/request';
import DeptListSelect from '@/pages/compliance/components/DeptListSelect.vue';

const platformCodeNameReq = await usePost('/api/compliance/listPlatformCodeName');
const getMappingConfigListReq = await usePost<TableData<MappingConfigColumn>>('/api/compliance/queryMapingConfigList');
const getCommonParameterTypeReq = await useGet('/api/compliance/queryCommonParameterType');

const router = useRouter();

const columns = [
  {
    title: '序号',
    dataIndex: 'order',
    width: pxToRem(60),
  },
  {
    title: '归属机构',
    key: 'belongBusinessDepartmentName',
    dataIndex: 'belongBusinessDepartmentName',
  },
  {
    title: '平台名称',
    key: 'thirdPartyPlatformName',
    dataIndex: 'thirdPartyPlatformName',
  },
  {
    title: '代码类型',
    key: 'parameterClassNo',
    dataIndex: 'parameterClassNo',
  },
  {
    title: '类型名称',
    key: 'parameterClassName',
    dataIndex: 'parameterClassName',
    width: pxToRem(180),
  },
  {
    title: '生效日期',
    key: 'effectiveTime',
    dataIndex: 'effectiveTime',
  },
  {
    title: '失效日期',
    key: 'invalidTime',
    dataIndex: 'invalidTime',
  },
  {
    title: '操作',
    width: pxToRem(230),
    key: 'operation',
    fixed: 'right',
  },
];

const lookRef = ref();
const selectBelongBusinessDepartmentNo = ref('');
const selectParameterClassNo = ref('');
const selectThirdPartyPlatformNo = ref('');
// 查看修改历史
const handleLook = async (belongBusinessDepartmentNo: string, parameterClassNo: string, thirdPartyPlatformNo: string) => {
  selectBelongBusinessDepartmentNo.value = belongBusinessDepartmentNo;
  selectParameterClassNo.value = parameterClassNo;
  selectThirdPartyPlatformNo.value = thirdPartyPlatformNo;
  lookRef.value.show();
};

// 点击映射配置 跳转明细映射配置页面
const handleCtrl = async (record: MappingConfigColumn) => {
  router.push({
    name: 'dataMappingDetail',
    query: {
      belongBusinessDepartmentNo: record?.belongBusinessDepartmentNo,
      parameterClassNo: record?.parameterClassNo,
      thirdPartyPlatformNo: record?.thirdPartyPlatformNo,
      parameterClassName: record?.parameterClassName,
    },
  });
};

const modelRef: Partial<PlatformBaseParams> = reactive({
  belongBusinessDepartmentNo: undefined,
  parameterClassNo: undefined,
  thirdPartyPlatformNo: undefined,
});

// 可选的表单校验对象
const rulesRef = reactive({
  belongBusinessDepartmentNo: [
    {
      required: true,
      message: '请选择机构',
    },
  ],
  thirdPartyPlatformNo: [
    {
      required: true,
      message: '请选择平台名称',
    },
  ],
});

const changeDeptCode = (val: string) => {
  modelRef.belongBusinessDepartmentNo = val;
};

const { validate, validateInfos, resetFields } = Form.useForm(modelRef, rulesRef);

const reset = () => {
  resetFields();
};

const submit = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
};

const dataSource = ref<MappingConfigColumn[]>([]);
const loading = ref(false);
const refresh = () => {
  validate().then(() => {
    loading.value = true;
    getMappingConfigListReq.fetchData({
      belongBusinessDepartmentNo: modelRef.belongBusinessDepartmentNo,
      parameterClassNo: modelRef.parameterClassNo || '',
      thirdPartyPlatformNo: modelRef.thirdPartyPlatformNo,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    }).then((res) => {
      if (res?.code === SUCCESS_CODE) {
        const { records = [], total = 0, current = 1, size = 10 } = res.data;
        dataSource.value = records.map(record => ({
          belongBusinessDepartmentName: record.belongBusinessDepartmentName,
          belongBusinessDepartmentNo: record.belongBusinessDepartmentNo,
          effectiveTime: record.effectiveTime,
          invalidTime: record.invalidTime,
          parameterClassName: record.parameterClassName,
          parameterClassNo: record.parameterClassNo,
          thirdPartyPlatformName: record.thirdPartyPlatformName,
          thirdPartyPlatformNo: record.thirdPartyPlatformNo,
          updatedDate: record.updatedDate,
        }));
        pagination.total = total;
        pagination.current = current;
        pagination.pageSize = size;
        // pagination.pages = pages;
      } else {
        dataSource.value = [];
        pagination.total = 0;
        pagination.current = 1;
        pagination.pageSize = 10;
        // pagination.pages = 0;
      }
    }).finally(() => {
      loading.value = false;
    });
  });
};

const { pagination } = usePagination(refresh);

// 类型名称
const typeOptions = ref<ParameterType[]>([]);
// 平台名称
const platformNameList = ref<PlatformCodeName[]>([]);

// 页面初始化标识
const initFlag = ref(false);

// 查询平台名称列表
// watchEffect(async () => {
//   platformNameList.value = [];
//   modelRef.thirdPartyPlatformNo = undefined;
//   if (modelRef.belongBusinessDepartmentNo) {
//     const { code, data } = await getPlatformCodeNameList({ belongBusinessDepartmentNo: modelRef.belongBusinessDepartmentNo });
//     if (code === SUCCESS_CODE && Array.isArray(data)) {
//       platformNameList.value = data.map((item) => ({
//         idParmThrdPtyPlatBase: item.idParmThrdPtyPlatBase,
//         platformCodeName: item.platformCodeName,
//         thirdPartyPlatformNo: item.thirdPartyPlatformNo,
//       }));
//       if (platformNameList.value?.[0]) {
//         modelRef.thirdPartyPlatformNo = platformNameList.value[0].thirdPartyPlatformNo;
//       }
//     }
//   }
// });
watch(() => modelRef.belongBusinessDepartmentNo, async (val) => {
  platformNameList.value = [];
  modelRef.thirdPartyPlatformNo = undefined;
  if (val) {
    const { code, data } = await platformCodeNameReq.fetchData({ belongBusinessDepartmentNo: val }) || {};
    if (code === SUCCESS_CODE && Array.isArray(data)) {
      platformNameList.value = data.map(item => ({
        idParmThrdPtyPlatBase: item.idParmThrdPtyPlatBase,
        platformCodeName: item.platformCodeName,
        thirdPartyPlatformNo: item.thirdPartyPlatformNo,
      }));
      if (platformNameList.value?.[0]) {
        modelRef.thirdPartyPlatformNo = platformNameList.value[0].thirdPartyPlatformNo;
      }
    }
  }
}, {
  immediate: true,
});

// 查询类别名称列表
// watchEffect(async () => {
//   typeOptions.value = [];
//   modelRef.parameterClassNo = undefined;
//   if (modelRef.thirdPartyPlatformNo) {
//     const { code, data } = await getCommonParameterTypeReq.fetchData({ thirdPartyPlatformNo: modelRef.thirdPartyPlatformNo }) || {};
//     if (code === SUCCESS_CODE && Array.isArray(data)) {
//       typeOptions.value = data.map((item) => ({
//         parameterClassNo: item.parameterClassNo,
//         parameterClassName: item.parameterClassName,
//       }));
//     }
//   }
// });

watch(() => modelRef.thirdPartyPlatformNo, async () => {
  typeOptions.value = [];
  modelRef.parameterClassNo = undefined;
  if (modelRef.thirdPartyPlatformNo) {
    const { code, data } = await getCommonParameterTypeReq.fetchData({ thirdPartyPlatformNo: modelRef.thirdPartyPlatformNo }) || {};
    if (code === SUCCESS_CODE && Array.isArray(data)) {
      typeOptions.value = data.map(item => ({
        parameterClassNo: item.parameterClassNo,
        parameterClassName: item.parameterClassName,
      }));
    }
  }
}, {
  immediate: true,
});

watchEffect(() => {
  if (modelRef.belongBusinessDepartmentNo && modelRef.thirdPartyPlatformNo && !initFlag.value) {
    submit();
    initFlag.value = true;
  }
});
</script>

<style lang="less" scoped>
@bgColor: #ffffff;

.data-mapping {
  &-head {
    margin: 14px;
    padding: 16px 14px;
    background: @bgColor;
    border-radius: 6px;
    overflow: hidden;
    .expand-area {
      cursor: pointer;
      display: flex;
      align-items: center;
      height: 32px;
      font-size: 12px;
      margin-left: 24px;
      color: #262626;
    }
  }
 &-content {
    margin: 14px;
    padding: 0 16px;
    background: @bgColor;
    border-radius: 6px;
    &-title {
      height: 64px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: rgba(0,0,0,0.8);
    }
  }
}
</style>
