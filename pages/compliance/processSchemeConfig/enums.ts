import { IconInformationFont, IconFenxijichuliFont } from '@pafe/icons-icore-agr-an';
import type { TableProps } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { computed } from 'vue';
import { isWordDigitValidator } from '@/utils/validators';
import { pxToRem } from '@/utils/tools';
import type { AddErrorHandleType } from '@/apiTypes/compliance';

export const columns: TableProps['columns'] = [
  {
    title: '序号',
    dataIndex: 'order',
    width: pxToRem(60) },
  {
    title: '归属机构',
    width: pxToRem(120),
    key: 'belongBusinessDepartmentName',
    dataIndex: 'belongBusinessDepartmentName',
  },
  {
    title: '平台名称',
    width: pxToRem(120),
    key: 'thirdPartyPlatformName',
    dataIndex: 'thirdPartyPlatformName',
  },
  {
    title: '配置方式',
    key: 'ruleMatchMethod',
    dataIndex: 'ruleMatchMethod',
    width: pxToRem(100),
  },
  {
    title: '失败类型',
    key: 'failureTypeName',
    dataIndex: 'failureTypeName',
    width: pxToRem(120),
  },
  {
    title: '失败类型细分',
    key: 'failureTypeSubName',
    dataIndex: 'failureTypeSubName',
    width: pxToRem(130),
  },
  {
    title: '处理方案',
    key: 'handleProposalName',
    dataIndex: 'handleProposalName',
    width: pxToRem(180),
  },
  {
    title: '错误代码/失败描述',
    key: 'ruleMatchContent',
    dataIndex: 'ruleMatchContent',
    width: pxToRem(180),
  },
  {
    title: '是否模糊匹配',
    key: 'fuzzyMatchFlag',
    dataIndex: 'fuzzyMatchFlag',
    width: pxToRem(130),
  },
  {
    title: '更新时间',
    width: pxToRem(180),
    key: 'updatedDate',
    dataIndex: 'updatedDate',
  },
  {
    title: '操作',
    width: pxToRem(150),
    key: 'operation',
    fixed: 'right',
  },
];

export const lookLayout = computed(() => [
  {
    title: '基础信息',
    col: 12,
    gutter: 28,
    icon: IconInformationFont,
    allowClear: true,
    itemList: [
      {
        label: '归属机构',
        value: 'belongBusinessDepartmentName',
      },
      {
        label: '平台名称',
        value: 'thirdPartyPlatformName',
      },
      {
        label: '配置方式',
        value: 'ruleMatchMethod',
      },
      {
        label: '错误代码/失败描述',
        value: 'ruleMatchContent',
      },
    ],
  }, {
    title: '分析及处理',
    col: 24,
    gutter: 28,
    allowClear: true,
    icon: IconFenxijichuliFont,
    itemList: [
      {
        label: '失败类型',
        value: 'failureTypeName',
      },
      {
        label: '失败类型细分',
        value: 'failureTypeSubName',
      },
      {
        label: '处理方案',
        value: 'handleProposalName',
      },
      {
        label: '备注',
        value: 'remark',
      },
    ],
  }]);

export const editModelRef = reactive<AddErrorHandleType>({
  idParmThrdPtyPlatErrorHandleProposal: '',
  thirdPartyPlatformNo: '',
  errorNoComment: '',
  errorCategoryNo: '',
  errorCategoryNoName: '',
  itAnalysisResult: '',
  errorNo: '',
  handleProposal: '',
  remark: '',
});
export const editRules = ref<Record<string, Rule[]>>({
  belongBusinessDepartmentNo: [
    { required: true, message: '请选择归属机构', trigger: 'blur' },
  ],
  thirdPartyPlatformNo: [
    { required: true, message: '请选择平台名称', trigger: 'blur' },
    { message: '平台名称长度最多50位', max: 50, trigger: 'change' },
    { message: '不能输入汉字、“-_/”以外的特殊字符', trigger: 'change', validator: isWordDigitValidator },
  ],
  errorNo: [
    { required: true, message: '请输入错误代码', trigger: 'blur' },
    { message: '错误代码长度最多8位', max: 8, trigger: 'blur' },
    { message: '不能输入汉字、“-_/”以外的特殊字符', trigger: 'blur', validator: isWordDigitValidator },
  ],
  errorCategoryNoName: [
    { required: true, message: '请选择错误代码分类', trigger: 'blur' },
    { message: '错误代码分类长度最多16位', max: 16, trigger: 'change' },
    // { message: '不能输入汉字、“-_/”以外的特殊字符', trigger: 'change', validator: isWordDigitValidator },
  ],
  errorNoComment: [
    { required: true, message: '请输入错误代码说明', trigger: 'blur' },
    { message: '错误代码说明长度最多255位', max: 255, trigger: 'blur' },
  ],
  itAnalysisResult: [
    { message: 'IT分析结果长度最多4000位', max: 4000, trigger: 'blur' },
  ],
  handleProposal: [
    { message: '处理方案长度最多4000位', max: 4000, trigger: 'blur' },
  ],
  remark: [
    { message: '备注信息最多500位', max: 500, trigger: 'blur' },
  ],
});
