<template>
  <a-modal
    v-model:open="show"
    wrap-class-name="add-and-edit-model"
    :centered="true"
    :title="innerTitle"
    @on-cancel="handleCancel"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div>
      <a-form ref="formRef" :colon="false">
        <div class="title">
          <VueIcon class="mr-[3px]" :icon="IconInformationFont" />基础信息
        </div>
        <div class="content">
          <a-row :gutter="28">
            <a-col :span="12">
              <a-form-item
                class="labelClassHack-140"
                label="机构"
                name="belongBusinessDepartmentNo"
                v-bind="validateInfos.belongBusinessDepartmentNo"
              >
                <DeptListSelect disabled :dept-code="modelRef.belongBusinessDepartmentNo" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item class="labelClassHack-140" label="平台名称" name="thirdPartyPlatformNo" v-bind="validateInfos.thirdPartyPlatformNo">
                <a-select
                  v-model:value="modelRef['thirdPartyPlatformNo']"
                  disabled
                  placeholder="请选择"
                  :allow-clear="true"
                  :show-search="true"
                  :options="platformList"
                  :filter-option="(val: string, options: PlatformCodeName) => options.platformCodeName.includes(val)"
                  :field-names="{ label: 'platformCodeName', value: 'thirdPartyPlatformNo' }"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item class="labelClassHack-140" label="配置方式" name="ruleMatchMethod" v-bind="validateInfos.ruleMatchMethod">
                <a-select v-model:value="modelRef['ruleMatchMethod']" placeholder="请选择" :allow-clear="true" @change="handleChange">
                  <a-select-option value="1">错误代码</a-select-option>
                  <a-select-option value="2">失败描述</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col v-show="modelRef['ruleMatchMethod'] === '1'" :span="12">
              <a-form-item class="labelClassHack-140" label="错误代码" name="ruleMatchContent" v-bind="validateInfos.ruleMatchContent">
                <a-input v-model:value="modelRef['ruleMatchContent']" allow-clear placeholder="请输入" />
              </a-form-item>
            </a-col>
            <a-col v-show="modelRef['ruleMatchMethod'] === '2'" :span="18">
              <a-form-item class="labelClassHack-140" label="失败描述" name="errorNoComment" v-bind="validateInfos.errorNoComment">
                <a-input v-model:value="modelRef['errorNoComment']" allow-clear placeholder="请输入" />
              </a-form-item>
            </a-col>
            <a-col v-show="modelRef['ruleMatchMethod'] === '2'" :span="4">
              <a-form-item class="labelClassHack-140" label="是否模糊匹配">
                <a-checkbox v-model:checked="checked" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item class="labelClassHack-140" label="重新匹配规则">
                <a-checkbox v-model:checked="refreshChecked" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="title">
          <VueIcon class="mr-[3px]" :icon="IconFenxijichuliFont" />分析及处理
        </div>
        <div class="content">
          <a-row :gutter="28">
            <a-col :span="24">
              <a-form-item class="labelClassHack-100" label="失败类型" name="failureType" v-bind="validateInfos.failureType">
                <a-select v-model:value="modelRef['failureType']" placeholder="请选择" :show-search="true" :options="failureTypeList" :filter-option="filterOption" :field-names="{ label: 'parameterName', value: 'parameterCode' }" @change="queryByClassNoAndIdParent" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item class="labelClassHack-100" label="失败类型细分" name="failureTypeSubdivision" v-bind="validateInfos.failureTypeSubdivision">
                <a-select v-model:value="modelRef['failureTypeSubdivision']" placeholder="请选择" :disabled="!modelRef.failureType" :allow-clear="true" :show-search="true" :options="failureList" :filter-option="filterOption" :field-names="{ label: 'parameterName', value: 'parameterCode' }" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item class="labelClassHack-100" label="处理方案" name="handleProposal" v-bind="validateInfos.handleProposal">
                <a-select v-model:value="modelRef['handleProposal']" placeholder="请选择" :disabled="!modelRef.failureType" :allow-clear="true" :show-search="true" :options="handleProposalList" :filter-option="filterOption" :field-names="{ label: 'parameterName', value: 'parameterCode' }" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item class="labelClassHack-100" label="备注" name="remark" v-bind="validateInfos.remark">
                <a-input v-model:value="modelRef['remark']" placeholder="请输入" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>

    <template #footer>
      <a-space>
        <slot />
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import type { Rule } from 'ant-design-vue/es/form/interface';
import { IconInformationFont, IconFenxijichuliFont } from '@pafe/icons-icore-agr-an';
import Form from 'ant-design-vue/es/form/Form';
import { defineModel } from 'vue';
import DeptListSelect from '@/pages/compliance/components/DeptListSelect.vue';
import { message } from '@/components/ui/Message';
import type { ApiResult } from '@/utils/service/types';
import { SUCCESS_CODE } from '@/utils/constants';
import type { PlatformCodeName, ErrorBaseType } from '@/apiTypes/compliance';
import { isWordDigitValidator } from '@/utils/validators';

import { usePost, $postOnClient } from '@/composables/request';

const platformCodeNameReq = await usePost('/api/compliance/listPlatformCodeName');
const { gateWay, service } = useRuntimeConfig().public || {};
type ParameterType = {
  parameterCode: string;
  parameterName: string;
  idParmComnParm: number;
};
const props = withDefaults(defineProps<{
  title?: string;
  updateApi: (params: object) => Promise<ApiResult<unknown>>;
  initValue: ErrorBaseType;
  onSearch: () => Promise<void>;
  failureTypeList: ParameterType[];
  handleProposalList: ParameterType[];
}>(), {
  title: '配置',
});
const innerTitle = computed(() => ('修改' + props.title));
const checked = ref<boolean>(false);
const refreshChecked = ref<boolean>(false);

const show = defineModel<boolean>('show', { required: true, default: false });

const changeDeptCode = (val: string) => {
  modelRef.belongBusinessDepartmentNo = val;
};

const modelRef = reactive<{
  belongBusinessDepartmentNo: string | undefined;
  idParmThrdPtyPlatErrorHandleProposal: string;
  thirdPartyPlatformNo: string;
  handleProposal: string;
  remark: string;
  fuzzyMatchFlag: string;
  ruleMatchMethod: string | undefined;
  failureType: string | undefined;
  ruleMatchContent: string;
  failureTypeSubdivision: string | undefined;
  errorNoComment: string;
  idParmErrorHandleProposal: string;
  reMatchRules: string;
}>({
  belongBusinessDepartmentNo: undefined,
  idParmThrdPtyPlatErrorHandleProposal: '',
  thirdPartyPlatformNo: '',
  handleProposal: '',
  remark: '',
  fuzzyMatchFlag: '',
  ruleMatchMethod: undefined,
  failureType: undefined,
  ruleMatchContent: '',
  failureTypeSubdivision: undefined,
  errorNoComment: '',
  idParmErrorHandleProposal: '',
  reMatchRules: '',
});

// 平台名称可选项
const platformList = ref<PlatformCodeName[]>([]);

const editRules = ref<Record<string, Rule[]>>({
  belongBusinessDepartmentNo: [
    { required: true, message: '请选择归属机构' },
  ],
  thirdPartyPlatformNo: [
    { required: true, message: '请选择平台名称', trigger: 'blur' },
    { message: '平台名称长度最多50位', max: 50, trigger: 'change' },
    { message: '不能输入汉字、“-_/”以外的特殊字符', trigger: 'change', validator: isWordDigitValidator },
  ],
  ruleMatchMethod: [
    { required: true, message: '请选择配置方式', trigger: 'change' },
  ],
  failureType: [
    { required: true, message: '请选择失败类型', trigger: 'change' },
  ],
  failureTypeSubdivision: [
    { required: true, message: '请选择失败类型细分', trigger: 'change' },
  ],
  handleProposal: [
    { required: true, message: '请选择处理方案', trigger: 'change' },
  ],
  ruleMatchContent: [
    { required: true, message: '请输入错误代码', trigger: 'change' },
    { message: '错误代码长度最多8位', max: 8, trigger: 'change' },
    { message: '不能输入汉字、“-_/”以外的特殊字符', trigger: 'change', validator: isWordDigitValidator },
  ],
  errorNoComment: [
    { required: true, message: '请输入失败描述', trigger: 'change' },
    { message: '错误代码说明长度最多255位', max: 255, trigger: 'change' },
  ],
  remark: [
    { message: '备注信息最多500位', max: 500, trigger: 'blur' },
  ],
});
// 模糊搜索
const filterOption = (input: string, option: Record<string, string>) => {
  return option.parameterName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const { resetFields, validate, validateInfos } = Form.useForm(modelRef, editRules);

const modelDepartment = ref();

const findDefaultPlateFormCode = async () => {
  await nextTick();
  const defaultValue = props.initValue.thirdPartyPlatformNo;
  let hasFinded = false;
  for (const item of platformList.value) {
    if (item.thirdPartyPlatformNo === defaultValue) {
      modelRef.thirdPartyPlatformNo = item.thirdPartyPlatformNo;
      hasFinded = true;
    }
  }

  if (!hasFinded) {
    modelRef.thirdPartyPlatformNo = platformList.value[0].thirdPartyPlatformNo;
  }
};

const queryPlatformCodeNameList = async () => {
  platformList.value = [];
  modelRef.thirdPartyPlatformNo = '';
  if (modelRef.belongBusinessDepartmentNo) {
    const { code, data } = await platformCodeNameReq.fetchData({ belongBusinessDepartmentNo: modelRef.belongBusinessDepartmentNo }) || {};
    if (code === SUCCESS_CODE && Array.isArray(data)) {
      platformList.value = data.map(item => ({
        idParmThrdPtyPlatBase: item.idParmThrdPtyPlatBase,
        thirdPartyPlatformNo: item.thirdPartyPlatformNo,
        platformCodeName: item.platformCodeName,
      }));
      await findDefaultPlateFormCode();
    }
  }
};
const failureList = ref<ParameterType[]>([]); // 失败类型细分
// 查询失败类型细分
const queryByClassNoAndIdParent = async () => {
  const idParentParmComnParm = props.failureTypeList?.filter(item => item.parameterCode === modelRef.failureType)?.[0]?.idParmComnParm;
  try {
    const res = await $postOnClient(`${gateWay}${service.administrate}/parmCommonParm/queryByClassNoAndIdParent`, { parameterClassNo: 'failure_type_subdivision', idParentParmComnParm });
    const { code, data } = res || {};
    if (code === SUCCESS_CODE && Array.isArray(data)) {
      failureList.value = data || [];
      const _index = failureList.value.findIndex(item => item.parameterCode === modelRef.failureTypeSubdivision);
      if (_index === -1) {
        modelRef.failureTypeSubdivision = '';
      }
    }
  } catch (error) {
    console.log(error);
  }
};

const resetAntherInput = () => {
  modelRef.idParmThrdPtyPlatErrorHandleProposal = props.initValue.idParmThrdPtyPlatErrorHandleProposal;
};

watch(show, (val) => {
  if (val) {
    // 打开了弹窗
    if (modelRef.belongBusinessDepartmentNo !== props.initValue.belongBusinessDepartmentNo) {
      // 机构发生了变化，需要同步更新机构、平台、代码分类
      // 重置机构默认值
      modelRef.belongBusinessDepartmentNo = props.initValue.belongBusinessDepartmentNo || '';
      if (modelDepartment.value) {
        modelDepartment.value.resetInputValue(props.initValue.belongBusinessDepartmentNo || '');
      }
      modelRef.failureType = props.initValue.failureType;
      if (modelRef.failureType) {
        queryByClassNoAndIdParent();
      }
      modelRef.ruleMatchMethod = props.initValue.ruleMatchMethod;
      modelRef.remark = props.initValue.remark || '';
      modelRef.handleProposal = props.initValue.handleProposal || '';
      modelRef.failureTypeSubdivision = props.initValue.failureTypeSubdivision || '';
      if (modelRef.ruleMatchMethod === '2') {
        modelRef.errorNoComment = props.initValue.ruleMatchContent || '';
      } else {
        modelRef.ruleMatchContent = props.initValue.ruleMatchContent || '';
      }
      if (props.initValue.fuzzyMatchFlag === '0') {
        checked.value = false;
      } else {
        checked.value = true;
      }
      refreshChecked.value = false;
    }
    // 重置其他输入框内容
    resetAntherInput();
  } else {
    resetFields();
  }
});

watch(() => modelRef.belongBusinessDepartmentNo, async () => {
  if (show.value) {
    // 机构发生了变化，重新请求数据
    await queryPlatformCodeNameList();
  }
});
const handleChange = () => {
  if (props.initValue.ruleMatchMethod === '1') {
    modelRef.ruleMatchContent = props.initValue.ruleMatchContent || '';
  } else {
    modelRef.errorNoComment = props.initValue.ruleMatchContent || '';
  }
};
const formRef = ref();
const handleOk = (): void => {
  if (modelRef.ruleMatchMethod === '1') {
    modelRef.errorNoComment = modelRef.ruleMatchContent;
  } else {
    modelRef.ruleMatchContent = '1';
  }
  validate()
    .then(() => {
      if (modelRef.ruleMatchMethod === '1') checked.value = false;
      const obj: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(modelRef)) {
        if (typeof value === 'string') {
          obj[key] = value.trim();
        } else {
          obj[key] = value;
        }
      }
      if (obj.ruleMatchMethod === '2') obj.ruleMatchContent = obj.errorNoComment;
      obj.fuzzyMatchFlag = checked.value ? '1' : '0';
      obj.idParmErrorHandleProposal = props.initValue.idParmErrorHandleProposal;
      obj.reMatchRules = refreshChecked.value ? '1' : '0';
      const params = toRaw(obj);
      props.updateApi(params).then((res) => {
        const { code, msg } = res;
        if (SUCCESS_CODE === code) {
          message.success(msg);
          show.value = false;
          // emits('onOk');
          props.onSearch();
        } else {
          message.error(msg);
        }
      });
    })
    .catch((err: Error) => {
      console.log('error', err);
    });
};

const handleCancel = (): void => {
  show.value = false;
};
</script>

<style lang="less">
@import url("../index.less");
</style>
