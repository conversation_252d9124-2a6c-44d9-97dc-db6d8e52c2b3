@bgColor: #f8f8f8;
@color: #333;
@titlebgColor: #F2F3F5;
@whiteBgcolor: #fff;

.ant-modal-root .ant-modal-wrap.add-and-edit-model,
.ant-modal-root .ant-modal-wrap.look-model {
  width: 800px;
  height: 690px;
  margin: auto;
  overflow: hidden;

  .labelClassHack-160 {
    .ant-col.ant-form-item-label {
      width: 160px;
    }
  }

  .labelClassHack-100 {
    .ant-col.ant-form-item-label {
      width: 100px;
    }
  }

  .labelClassHack-80 {
    .ant-col.ant-form-item-label {
      width: 80px;
    }
  }

  .labelClassHack-140 {
    .ant-col.ant-form-item-label {
      width: 140px;
    }
  }

  .ant-modal {
    width: 100% !important;

    .ant-modal-header {
      height: 48px;
      font-size: 16px;
      padding: 0 20px;
      display: flex;
      align-items: center;
      background: @titlebgColor;
      border-radius: 0;
    }

    .ant-modal-close {
      top: 14px;
      right: 20px;

      .ant-modal-close-x {
        font-size: 12px;
      }
    }

    .ant-modal-body {
      max-height: 450px;
      overflow-y: auto;
      padding: 0 20px 20px;
    }

    .ant-modal-content {
      padding: 0;
      border-radius: 0;
    }

    .ant-modal-footer {
      display: flex;
      height: 72px;
      align-items: center;
      justify-content: flex-end;
      padding: 0 20px;
      bottom: 0;
      background: @whiteBgcolor;
    }
  }

  .title {
    color: @color;
    font-size: 14px;
    margin-bottom: 9px;
    margin-top: 15px;
  }

  .content {
    background-color: @bgColor;
    border-radius: 4px;
    padding: 13px;
    margin-bottom: 15px;
  }
}