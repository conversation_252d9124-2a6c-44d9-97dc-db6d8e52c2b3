<template>
  <a-modal v-model:open="visible" wrap-class-name="look-model" :title="title" @ok="handleOk">
    <div>
      <a-form ref="formRef" :model="form" :colon="false" label-align="left">
        <template v-for="item in formLayout" :key="item.title">
          <div class="title">
            <VueIcon v-if="item.icon" class="mr-[3px]" :icon="item.icon" />{{ item.title }}
          </div>
          <div class="content">
            <a-row :gutter="item.gutter">
              <template v-for="k in item.itemList" :key="k.value">
                <a-col v-if="k.deepKey && k.deepValue === form[k.deepKey] || !k.deepKey" :span="k.col ? k.col : item.col">
                  <a-form-item :label="k.label" :label-col="item.labelCol ? item.labelCol : null">
                    <template v-if="k.label !== '接口文档' && form[k.value] || String(form[k.value]) === '0'">
                      <a-tooltip placement="topLeft" :title="form[k.value]" arrow-point-at-center>
                        <div v-if="k.value === 'ruleMatchMethod'" class="max-w-[200px] truncate">{{ (k.render ? k.render(form[k.value]) : String(form[k.value])) === '1' ? '错误代码' : '失败描述' }}</div>
                        <div v-else class="max-w-[200px] truncate">{{ k.render ? k.render(form[k.value]) : String(form[k.value]) }}</div>
                      </a-tooltip>
                    </template>
                    <template v-if="form[k.value] && k?.type?.__name?.indexOf('Cus') > -1">
                      <component :is="k.type" v-bind="k?.$attrs" v-model="form[k.value]" style="width: 100%;" />
                    </template>
                  </a-form-item>
                </a-col>
              </template>
            </a-row>
          </div>
        </template>
      </a-form>
    </div>

    <template #footer>
      <a-space>
        <template v-if="extendBtn.length">
          <a-button v-for="itemBtn in extendBtn" :key="itemBtn.btnText" v-bind="itemBtn">{{ itemBtn.btnText }}</a-button>
        </template>
        <a-button type="primary" @click="handleOk">{{ okText }}</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import type { ButtonProps } from 'ant-design-vue';
import { ref, toRefs } from 'vue';
import type { IconDefinition } from '@pafe/icons-icore-agr-an/lib/types';
import type { InterfaceBaseType, PlatformBaseType } from '@/apiTypes/compliance';

const props = withDefaults(defineProps<{
  formLayout: {
    title: string;
    col: number;
    gutter: number;
    icon: IconDefinition;
    allowClear: boolean;
    itemList: {
      label: string;
      value: string;
      deepKey?: string;
      render?: (params: unknown) => void;
      deepValue?: string;
      col?: string;
      type: {
        __name: string;
      };
      [key: string]: unknown;
    }[];
    [k: string]: unknown;
  }[];
  title?: string;
  okText?: string;
  extendBtn?: Array<ButtonProps & { btnText: string }>;
}>(), {
  extendBtn: () => [],
  title: '查看',
  okText: '确定',
});

const { formLayout } = toRefs(props);

const visible = ref(false);

const handleOk = (): void => {
  visible.value = false;
};

const form = ref<Partial<PlatformBaseType & InterfaceBaseType & {
  [props: string]: string;
}>>({});
const setForm = (state: PlatformBaseType & InterfaceBaseType & {
  [props: string]: string;
}): void => {
  form.value = state;
};
const show = (): void => {
  visible.value = true;
};

const getForm = () => form.value;

defineExpose({
  setForm,
  show,
  getForm,
});
</script>

<style lang="less">
@import url("../index.less");
</style>
