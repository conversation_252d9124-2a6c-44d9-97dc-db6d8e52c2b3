<template>
  <div class="process-config">
    <div class="process-config-head">
      <div class="flex">
        <a-form class="flex-grow" :label-col="{ style: { width: pxToRem(95) } }" :colon="false">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="机构" name="belongBusinessDepartmentNo" v-bind="validateInfos.belongBusinessDepartmentNo" :label-col="{ style: { width: pxToRem(80) } }">
                <DeptListSelect :dept-code="modelRef.belongBusinessDepartmentNo" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="平台名称" name="thirdPartyPlatformNo" v-bind="validateInfos.thirdPartyPlatformNo">
                <a-select
                  v-model:value="modelRef['thirdPartyPlatformNo']"
                  placeholder="请选择"
                  :allow-clear="true"
                  :show-search="true"
                  :options="platformList"
                  :filter-option="(val: string, options: PlatformCodeName) => options.platformCodeName.includes(val)"
                  :field-names="{ label: 'platformCodeName', value: 'thirdPartyPlatformNo' }"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item class="labelClassHack-100" label="处理方案" name="handleProposal" v-bind="validateInfos.handleProposal">
                <a-select v-model:value="modelRef['handleProposal']" placeholder="请选择" :allow-clear="true" :show-search="true" :options="handle_proposal_list" :filter-option="filterOption" :field-names="{ label: 'parameterName', value: 'parameterCode' }" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-if="showExpandStatus" :gutter="16">
            <a-col :span="8">
              <a-form-item class="labelClassHack-100" label="失败类型" name="failureType" v-bind="validateInfos.failureType">
                <a-select v-model:value="modelRef['failureType']" placeholder="请选择" :show-search="true" :allow-clear="true" :options="failure_type_list" :filter-option="filterOption" :field-names="{ label: 'parameterName', value: 'parameterCode' }" @change="queryByClassNoAndIdParent" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item class="labelClassHack-100" label="失败类型细分" name="failureTypeSubdivision" v-bind="validateInfos.failureTypeSubdivision">
                <a-select v-model:value="modelRef['failureTypeSubdivision']" placeholder="请选择" :disabled="!modelRef.failureType" :allow-clear="true" :show-search="true" :options="failureList" :filter-option="filterOption" :field-names="{ label: 'parameterName', value: 'parameterCode' }" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="expand-area" @click="switchExpand">
          <span>{{ showExpandStatus ? '收起' : '展开' }}</span>
          <VueIcon :icon="showExpandStatus ? IconChevronUpFont : IconChevronDownFont" />
        </div>
      </div>
      <div class="flex justify-center">
        <a-button class="mr-[12px]" @click="reset">重置</a-button>
        <a-button type="primary" ghost @click="submit">查询</a-button>
      </div>
    </div>
    <div class="process-config-content">
      <div class="process-config-content-title">
        <div class="text-[16px] text-[rgba(0,0,0,0.8)] font-semibold">错误代码清单</div>
        <a-space :size="8">
          <a-button type="primary" ghost @click="clearErrorHandleProposalId">规则绑定重置</a-button>
          <a-button type="primary" @click="handleAdd">新增</a-button>
        </a-space>
      </div>
      <a-table
        :data-source="dataSource"
        :columns="columns"
        :loading="loading"
        :bordered="false"
        :scroll="{ x: '9rem', y: '50vh' }"
        :row-class-name="(_record: unknown, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
        :pagination="pagination"
      >
        <template #bodyCell="{ column, text, record, index }">
          <template v-if="column.dataIndex === 'order'">
            {{ index + 1 }}
          </template>
          <template v-if="column.dataIndex === 'ruleMatchMethod'">
            {{ record.ruleMatchMethod === '1' ? '错误描述' : '失败描述' }}
          </template>
          <template v-if="column.dataIndex === 'fuzzyMatchFlag'">
            {{ record.fuzzyMatchFlag === '1' ? '是' : '否' }}
          </template>
          <template v-if="['handleProposalName', 'ruleMatchContent'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] truncate">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.key === 'operation'">
            <a-button type="link" size="small" @click="handleLook(record.idParmErrorHandleProposal)">查看</a-button>
            <a-button type="link" size="small" @click="handleUpdate(record as ErrorBaseType)">修改</a-button>
            <a-button type="link" size="small" @click="handleDelete(record.idParmErrorHandleProposal)">删除</a-button>
          </template>
        </template>
      </a-table>
    </div>
    <EditModel v-model:show="editModelShow" title="处理方案配置" :init-value="tableRowData" :update-api="editErrorHandleReq.fetchData" :failure-type-list="failure_type_list" :handle-proposal-list="handle_proposal_list" :on-search="onSearch2" />
    <LookModel ref="looRef" title="处理方案详情" :form-layout="lookLayout" ok-text="返回" />
    <AddModel v-model:show="showAddModel" title="处理方案配置" :dept="modelRef['belongBusinessDepartmentNo']" :add-api="addErrorHandleReq.fetchData" :failure-type-list="failure_type_list" :handle-proposal-list="handle_proposal_list" :on-search="onSearch2" />
  </div>
</template>

<script setup lang="ts">
import { Modal, Form } from 'ant-design-vue';
import { IconChevronDownFont, IconChevronUpFont } from '@pafe/icons-icore-agr-an';
import LookModel from './components/lookModel/lookModel.vue';
import EditModel from './components/editModel/editModel.vue';
import AddModel from './components/editModel/addModel.vue';
import { columns, editModelRef, editRules, lookLayout } from './enums';
import DeptListSelect from '@/pages/compliance/components/DeptListSelect.vue';
import type { ErrorBaseType, ErrorColumn, PlatformCodeName, QueryErrorHandleListType } from '@/apiTypes/compliance';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePagination } from '@/composables/usePagination';
import { pxToRem } from '@/utils/tools';
import { useGet, usePost, $postOnClient, $post } from '@/composables/request';
import { message } from '@/components/ui/Message';
import type { TableData } from '@/apiTypes/interface';

const platformCodeNameReq = await usePost('/api/compliance/listPlatformCodeName');
const queryErrorHandleListReq = await usePost<TableData<ErrorColumn>>('/api/compliance/queryErrorHandleList');
const deleteErrorHandleReq = await useGet('/api/compliance/deleteErrorHandle');
const queryErrorHandleReq = await useGet('/api/compliance/queryErrorHandle');
const addErrorHandleReq = await usePost('/api/compliance/addErrorHandle');
const editErrorHandleReq = await usePost('/api/compliance/editErrorHandle');
const { gateWay, service } = useRuntimeConfig().public || {};

const looRef = ref();

const visibleBox = ref<boolean>(false);

const onSearch2 = async () => {
  visibleBox.value = false;
  editForm.resetFields();
  submit();
};

// 模糊搜索
const filterOption = (input: string, option: Record<string, string>) => {
  return option.parameterName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
// 是否展开额外筛选器
const showExpandStatus = ref(true);
// 切换展开状态
const switchExpand = () => {
  showExpandStatus.value = !showExpandStatus.value;
};

const changeDeptCode = (val: string) => {
  modelRef.belongBusinessDepartmentNo = val;
};

const showAddModel = ref(false);
const handleAdd = async () => {
  showAddModel.value = true;
  // visibleBox.value = true;
  // editModelRef.idParmThrdPtyPlatErrorHandleProposal = '';
  // await nextTick()
  // if (addAndEditRef.value?.visible) {
  //   editModelRef. debugger
  //   addAndEditRef.value.setForm({
  //     belongBusinessDepartmentNo: modelRef.belongBusinessDepartmentNo
  //   });
  //   addAndEditRef.value.show();
  // }
};

// 修改
const editModelShow = ref(false);

const tableRowData = ref<ErrorBaseType>();
const handleUpdate = async (record: ErrorBaseType) => {
  // visibleBox.value = true;
  // 修改加上编辑的id的值 区别新增还是编辑
  // editModelRef.idParmThrdPtyPlatErrorHandleProposal = record.idParmThrdPtyPlatErrorHandleProposal;
  console.log(record);
  // const { updatedDate, ...rest } = record;
  // 初始化默认值
  tableRowData.value = {
    idParmThrdPtyPlatErrorHandleProposal: record.idParmThrdPtyPlatErrorHandleProposal || '',
    belongBusinessDepartmentName: record.belongBusinessDepartmentName || '',
    belongBusinessDepartmentNo: record.belongBusinessDepartmentNo || '',
    errorNo: record.errorNo || '',
    errorNoComment: record.errorNoComment || '',
    handleProposal: record.handleProposal || '',
    itAnalysisResult: record.itAnalysisResult || '',
    thirdPartyPlatformName: record.thirdPartyPlatformName || '',
    thirdPartyPlatformNo: record.thirdPartyPlatformNo || '',
    updatedDate: record.updatedDate || '',
    ruleMatchMethod: record.ruleMatchMethod,
    ruleMatchContent: record.ruleMatchContent,
    fuzzyMatchFlag: record.fuzzyMatchFlag,
    failureType: record.failureType,
    failureTypeSubdivision: record.failureTypeSubdivision,
    idParmErrorHandleProposal: record.idParmErrorHandleProposal,
    remark: record.remark,
  };

  // await getErrorCategoryList(rest.thirdPartyPlatformNo);

  // if (addAndEditRef.value?.visible) {
  //   addAndEditRef.value.setForm(rest);

  //   addAndEditRef.value.show();
  // }

  editModelShow.value = true;
};

const handleLook = async (idParmErrorHandleProposal: string) => {
  looRef.value.setForm({});
  const { code, data, msg = '' } = await queryErrorHandleReq.fetchData({ idParmErrorHandleProposal }) || {};
  if (SUCCESS_CODE === code) {
    looRef.value.setForm(data);
    looRef.value.show();
  } else {
    message.error(msg);
  }
};

const modelRef: Partial<QueryErrorHandleListType> = reactive({
  belongBusinessDepartmentNo: undefined,
  thirdPartyPlatformNo: '',
  errorNo: '',
  errorNoComment: '',
  handleProposal: undefined,
  failureType: undefined,
  failureTypeSubdivision: undefined,
});

const rulesRef = reactive({
  belongBusinessDepartmentNo: [
    {
      required: true,
      message: '请选择机构',
    },
  ],
  thirdPartyPlatformNo: [
    {
      required: true,
      message: '请选择平台名称',
    },
  ],
});

const { resetFields, validate, validateInfos } = Form.useForm(modelRef, rulesRef);

const dataSource = ref<ErrorColumn[]>([]);

const loading = ref(false);
const reset = () => {
  resetFields();
};

const refresh = () => {
  validate().then(() => {
    loading.value = true;
    queryErrorHandleListReq.fetchData({
      belongBusinessDepartmentNo: modelRef.belongBusinessDepartmentNo,
      errorNo: modelRef.errorNo,
      errorNoComment: modelRef.errorNoComment,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      thirdPartyPlatformNo: modelRef.thirdPartyPlatformNo,
      failureType: modelRef.failureType,
      failureTypeSubdivision: modelRef.failureTypeSubdivision,
      handleProposal: modelRef.handleProposal,
    }).then((res) => {
      if (res?.code === SUCCESS_CODE) {
        const { records = [], total = 0, current = 1, size = 10 } = res.data;
        dataSource.value = records.map(record => ({
          belongBusinessDepartmentName: record.belongBusinessDepartmentName,
          belongBusinessDepartmentNo: record.belongBusinessDepartmentNo || '',
          thirdPartyPlatformNo: record.thirdPartyPlatformNo,
          thirdPartyPlatformName: record.thirdPartyPlatformName,
          errorNo: record.errorNo,
          errorNoComment: record.errorNoComment,
          errorCategoryNoName: record.errorCategoryNoName,
          itAnalysisResult: record.itAnalysisResult,
          handleProposal: record.handleProposal,
          updatedDate: record.updatedDate,
          idParmThrdPtyPlatErrorHandleProposal: record.idParmThrdPtyPlatErrorHandleProposal,
          ruleMatchMethod: record.ruleMatchMethod,
          ruleMatchContent: record.ruleMatchContent,
          fuzzyMatchFlag: record.fuzzyMatchFlag,
          failureType: record.failureType,
          failureTypeSubdivision: record.failureTypeSubdivision,
          idParmErrorHandleProposal: record.idParmErrorHandleProposal,
          failureTypeName: record.failureTypeName,
          handleProposalName: record.handleProposalName,
          failureTypeSubName: record.failureTypeSubName,
          remark: record.remark,
        }));
        pagination.total = total;
        pagination.current = current;
        pagination.pageSize = size;
        // pagination.pages = pages;
      } else {
        dataSource.value = [];
        pagination.total = 0;
        pagination.current = 1;
        pagination.pageSize = 10;
        // pagination.pages = 0;
      }
    }).finally(() => {
      loading.value = false;
    });
  });
};
type SelectMapsType = {
  selectMaps?: OptionType;
};
type ParameterType = {
  parameterCode: string;
  parameterName: string;
  idParmComnParm: number;
};
type OptionType = {
  failure_type?: ParameterType[];
  handle_proposal?: ParameterType[];
};
// 查询失败类型
const failure_type_list = ref<ParameterType[]>([]); // 失败类型
const handle_proposal_list = ref<ParameterType[]>([]); // 养殖用途
try {
  const res = await $post(`${gateWay}${service.administrate}/parmCommonParm/querySelect`, ['failure_type', 'handle_proposal'], { key: 'farmer_breed_select' });
  if (res && res.code === SUCCESS_CODE) {
    const selectMap = (res?.data as SelectMapsType)?.selectMaps as OptionType;
    failure_type_list.value = selectMap?.failure_type || [];
    handle_proposal_list.value = selectMap?.handle_proposal || [];
  } else {
    failure_type_list.value = [];
    handle_proposal_list.value = [];
  }
} catch {
  failure_type_list.value = [];
  handle_proposal_list.value = [];
}
const failureList = ref<ParameterType[]>([]); // 失败类型细分
// 查询失败类型细分
const queryByClassNoAndIdParent = async () => {
  if (!modelRef.failureType) {
    modelRef.failureTypeSubdivision = '';
  }
  const idParentParmComnParm = failure_type_list.value?.filter(item => item.parameterCode === modelRef.failureType)?.[0]?.idParmComnParm;
  try {
    const res = await $postOnClient(`${gateWay}${service.administrate}/parmCommonParm/queryByClassNoAndIdParent`, { parameterClassNo: 'failure_type_subdivision', idParentParmComnParm });
    const { code, data } = res || {};
    if (code === SUCCESS_CODE && Array.isArray(data)) {
      failureList.value = data || [];
      const _index = failureList.value.findIndex(item => item.parameterCode === modelRef.failureTypeSubdivision);
      if (_index === -1) {
        modelRef.failureTypeSubdivision = '';
      }
    }
  } catch (error) {
    console.log(error);
  }
};

const { pagination } = usePagination(refresh);

const editForm = Form.useForm(editModelRef, editRules);

// 平台名称可选项
const platformList = ref<PlatformCodeName[]>([]);

const handleDelete = (idParmErrorHandleProposal: string) => {
  Modal.confirm({
    title: '温馨提示',
    content: '即将删除，请谨慎操作！',
    class: 'bell-bg',
    centered: true,
    onOk() {
      deleteErrorHandleReq.fetchData({ idParmErrorHandleProposal }).then((res) => {
        const { code, msg = '' } = res || {};
        if (SUCCESS_CODE === code) {
          message.success('删除成功');
          submit();
        } else {
          message.error(msg);
        }
      });
    },
  });
};

const submit = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
};

// watchEffect(() => {
//   platformList.value = [];
//   modelRef.thirdPartyPlatformNo = '';
//   if (modelRef.belongBusinessDepartmentNo) {
//     getPlatformCodeNameList({ belongBusinessDepartmentNo: modelRef.belongBusinessDepartmentNo }).then((res) => {
//       const { code, data } = res;
//       if (code === SUCCESS_CODE && Array.isArray(data)) {
//         platformList.value = data.map((item) => ({
//           idParmThrdPtyPlatBase: item.idParmThrdPtyPlatBase,
//           thirdPartyPlatformNo: item.thirdPartyPlatformNo,
//           platformCodeName: item.platformCodeName,
//         }));
//         if (data[0]) {
//           modelRef.thirdPartyPlatformNo = data[0].thirdPartyPlatformNo;
//         }
//       }
//     });
//   }
// });

watch(() => modelRef.belongBusinessDepartmentNo, (val) => {
  platformList.value = [];
  modelRef.thirdPartyPlatformNo = '';
  if (val) {
    platformCodeNameReq.fetchData({ belongBusinessDepartmentNo: val }).then((res) => {
      const { code, data } = res || {};
      if (code === SUCCESS_CODE && Array.isArray(data)) {
        platformList.value = data.map(item => ({
          idParmThrdPtyPlatBase: item.idParmThrdPtyPlatBase,
          thirdPartyPlatformNo: item.thirdPartyPlatformNo,
          platformCodeName: item.platformCodeName,
        }));
        if (data[0]) {
          modelRef.thirdPartyPlatformNo = data[0].thirdPartyPlatformNo;
        }
      }
    });
  }
}, { immediate: true });

// 规则绑定重置
const clearErrorHandleProposalId = async () => {
  const res = await $postOnClient(`${gateWay}${service.compliance}/platErrorHandle/clearErrorHandleProposalId`, { platformConfirmCode: modelRef.thirdPartyPlatformNo });
  if (res?.code === SUCCESS_CODE) {
    message.success('重置成功');
  } else {
    message.error('重置失败');
  }
};
const initFlag = ref(false);
// 首次进来自动查询列表
watchEffect(() => {
  if (modelRef.thirdPartyPlatformNo && !initFlag.value) {
    submit();
    initFlag.value = true;
  }
});
</script>

<style lang="less">
@bgColor: #ffffff;
/* .ant-tooltip .ant-tooltip-inner::-webkit-scrollbar-thumb {
.ant-tooltip .ant-tooltip-inner::-webkit-scrollbar-thumb {
  background-color: #fff;
}
.ant-tooltip .ant-tooltip-inner{
  max-height: 100px;
  overflow-y: auto;
} */
.process-config {
  &-head {
    margin: 14px;
    padding: 16px 14px;
    background: @bgColor;
    border-radius: 6px;
    overflow: hidden;
    .expand-area {
      cursor: pointer;
      display: flex;
      align-items: center;
      height: 32px;
      font-size: 12px;
      margin-left: 24px;
      color: #262626;
    }
  }
  &-content {
    margin: 14px;
    padding: 0 16px;
    background: @bgColor;
    border-radius: 6px;
    &-title {
      height: 64px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
