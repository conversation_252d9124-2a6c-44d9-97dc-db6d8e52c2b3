<template>
  <a-modal
    v-model:open="visible"
    :width="pxToRem(800)"
    wrap-class-name="platform-edit-modal"
    destroy-on-close
    :after-close="handleAfterClose"
    :centered="true"
    :title="innerTitle"
    @on-cancel="handleCancel"
    @ok="hide"
  >
    <div>
      <a-form ref="formRef" :model="editModalRef" :colon="false">
        <div class="text-[14px] mb-[9px] text-[#333]">
          <VueIcon class="mr-[3px]" :icon="IconInformationFont" />
          基础信息
        </div>
        <div class="rounded-[4px] px-[15px] pt-[15px] mb-[15px] bg-[#f8f8f8]">
          <a-row :gutter="28" class="display-ctrl">
            <a-col span="12">
              <a-form-item label="机构" name="belongBusinessDepartmentNo" v-bind="validateInfos.belongBusinessDepartmentNo" class="labelClassHack-80">
                <DeptListSelect :dept-code="editModalRef.belongBusinessDepartmentNo" :disabled="editModalRef.idParmThrdPtyPlatBase ? true : false" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
            <a-col span="12" />
            <a-col span="12">
              <a-form-item label="平台名称" name="thirdPartyPlatformName" v-bind="validateInfos.thirdPartyPlatformName" class="labelClassHack-80">
                <a-input v-model:value="editModalRef.thirdPartyPlatformName" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="平台代码" name="thirdPartyPlatformNo" v-bind="validateInfos.thirdPartyPlatformNo" class="labelClassHack-80">
                <a-input v-model:value="editModalRef.thirdPartyPlatformNo" :disabled="editModalRef.idParmThrdPtyPlatBase ? true : false" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="生效日期" name="effectiveTime" v-bind="validateInfos.effectiveTime" class="labelClassHack-80">
                <a-date-picker v-model:value="editModalRef.effectiveTime" placeholder="请选择" :format="dateFormat" :disabled-date="disabledDate" :value-format="dateFormat" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="失效日期" name="invalidTime" v-bind="validateInfos.invalidTime" class="labelClassHack-80">
                <a-date-picker v-model:value="editModalRef.invalidTime" placeholder="请选择" :format="dateFormat" :disabled-date="disabledDate" :value-format="dateFormat" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="text-[14px] mb-[9px] text-[#333]">
          <VueIcon class="mr-[3px]" :icon="IconFuwuxinxiFont" />
          服务信息
        </div>
        <div class="rounded-[4px] px-[15px] pt-[15px] mb-[15px] bg-[#f8f8f8]">
          <a-row :gutter="28" class="display-ctrl">
            <a-col span="12">
              <a-form-item label="服务地址" name="platformServiceAddress" v-bind="validateInfos.platformServiceAddress" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.platformServiceAddress" placeholder="请输入" />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="授权账号" name="serviceAuthorizationAccountNo" v-bind="validateInfos.serviceAuthorizationAccountNo" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.serviceAuthorizationAccountNo" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="授权密码" name="serviceAuthorizationPassword" v-bind="validateInfos.serviceAuthorizationPassword" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.serviceAuthorizationPassword" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="服务供应商" name="serviceProviderName" v-bind="validateInfos.serviceProviderName" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.serviceProviderName" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="供应商联系人" name="supplierContactName" v-bind="validateInfos.supplierContactName" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.supplierContactName" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="供应商联系电话" name="contactNumber" v-bind="validateInfos.contactNumber" class="labelClassHack-140">
                <a-input v-model:value="editModalRef.contactNumber" placeholder="请输入" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="对接方式" name="platformDockMethod" v-bind="validateInfos.platformDockMethod" class="labelClassHack-80">
                <a-select v-model:value="editModalRef.platformDockMethod" placeholder="请选择" :options="duijieOptions" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="对接地址" name="serviceDockAddress" v-bind="validateInfos.serviceDockAddress" class="labelClassHack-80">
                <a-input v-model:value="editModalRef.serviceDockAddress" placeholder="请输入IP+端口" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="24">
              <a-form-item label="备注信息" name="remark" v-bind="validateInfos.remark" class="labelClassHack-80">
                <a-input v-model:value="editModalRef.remark" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="text-[14px] mb-[9px] text-[#333]">
          <VueIcon class="mr-[3px]" :icon="IconPingtaijiekouFont" />
          平台接口文档
        </div>
        <div class="rounded-[4px] px-[15px] pt-[15px] mb-[15px] bg-[#f8f8f8]">
          <a-row :gutter="28" class="display-ctrl">
            <a-col span="12">
              <a-form-item label="文档类型" name="attachmentClassNo" class="labelClassHack-80">
                <a-select v-model:value="attachmentClassNo" placeholder="请选择" :options="docOptions" allow-clear />
              </a-form-item>
            </a-col>
            <a-col span="24">
              <a-form-item label="接口文档" name="attDocSaveList" class="labelClassHack-80">
                <CusUpload v-model:value="(editModalRef.attDocSaveList as CusUploadListType[])" text="文件上传" :attachment-class-no="attachmentClassNo" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>

    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import {
  IconInformationFont,
  IconFuwuxinxiFont,
  IconPingtaijiekouFont,
} from '@pafe/icons-icore-agr-an';
import dayjs, { type Dayjs } from 'dayjs';
import Form from 'ant-design-vue/es/form/Form';
import CusUpload, { type CusUploadListType } from './cusUpload.vue';
import { message } from '@/components/ui/Message';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import { isDigitSignValidator, isWordDigitValidator } from '@/utils/validators';
import type { PlatformBaseType } from '@/apiTypes/compliance';
import { usePost } from '@/composables/request';
import DeptListSelect from '@/pages/compliance/components/DeptListSelect.vue';

const changeDeptCode = (val: string) => {
  editModalRef.belongBusinessDepartmentNo = val;
};

const props = defineProps<{
  formProps: PlatformBaseType;
}>();

const visible = defineModel<boolean>('show', {
  required: false,
  default: false,
});

const platformUpdateReq = await usePost('/api/compliance/platformUpdate');
const platformSaveReq = await usePost('/api/compliance/platformSave');

const setForm = (formData?: PlatformBaseType): void => {
  Object.assign(editModalRef, { ...formData });
};

const formRef = ref();

watch(
  visible,
  async (val) => {
    if (val) {
      await nextTick();
      formRef.value?.clearValidate();
      setForm(toRaw(props.formProps) || {});
    }
  },
  {
    immediate: true,
  },
);

const dateFormat = 'YYYY-MM-DD';
const disabledDate = (current: Dayjs) =>
  current && current < dayjs().subtract(1, 'day').endOf('day');
const docOptions = [
  { label: '平台技术文档', value: 'F0101' },
  { label: '红头文件', value: 'F0102' },
  { label: '数据外发签报', value: 'F0103' },
];
const duijieOptions = [
  { label: '公网', value: '01' },
  { label: '专线', value: '02' },
];

const title = '平台配置';
const editRules = ref({
  belongBusinessDepartmentNo: [{ required: true, message: '请选择机构' }],
  thirdPartyPlatformName: [
    { required: true, message: '请输入平台名称', trigger: 'blur' },
    { message: '平台名称最多64位', max: 64, trigger: 'change' },
  ],
  thirdPartyPlatformNo: [
    { required: true, message: '请输入平台代码', trigger: 'blur' },
    { message: '平台代码长度最多50位', max: 50, trigger: 'change' },
    {
      message: '不能输入汉字、“-_/”以外的特殊字符',
      trigger: 'change',
      validator: isWordDigitValidator,
    },
  ],
  effectiveTime: [
    { required: true, message: '请选择生效日期', trigger: 'change' },
  ],
  platformServiceAddress: [
    { message: '服务地址最多255位', max: 255, trigger: 'blur' },
  ],
  serviceAuthorizationAccountNo: [
    { message: '授权账号最多64位', max: 64, trigger: 'blur' },
  ],
  serviceAuthorizationPassword: [
    { message: '授权密码最多255位', max: 255, trigger: 'blur' },
  ],
  serviceProviderName: [
    { message: '服务供应商最多64位', max: 64, trigger: 'blur' },
  ],
  supplierContactName: [
    { message: '供应商联系人最多32位', max: 32, trigger: 'blur' },
  ],
  contactNumber: [
    { message: '供应商联系人电话最多20位', max: 20, trigger: 'blur' },
    {
      message: '只能输入数字,-_/',
      trigger: 'change',
      validator: isDigitSignValidator,
    },
  ],
  remark: [{ message: '备注信息最多225位', max: 225, trigger: 'blur' }],
});
// 表单初始化值
const editModalRef = reactive<PlatformBaseType>({
  belongBusinessDepartmentNo: undefined,
  thirdPartyPlatformName: '',
  effectiveTime: '',
  invalidTime: '',
  contactNumber: '',
  departmentNoAndName: '',
  idParmThrdPtyPlatBase: undefined,
  platformServiceAddress: '',
  remark: '',
  serviceAuthorizationAccountNo: '',
  serviceAuthorizationPassword: '',
  serviceProviderName: '',
  supplierContactName: '',
  thirdPartyPlatformNo: '',
  attDocSaveList: [],
  platformDockMethod: '', // 对接方式
  serviceDockAddress: '', // 对接地址
});

// 文档类型
const attachmentClassNo = ref('F0101');

const { resetFields, validate, validateInfos } = Form.useForm(
  editModalRef,
  editRules,
);
const innerTitle = computed(
  () =>
    (editModalRef.idParmThrdPtyPlatBase === '' ||
      editModalRef.idParmThrdPtyPlatBase === undefined
      ? '新增'
      : '修改') + title,
);
const emits = defineEmits(['onOk']);
const handleAfterClose = () => {
  resetFields();
};

const hide = (): void => {
  visible.value = false;
};
const handleCancel = (): void => {
  setForm();
  hide();
};

const handleOk = (): void => {
  validate()
    .then(async () => {
      const obj: Record<string, unknown> = {};
      // 过滤所有数据中的前后空格
      for (const [key, value] of Object.entries(editModalRef)) {
        if (typeof value === 'string') {
          obj[key] = value.trim();
        } else {
          obj[key] = value;
        }
      }
      if (
        editModalRef.idParmThrdPtyPlatBase === '' ||
        editModalRef.idParmThrdPtyPlatBase === undefined
      ) {
        const res = await platformSaveReq.fetchData(obj);
        const { code, msg = '' } = res || {};
        if (SUCCESS_CODE === code) {
          message.success(msg);
          hide();
          emits('onOk');
        } else {
          message.error(msg);
        }
      } else {
        const { code, msg } = (await platformUpdateReq.fetchData(obj)) || {};
        if (SUCCESS_CODE === code) {
          message.success(msg);
          hide();
          emits('onOk');
        } else {
          message.error(msg);
        }
      }
    })
    .catch((err) => {
      formRef.value.scrollToField(
        err?.errorFields?.[0].name,
        document
          .querySelector('.ant-form-item-has-error')
          ?.scrollIntoView(false),
      );
    });
};
</script>

<style lang="less">
.platform-edit-modal {
  .labelClassHack-160 {
    .ant-col.ant-form-item-label {
      width: 160px;
    }
  }
  .labelClassHack-100 {
    .ant-col.ant-form-item-label {
      width: 100px;
    }
  }
  .labelClassHack-80 {
    .ant-col.ant-form-item-label {
      width: 80px;
    }
  }
  .labelClassHack-140 {
    .ant-col.ant-form-item-label {
      width: 140px;
    }
  }
}
</style>
