<template>
  <div class="cus-tainer">
    <div class="search-wrapper mx-[14px] my-[12px] bg-white overflow-hidden min-h-[64px] rounded-[6px]">
      <div :class="['search-form px-[16px] pt-[16px] no-operation']">
        <a-form :label-col="{ style: { width: pxToRem(80) } }" :colon="false">
          <a-row :gutter="24">
            <a-col span="8">
              <a-form-item label="机构" name="belongBusinessDepartmentNo" v-bind="form.validateInfos.belongBusinessDepartmentNo">
                <DeptListSelect :dept-code="modalRef.belongBusinessDepartmentNo" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
            <a-col span="8">
              <a-form-item label="平台名称" name="idParmThrdPtyPlatBase" v-bind="form.validateInfos.idParmThrdPtyPlatBase">
                <a-select
                  v-model:value="modalRef.idParmThrdPtyPlatBase"
                  show-search
                  allow-clear
                  placeholder="请选择"
                  :filter-option="(val: string, options: PlatformCodeName) => options.platformCodeName.includes(val)"
                  :field-names="{ label: 'platformCodeName', value: 'idParmThrdPtyPlatBase' }"
                  :options="platformNameList"
                />
              </a-form-item>
            </a-col>
            <a-col span="8">
              <a-form-item label="生效日期" name="effectiveTime" v-bind="form.validateInfos.effectiveTime">
                <a-date-picker v-model:value="modalRef.effectiveTime" allow-clear placeholder="选择日期" style="width:100%" :format="dateFormat" :value-format="dateFormat" />
              </a-form-item>
            </a-col>
            <a-col v-show="showExpandStatus" span="8">
              <a-form-item label="失效日期" name="invalidTime" v-bind="form.validateInfos.invalidTime">
                <a-date-picker v-model:value="modalRef.invalidTime" allow-clear placeholder="选择日期" style="width:100%" :format="dateFormat" :value-format="dateFormat" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="h-[32px] flex items-center w-[80px]">
          <div class="expand-area" @click="switchExpand">
            <span>{{ showExpandStatus ? '收起' : '展开' }}</span>
            <VueIcon :icon="showExpandStatus ? IconChevronUpFont : IconChevronDownFont" />
          </div>
        </div>
      </div>
      <div class="flex justify-center px-[16px] pb-[16px]">
        <a-button class="mr-[12px]" @click="onReset">重置</a-button>
        <a-button type="primary" ghost @click="onSearch">查询</a-button>
      </div>
    </div>
    <div class="px-[14px] py[12px]">
      <div class="flex h-full overflow-hidden">
        <a-card class="w-full h-full rounded-md" :body-style="{ overflow: 'auto' }" :bordered="false">
          <template #title>
            <div class="flex items-center justify-between">
              <span>平台基本配置信息</span>
              <a-button type="primary" @click="handleAdd">新增</a-button>
            </div>
          </template>
          <div class="cus-table">
            <a-table
              :scroll="{ x: '10rem', y: '50vh' }"
              :data-source="dataSource"
              :columns="columns"
              :loading="loading"
              :pagination="pagination"
              :expand-column-width="100"
              :row-class-name="(_: unknown, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
              :row-key="(dataIndex: number) => dataIndex + 1"
            >
              <template #bodyCell="{ column, record, text }">
                <template v-if="['createdDate', 'updatedDate', 'effectiveTime', 'invalidTime', 'thirdPartyPlatformName', 'thirdPartyPlatformNo'].includes(column.dataIndex as string)">
                  <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
                    <div class="max-w-[200px] truncate">{{ text }}</div>
                  </a-tooltip>
                </template>
                <template v-if="column.dataIndex === 'operation'">
                  <a-button type="link" size="small" @click="handleLook(record.idParmThrdPtyPlatBase)">查看</a-button>
                  <a-button type="link" size="small" @click="handleUpdate(record as PlatformBaseType)">修改</a-button>
                  <a-button type="link" size="small" @click="handleDelete(record.idParmThrdPtyPlatBase)">删除</a-button>
                </template>
              </template>
            </a-table>
          </div>
        </a-card>
      </div>
    </div>
    <AddAndEditModal ref="addAndEditRef" v-model:show="showEditModal" :form-props="modalForm" @on-ok="handleOk" />
    <LookModal ref="looRef" />
  </div>
</template>

<script setup lang="ts">
import { Modal, type TableProps } from 'ant-design-vue';
import { IconChevronDownFont, IconChevronUpFont } from '@pafe/icons-icore-agr-an';
import { ref, reactive } from 'vue';
import Form from 'ant-design-vue/es/form/Form';
import AddAndEditModal from './editModal.vue';
import LookModal from './lookModal.vue';
import DeptListSelect from '@/pages/compliance/components/DeptListSelect.vue';
import type { ApiResult } from '@/composables/request';
import type { PlatformBaseType, PlatformCodeName, DepartmentNoType } from '@/apiTypes/compliance';
import { message } from '@/components/ui/Message';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePagination } from '@/composables/usePagination';
import { pxToRem } from '@/utils/tools';
import { useGet, usePost } from '@/composables/request';

const changeDeptCode = (val: string) => {
  modalRef.belongBusinessDepartmentNo = val;
};

const looRef = ref();
const addAndEditRef = ref();
const modalForm = ref<{ [K in keyof PlatformBaseType]?: PlatformBaseType[K] }>({});
const showEditModal = ref(false);
const handleAdd = (): void => {
  modalForm.value = {};
  showEditModal.value = true;
};

// 是否展开额外筛选器
const showExpandStatus = ref(true);
// 切换展开状态
const switchExpand = () => {
  showExpandStatus.value = !showExpandStatus.value;
};

const { fetchData: platFormDetailFetchData } = await useGet<PlatformBaseType, { id: string }>('api/compliance/platformDetail');
const handleUpdate = async (record: PlatformBaseType) => {
  if (record.idParmThrdPtyPlatBase) {
    const { code, data, msg } = await platFormDetailFetchData({ id: record.idParmThrdPtyPlatBase }) || {};
    if (SUCCESS_CODE === code) {
      modalForm.value = data!;
      showEditModal.value = true;
    } else {
      console.log(msg);
    }
  } else {
    message.error('请检查数据是否正确');
  }
};

const handleLook = async (id: string): Promise<void> => {
  looRef.value.setForm({});
  const { code, data, msg } = await platFormDetailFetchData({ id }) || {};
  if (SUCCESS_CODE === code) {
    looRef.value.setForm(data);
    looRef.value.show();
  } else {
    message.error(msg || '');
  }
};

const platformNameList = ref<Array<PlatformCodeName>>([]);
const modalRef: Partial<SearchType> = reactive<Partial<SearchType>>({
  belongBusinessDepartmentNo: undefined,
  idParmThrdPtyPlatBase: undefined,
  createdDate: undefined,
  effectiveTime: undefined,
  invalidTime: undefined,
});

// 搜索表单校验对象
const rulesRef = reactive({
  belongBusinessDepartmentNo: [
    {
      required: true,
      message: '请选择机构',
    },
  ],
  idParmThrdPtyPlatBase: [
    {
      required: true,
      message: '请选择平台名称',
    },
  ],
});
const form = Form.useForm(modalRef, rulesRef);
const loading = ref(false);
const getPlatformPageParams = ref();
const { fetchData: platformFetchData } = await usePost('/api/compliance/getPlatformPage');
const getTableData = async () => {
  loading.value = true;
  getPlatformPageParams.value = { ...modalRef, pageNum: pagination.current, pageSize: pagination.pageSize };
  platformFetchData(getPlatformPageParams).then((res) => {
    const { data, code } = res || {};
    if (SUCCESS_CODE === code && data) {
      const { records, total = 0, current = 1, size = 10 } = data;
      dataSource.value = records;
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    // pagination.pages = pages;
    } else {
      dataSource.value = [];
      pagination.total = 0;
      pagination.current = 1;
      pagination.pageSize = 10;
    // pagination.pages = 0;
    }
  }).finally(() => {
    loading.value = false;
  });
};
// 分页处理
const { pagination } = usePagination(getTableData);
const dataSource = ref<PlatformBaseType[]>([]);

// 查询表格数据
const onSearch = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  const { validate } = form;
  validate().then(() => {
    getTableData();
  });
};

// 重置表单
const onReset = () => {
  pagination.current = 1;
  form?.resetFields();
};

const dateFormat = 'YYYY-MM-DD';
interface SearchType {
  [key: string]: string;
  belongBusinessDepartmentNo: string;
  idParmThrdPtyPlatBase: string;
  createdDate: string;
  effectiveTime: string;
  invalidTime: string;
}
// 表格展示配置
const columns: TableProps['columns'] = [
  {
    title: '序号',
    customRender: ({ index }) => index + 1,
    width: pxToRem(60),
  },
  {
    title: '归属机构',
    dataIndex: 'departmentNoAndName',
    width: pxToRem(120),
  },
  {
    title: '平台名称',
    dataIndex: 'thirdPartyPlatformName',
    width: pxToRem(120),
  },
  {
    title: '平台代码',
    dataIndex: 'thirdPartyPlatformNo',
    width: pxToRem(120),
  },

  {
    title: '创建时间',
    dataIndex: 'createdDate',
    width: pxToRem(100),
  },
  {
    title: '更新时间',
    dataIndex: 'updatedDate',
    width: pxToRem(100),
  },
  {
    title: '生效日期',
    dataIndex: 'effectiveTime',
    width: pxToRem(100),
  },
  {
    title: '失效日期',
    dataIndex: 'invalidTime',
    width: pxToRem(100),
  },
  {
    title: '操作',
    width: pxToRem(160),
    dataIndex: 'operation',
    fixed: 'right',
  },
];
const handleDelete = (id: string) => {
  Modal.confirm({
    title: '温馨提示',
    content: '删除后，平台信息将不可使用，请谨慎操作！',
    class: 'bell-bg',
    centered: true,
    async onOk() {
      const { data: res } = await useFetch<ApiResult<unknown>>('/api/compliance/platformDelete',
        { method: 'post',
          body: {},
          query: { id },
          deep: false,
        });
      const { code, msg = '' } = res.value || {};
      if (SUCCESS_CODE === code) {
        message.success(msg);
        onSearch();
      } else {
        message.error(msg);
      }
    },
    onCancel() {
      console.log('Cancel');
    },
  });
};
const handleOk = () => {
  onSearch();
};

// 页面初始化标识
const initFlag = ref(false);
const { fetchData: platformCodeNameFetchData } = await usePost<PlatformCodeName[], DepartmentNoType>('/api/compliance/listPlatformCodeName');

watch(() => modalRef.belongBusinessDepartmentNo, async () => {
  platformNameList.value = [];
  modalRef.idParmThrdPtyPlatBase = '';
  if (modalRef.belongBusinessDepartmentNo) {
    const { data, code } = await platformCodeNameFetchData({ belongBusinessDepartmentNo: modalRef.belongBusinessDepartmentNo }) || {};
    if (SUCCESS_CODE === code && data) {
      platformNameList.value = data;
      if (platformNameList.value?.[0]) {
        modalRef.idParmThrdPtyPlatBase = platformNameList.value[0].idParmThrdPtyPlatBase;
      }
    }
  }
});

watchEffect(() => {
  if (modalRef.idParmThrdPtyPlatBase && !initFlag.value) {
    onSearch();
    initFlag.value = true;
  }
});
</script>

<style lang="less" scoped>
.cus-tainer {
  .ant-card {
    .ant-card-head {
      border-bottom: 0;
      padding: 0 16px;
    }
    .ant-card-body {
      padding: 0 16px;
    }
  }
  .search-form {
    display: flex;
    > form {
      flex-grow: 1;
    }
  }

  .expand-area {
    cursor: pointer;
    display: flex;
    align-items: center;
    height: 32px;
    font-size: 12px;
    margin-left: 24px;
    color: #262626;
  }
}
</style>
