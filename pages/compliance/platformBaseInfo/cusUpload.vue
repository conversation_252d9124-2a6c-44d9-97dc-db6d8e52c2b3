<template>
  <a-spin :spinning="loading">
    <a-upload v-model:file-list="list" :before-upload="handleBefore" @change="handleUploadChange">
      <a-button v-if="!$attrs?.look" class="mb-[6px]" :icon="$attrs?.icon">{{ text }}</a-button>
      <template #itemRender="{ file }">
        <div v-if="file.fileName" class="flex items-center ">
          <fieldset>
            <span v-if="file.attachmentClassNo" class="mr-6px">【{{ attachmentMap[file.attachmentClassNo] }}】</span>
            <VueIcon class="text-[#2D8CF0] mr-4px" :icon="IconAttachmentFont" />
            <a-button type="link" size="small" class="text-[12px] text-[#2D8CF0] py-[0px]" @click="handlePreview(file)">{{ file.fileName }}</a-button>
            <a-button v-if="!$attrs?.look" type="link" size="small" class="text-[12px] text-[#2D8CF0] py-[0px]" @click="handleDelete(file)">删除</a-button>
          </fieldset>
        </div>
      </template>
    </a-upload>
  </a-spin>
</template>

<script lang='ts' setup>
import { IconAttachmentFont } from '@pafe/icons-icore-agr-an';
import { message } from '@/components/ui/Message';
import { SUCCESS_CODE } from '@/utils/constants';
import { iobsUploadPro } from '@/utils/iobsUpload';
import { $postOnClient } from '@/composables/request';

export type CusUploadListType = Array<Partial<File & {
  iobsBucketName: string;
  storageTypeCode: string;
  fileKey: string;
  fileSize: number;
  fileName: string;
  uid: string;
  attachmentClassNo: string;
}>>;
const attachmentMap: { [key: string]: string } = {
  F0101: '平台技术文档',
  F0102: '红头文件',
  F0103: '数据外发签报',
};
const emits = defineEmits(['update:value']);
const props = withDefaults(defineProps<{
  value: Array<CusUploadListType>;
  text?: string;
  attachmentClassNo?: string; // 文档类型
}>(), {
  value: () => [],
  origin: false,
  text: '上传',
  attachmentClassNo: '',
});
const loading = ref(false);
const { value, attachmentClassNo } = toRefs(props);
const list = ref(value);
const tmpArr = ref<CusUploadListType>([]);
const handlePreview = async (file: File) => {
  const { data, msg, code } = await $postOnClient('/api/iobs/getInIobsUrl', file) || {};
  if (SUCCESS_CODE === code) {
    message.success(msg || '');
    const { fileUrl } = data;
    window.open(fileUrl);
  } else {
    message.error(msg || '');
  }
};
const handleBefore = () => {
  tmpArr.value = [];
  return false;
};
const handleDelete = (file: CusUploadListType) => {
  const index = list.value.indexOf(file);
  const newFileList = [...list.value];
  newFileList.splice(index, 1);
  emits('update:value', newFileList);
};
// 文件数据上传
const handleUploadChange = async (e: { file: File & { uid: string }; fileList: Array<CusUploadListType> }) => {
  loading.value = true;
  const { file, fileList } = e;
  let resFileList = [...fileList];
  // 过滤本地文件数据
  resFileList = resFileList.filter(file => (file as unknown as { fileName: string }).fileName);
  const { data, msg, code } = await iobsUploadPro(file);
  loading.value = false;
  if (SUCCESS_CODE === code) {
    tmpArr.value.push({
      iobsBucketName: data.bucket,
      storageTypeCode: '02',
      fileKey: data.key,
      fileSize: file.size,
      fileName: file.name,
      attachmentClassNo: attachmentClassNo.value,
    });
    const dataAll = [...resFileList, ...tmpArr.value];
    // 过滤完成后合并数据
    emits('update:value', dataAll);
    message.success(msg);
  } else {
    // 文件上传失败不更新数据
    message.error(msg);
  }
};
</script>
