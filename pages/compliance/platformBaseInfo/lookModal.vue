<template>
  <a-modal v-model:open="visible" :width="pxToRem(800)" wrap-class-name="platform-edit-modal" destroy-on-close title="查看平台配置" @ok="handleOk">
    <div>
      <a-form ref="formRef" :model="form" label-align="left">
        <div class="title text-[14px] mb-[9px] text-[#333]">
          <VueIcon class="mr-[3px]" :icon="IconInformationFont" /> 基础信息
        </div>
        <div class="content rounded-[4px] px-[15px] pt-[15px] mb-[15px] bg-[#f8f8f8]">
          <a-row ::gutter="28">
            <a-col span="12">
              <a-form-item label="归属机构">{{ form.departmentNoAndName }}</a-form-item>
            </a-col>
            <a-col span="12" />
            <a-col span="12">
              <a-form-item label="平台名称">{{ form.thirdPartyPlatformName }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="平台代码">{{ form.thirdPartyPlatformNo }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="生效日期">{{ form.effectiveTime }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="失效日期">{{ form.invalidTime }}</a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="title text-[14px] mb-[9px] text-[#333]">
          <VueIcon class="mr-[3px]" :icon="IconFuwuxinxiFont" /> 服务信息
        </div>
        <div class="content rounded-[4px] px-[15px] pt-[15px] mb-[15px] bg-[#f8f8f8]">
          <a-row :gutter="28">
            <a-col span="12">
              <a-form-item label="服务地址">
                <a-tooltip placement="topLeft" :title="form.platformServiceAddress" arrow-point-at-center>
                  <div class="max-w-[220px] truncate">{{ form.platformServiceAddress }}</div>
                </a-tooltip>
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="授权账号">{{ form.serviceAuthorizationAccountNo }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="授权密码">{{ form.serviceAuthorizationPassword }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="服务供应商">{{ form.serviceProviderName }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="供应商联系人">{{ form.supplierContactName }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="供应商联系电话">{{ form.contactNumber }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="对接方式">{{ render(form.platformDockMethod, duijieOptions) }}</a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="对接地址">{{ form.serviceDockAddress }}</a-form-item>
            </a-col>
            <a-col span="24">
              <a-form-item label="备注信息">{{ form.remark }}</a-form-item>
            </a-col>
          </a-row>
        </div>
        <div class="title text-[14px] mb-[9px] text-[#333]">
          <VueIcon class="mr-[3px]" :icon="IconPingtaijiekouFont" /> 平台接口文档
        </div>
        <div class="content rounded-[4px] px-[15px] pt-[15px] mb-[15px] bg-[#f8f8f8]">
          <a-row :gutter="28">
            <!-- <a-col span="12">
              <a-form-item label="文档类型">
                {{ render(form.attachmentClassNo, docOptions) }}
              </a-form-item>
            </a-col> -->
            <a-col span="24">
              <a-form-item label="接口文档">
                <!-- <template v-for="item in form.attDocSaveList" :key="item.attachmentClassNo">
                  {{item.attachmentClassNo}}
                </template> -->
                <CusUpload v-model:value="(form.attDocSaveList as CusUploadListType[])" look="true" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </div>

    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleOk">返回</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { IconInformationFont, IconFuwuxinxiFont, IconPingtaijiekouFont } from '@pafe/icons-icore-agr-an';
import CusUpload, { type CusUploadListType } from './cusUpload.vue';
import type { InterfaceBaseType, PlatformBaseType } from '@/apiTypes/compliance';
import { pxToRem } from '@/utils/tools';

// const docOptions = [
//   { label: '平台技术文档', value: 'F0101' },
//   { label: '红头文件', value: 'F0102' },
//   { label: '数据外发签报', value: 'F0103' },
// ];
const duijieOptions = [
  { label: '公网', value: '01' },
  { label: '专线', value: '02' },
];
const render = (val: number | string | undefined, options: Array<{ label: string; value: string }>) => {
  const tmp = options.find(item => String(item.value) === String(val));
  return tmp?.label;
};
const visible = ref(false);
const handleOk = (): void => {
  visible.value = false;
};
const form = ref<Partial<PlatformBaseType & InterfaceBaseType & {
  [props: string]: string;
}>>({});
const setForm = (state: PlatformBaseType & InterfaceBaseType & {
  [props: string]: string;
}): void => {
  form.value = state;
};
const show = (): void => {
  visible.value = true;
};
const getForm = () => form.value;
defineExpose({
  setForm,
  show,
  getForm,
});
</script>
