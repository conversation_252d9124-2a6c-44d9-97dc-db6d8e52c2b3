<template>
  <a-modal v-model:open="visible" :after-close="handleAfterClose" centered :title="title">
    <a-form :label-col="{ span: 6 }" :colon="false">
      <a-form-item label="平台名称" name="thirdPartyPlatformNo" v-bind="validateInfos.thirdPartyPlatformNo">
        <a-select
          v-model:value="editModelRef['thirdPartyPlatformNo']"
          :disabled="type !== 'add'"
          :allow-clear="true"
          :show-search="true"
          :options="platformNameList"
          :filter-option="(val: string, options: PlatformCodeName) => options.platformCodeName.includes(val)"
          :field-names="{ label: 'platformCodeName', value: 'thirdPartyPlatformNo' }"
          @change="handleChangePlatform"
        />
      </a-form-item>
      <a-form-item label="类型名称" name="parameterClassNo" v-bind="validateInfos.parameterClassNo">
        <a-select
          v-model:value="editModelRef['parameterClassNo']"
          :disabled="type !== 'add'"
          :allow-clear="true"
          :show-search="true"
          :options="parameterTypeList"
          :filter-option="(val: string, options: ParameterType) => options.parameterClassName.includes(val)"
          :field-names="{ label: 'parameterClassName', value: 'parameterClassNo' }"
        />
      </a-form-item>
      <a-form-item label="代码" name="parameterCode" v-bind="validateInfos.parameterCode">
        <a-input v-model:value="editModelRef['parameterCode']" placeholder="请输入代码" :disabled="type === 'view'" />
      </a-form-item>
      <a-form-item label="代码名称" name="parameterName" v-bind="validateInfos.parameterName">
        <a-input v-model:value="editModelRef['parameterName']" placeholder="请输入代码名称" :disabled="type === 'view'" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button v-if="type === 'view'" type="primary" @click="visible = false">返回</a-button>
      <div v-else>
        <a-button @click="visible = false">取消</a-button>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import Form from 'ant-design-vue/es/form/Form';
import { SUCCESS_CODE } from '@/utils/constants';
// import { addPlatCommonParameter, editPlatCommonParameter, getCommonParameterType } from '@/api/compliance';
// import { ParameterType, PlatformCodeName } from '@/api/compliance.interface';
import type { PlatformCodeName, ParameterType, PlatformEditForm } from '@/apiTypes/compliance';
import { usePost, useGet } from '@/composables/request';
import { message } from '@/components/ui/Message';

const emits = defineEmits(['onOk']);
const props = defineProps<{
  title: string;
  type: string;
  platformNameList: PlatformCodeName[];
}>();

const show = (): void => {
  visible.value = true;
};

const setForm = (formData?: PlatformEditForm) => {
  if (formData) {
    editModelRef.idParmThrdPtyPlatCommonParameter = formData.idParmThrdPtyPlatCommonParameter;
    editModelRef.thirdPartyPlatformNo = formData.thirdPartyPlatformNo;
    editModelRef.parameterClassNo = formData.parameterClassNo;
    editModelRef.parameterCode = formData.parameterCode;
    editModelRef.parameterName = formData.parameterName;
  }
};

defineExpose({
  show,
  setForm,
});

const getCommonParameterTypeReq = await useGet('/api/compliance/queryCommonParameterType');
const addPlatCommonParameter = await usePost('/api/compliance/addPlatCommonParameter');
const editPlatCommonParameter = await usePost('/api/compliance/editPlatCommonParameter');

// 查询类型名称
const parameterTypeList = ref<ParameterType[]>([]);
const getParameterList = async (thirdPartyPlatformNo: string) => {
  const { code, data } = await getCommonParameterTypeReq.fetchData({ thirdPartyPlatformNo }) || {};
  if (code === SUCCESS_CODE && Array.isArray(data)) {
    parameterTypeList.value = data.map(item => ({
      parameterClassName: item.parameterClassName,
      parameterClassNo: item.parameterClassNo,
    }));
  } else {
    parameterTypeList.value = [];
  }
};

// 切换平台名称后清空已选类型名称
const handleChangePlatform = () => {
  editModelRef.parameterClassNo = '';
};

// 新增平台数据
const editModelRef = reactive({
  idParmThrdPtyPlatCommonParameter: '',
  thirdPartyPlatformNo: '',
  parameterClassNo: '',
  parameterCode: '',
  parameterName: '',
});

const editRulesRef = ref({
  thirdPartyPlatformNo: [{ required: true, message: '请选择平台名称', trigger: 'change' }],
  parameterClassNo: [{ required: true, message: '请选择类型名称', trigger: 'change' }],
  parameterCode: [
    { required: true, message: '请输入代码', trigger: 'blur' },
    { message: '代码最多64位', max: 64, trigger: 'change' },
  ],
  parameterName: [
    { required: true, message: '请输入代码名称', trigger: 'blur' },
    { message: '代码名称最多64位', max: 64, trigger: 'change' },
  ],
});

const { resetFields, validate, validateInfos } = Form.useForm(editModelRef, editRulesRef);

const visible = ref(false);

// 重置
const handleAfterClose = () => {
  resetFields();
  setForm();
};

// 确认
const handleOk = () => {
  validate()
    .then(async () => {
      const typeMap: Record<string, () => void> = {
        add: async () => {
          const { code, msg = '' } = await addPlatCommonParameter.fetchData(editModelRef) || {};
          if (code === SUCCESS_CODE) {
            message.success('新增成功');
            visible.value = false;
            emits('onOk');
          } else {
            message.error(msg);
          }
        },
        update: async () => {
          const { code, msg = '' } = await editPlatCommonParameter.fetchData(editModelRef) || {};
          if (code === SUCCESS_CODE) {
            message.success('保存成功');
            visible.value = false;
            emits('onOk');
          } else {
            message.error(msg);
          }
        },
      };
      typeMap[props.type]();
    });
};

watchEffect(() => {
  getParameterList(editModelRef.thirdPartyPlatformNo);
});
</script>
