<template>
  <div class="platform-manage">
    <div class="mx-[14px] my-[12px] px-[16px] pt-[16px] pb-[20px] bg-[#ffffff] rounded-[6px]">
      <a-form class="flex-grow" :label-col="{ style: { width: pxToRem(80) } }" :colon="false">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="机构" name="belongBusinessDepartmentNo" v-bind="validateInfos.belongBusinessDepartmentNo">
              <DeptListSelect :dept-code="listModelRef.belongBusinessDepartmentNo" @change-dept-code="changeDeptCode" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="平台名称" name="thirdPartyPlatformNo" v-bind="validateInfos.thirdPartyPlatformNo">
              <a-select
                v-model:value="listModelRef['thirdPartyPlatformNo']"
                placeholder="请选择"
                :allow-clear="true"
                :show-search="true"
                :options="platformNameList"
                :filter-option="(val: string, options: PlatformCodeName) => options.platformCodeName.includes(val)"
                :field-names="{ label: 'platformCodeName', value: 'thirdPartyPlatformNo' }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="类型名称" name="parameterClassNo" v-bind="validateInfos.parameterClassNo">
              <a-select
                v-model:value="listModelRef['parameterClassNo']"
                placeholder="请选择"
                :allow-clear="true"
                :show-search="true"
                :options="parameterTypeList"
                :filter-option="(val: string, options: ParameterBaseType) => options.parameterClassName.includes(val)"
                :field-names="{ label: 'parameterClassName', value: 'parameterClassNo' }"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex justify-center">
        <a-button class="mr-[12px]" @click="handleReset">重置</a-button>
        <a-button type="primary" ghost @click="handleQuery(true)">查询</a-button>
      </div>
    </div>
    <div class="mx-[14px] my-[12px] px-[16px] bg-[#ffffff] rounded-[6px]">
      <div class="h-[64px] flex justify-between items-center">
        <span class="text-[16px] text-[rgba(0,0,0,0.8)] font-semibold">平台数据维护</span>
        <div class="text-[14px] space-x-[8px]">
          <a-button @click="handleDownloadModel">模版下载</a-button>
          <a-upload ref="uploadRef" v-model:file-list="fileList" :show-upload-list="false" action="" :before-upload="handleBeforeChange" @change="handleUpload">
            <a-button>上传</a-button>
          </a-upload>
          <a-button @click="handleExport">导出</a-button>
          <a-button type="primary" @click="handleAdd">新增</a-button>
        </div>
      </div>
      <a-table
        :data-source="dataSource"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        :bordered="false"
        :row-class-name="(_record: unknown, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
        :scroll="{ y: '50vh' }"
      >
        <template #bodyCell="{ column, text, record, index }">
          <template v-if="column.dataIndex === 'order'">{{ index + 1 }}</template>
          <template v-if="['thirdPartyPlatformName', 'parameterClassName'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] truncate">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <a-button type="link" size="small" @click="handleView(record.idParmThrdPtyPlatCommonParameter)">查看</a-button>
            <a-button type="link" size="small" @click="handleUpdate(record as ParameterBaseType)">修改</a-button>
            <a-button type="link" size="small" @click="handleDelete(record.idParmThrdPtyPlatCommonParameter)">删除</a-button>
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <edit-modal ref="editRef" :title="modelTitle" :type="modelType" :platform-name-list="platformNameList" @on-ok="handleQuery(true)" />
</template>

<script setup lang="ts">
import { Modal } from 'ant-design-vue';
import Form from 'ant-design-vue/es/form/Form';
import type { UploadListType } from 'ant-design-vue/es/upload/interface';
import EditModal from './platformDataEdit.vue';
import DeptListSelect from '@/pages/compliance/components/DeptListSelect.vue';
import { usePagination } from '@/composables/usePagination';
import { SUCCESS_CODE } from '@/utils/constants';
import { downloadBlob, pxToRem } from '@/utils/tools';
import { usePost, useGet, $postOnClient } from '@/composables/request';
import type { PlatformCodeName, ParameterBaseType, ParameterType, DepartmentNoType } from '@/apiTypes/compliance';
import type { TableData } from '@/apiTypes/interface';
import { message } from '@/components/ui/Message';

const changeDeptCode = (val: string) => {
  listModelRef.belongBusinessDepartmentNo = val;
};

// 平台名称
const platformNameList = ref<PlatformCodeName[]>([]);

// 类型名称
const parameterTypeList = ref<ParameterType[]>([]);

const getPlatformCodeNameListReq = await usePost<PlatformCodeName[], DepartmentNoType>('/api/compliance/listPlatformCodeName');
const getCommonParameterTypeReq = await useGet('/api/compliance/queryCommonParameterType');
const getDetailReq = await useGet('/api/compliance/getPlatCommonDetail');
const deleteCommonParameterReq = await useGet('/api/compliance/deletePlatCommon');
const getPlatCommonListReq = await usePost<TableData<ParameterBaseType>>('/api/compliance/getPlatCommonList');

const listModelRef = reactive<{
  belongBusinessDepartmentNo: string;
  thirdPartyPlatformNo?: string;
  parameterClassNo?: string;
}>({
  belongBusinessDepartmentNo: '',
  thirdPartyPlatformNo: undefined,
  parameterClassNo: undefined,
});

const listRulesRef = ref({
  belongBusinessDepartmentNo: [{ required: true, message: '请选择机构' }],
  thirdPartyPlatformNo: [{ required: true, message: '请选择平台名称' }],
});

const { resetFields, validate, validateInfos } = Form.useForm(listModelRef, listRulesRef);

const dataSource = ref<ParameterBaseType[]>([]);
const loading = ref(false);

// 重置
const handleReset = () => {
  resetFields();
};
// 查询
const handleQuery = (refreshFlag?: boolean) => {
  const params = {
    belongBusinessDepartmentNo: listModelRef.belongBusinessDepartmentNo,
    thirdPartyPlatformNo: listModelRef.thirdPartyPlatformNo,
    parameterClassNo: listModelRef.parameterClassNo,
    pageSize: pagination.pageSize,
    pageNum: pagination.current,
  };
  validate().then(() => {
    loading.value = true;
    if (refreshFlag) {
      params.pageNum = 1;
    }
    getPlatCommonListReq.fetchData(params).then((res) => {
      if (res?.code === SUCCESS_CODE && Array.isArray(res?.data?.records)) {
        const { records, total = 0, current = 1, size = 10 } = res.data;
        dataSource.value = records.map(item => ({
          thirdPartyPlatformNo: item.thirdPartyPlatformNo,
          thirdPartyPlatformName: item.thirdPartyPlatformName,
          parameterClassNo: item.parameterClassNo,
          parameterClassName: item.parameterClassName,
          parameterCode: item.parameterCode,
          parameterName: item.parameterName,
          idParmThrdPtyPlatCommonParameter: item.idParmThrdPtyPlatCommonParameter || '',
        }));
        pagination.total = total;
        pagination.current = current;
        pagination.pageSize = size;
        // pagination.pages = pages;
      } else {
        dataSource.value = [];
        pagination.total = 0;
        pagination.current = 1;
        pagination.pageSize = 10;
        // pagination.pages = 0;
      }
    }).finally(() => { loading.value = false; });
  });
};

// 分页处理
const { pagination } = usePagination(handleQuery);

// 表格每列数据
const columns = [
  {
    title: '序号',
    dataIndex: 'order',
    width: '5%',
  },
  {
    title: '平台名称',
    dataIndex: 'thirdPartyPlatformName',
  },
  {
    title: '类型代码',
    dataIndex: 'parameterClassNo',
    width: '22%',
  },
  {
    title: '类型名称',
    dataIndex: 'parameterClassName',
  },
  {
    title: '代码',
    dataIndex: 'parameterCode',
    width: '10%',
  },
  {
    title: '代码名称',
    dataIndex: 'parameterName',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '20%',
  },
];

const modelTitle = ref('');
const modelType = ref('');
const editRef = ref();

// 增
const handleAdd = (): void => {
  modelTitle.value = '新增';
  modelType.value = 'add';
  editRef.value.setForm({
    thirdPartyPlatformNo: listModelRef.thirdPartyPlatformNo,
    parameterClassNo: listModelRef.parameterClassNo,
  });
  editRef.value.show();
};

// 改
const handleUpdate = (record: ParameterBaseType) => {
  modelTitle.value = '修改';
  modelType.value = 'update';
  editRef.value.setForm(record);
  editRef.value.show();
};

// 查
const handleView = async (id: string): Promise<void> => {
  const { code, data } = await getDetailReq.fetchData({ idParmThrdPtyPlatCommonParameter: id }) || {};
  if (code === SUCCESS_CODE) {
    modelTitle.value = '查看';
    modelType.value = 'view';
    editRef.value.setForm(data);
    editRef.value.show();
  }
};

// 删
const handleDelete = (id: string) => {
  Modal.confirm({
    title: '温馨提示',
    content: '删除后，平台数据将不可使用，请谨慎操作！',
    class: 'bell-bg',
    centered: true,
    onOk: async () => {
      const { code, msg = '' } = await deleteCommonParameterReq.fetchData({ idParmThrdPtyPlatCommonParameter: id }) || {};
      if (code === SUCCESS_CODE) {
        message.success(msg);
        setTimeout(() => handleQuery(true), 0);
      } else {
        message.error(msg);
      }
    },
  });
};

// 下载模版
const handleDownloadModel = async () => {
  const { xPortalToken } = useRuntimeConfig().public || {};
  await $fetch('/gateway/icore-agr-an.compliance/platCommonParameter/downPlatCommonParameterTemplate', {
    method: 'get',
    headers: {
      'X-Portal-Token': xPortalToken,
      'Content-Type': 'application/json',
    },
    query: { thirdPartyPlatformNo: listModelRef.thirdPartyPlatformNo },
    async onResponse({ response }) {
      let fileName = '';
      const contentDisposition = response.headers.get('Content-Disposition');
      const fileData = response._data;
      const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
      if (contentDisposition) {
        const match = contentDisposition.match(/filename=(.+)/);
        if (match) {
          fileName = decodeURI(match[1]);
        }
      }
      downloadBlob(fileData, fileName, fileType);
    },
  });
};

// 上传
const fileList = ref([]);
const uploadRef = ref();
const handleBeforeChange = () => false;
const handleUpload = async (e: { file: File; fileList: UploadListType }) => {
  const { file } = e;
  const formData = new FormData();
  formData.append('file', file);
  const { code, msg = '' } = await $postOnClient('/api/compliance/uploadPlatCommonParameter', formData) || {};
  if (code === SUCCESS_CODE) {
    message.success('上传成功');
  } else {
    message.error(msg);
  }
};

// 页面初始化标识
const initFlag = ref(false);

// 导出
const handleExport = async () => {
  const { xPortalToken } = useRuntimeConfig().public || {};
  await $fetch('/gateway/icore-agr-an.compliance/platCommonParameter/downPlatCommonParameter', {
    method: 'get',
    headers: {
      'X-Portal-Token': xPortalToken,
      'Content-Type': 'application/json',
    },
    query: {
      thirdPartyPlatformNo: listModelRef.thirdPartyPlatformNo,
      parameterClassNo: listModelRef.parameterClassNo,
    },
    async onResponse({ response }) {
      let fileName = '';
      const contentDisposition = response.headers.get('Content-Disposition');
      const fileData = response._data;
      const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
      if (contentDisposition) {
        const match = contentDisposition.match(/filename=(.+)/);
        if (match) {
          fileName = decodeURI(match[1]);
        }
      }
      downloadBlob(fileData, fileName, fileType);
    },
  });
};

watchEffect(async () => {
  platformNameList.value = [];
  listModelRef.thirdPartyPlatformNo = undefined;
  if (listModelRef.belongBusinessDepartmentNo) {
    getPlatformCodeNameListReq.fetchData({ belongBusinessDepartmentNo: listModelRef.belongBusinessDepartmentNo }).then((res) => {
      const { code, data } = res || {};
      if (code === SUCCESS_CODE) {
        platformNameList.value = data!;
        if (Array.isArray(data) && data[0]) {
          listModelRef.thirdPartyPlatformNo = data[0].thirdPartyPlatformNo;
        }
      }
    });
  }
});

watchEffect(async () => {
  parameterTypeList.value = [];
  listModelRef.parameterClassNo = undefined;
  if (listModelRef.thirdPartyPlatformNo) {
    const { code, data } = await getCommonParameterTypeReq.fetchData({ thirdPartyPlatformNo: listModelRef.thirdPartyPlatformNo }) || {};
    if (code === SUCCESS_CODE && Array.isArray(data)) {
      parameterTypeList.value = data.map(item => ({
        parameterClassNo: item.parameterClassNo,
        parameterClassName: item.parameterClassName,
      }));
    }
  }
});

// 首次进来自动查询列表
watchEffect(() => {
  if (listModelRef.thirdPartyPlatformNo && !initFlag.value) {
    handleQuery(true);
    initFlag.value = true;
  }
});
</script>
