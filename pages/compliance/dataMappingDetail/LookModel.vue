<template>
  <a-modal v-model:open="visible" wrap-class-name="look-model" title="业务代码映射配置" @ok="handleOk">
    <div class="data-mapping-detail-model">
      <div class="top-warp">
        <div class="row-one text-style">类型代码：{{ typeCode }}</div>
        <div class="text-style">类型名称：{{ typeName }}</div>
      </div>
      <!-- 一行 -->
      <div class="warp">
        <!-- 左 -->
        <div class="my-co">
          <div class="co-title">我司</div>
          <div class="row">
            <div class="co-name">业务名称：{{ pinganNo }}-{{ platName }} </div>
            <div class="right-icon">
              <div class="icon-title">映射</div>
              <div class="jt-icon">
                <!-- > -->
                <VueIcon class="jt" :icon="IconChevronRightFont" />
              </div>
            </div>
          </div>
        </div>
        <!-- 右 -->
        <div class="pingtai">
          <div class="pt-title">平台</div>
          <div class="pt-content">
            <div class="label-text">名称</div>
            <!-- <a-select :size="size" :options="platOptions"></a-select> -->
            <a-select v-model:value="selectV" show-search allow-clear :filter-option="handleSearch" @change="handleChange">
              <a-select-option v-for="(v, i) in platOptions" :key="i" :value="v.value" :label="v.label">
                {{ v.label }}
              </a-select-option>
            </a-select>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { IconChevronRightFont } from '@pafe/icons-icore-agr-an';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { usePost } from '@/composables/request';
import type { DetailMappingConfigColumn, SelectOptionsType, PlatformRes } from '@/apiTypes/compliance';

const show = (): void => {
  visible.value = true;
};

defineExpose({
  show,
});

const addPlatMapingConfigReq = await usePost('/gateway/icore-agr-an.compliance/mapingConfig/platMapingConfig');
const queryPlatCommonParameterListByParamsReq = await usePost<PlatformRes[]>('/gateway/icore-agr-an.compliance/platCommonParameter/queryPlatCommonParameterListByParams');
const queryPlatMapingConfigReq = await usePost('/gateway/icore-agr-an.compliance/mapingConfig/queryPlatMapingConfig');

const props = withDefaults(defineProps<{
  onSearch: () => void;
  dataMapDetailRow: DetailMappingConfigColumn;
}>(), {
});

const visible = ref(false);
const parameterCodeV = ref('');
const route = useRoute();

const handleOk = (): void => {
  const { effectiveTime, idParmComnParmPaAndThrdPtyPlatMapping, invalidTime, parameterClassNo, pinganDataValueNo } = props.dataMapDetailRow;
  addPlatMapingConfigReq.fetchData({
    effectiveTime: effectiveTime || null,
    idParmComnParmPaAndThrdPtyPlatMapping: idParmComnParmPaAndThrdPtyPlatMapping || null,
    invalidTime: invalidTime || null,
    parameterClassNo: parameterClassNo || '',
    pinganDataValueNo: pinganDataValueNo || '',
    platformDataValueNo: parameterCodeV.value || '',
    thirdPartyPlatformNo: route.query.thirdPartyPlatformNo || '',
  }).then((res) => {
    const { code, msg } = res || {};
    if (SUCCESS_CODE === code) {
      message.success('保存成功');
      props.onSearch();
      selectV.value = '';
      platOptions.value = [];
      visible.value = false;
    } else {
      message.error(msg || '');
    }
  });
};

const handleSearch = (val: string, options: { label: string }) => options.label.includes(val);

const platOptions = ref<SelectOptionsType[]>([]);

const selectV = ref<string>('');

const getPlatSelectList = () => {
  const { parameterClassNo, thirdPartyPlatformNo } = props.dataMapDetailRow;
  // 获取平台下拉列表
  queryPlatCommonParameterListByParamsReq.fetchData({
    parameterClassNo: parameterClassNo || '',
    thirdPartyPlatformNo: thirdPartyPlatformNo || '',
  })
    .then((res) => {
      if (res?.code === SUCCESS_CODE) {
        const platOptionsArr: SelectOptionsType[] = [];
        if (res?.data?.length) {
          res.data.forEach((r) => {
            platOptionsArr.push({
              value: r.parameterCode + '&' + r.thirdPartyPlatformNo,
              label: r.parameterName,
            });
          });
          platOptions.value = platOptionsArr;
        }
      }
    });
};

// 平台下拉框选择
const handleChange = (v: string) => {
  if (v) {
    const vArr = v.split('&');
    parameterCodeV.value = vArr[0];
  } else {
    parameterCodeV.value = '';
  }
};

const typeCode = ref<string>(''); // 类型代码
const typeName = ref<string>(''); // 类型名称
// 业务名称
const pinganNo = ref<string>('');
const platName = ref<string>('');

const queryDetail = () => {
  const { parameterClassNo, pinganDataValueNo, thirdPartyPlatformNo } = props.dataMapDetailRow;
  queryPlatMapingConfigReq.fetchData({
    parameterClassNo: parameterClassNo || '',
    pinganDataValueNo: pinganDataValueNo || '',
    thirdPartyPlatformNo: thirdPartyPlatformNo || '',
  })
    .then((res) => {
      if (res?.code === SUCCESS_CODE) {
        const { parameterClassNo, parameterClassName, pinganDataValueNo, pinganDataValueName, platformDataValueName } = res.data;
        typeCode.value = parameterClassNo;
        typeName.value = parameterClassName;
        pinganNo.value = pinganDataValueNo;
        platName.value = pinganDataValueName;
        selectV.value = platformDataValueName;
      }
    });
};

watch(visible, (newV: boolean) => {
  if (newV) {
    // 展示明细
    queryDetail();

    // 展示平台下拉列表
    getPlatSelectList();
  }
});
</script>

<style lang="less">
@bgColor: #f8f8f8;
@color: #333;
@titlebgColor: #F2F3F5;

.ant-modal-root .ant-modal-wrap.look-model {
  width: 697px;
  margin: auto;
  overflow: hidden;

  .ant-modal {
    width: 100% !important;

    .ant-modal-header {
      height: 48px;
      font-size: 16px;
      padding: 0 20px;
      display: flex;
      align-items: center;
      background: @titlebgColor;
      border-radius: 0;
      /* 优化 */
      margin-bottom: 0;
    }

    .ant-modal-close {
      top: 14px;
      right: 20px;

      .ant-modal-close-x {
        font-size: 12px;
      }
    }

    .ant-modal-body {
      // height: 570px;
      overflow-y: auto;
      padding: 0 20px;
    }

    .ant-modal-content {
      padding: 0;
      border-radius: 0;
    }

    .ant-modal-footer {
      display: flex;
      height: 72px;
      align-items: center;
      justify-content: flex-end;
      padding: 0 20px;
      position: sticky;
      bottom: 0;
    }
  }

  .title {
    color: @color;
    font-size: 14px;
    margin-bottom: 9px;
  }

  .content {
    background-color: @bgColor;
    border-radius: 4px;
    padding: 13px;
    margin-bottom: 15px;
  }

  .data-mapping-detail-model {
    margin-top: 16px;

    .text-style {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.90);
      letter-spacing: 0;
      font-weight: 400;
    }

    .top-warp {
      background: #F8F8F8;
      border-radius: 4px;
      padding-left: 12px;
      padding-top: 12px;
      padding-bottom: 16px;

      .row-one {
        margin-bottom: 16px;
      }

    }

    .warp {
      display: flex;
      margin-top: 16px;

      .my-co {

        .co-title {
          font-size: 14px;
          color: #333333;
          letter-spacing: 0;
          font-weight: 500;
          margin-bottom: 13px;
        }

        .row {
          display: flex;

          .co-name {
            width: 255px;
            background: #F8F8F8;
            border-radius: 4px;
            padding-top: 21px;
            padding-bottom: 21px;
            padding-left: 14px;
            padding-right: 10px;

            font-size: 14px;
            color: rgba(0, 0, 0, 0.55);
            letter-spacing: 0;
            font-weight: 400;
          }

          .right-icon {
            margin-left: 10px;

            .icon-title {
              width: 28px;
              height: 22px;
              font-size: 14px;
              color: rgba(0, 0, 0, 0.55);
              letter-spacing: 0;
              line-height: 22px;
              font-weight: 400;
              margin-bottom: 3px;
            }

            .jt-icon {
              width: 32px;
              height: 32px;
              line-height: 32px;
              text-align: center;
              background: #F2F3F5;
              border: 1px solid #D4D6D9;
              border-radius: 4px;
              font-size: 8.92px;
              color: rgba(0, 0, 0, 0.55);

              .jt {
                width: 8.92px;
                height: 5.38px;
              }
            }
          }
        }
      }

      .pingtai {
        margin-left: 9px;

        .pt-title {
          font-size: 14px;
          color: #333333;
          letter-spacing: 0;
          font-weight: 500;
          margin-bottom: 13px;
        }

        .pt-content {

          display: flex;
          padding-left: 21px;
          padding-top: 15px;
          padding-bottom: 17px;
          padding-right: 14px;
          background: #F8F8F8;
          border-radius: 4px;
          width: 353px;

          .label-text {
            margin-right: 10px;
            width: 45px;
            height: 32px;
            display: block;
            line-height: 32px;

            font-size: 14px;
            color: rgba(0, 0, 0, 0.55);
            letter-spacing: 0;
            font-weight: 400;
          }

          .ant-select {
            width: calc(100% - 45px);
          }
        }

      }

    }
  }
}
</style>
