<template>
  <div class="mapping-detail">
    <div class="mapping-detail-head">
      <a-form :colon="false">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="代码类型" :rules="[{ required: true }]">
              <a-input v-model:value="parameterClassNo" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="类型名称" :rules="[{ required: true }]">
              <a-input v-model:value="parameterClassName" disabled />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="mapping-detail-content">
      <div class="mapping-detail-content-title">
        <div class="flex items-center space-x-[12px]">
          <span class="text-[16px] text-[rgba(0,0,0,0.8)] font-semibold">明细映射配置清单</span>
          <VueIcon :icon="IconErrorCircleFilledFont" class="icon-i" @mouseenter="enterM" @mouseleave="leaveM" />
          <span v-if="wxtsFlag" class="wxts" />
          <span v-else class="wxts">温馨提示：请为映射为空的业务进行配置</span>
        </div>
        <a-space>
          <a-button @click="downloadFile">模版下载</a-button>
          <a-upload ref="uploadRef" v-model:file-list="fileList" :show-upload-list="false" action="" :before-upload="handleBeforeChange" @change="handleUpload">
            <a-button type="primary">上传</a-button>
          </a-upload>
        </a-space>
      </div>
      <a-table
        :data-source="dataSource"
        :columns="columns"
        :loading="loading"
        :bordered="false"
        :scroll="{ x: 'max-content' }"
        :row-class-name="(_record: unknown, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
        :pagination="pagination"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'order'">
            {{ index + 1 }}
          </template>
          <template v-if="column.key === 'operation'">
            <a-button type="link" size="small" @click="handleLook(record as DetailMappingConfigColumn)">
              映射配置
            </a-button>
            <a-button
              v-if="record?.idParmComnParmPaAndThrdPtyPlatMapping"
              type="link"
              size="small"
              @click="handleDelete(record?.idParmComnParmPaAndThrdPtyPlatMapping)"
            >
              删除
            </a-button>
          </template>
        </template>
      </a-table>
    </div>
    <LookModel ref="looRef" :on-search="submit" :data-map-detail-row="dataMapDetailRow" />
  </div>
</template>

<script setup lang="ts">
import { Modal } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { UploadListType } from 'ant-design-vue/es/upload/interface';
import LookModel from './LookModel.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { downloadBlob, pxToRem } from '@/utils/tools';
import { usePagination } from '@/composables/usePagination';
import type { DetailMappingConfigColumn } from '@/apiTypes/compliance';
import type { TableData } from '@/apiTypes/interface';
import { usePost, $getOnClient, useGet, $postOnClient } from '@/composables/request';
import { message } from '@/components/ui/Message';

const getParamCodeListReq = await usePost<TableData<DetailMappingConfigColumn>>('/gateway/icore-agr-an.compliance/mapingConfig/queryParamCodeList');
const downPlatMapingConfigReq = await useGet('/gateway/icore-agr-an.compliance/mapingConfig/downPlatMapingConfig', { onResponse({ response }) {
  let fileName = '';
  const contentDisposition = response.headers.get('Content-Disposition');
  const fileData = response._data;
  const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
  if (contentDisposition) {
    const match = contentDisposition.match(/filename=(.+)/);
    if (match) {
      fileName = decodeURI(match[1]);
    }
  }
  downloadBlob(fileData, fileName, fileType);
} });

const route = useRoute();

const parameterClassNo = computed(() => route.query?.parameterClassNo);
const parameterClassName = computed(() => route.query?.parameterClassName);

const looRef = ref();

// 上传
const fileList = ref([]);
const uploadRef = ref();
const handleBeforeChange = () => false;
const handleUpload = async (e: { file: File; fileList: UploadListType }) => {
  const { file } = e;
  const formData = new FormData();
  formData.append('file', file);
  const { code, msg } = await $postOnClient('/api/compliance/uploadPlatMapingConfig', formData) || {};
  if (code === SUCCESS_CODE) {
    message.success('上传成功');
  } else {
    message.error(msg || '');
  }
};

const dataMapDetailRow = ref({});
const handleLook = async (record: DetailMappingConfigColumn) => {
  dataMapDetailRow.value = record;
  looRef.value.show();
};

const dataSource = ref<DetailMappingConfigColumn[]>([]);
const loading = ref(false);

const refresh = () => {
  const {
    belongBusinessDepartmentNo,
    parameterClassNo,
    thirdPartyPlatformNo,
  } = route.query || {};
  loading.value = true;
  getParamCodeListReq.fetchData({
    belongBusinessDepartmentNo,
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
    parameterClassNo,
    thirdPartyPlatformNo,
  }).then((res) => {
    if (res?.code === SUCCESS_CODE && Array.isArray(res?.data?.records)) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data;
      dataSource.value = records.map(item => ({
        pinganDataValueNo: item.pinganDataValueNo,
        pinganDataValueName: item.pinganDataValueName,
        platformDataValueNo: item.platformDataValueNo,
        platformDataValueName: item.platformDataValueName,
        updatedDate: item.updatedDate,
        effectiveTime: item.effectiveTime,
        invalidTime: item.invalidTime,
        parameterClassName: item.parameterClassName,
        parameterClassNo: item.parameterClassNo,
        thirdPartyPlatformNo: item.thirdPartyPlatformNo,
        idParmComnParmPaAndThrdPtyPlatMapping: item.idParmComnParmPaAndThrdPtyPlatMapping,
      }));
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
      // pagination.pages = pages;
    }
  }).finally(() => {
    loading.value = false;
  });
};

const submit = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
};

const { pagination } = usePagination(refresh);

// 下载模版
const downloadFile = async () => {
  await downPlatMapingConfigReq.fetchData({
    thirdPartyPlatformNo: route.query?.thirdPartyPlatformNo as string,
    parameterClassNo: route.query?.parameterClassNo as string,
  });
};

await submit();
onActivated(() => {
  submit();
});

const handleDelete = (id: string) => {
  Modal.confirm({
    title: '温馨提示',
    content: '删除后，数据映射配置将不可使用，请谨慎操作！',
    class: 'bell-bg',
    centered: true,
    onOk() {
      $getOnClient('/gateway/icore-agr-an.compliance/mapingConfig/deletePlatMapingConfig', { idParmComnParmPaAndThrdPtyPlatMapping: id }).then((res) => {
        const { code, msg } = res || {};
        if (SUCCESS_CODE === code) {
          message.success('删除成功');
          submit();
        } else {
          message.error(msg || '');
        }
      });
    },
    onCancel() {
      console.log('Cancel');
    },
  });
};
const wxtsFlag = ref(true);
const enterM = () => {
  wxtsFlag.value = !wxtsFlag.value;
};
const leaveM = () => {
  wxtsFlag.value = !wxtsFlag.value;
};

const columns = [
  {
    title: '序号',
    dataIndex: 'order',
    width: pxToRem(60),
  },
  {
    title: '我司业务代码',
    key: 'pinganDataValueNo',
    dataIndex: 'pinganDataValueNo',
  },
  {
    title: '我司业务名称',
    key: 'pinganDataValueName',
    dataIndex: 'pinganDataValueName',
  },
  {
    title: '平台映射代码',
    key: 'platformDataValueNo',
    dataIndex: 'platformDataValueNo',
  },
  {
    title: '平台映射名称',
    key: 'platformDataValueName',
    dataIndex: 'platformDataValueName',
  },
  {
    title: '更新时间',
    key: 'updatedDate',
    dataIndex: 'updatedDate',
  },
  {
    title: '生效日期',
    key: 'effectiveTime',
    dataIndex: 'effectiveTime',
  },
  {
    title: '失效日期',
    key: 'invalidTime',
    dataIndex: 'invalidTime',
  },
  {
    title: '操作',
    width: 230,
    key: 'operation',
    fixed: 'right',
  },
];
</script>

<style lang="less" scoped>
@bgColor: #ffffff;
.mapping-detail {
  height: calc(100vh - 40px);
  overflow-y: scroll;
  :deep(.icon-i) {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.55);
  }

  :deep(.wxts) {
    margin-left: 9px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.55);
    letter-spacing: 0;
    font-weight: 400;
  }

  &-head {
    margin: 14px;
    padding: 16px 14px;
    background: @bgColor;
    border-radius: 6px;
    overflow: hidden;
    .expand-area {
      cursor: pointer;
      display: flex;
      align-items: center;
      height: 32px;
      font-size: 12px;
      margin-left: 24px;
      color: #262626;
    }
  }
 &-content {
    margin: 14px;
    padding: 0 16px;
    background: @bgColor;
    border-radius: 6px;
    &-title {
      height: 64px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
