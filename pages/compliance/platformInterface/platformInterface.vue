<template>
  <div class="cus-tainer">
    <div class="search-wrapper mx-[14px] my-[12px] bg-white overflow-hidden min-h-[64px] rounded-[6px]">
      <div :class="['search-form px-[16px] pt-[16px] no-operation']">
        <a-form :label-col="{ style: { width: pxToRem(80) } }" :colon="false">
          <a-row :gutter="24">
            <a-col span="8">
              <a-form-item label="机构" name="belongBusinessDepartmentNo" v-bind="form.validateInfos.belongBusinessDepartmentNo">
                <DeptListSelect :dept-code="modalRef.belongBusinessDepartmentNo" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
            <a-col span="8">
              <a-form-item label="平台名称" name="idParmThrdPtyPlatBase" v-bind="form.validateInfos.idParmThrdPtyPlatBase">
                <a-select
                  v-model:value="modalRef.idParmThrdPtyPlatBase"
                  show-search
                  allow-clear
                  placeholder="请选择"
                  :filter-option="(val: string, options: PlatformCodeName) => options.platformCodeName.includes(val)"
                  :field-names="{ label: 'platformCodeName', value: 'idParmThrdPtyPlatBase' }"
                  :options="platformNameList"
                />
              </a-form-item>
            </a-col>
            <a-col span="8">
              <a-form-item label="接口名称" name="idParmThrdPtyPlatInterface" v-bind="form.validateInfos.idParmThrdPtyPlatInterface">
                <a-select
                  v-model:value="modalRef.idParmThrdPtyPlatInterface"
                  show-search
                  allow-clear
                  placeholder="请选择"
                  :filter-option="(val: string, options: InterfaceCodeName) => options.thirdPartyPlatformInterfaceName.includes(val)"
                  :field-names="{ label: 'thirdPartyPlatformInterfaceName', value: 'idParmThrdPtyPlatInterface' }"
                  :options="interfaceNameList"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="flex justify-center px-[16px] pb-[16px]">
        <a-button class="mr-[12px]" @click="onReset">重置</a-button>
        <a-button type="primary" ghost @click="onSearch">查询</a-button>
      </div>
    </div>
    <div class="px-[14px] py[12px]">
      <div class="flex h-full overflow-hidden">
        <a-card class="w-full h-full rounded-md" :body-style="{ overflow: 'auto' }" :bordered="false">
          <template #title>
            <div class="flex items-center justify-between">
              <span>接口基本信息配置</span>
              <a-button type="primary" @click="handleAdd">新增</a-button>
            </div>
          </template>
          <div class="cus-table">
            <a-table
              :scroll="{ x: '10.5rem', y: '50vh' }"
              :data-source="dataSource"
              :columns="columns"
              :loading="loading"
              :pagination="pagination"
              :expand-column-width="100"
              :row-class-name="(_: unknown, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
              :row-key="(dataIndex: number) => dataIndex + 1"
            >
              <template #bodyCell="{ column, record, text }">
                <template v-if="['departmentNoAndName', 'thirdPartyPlatformName', 'thirdPartyPlatformInterfaceName', 'thirdPartyPlatformInterfaceNo', 'invalidTime'].includes(column.dataIndex as string)">
                  <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
                    <div class="max-w-[200px] truncate">{{ text }}</div>
                  </a-tooltip>
                </template>
                <template v-if="column.dataIndex === 'operation'">
                  <a-button type="link" size="small" @click="handleLook(record.idParmThrdPtyPlatInterface)">查看</a-button>
                  <a-button type="link" size="small" @click="handleUpdate(record.idParmThrdPtyPlatInterface)">修改</a-button>
                  <a-button type="link" size="small" @click="handleDelete(record.idParmThrdPtyPlatInterface)">删除</a-button>
                  <a-button v-if="record.parmThrdPtyPlatItfcReqtMsgTmplDto" type="link" size="small" @click="handleLookMsg(record.parmThrdPtyPlatItfcReqtMsgTmplDto)">报文样例查看</a-button>
                </template>
              </template>
            </a-table>
          </div>
        </a-card>
        <EditModal :id="editId" v-model:visible="editVisible" :type="editType" :dept-code="modalRef.belongBusinessDepartmentNo" @on-ok="handleOk" />
        <LookModal :id="lookId" v-model:visible="lookVisible" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Modal, Form, type TableProps } from 'ant-design-vue';
import { debounce } from 'lodash-es';
import LookModal from './lookModal.vue';
import EditModal from './editModal.vue';
import DeptListSelect from '@/pages/compliance/components/DeptListSelect.vue';
import type { TableData } from '@/apiTypes/interface';
import type { InterfaceBaseType, InterfaceCodeName, PlatformCodeName, DepartmentNoType, GetInterfaceListParams } from '@/apiTypes/compliance';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePagination } from '@/composables/usePagination';
import { downloadBlob, pxToRem } from '@/utils/tools';
import { $get, $post } from '@/utils/request';
import { message } from '@/components/ui/Message';

const changeDeptCode = (val: string) => {
  modalRef.belongBusinessDepartmentNo = val;
};

const handleLookMsg = async (templateInfo: { requestMessageTemplateFileAddress: string; requestMessageTemplateFileName: string }) => {
  if (!templateInfo) {
    message.info('无数据');
    return;
  }
  const { requestMessageTemplateFileName } = templateInfo;
  getDownTemplate(requestMessageTemplateFileName);
};
const getDownTemplate = debounce(async (documentName: string) => {
  await $get('/gateway/icore-agr-an.compliance/pageConfig/interface/downTemplate', { documentName }, {
    async onResponse({ response }) {
      let fileName = '';
      const contentDisposition = response.headers.get('Content-Disposition');
      const fileData = response._data;
      const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
      if (contentDisposition) {
        const match = contentDisposition.match(/filename=(.+)/);
        if (match) {
          fileName = decodeURI(match[1]);
        }
      }
      downloadBlob(fileData, fileName, fileType);
    },
  });
}, 1000);

const modalRef: Partial<searchType> = reactive<Partial<searchType>>({
  belongBusinessDepartmentNo: undefined,
  idParmThrdPtyPlatBase: undefined,
  idParmThrdPtyPlatInterface: undefined,
});
// 搜索的表单校验对象
const rulesRef = reactive({
  belongBusinessDepartmentNo: [
    {
      required: true,
      message: '请选择机构',
    },
  ],
  idParmThrdPtyPlatBase: [
    {
      required: true,
      message: '请选择平台名称',
    },
  ],
});
const form = Form.useForm(modalRef, rulesRef);
const loading = ref(false);
const dataSource = ref<InterfaceBaseType[]>([]);
const getTableData = async () => {
  try {
    loading.value = true;
    const { code, data } = await $post<TableData<InterfaceBaseType>, GetInterfaceListParams>('/api/compliance/queryInterfaceList', { ...modalRef, pageNum: pagination.current!, pageSize: pagination.pageSize! }, { canCancel: true });
    if (code === SUCCESS_CODE && data) {
      const { records, total = 0, current = 1, size = 10 } = data;
      dataSource.value = records;
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
      // pagination.pages = pages;
    } else {
      dataSource.value = [];
      pagination.total = 0;
      pagination.current = 1;
      pagination.pageSize = 10;
      // pagination.pages = 0;
    }
    loading.value = false;
  } catch (e) {
    loading.value = false;
    console.log(e);
  }
};
// 分页处理
const { pagination } = usePagination(getTableData);

// 查询表格数据
const onSearch = () => {
  pagination.current = 1;
  const { validate } = form;
  validate().then(() => {
    getTableData();
  });
};

// 重置表单
const onReset = () => {
  pagination.current = 1;
  form?.resetFields();
};
const platformNameList = ref<Array<PlatformCodeName>>([]);
const interfaceNameList = ref<Array<InterfaceCodeName>>([]);

// 表格展示配置
const columns: TableProps['columns'] = [
  {
    title: '序号',
    customRender: ({ index }: { index: number }) => index + 1,
    width: 80,
  },
  {
    title: '归属机构',
    dataIndex: 'departmentNoAndName',
    width: pxToRem(150),
  },
  {
    title: '所属平台',
    dataIndex: 'thirdPartyPlatformName',
    width: pxToRem(200),
  },
  {
    title: '接口名称',
    dataIndex: 'thirdPartyPlatformInterfaceName',
    width: pxToRem(180),
  },
  {
    title: '接口代码',
    dataIndex: 'thirdPartyPlatformInterfaceNo',
    width: pxToRem(180),
  },
  {
    title: '最新更新时间',
    dataIndex: 'updatedDate',
    width: pxToRem(180),
  },
  {
    title: '生效日期',
    dataIndex: 'effectiveTime',
    width: pxToRem(180),
  },
  {
    title: '失效日期',
    dataIndex: 'invalidTime',
    width: pxToRem(180),
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: pxToRem(250),
    fixed: 'right',
  },
];
interface searchType {
  [key: string]: string;
  belongBusinessDepartmentNo: string;
  idParmThrdPtyPlatBase: string;
  idParmThrdPtyPlatInterface: string;
}

const handleOk = () => {
  editVisible.value = false;
  onSearch();
};

const editVisible = ref(false);
const editId = ref('');
const editType = ref('');
const lookVisible = ref(false);
const lookId = ref('');

// 新增
const handleAdd = () => {
  editVisible.value = true;
  editId.value = '';
  editType.value = 'add';
};

// 编辑
const handleUpdate = (id: string) => {
  if (id) {
    editVisible.value = true;
    editId.value = id;
    editType.value = 'edit';
  } else {
    message.error('请检查数据是否正确');
  }
};

// 查看
const handleLook = (id: string) => {
  if (id) {
    lookVisible.value = true;
    lookId.value = id;
  } else {
    message.error('请检查数据是否正确');
  }
};

// 删除
const handleDelete = (id: string) => {
  Modal.confirm({
    title: '温馨提示',
    content: '删除后，接口信息将不可使用，请谨慎操作！',
    class: 'bell-bg',
    centered: true,
    onOk() {
      $get('/api/compliance/deleteInterface', { id }).then((res) => {
        const { code, msg = '' } = res || {};
        if (SUCCESS_CODE === code) {
          message.success(msg);
          onSearch();
        } else {
          message.error(msg);
        }
      });
    },
    onCancel() {
      console.log('Cancel');
    },
  });
};

// 页面初始化标识
const initFlag = ref(false);

watchEffect(async () => {
  platformNameList.value = [];
  modalRef.idParmThrdPtyPlatBase = '';
  if (modalRef.belongBusinessDepartmentNo) {
    const { data, code } = await $post<PlatformCodeName[], DepartmentNoType>('/api/compliance/listPlatformCodeName', { belongBusinessDepartmentNo: modalRef.belongBusinessDepartmentNo });
    if (SUCCESS_CODE === code && data) {
      platformNameList.value = data;
      if (platformNameList.value?.[0]) {
        modalRef.idParmThrdPtyPlatBase = platformNameList.value[0].idParmThrdPtyPlatBase;
      }
    }
  }
});

watchEffect(async () => {
  interfaceNameList.value = [];
  modalRef.idParmThrdPtyPlatInterface = '';
  if (modalRef.idParmThrdPtyPlatBase) {
    const { data, code } = await $post<Array<InterfaceCodeName>, { idParmThrdPtyPlatBase: string }>('/api/compliance/listInterfaceCodeName', { idParmThrdPtyPlatBase: modalRef.idParmThrdPtyPlatBase }) || {};
    if (SUCCESS_CODE === code && data) {
      interfaceNameList.value = data;
    }
  }
});

watchEffect(() => {
  if (modalRef.idParmThrdPtyPlatBase && !initFlag.value) {
    onSearch();
    initFlag.value = true;
  }
});
</script>
