<template>
  <div class="clear-cache-form mx-[14px] mt-[14px] bg-white p-[14px]">
    <div class="data-mapping-head">
      <a-form :colon="false">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item
              label="rediskey"
              name="rediskey"
            >
            <a-input v-model:value="modelRef.rediskey"></a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex justify-center">
        <a-button type="primary" ghost @click="submit" :loading="loading">清除</a-button>
      </div>
    </div>
    <a-button @click="sendSSE">发起sse{{j}}</a-button>

  </div>
</template>
<script setup lang="ts">
import { useGet } from '@/composables/request'
import { message } from '@/components/ui/Message'

const clearCache =  await useGet('/api/common/clearCache')
const loading = ref(false);

const modelRef = reactive({
  rediskey: undefined,
});

const submit = async () => {
  if (modelRef.rediskey) {
    loading.value = true;
    try {
      const res = await clearCache.fetchData({
        redisKey: modelRef.rediskey
      })
      loading.value = false;
      message.success(res.msg)
    } catch(err) {
      loading.value = false;
      message.error(err.msg  || err.message || '接口报错')

    }
  } else {
    message.error('请输入rediskey')
  }
  
}
const j = ref(0)
const sendSSE = () => {
  const eventSource = new EventSource(location.origin + '/api/downloadDemo/sseDemo')
    
  eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);
    j.value = JSON.parse(event.data).times;

    if (data.close) {
      // 客户端主动发起关闭请求
      eventSource.close();
    }
  }

  eventSource.onerror = (event) => {
    // 服务端发起了关闭请求，客户端同样需要执行关闭请求操作
    if (event.target.readyState === 0 || event.target.readyState === 2) {
      eventSource.close();
    }
    
  }
}
</script>