export interface SearchFormState {
  departmentCode: string; // 机构编码
  containLowDepartment?: boolean; // 是否包含下级机构
  lastRiskType: string; // 标的类型
  insureDate: [string, string]; // 保单生成日期
  riskCheckStatus: string[]; // 验标状态
  insuredName: string; // 被保险人名称
  createDate: [string, string]; // 创建日期
  policyNo: string; // 保单号
}
export interface DataType {
  records: Record<string, string>[];
  total: number;
  current: number;
  size: number;
}
export type Key = string | number; // 表格勾选回调数据类型
