<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-16px">
        <div class="leading-[32px] text-[16px] text-[rgba(0,0,0,0.9)] font-bold">历史批改记录</div>
      </div>
      <a-table :columns="listColumns" :pagination="pagination" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content' }">
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'endorseNo'">
            <CopyLink :text="text" @click="openEndorseDetail(text, record.endorseApplyNo)" />
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnsType } from 'ant-design-vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import CopyLink from '@/components/ui/CopyLink.vue';

const listColumns: TableColumnsType = [
  { title: '批单号', dataIndex: 'endorseNo' },
  { title: '投保人', dataIndex: 'applicantName' },
  { title: '场景', dataIndex: 'edrSceneName' },
  { title: '批改时间', dataIndex: 'applyDate' },
  { title: '生效时间', dataIndex: 'effectiveDate' },
  { title: '保费变化', dataIndex: 'actualPremiumChange' },
  { title: '处理人', dataIndex: 'applyBy' },
];
const { gateWay, service } = useRuntimeConfig().public || {};
const dataSource = ref<Record<string, string>[]>([]);
const loading = ref(false);
const route = useRoute();
interface DataType {
  records: Record<string, string>[];
  total: number;
  current: number;
  size: number;
}
const getListReq = await usePost<DataType>(`${gateWay}${service.endorse}/web/policy/endorseHis`);

// 列表分页查询
const refresh = async () => {
  loading.value = true;
  try {
    const params = {
      policyNo: route.query.policyNo || '',
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    const res = await getListReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data || [];
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      message.warning(res?.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const router = useRouter();
// 跳转批改信息详情页面 ENDORSE_APPLY_NO/ENDORSE_NO
const openEndorseDetail = (voucherNo: string, endorseApplyNo: string) => {
  router.push({
    path: '/approvalTraceCheck',
    query: {
      documentNo: voucherNo,
      documentType: voucherNo.slice(0, 1) === '3' ? 'ENDORSE_NO' : 'ENDORSE_APPLY_NO',
      t: new Date().getTime(),
      endorseApplyNo,
    },
  });
};
const { pagination } = usePagination(refresh);
onMounted(() => {
  refresh();
});
</script>
