<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-16px">
        <div class="leading-[32px] text-[16px] text-[rgba(0,0,0,0.9)] font-bold">理赔记录</div>
      </div>
      <a-table :columns="listColumns" :pagination="pagination" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content' }">
        <template #bodyCell="{ column, index }">
          <template v-if="column.dataIndex === 'order'">{{ index + 1 }}</template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnsType } from 'ant-design-vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';

const listColumns: TableColumnsType = [
  { title: '序号', dataIndex: 'order' },
  { title: '保单号', dataIndex: 'policyNo' },
  { title: '报案号', dataIndex: 'reportNo' },
  { title: '赔案号', dataIndex: 'caseNo' },
  { title: '被保险人', dataIndex: 'insuredName' },
  { title: '出险日期', dataIndex: 'accidentDate' },
  { title: '赔案状态', dataIndex: 'caseStatusName' },
  { title: '未决金额', dataIndex: 'unsettledAmount' },
  { title: '已决金额', dataIndex: 'totalAmount' },
];
const { gateWay, service } = useRuntimeConfig().public || {};
const dataSource = ref<Record<string, string>[]>([]);
const loading = ref(false);
const route = useRoute();
interface DataType {
  seachList: Record<string, string>[];
  totalCount: number;
  pageNum: number;
  pageSize: number;
}
const getListReq = await usePost<DataType>(`${gateWay}${service.policy}/web/policy/queryClaimCase`);

// 列表分页查询
const refresh = async () => {
  loading.value = true;
  try {
    const params = {
      policyNo: route.query.policyNo || '',
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    const res = await getListReq.fetchData({ claimQueryReqDTO: params });
    if (res && res.code === SUCCESS_CODE) {
      const { seachList = [], totalCount = 0, pageNum = 1, pageSize = 10 } = res.data || [];
      dataSource.value = seachList || [];
      pagination.total = totalCount;
      pagination.current = pageNum;
      pagination.pageSize = pageSize;
    } else {
      message.warning(res?.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const { pagination } = usePagination(refresh);
onMounted(() => {
  refresh();
});
</script>
