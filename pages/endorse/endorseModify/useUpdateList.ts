import { $getOnClient } from '@/composables/request';
import { downloadBlob } from '@/utils/tools';

export default function useUpdateList() {
  const route = useRoute();
  const modalVisible = ref(false);
  const openModal = () => {
    modalVisible.value = true;
  };
  const listInfo = computed(() => ({
    farmerlistNo: route.query.id as string,
    farmerlistName: route.query.farmerlistName as string,
  }));

  const { service } = useRuntimeConfig().public || {};
  const exporting = ref(false);
  const dataExport = async () => {
    exporting.value = true;
    await $getOnClient(
      `/gateway${service.farmer}/file/singleFarmerListExport`,
      {
        position: 3, // 批量导出固定传3
        farmerListNo: route.query.id || '',
      },
      {
        async onResponse({ response }) {
          let fileName = '';
          const contentDisposition = response.headers.get('Content-Disposition');
          const fileData = response._data;
          const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
          if (contentDisposition) {
            const match = contentDisposition.match(/filename=(.+)/);
            if (match) {
              fileName = decodeURI(match[1]);
            }
          }
          downloadBlob(fileData, fileName, fileType);
        },
      },
    );
    exporting.value = false;
  };
  return {
    modalVisible,
    openModal,
    listInfo,
    exporting,
    dataExport,
  };
}
