<template>
  <div class="flex justify-end mb-[10px]">
    <a-button type="primary" @click="showDrawer = true">添加特约</a-button>
  </div>
  <a-table v-if="specialData && specialData.length" :columns="columns" :data-source="specialData" :pagination="false">
    <template #bodyCell="{ column, text, record }">
      <template v-if="column.dataIndex === 'promiseType'">
        {{ text === 'N' ? '固定特约' : text === 'X' ? '半固定特约' : '自定义特约' }}
      </template>
      <template v-if="column.dataIndex === 'promiseDesc'">
        <template v-if="record.promiseCode === 'N' || record.promiseCode === 'F'">
          {{ text }}
        </template>
        <template v-else>
          <template v-for="item in record.promiseControlList">
            {{ item.value }}
          </template>
        </template>
      </template>
      <template v-if="column.dataIndex === 'operation'">
        <a-button class="p-0" type="link" @click="removeSpecial">删除</a-button>
      </template>
    </template>
  </a-table>
  <a-table v-else :columns="columns" :data-source="specialDataDefault" :pagination="false">
    <template #bodyCell="{ column }">
      <template v-if="column.dataIndex === 'promiseType'">通用特约</template>
    </template>
  </a-table>
  <a-drawer v-model:open="showDrawer" :body-style="{ padding: '0 20px' }" width="600" :closable="false">
    <div class="special-list">
      <a-tabs v-model:active-key="activeTab">
        <a-tab-pane v-for="item in specialTypeList" :key="item.value" :tab="item.label" :loading="loading">
          <template v-if="['N'].includes(activeTab)">
            <div class="flex justify-between mb-[18px]">
              <a-input v-model:value="keyWordN" allow-clear placeholder="请输入特约名称" />
              <a-button class="ml-[8px]" @click="refreshN">查询</a-button>
            </div>
            <a-radio-group v-if="totalsN > 0" v-model:value="selectedSpecialN">
              <!-- 报ts错误，item换成dom -->
              <template v-for="(dom, index) in specialListN" :key="index">
                <div class="block bg-[#F9FAFA] border border-solid border-[rgba(212,214,217,1)] rounded text-[14px] pt-[8px] px-[16px] pb-[16px] hover:border-[#07C160] mb-[12px]">
                  <a-radio :key="dom.idSpecialPromiseDefine" :value="dom.idSpecialPromiseDefine">
                    <span class="text-[rgba(0,0,0,0.90)] font-semibold break-all leading-[22px]">{{ dom.specialPromiseName }}</span>
                  </a-radio>
                  <p class="leading-[22px] mt-[12px] mb-[8px] text-[rgba(0,0,0,0.55)]">
                    {{ dom.specialPromiseCode }}
                  </p>
                  <p v-show="dom.display" class="my-[0] leading-[22px] text-[rgba(0,0,0,0.55)]">
                    {{ dom.specialPromiseDesc }}
                  </p>
                  <div class="flex cursor-pointer justify-between">
                    <p v-show="!dom.display" class="truncate my-[0] leading-[22px] text-[rgba(0,0,0,0.55)]">
                      {{ dom.specialPromiseDesc }}
                    </p>
                    <div v-show="dom.display" />
                    <div class="flex shrink-0 text-[#576B95] items-center" @click="showNDetail(dom)">
                      <VueIcon :class="['next-icon', 'mr-[4px]', !dom.display ? 'rotate-[-90deg]' : 'rotate-[90deg]', 'transition-transform']" :icon="IconChevronLeftDoubleFont" />
                      <span>查看详情</span>
                    </div>
                  </div>
                </div>
              </template>
            </a-radio-group>
            <a-empty v-else :image="simpleImage" />
            <a-pagination v-if="totalsN > 0" v-model:current="currentN" v-model:page-size="pageSizeN" class="flex justify-end" :total="totalsN" show-quick-jumper show-size-changer responsive size="small" @change="onChange" />
            <div class="h-[67px]" />
          </template>
          <template v-else-if="['X'].includes(activeTab)">
            <div class="flex justify-between mb-[18px]">
              <a-input v-model:value="keyWordX" allow-clear placeholder="请输入特约名称" />
              <a-button class="ml-[8px]" @click="refreshX">查询</a-button>
            </div>
            <a-radio-group v-if="totalsX > 0" v-model:value="selectedSpecialX">
              <template v-for="(it, index) in specialListX" :key="index">
                <div class="block bg-[#F9FAFA] border border-solid border-[rgba(212,214,217,1)] rounded text-[14px] pt-[8px] px-[16px] pb-[16px] hover:border-[#07C160] mb-[12px]">
                  <a-radio :key="it.idSpecialPromiseDefine" :value="it.idSpecialPromiseDefine">
                    <span class="text-[rgba(0,0,0,0.90)] font-semibold break-all leading-[22px]">{{ it.specialPromiseName }}</span>
                  </a-radio>
                  <p class="leading-[22px] mt-[12px] mb-[8px] text-[rgba(0,0,0,0.55)]">
                    {{ it.specialPromiseCode }}
                  </p>

                  <div v-show="it.display" class="flex flex-wrap items-center">
                    <template v-for="(xItem, xIndex) in it.xSpecialPromiseList" :key="xIndex">
                      <span v-if="xItem.type === 'F'" class="leading-[22px] text-[rgba(0,0,0,0.55)]">{{ xItem.fixedMsg }}</span>
                      <div v-else :class="[xItem.type === 'N' ? 'w-[280px]' : 'w-[150px]', 'mx-[4px]']">
                        <a-input v-model:value="xItem.value" :type="xItem.type === 'N' ? 'number' : 'text'" :placeholder="xItem.placeholder" class="customer-input" @blur="validateContent(xItem)">
                          <template #prefix>
                            <span v-if="xItem.type === 'N'" class="inline-block leading-[22px] text-[12px] bg-[#FFF9EB] rounded-[4px] text-[#E6AD1C] px-[1px]">数字</span>
                            <span v-else class="inline-block leading-[22px] text-[12px] bg-[#EAF1FF] rounded-[4px] text-[#576B95] px-[1px]">文本</span>
                          </template>
                        </a-input>
                        <div v-show="xItem.tips" class="text-[#fa5151] text-[14px]">
                          {{ xItem.tips }}
                        </div>
                      </div>
                    </template>
                  </div>
                  <div class="flex cursor-pointer justify-between">
                    <p v-show="!it.display" class="truncate my-[0] leading-[22px] text-[rgba(0,0,0,0.55)]">
                      {{ it.specialPromiseDesc }}
                    </p>
                    <div v-show="it.display" />
                    <div class="flex shrink-0 text-[#576B95] items-center" @click="viewDetail(it)">
                      <VueIcon :class="['next-icon', 'mr-[4px]', !it.display ? 'rotate-[-90deg]' : 'rotate-[90deg]', 'transition-transform']" :icon="IconChevronLeftDoubleFont" />
                      <span>查看详情</span>
                    </div>
                  </div>
                </div>
              </template>
            </a-radio-group>
            <a-empty v-else :image="simpleImage" />
            <a-pagination v-if="totalsX > 0" v-model:current="currentX" v-model:page-size="pageSizeX" class="flex justify-end" :total="totalsX" show-quick-jumper show-size-changer responsive size="small" @change="onChange" />
            <div class="h-[67px]" />
          </template>
          <template v-else>
            <div v-if="customSpecialCode">
              <a-textarea v-model:value="customSpecialContent" show-count :maxlength="4000" :rows="8" @change="textAreaChange(customSpecialContent)" />
              <div v-show="customSpecialTips" class="text-[#fa5151] text-[14px]">
                {{ customSpecialTips }}
              </div>
            </div>
            <a-empty v-else :image="simpleImage" />
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>
    <div style="width: 552px" class="absolute bottom-0 bg-white flex justify-end space-x-[8px] p-14px">
      <a-button @click="showDrawer = false">取消</a-button>
      <a-button type="primary" @click="confirm">确定</a-button>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { IconChevronLeftDoubleFont } from '@pafe/icons-icore-agr-an';
import { Empty } from 'ant-design-vue';
import type { SpecialPromise } from '../insuranceFormFill';
import { pxToRem } from '@/utils/tools';
import { useGet, usePost } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import type { SpecialListItem, SpecialDetail, SpecialContentInfo } from '@/apiTypes/insure';
import { message } from '@/components/ui/Message';

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const { service } = useRuntimeConfig().public || {};

const querySpecialPromiseAbstractListReq = await usePost(`/gateway${service.accept}/specialPromise/querySpecialPromiseList`);
const querySpecialPromiseDetailReq = await useGet<SpecialDetail>('/api/accept/querySpecialPromiseDetail');

const props = defineProps<{
  departmentCode: string;
  marketProductCode?: string;
}>();

const columns = [
  {
    title: '特约类型',
    dataIndex: 'promiseType',
    width: pxToRem(100),
  },
  {
    title: '特约内容',
    dataIndex: 'promiseDesc',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: pxToRem(80),
  },
];
const specialData = defineModel<SpecialPromise[]>(); // 没有数据时，暂时通用模版；通用模版无需传数据给后端；并且只能有一个特约存在，新增即是覆盖

const specialDataDefault = [
  {
    promiseCode: '', // 编码
    promiseDesc: '无其他特别约定', // 特约内容
    promiseType: '',
  },
];

// 删除特约
const removeSpecial = () => {
  specialData.value = [];
};

const showDrawer = ref(false);

const specialTypeList = [
  { label: '固定特约', value: 'N' },
  { label: '半固定特约', value: 'X' },
  { label: '自定义特约', value: 'F' },
];
const activeTab = ref('N');
const selectedSpecialN = ref(''); // group radio 选中的固定特约
const selectedSpecialX = ref(''); // group radio 选中的半固定特约
const specialListX = ref<SpecialListItem[]>([]); // 半固定特约列表数据
const specialListN = ref<SpecialListItem[]>([]); // 固定特约列表数据

const loading = ref(false);
// 固定特约分页字段
const pageSizeN = ref(10);
const currentN = ref(1);
const totalsN = ref(0);
const keyWordN = ref('');

// 半固定特约分页 字段
const pageSizeX = ref(10);
const currentX = ref(1);
const totalsX = ref(0);
const keyWordX = ref('');

const customSpecialContent = ref(''); // 自定义特约内容
const customSpecialTips = ref(''); // 自定义特约错误提示
const customSpecialCode = ref(''); // 自定义特约编码，从接口获取

// 查询固定特约
const querySpecialListN = async () => {
  loading.value = true;
  await nextTick();
  querySpecialPromiseAbstractListReq
    .fetchData({
      specialPromiseType: 'N',
      marketProductCodeList: props.marketProductCode ? [props.marketProductCode] : [],
      specialPromiseName: keyWordN.value,
      specialPromiseDept: props.departmentCode,
      pageNum: currentN.value,
      pageSize: pageSizeN.value,
    })
    .then((res) => {
      const { msg = '' } = res || {};
      if (res && res.code === SUCCESS_CODE) {
        const { records, total = 0, current = 1, size = 10 } = res.data;
        specialListN.value = records.map((item: Record<string, string>) => {
          return {
            idSpecialPromiseDefine: item.idSpecialPromiseDefine || '',
            departmentCode: item.departmentCode || '',
            departmentName: item.departmentName || '',
            marketProductName: item.marketProductName || '',
            specialPromiseCode: item.specialPromiseCode || '',
            specialPromiseName: item.specialPromiseName || '',
            specialPromiseType: item.specialPromiseType || '',
            specialPromiseTypeName: item.specialPromiseTypeName || '',
            specialPromiseDesc: item.specialPromiseDesc || '',
            updateTime: item.updateTime || '',
            createdBy: item.createdBy || '',
            specialPromiseStatus: item.specialPromiseStatus || '',
            specialPromiseStatusName: item.specialPromiseStatusName || '',
            createdRole: item.createdRole || '',
            disabled: item.disabled || false,
            display: false,
          };
        });
        totalsN.value = total;
        currentN.value = current;
        pageSizeN.value = size;
      } else {
        message.error(msg);
        specialListN.value = [];
        totalsN.value = 0;
        currentN.value = 1;
        pageSizeN.value = 10;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 查询半固定特约
const querySpecialListX = async () => {
  loading.value = true;
  await nextTick();
  querySpecialPromiseAbstractListReq
    .fetchData({
      specialPromiseType: 'X',
      marketProductCodeList: props.marketProductCode ? [props.marketProductCode] : [],
      specialPromiseName: keyWordX.value,
      specialPromiseStatusList: ['1', '2', '3'], // 只查询状态为【生效】的特约
      specialPromiseDept: props.departmentCode,
      reqSource: 'apply', // 区分出单调用和特约查询
      pageNum: currentX.value,
      pageSize: pageSizeX.value,
    })
    .then((res) => {
      const { msg = '' } = res || {};
      if (res && res.code === SUCCESS_CODE) {
        const { records, total = 0, current = 1, size = 10 } = res.data;
        specialListX.value = records.map((item: Record<string, string>) => {
          return {
            idSpecialPromiseDefine: item.idSpecialPromiseDefine || '',
            departmentCode: item.departmentCode || '',
            departmentName: item.departmentName || '',
            marketProductName: item.marketProductName || '',
            specialPromiseCode: item.specialPromiseCode || '',
            specialPromiseName: item.specialPromiseName || '',
            specialPromiseType: item.specialPromiseType || '',
            specialPromiseTypeName: item.specialPromiseTypeName || '',
            specialPromiseDesc: item.specialPromiseDesc || '',
            updateTime: item.updateTime || '',
            createdBy: item.createdBy || '',
            specialPromiseStatus: item.specialPromiseStatus || '',
            specialPromiseStatusName: item.specialPromiseStatusName || '',
            createdRole: item.createdRole || '',
            disabled: item.disabled || false,
            display: false,
            xSpecialPromiseList: [],
          };
        });
        totalsX.value = total;
        currentX.value = current;
        pageSizeX.value = size;
      } else {
        message.error(msg);
        specialListX.value = [];
        totalsX.value = 0;
        currentX.value = 1;
        pageSizeX.value = 10;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 查询自定义特约
const querySpecialListF = async () => {
  await nextTick();
  querySpecialPromiseAbstractListReq
    .fetchData({
      specialPromiseType: 'F',
      marketProductCodeList: props.marketProductCode ? [props.marketProductCode] : [],
      specialPromiseName: '',
      specialPromiseDept: props.departmentCode,
      pageNum: currentN.value,
      pageSize: pageSizeN.value,
    })
    .then((res) => {
      const { msg = '' } = res || {};
      if (res && res.code === SUCCESS_CODE) {
        const { records } = res.data;
        if (records && records[0]) {
          customSpecialCode.value = records[0].specialPromiseCode || '';
        } else {
          customSpecialCode.value = '';
        }
      } else {
        message.error(msg);
        customSpecialCode.value = '';
      }
    });
};

const refreshN = () => {
  totalsN.value = 0;
  currentN.value = 1;
  pageSizeN.value = 10;
  // keyWord.value = '';
  querySpecialListN();
};

const refreshX = () => {
  totalsX.value = 0;
  currentX.value = 1;
  pageSizeX.value = 10;
  // keyWord.value = '';
  querySpecialListX();
};

const textAreaChange = (value: string) => {
  if (value && value.length) {
    customSpecialTips.value = '';
    return true;
  } else {
    customSpecialTips.value = '请输入特约内容';
    return false;
  }
};

// 查看详情
interface paramsType {
  type: string;
  needRange?: string;
  mixNum: string | undefined;
  maxNum: string | undefined;
  needDecimals: string | undefined;
  decimalPlaces: string | undefined;
  needTextMax: string | undefined;
  textMaxLength: string;
  display: boolean;
  idSpecialPromiseDefine: string;
}
const detailLoading = ref(false);
const textTogether = (params: paramsType) => {
  const { type, needRange, mixNum, maxNum, needDecimals, decimalPlaces, needTextMax, textMaxLength } = params;
  let text = '';
  if (type === 'N') {
    if (needRange === '1') {
      text += `数值范围是${mixNum}-${maxNum}${needDecimals === '1' ? '，' : ''}`;
    }
    if (needDecimals === '1') {
      text += `保留${decimalPlaces}位小数`;
    }
    if (needRange === '0' && needDecimals === '0') {
      text += '自由输入';
    }
  } else {
    if (needTextMax === '1') {
      text += `文本长度为${textMaxLength}`;
    } else {
      text += '自由输入';
    }
  }
  return text;
};

const viewDetail = (item: paramsType) => {
  item.display = !item.display;
  if (item.xSpecialPromiseList.length) return;
  detailLoading.value = true;
  querySpecialPromiseDetailReq
    .fetchData({ idSpecialPromiseDefine: item.idSpecialPromiseDefine })
    .then((res) => {
      const { msg = '' } = res || {};
      if (res?.code === SUCCESS_CODE) {
        if (res?.data) {
          item.xSpecialPromiseList = res.data.specialPromiseControlList?.map((item) => {
            const { needRange, needDecimals, needTextMax, mixNum, maxNum, decimalPlaces, textMaxLength } = item.dataMap || {};
            const placeholder = textTogether({ type: item.type, needRange, mixNum, maxNum, needDecimals, decimalPlaces, needTextMax, textMaxLength });
            return {
              ...item,
              placeholder,
              value: '',
              tips: '',
              large: needRange === '1' && needDecimals === '1',
            };
          });
        }
      } else {
        message.error(msg);
      }
    })
    .finally(() => {
      detailLoading.value = false;
    });
};
// 半固定特约输入框校验
const validateContent = (item: SpecialContentInfo, isRequier?: boolean) => {
  if (isRequier && !item.value) {
    item.tips = '请输入';
    return false;
  }
  if (!isRequier && !item.value) return false;
  const type = item.type;
  const { needRange, needDecimals, needTextMax, mixNum, maxNum, decimalPlaces, textMaxLength } = item.dataMap || {};
  if (type === 'N') {
    if (needRange === '1') {
      if (!(Number(item.value) <= Number(maxNum) && Number(item.value) >= Number(mixNum))) {
        item.tips = '请按照规则输入';
        return false;
      }
    }
    if (needDecimals === '1') {
      const findIndex = item.value.indexOf('.');
      const point = item.value.length - findIndex - 1;
      if (findIndex > -1 && point > Number(decimalPlaces)) {
        item.tips = '请按照规则输入';
        return false;
      }
    }
    item.tips = '';
    return true;
  } else {
    if (needTextMax === '1') {
      if (item.value.length > Number(textMaxLength)) {
        item.tips = '请按照规则输入';
        return false;
      }
    }
    item.tips = '';
    return true;
  }
};

const showNDetail = (item) => {
  item.display = !item.display;
};

const confirm = () => {
  if (activeTab.value === 'N') {
    if (!selectedSpecialN.value) {
      message.error('请选择一个特约');
      return;
    }
    const index = specialListN.value.findIndex((val) => val.idSpecialPromiseDefine === selectedSpecialN.value);

    // 更新值
    specialData.value = [
      {
        promiseCode: specialListN.value[index].specialPromiseCode || '', // 编码
        promiseDesc: specialListN.value[index].specialPromiseDesc || '', // 特约内容
        promiseType: 'N',
      },
    ];
    showDrawer.value = false;
  } else if (activeTab.value === 'X') {
    if (!selectedSpecialX.value) {
      message.error('请选择一个特约');
      return;
    }
    const index = specialListX.value.findIndex((val) => val.idSpecialPromiseDefine === selectedSpecialX.value);
    if (index > -1 && (!specialListX.value[index].xSpecialPromiseList || !specialListX.value[index].xSpecialPromiseList.length)) {
      // 没有填写过数据
      message.error('请完整填写半固定特约数据');
      return;
    }
    // 校验所有输入框是否已经填完
    let isTrue = true;
    for (const item of specialListX.value[index].xSpecialPromiseList) {
      if (item.type !== 'F' && !validateContent(item, true)) {
        // 非普通文本进行校验
        isTrue = false;
      }
    }
    if (isTrue) {
      // 校验通过
      const promiseControlList = specialListX.value[index].xSpecialPromiseList.map((val, index) => {
        return {
          num: index + 1,
          type: val.type,
          value: val.type === 'F' ? val.fixedMsg : val.value,
        };
      });
      // 更新值
      specialData.value = [
        {
          promiseCode: specialListX.value[index].specialPromiseCode || '', // 编码
          promiseDesc: promiseControlList.map((item) => item.value).join(''), // 特约内容
          promiseType: 'X',
          promiseControlList,
        },
      ];
      showDrawer.value = false;
    }
  } else {
    if (textAreaChange(customSpecialContent.value)) {
      // 校验通过
      specialData.value = [
        {
          promiseCode: customSpecialCode.value || '', // 编码
          promiseDesc: customSpecialContent.value || '', // 特约内容
          promiseType: 'F',
        },
      ];
      showDrawer.value = false;
    }
  }
};

watch(
  () => showDrawer.value,
  (show) => {
    // 打开弹窗，如果没有数据，则加载数据
    if (show) {
      if (['N'].includes(activeTab.value) && !specialListN.value?.length) {
        refreshN();
      } else if (['X'].includes(activeTab.value) && !specialListX.value?.length) {
        refreshX();
      } else if (['F'].includes(activeTab.value) && !customSpecialCode.value) {
        querySpecialListF();
      }
    } else {
      // 清除已填数据
      selectedSpecialX.value = '';
      selectedSpecialN.value = '';
      customSpecialContent.value = '';
      specialListX.value = [];
      specialListN.value = [];
    }
  },
);
// 点击分页，切换页数
const onChange = () => {
  if (['N'].includes(activeTab.value)) {
    querySpecialListN();
  } else if (['X'].includes(activeTab.value)) {
    querySpecialListX();
  }
};

watch(
  () => activeTab.value,
  (active) => {
    // 切换tabs，如果没有数据则加载数据
    if (['N'].includes(active) && !specialListN.value?.length) {
      refreshN();
    } else if (['X'].includes(active) && !specialListX.value?.length) {
      refreshX();
    } else if (['F'].includes(active) && !customSpecialCode.value) {
      querySpecialListF();
    }
  },
);

watch(
  () => selectedSpecialN.value,
  (val) => {
    if (val) {
      selectedSpecialX.value = '';
    }
  },
);

watch(
  () => selectedSpecialX.value,
  (val) => {
    if (val) {
      selectedSpecialN.value = '';
    }
  },
);
</script>

<style lang="less" scoped>
.special-list {
  height: 100%;
  :deep(.ant-radio-wrapper) {
    display: flex;
    align-items: center;
  }
  :deep(.ant-radio-group.ant-radio-group-outline) {
    display: block;
  }
  :deep(.ant-radio) {
    align-self: unset;
  }
  :deep(.ant-input-affix-wrapper.customer-input) {
    padding-left: 2px;
  }
  :deep(.ant-tabs) {
    height: 100%;
    .ant-tabs-content {
      height: 100%;
      overflow-y: auto;
      box-sizing: border-box;
      padding-right: 2px;
    }
  }
}
</style>
