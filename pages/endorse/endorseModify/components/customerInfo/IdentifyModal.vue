<template>
  <a-modal v-model:open="visible" title="您输入的客户名称未验证，请上传并识别客户证件影像" :width="pxToRem(500)" centered>
    <div class="identify-body">
      <div class="form-title">
        <span>上传客户证件图片</span>
      </div>
      <a-upload-dragger
        v-model:file-list="fileList"
        name="file"
        :multiple="true"
        :action="action"
        :accept="accept"
        :before-upload="beforeUpload"
        :max-count="1"
        :show-upload-list="false"
        @change="handleChange"
        @drop="handleDrop"
      >
        <p class="ant-upload-drag-icon">
          <VueIcon :icon="IconTongyongShangchuanFont" />
        </p>
        <p class="ant-upload-text">点击或将文件拖拽到这里上传</p>
        <div class="ant-upload-hint">
          <p class="text-left ml-[12px] my-[5px] pt-[10px] text-xs">注：图片格式为JPG、PNG；</p>
          <p class="text-left m-0 indent-1 text-xs"> 如无证件图片，请点击“暂无证件照片”按钮，进行手动录入</p>
        </div>
      </a-upload-dragger>
      <p>待上传文件： <a href="#" class="text-cyan-600"> {{ uploadFile }}</a></p>
    </div>

    <template #footer>
      <a-button key="back" @click="handleCancel"> 暂无证件照片 </a-button>
      <a-button key="submit" type="primary" :loading="confirmLoading" @click="handleOk">
        确认上传
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue';
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';
import { IconTongyongShangchuanFont } from '@pafe/icons-icore-agr-an';
import { pxToRem } from '@/utils/tools';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';

const route = useRoute();

const visible = defineModel<boolean>('visible', {
  required: true,
  default: false,
});

const props = withDefaults(defineProps<{
  documentType: string; // 证件类型
  documentGroupId: string; // 证件id
}>(), {
  documentType: '',
  documentGroupId: '',
});
const emit = defineEmits(['okBtn']);
const { gateWay, service } = useRuntimeConfig().public || {};

const gatewayAcceptURL = gateWay + service.accept;
const confirmLoading = ref(false);
// 上传
// 上传的地址
const action = ref('');
// 接受上传的文件类型
const accept = '.jpg,.png';
// 已经上传的文件列表
const fileList = ref([]);
const uploadFile = ref('');
const fileInfo = ref<UploadChangeParam>();
watchEffect(() => {
  if (visible.value) {
    fileList.value = [];
    uploadFile.value = '';
  }
});
const imageTypeList = ['jpeg', 'png'];
// 上传前
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  // image/png
  const type = file.type.split('/')[1];
  const isPNG = imageTypeList.indexOf(type) === -1;
  if (isPNG) {
    message.error(`${file.name} 图片格式不正确`);
  }
  // false 手动上传
  return false;
};
// 上传 change
const handleChange = (info: UploadChangeParam) => {
  fileInfo.value = info;
  const status = info.file.status;
  if (status !== 'uploading') {
    uploadFile.value = info.fileList.map(item => item.name).join('；');
  }
};
// 拖动上传
function handleDrop() {

}

// 确认上传
const handleOk = async () => {
  const file = fileInfo.value?.file as unknown as File;
  if (!uploadFile.value) {
    return message.error('请选择文件');
  }
  const { documentType, documentGroupId } = props;
  const formData = new FormData();
  formData.append('file', file);
  formData.append('documentType', documentType);
  formData.append('documentGroupId', documentGroupId);
  formData.append('applyPolicyNo', route.query.applyPolicyNo as string);
  confirmLoading.value = true;

  try {
    const res = await $postOnClient(
      `${gatewayAcceptURL}/apply/attachment/uploadOCRCustomerCertificate`,
      formData,
    );
    const { code, msg } = res || {};
    if (code === SUCCESS_CODE) {
      message.success('上传成功');
      setVisible(false);
      emit('okBtn', res?.data);
    } else {
      message.error(msg);
    }
    confirmLoading.value = false;
  } catch {
    confirmLoading.value = false;
  }
};
// 暂无证件照片
const handleCancel = () => {
  setVisible(false);
};
const setVisible = (flag: boolean) => {
  visible.value = flag;
};
</script>
