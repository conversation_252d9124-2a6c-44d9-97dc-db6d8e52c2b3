<template>
  <a-form ref="formRef" :model="data" :colon="false">
    <div class="p-16px bg-white rounded">
      <div class="form-title">投保组织者</div>
      <div class="grid grid-cols-3 gap-x-16px">
        <a-form-item required label="名称" name="organizerName" :label-col="labelColStyle" :rules="[{ validator: checkClientName }]">
          <a-input v-model:value="data.organizerName" placeholder="请输入" />
        </a-form-item>
        <a-form-item required label="证件类型" name="certificateType" :label-col="labelColStyle">
          <a-select v-model:value="data.certificateType" class="w-full" :options="optionsData.groupCertificate" />
        </a-form-item>
        <a-form-item required label="证件号码" name="certificateNo" :label-col="labelColStyle" :rules="[{ required: true, message: '证件号码不能为空' }, { max: 28, message: '证件号码长度不能超过28' }, { validator: IDValidator }]">
          <a-input v-model:value="data.certificateNo" />
        </a-form-item>
        <a-form-item class="col-span-3" name="certificateValidDate" :rules="[{ required: true, message: '证件有效时间不能为空' }]">
          <RangeTimePicker v-model:start-time="data.certificateIssueDate" v-model:end-time="data.certificateValidDate" required />
        </a-form-item>
        <a-form-item required name="address" label="地址" :label-col="labelColStyle" class="col-span-2">
          <div class="flex space-x-8px">
            <RegionSelect
              ref="addressRef"
              v-model:province="data.province"
              v-model:city="data.city"
              v-model:county="data.county"
              :region-level="3"
              style="width: 60%"
              @change-selected="changeAddress"
            />
            <a-input v-model:value="data.address" style="width: 40%" placeholder="请输入" />
          </div>
        </a-form-item>
        <a-form-item required name="postalCode" label="邮政编码" :label-col="labelColStyle" :rules="[{ validator: isPostCode }]">
          <a-input v-model:value="data.postalCode" placeholder="请输入" />
        </a-form-item>
        <a-form-item required name="contactPerson" label="联系人" :label-col="labelColStyle" :rules="[{ max: 120, message: '不能超过120个字符' }]">
          <a-input v-model:value="data.contactPerson" placeholder="请输入" />
        </a-form-item>
        <a-form-item required name="contactTelephone" label="联系电话" :label-col="labelColStyle" :rules="[{ validator: isMobilePhone }]">
          <a-input v-model:value="data.contactTelephone" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="电子邮箱" name="email" :label-col="labelColStyle" :rules="[{ validator: isEmail }]">
          <a-input v-model:value="data.email" placeholder="请输入" />
        </a-form-item>
        <template v-if="organizationExpand">
          <a-form-item label="经营范围" name="businessScope" :label-col="labelColStyle" :rules="[{ max: 200, message: '不能超过200个字符' }]">
            <a-input v-model:value="data.businessScope" placeholder="请输入" />
          </a-form-item>
          <a-form-item label="行业" name="industryCode" :label-col="labelColStyle">
            <a-cascader v-model:value="industry" class="w-full" placeholder="请输入" :options="optionsData.industry" />
          </a-form-item>
          <a-form-item label="组织机构类型" name="organizationType" :label-col="labelColStyle">
            <a-select v-model:value="data.organizationType" class="w-full" placeholder="请输入" :options="optionsData.organizationType" />
          </a-form-item>
        </template>
      </div>
      <div :class="['flex', 'justify-end', 'items-center']">
        <div :class="['flex', 'items-center', 'space-x-[4px]', 'text-[#576B95]', 'cursor-pointer']" @click="() => (organizationExpand = !organizationExpand)">
          <VueIcon :icon="organizationExpand ? IconChevronLeftDoubleFont : IconChevronRightDoubleFont" class="rotate-90" />
          <span>{{ organizationExpand ? '收起' : '展示' }}更多</span>
        </div>
      </div>
    </div>
  </a-form>
</template>

<script setup lang="ts">
import { IconChevronLeftDoubleFont, IconChevronRightDoubleFont } from '@pafe/icons-icore-agr-an';
import type { Rule } from 'ant-design-vue/es/form';
import type { ValueType } from 'ant-design-vue/es/vc-cascader/Cascader';
import RangeTimePicker from './RangeTimePicker.vue';
import type { OrganizerInfoData, OptionsData } from './customerInfo';
import { pxToRem } from '@/utils/tools';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import { checkClientName, isPostCode, isMobilePhone, isEmail, isOrgCode, isTaxationNo, isBusinessNo, isLicenseNo, isSocialCreditNumber } from '@/utils/validators';

const labelColStyle = {
  style: {
    width: pxToRem(100),
  },
};
defineProps<{
  optionsData: OptionsData; // 基础数据
}>();
const formRef = ref();
// 地址ref
const addressRef = ref();
// 表单数据
const data = defineModel<OrganizerInfoData>('data', { default: {} });

// 行业 由于后端返回结构和antd组件数据源结构不一致，这里需要把行业的两个字段转为数组
const industry = computed({
  get() {
    if (!data?.value?.industryCodeLevel2 || !data?.value?.industryCode) {
      return [];
    }
    return [data.value.industryCode, data.value.industryCodeLevel2] as ValueType;
  },
  set(value) {
    data.value.industryCode = value?.[0] as ValueType;
    data.value.industryCodeLevel2 = value?.[1] as ValueType;
  },
});

const organizationExpand = ref(false); // 投保组织者信息展开/收起
// 校验证件类型
const IDValidator = (rule: Rule, value: string) => {
  value = value?.trim().replace(/\s+/g, ''); // 统一处理字符串的trim和去空格操作
  switch (data.value.certificateType) {
    case '01':
      return isOrgCode(rule, value);
    case '02':
      return isTaxationNo(rule, value);
    case '04':
      return isBusinessNo(rule, value);
    case '06':
      return isLicenseNo(rule, value);
    case '08':
      return isSocialCreditNumber(rule, value);
    case '99':
      if (/[\u4E00-\u9FA5]/g.test(value)) {
        return Promise.reject('不能输入中文');
      }
      break;
    default:
      break;
  }
  return Promise.resolve();
};

type AddressType = 'province' | 'city' | 'county';
const address = ref({
  provinceName: '',
  cityName: '',
  countyName: '',
});
// 地址改变
const changeAddress = (option: { label: string }, type: AddressType) => {
  if (['province', 'city', 'county'].includes(type)) {
    address.value[`${type}Name` as 'provinceName' | 'cityName' | 'countyName'] = option?.label || '';
  }
  data.value.address = address.value.provinceName + address.value.cityName + address.value.countyName;
};

const validate = async () => {
  if (formRef.value) {
    try {
      await formRef.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};

defineExpose({
  validate,
});
</script>

<style>
.form-title {
  position: relative;
  font-size: 14px;
  font-weight: 600;
  padding-left: 7px;
  margin-bottom: 10px;
  width: fit-content;

  &::before {
    position: absolute;
    left: 0;
    top: 6px;
    content: '';
    width: 3px;
    height: 10px;
    background: #07c160;
  }
}
</style>
