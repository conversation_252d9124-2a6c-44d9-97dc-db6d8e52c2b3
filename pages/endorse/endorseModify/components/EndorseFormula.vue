<template>
  <a-form ref="formRef" :model="data" :colon="false">
    <a-form-item required label="保费变化公式选择" name="calculateType" :label-col="{ style: { width: pxToRem(130) } }">
      <a-radio-group v-model:value="data.calculateType">
        <a-radio value="2">按保险起期计算</a-radio>
        <a-radio value="1">按批单生效日期计算</a-radio>
      </a-radio-group>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { pxToRem } from '@/utils/tools';

const data = defineModel<Record<string, string>>('data', { default: {} });

const formRef = ref();
defineExpose({
  validate: async () => {
    if (formRef.value) {
      try {
        await formRef.value.validateFields();
        return { valid: true, errors: [] };
      } catch (errors) {
        return { valid: false, errors };
      }
    }
  },
});
</script>

<style lang="less" scoped></style>
