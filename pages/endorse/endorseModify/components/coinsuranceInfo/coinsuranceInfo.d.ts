// 共保信息接口
export interface CoinsuranceInfo {
  innerCoinsuranceMark?: string; // 共保标志  系统内外共保标志:0或空-系统外共保;1-系统内共保;2-系统外同时系统内共保,
  totalInsuredAmount?: string; // 总保额
  totalPremium?: string; // 总保费
  innerCoinsuranceAgreement?: string; // 共保协议编码
  coinsuranceDetailList?: CoinsuranceDetail[]; // 共保信息列表
}

// 共保信息表格行接口
export interface CoinsuranceDetail {
  uuid?: string; // uuid
  reinsureCompanyCode?: string; // 共保公司编码
  reinsureCompanyName?: string; // 共保公司名称
  reinsureScale?: string; // 共保比例
  insuredAmount?: string; // 保额
  premium?: string; // 保费
  callPremium?: string; // 复核保费（页面暂未得使用字段 2024.08.30）
  acceptInsuranceFlag?: string; // 是否主承保：0否,1是
  coinsuranceType?: string; // 共保属性:0-系统外共保,1-系统内共保
  coinsuranceDesc?: string; // 共保描述
  coinsureCompanyFinanceDeptCode?: string; // 财务机构编码
  coinsureCompanyMgDeptCode?: string; // 管理机构编码 （页面暂未使用字段 2024.08.30）
  coinsureEmployeeList?: CoinsureEmployee[];
  commisionScale?: string; // 份额
  fromFirst?: boolean; // 是否来源于第一屏
  developFlg?: string; // 是否共展 N-否，Y-是
}

// 系统内共保业务员接口
export interface CoinsureEmployee {
  uuid?: string; // uuid
  employeeCode?: string; // 业务员编码$
  employeeName?: string; // 业务员名称
  commisionScale?: string; // 佣金比例
  reinsureScale?: string; // 份额
  mainEmployeeFlag?: string; // 主承业务员标识(是否主承保：0否,1是)
  coDevelopChannelClassCode?: string; // 业务员企划八分渠道
  employeeOptions?: CoinsureEmployee[]; // 业务员下拉框数据
  channelOptions?: Channel[]; // 渠道列表
}

// 系统内共保业务员渠道
export interface Channel {
  channelSourceCode?: string; // 渠道id
  channelSourceName?: string; // 渠道名称
}

// 系统外共保公司接口
export interface Company {
  companyCode?: string; // 共保公司编码
  companyName?: string; // 共保公司名称
  disabled?: boolean; // 是否可选
}

// 财务机构
export interface FinanceDept {
  financeDeptCode?: string; // 财务机构编码
  financeDeptName?: string; // 财务机构名称
}
