<template>
  <a-form ref="formRef" :model="data" :colon="false">
    <div class="grid grid-cols-3 gap-x-16px bg-white h-[46px] items-center">
      <div class="text-[#333]">
        <span class="text-[rgba(0,0,0,.6)] w-[54px] inline-block text-left">原保费</span>
        <span class="font-number">{{ data.originPremium }}</span>
      </div>
      <div class="text-[#333]">
        <span class="text-[rgba(0,0,0,.6)] w-[54px] inline-block text-left">现保费</span>
        <span class="font-number">{{ data.totalActualPremium }}</span>
      </div>
      <div class="text-[#333]">
        <span class="text-[rgba(0,0,0,.6)] w-[68px] inline-block text-left">保费变化</span>
        <span class="font-number">{{ data.actualPremiumChange }}</span>
      </div>
    </div>
    <div class="grid grid-cols-3 gap-x-16px bg-white h-[46px] mb-[16px] items-center">
      <div class="text-[#333]">
        <span class="text-[rgba(0,0,0,.6)] w-[68px] inline-block text-left">批改日期</span>
        <span class="font-number">{{ data.applyDate || '-' }}</span>
      </div>
      <div class="text-[#333]">
        <a-form-item required label="生效日期" class="w-[100%]" name="effectiveDate" :label-col="{ style: { width: pxToRem(80) } }" :rules="[{ required: true, message: '请选择生效日期' }, { validator: dateValidator }]">
          <CompareRender v-if="readonly" :value="data.effectiveDate" value-path="edrApplyBaseInfoValue.effectiveDate" />
          <a-date-picker v-else v-model:value="data.effectiveDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        </a-form-item>
      </div>
      <div v-if="showReason" class="text-[#333]">
        <a-form ref="reasonFormRef" :model="reason" :colon="false">
          <a-form-item required label="批改原因" class="w-[100%]" name="cancelType" :label-col="{ style: { width: pxToRem(80) } }">
            <span v-if="readonly">{{ reasonLabel }}</span>
            <a-select v-else v-model:value="reason.cancelType" :options="reasonOptions" />
          </a-form-item>
        </a-form>
      </div>
    </div>
    <a-form-item label="批文" name="endorseComment" :label-col="{ style: { width: pxToRem(54) } }">
      <div v-if="readonly">
        <CompareRender :value="data.endorseComment" value-path="edrApplyBaseInfoValue.endorseComment" />
      </div>
      <a-textarea v-else v-model:value="data.endorseComment" class="h-[64px]" placeholder="请输入" show-count :maxlength="2000" />
    </a-form-item>
    <a-form-item required label="是否发送电子批单" name="isSendElectronicEdrDocument" :label-col="{ style: { width: pxToRem(130) } }">
      <span v-if="readonly">{{ data.isSendElectronicEdrDocument ? ' 是' : '否' }}</span>
      <a-radio-group v-else v-model:value="data.isSendElectronicEdrDocument">
        <a-radio :value="true">是</a-radio>
        <a-radio :value="false">否</a-radio>
      </a-radio-group>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form/interface';
import dayjs from 'dayjs';
import type { EndorseBaseInfo } from '../endorseModify.d';
import CompareRender from '@/pages/review/endorseVerifyDetail/components/CompareRender.vue';
import { pxToRem } from '@/utils/tools';

const reasonOptions = [
  {
    value: '1',
    label: '客户申请',
  },
  {
    value: '2',
    label: '出单问题修正',
  },
  {
    value: '3',
    label: '内部核查修改',
  },
  {
    value: '4',
    label: '政府要求',
  },
];

const reasonLabel = computed(() => {
  if (reason.value.cancelType) {
    const selected = reasonOptions.find(k => k.value === reason.value.cancelType);
    return selected ? selected.label : '-';
  }
  return '-';
});

const data = defineModel<EndorseBaseInfo>('data', { default: {} });
const reason = defineModel<{ cancelType: string }>('reason', { default: {} });
const { readonly = false, isSurrender = false, insuranceBeginDate = '' } = defineProps<{ readonly?: boolean; isSurrender?: boolean; insuranceBeginDate?: string }>();
const formRef = ref();
const reasonFormRef = ref();

const showReason = computed(() => {
  return isSurrender || Number(data.value.actualPremiumChange) < 0;
});

const dateValidator = (_: Rule, value: string) => {
  if (dayjs(value).isBefore(dayjs(insuranceBeginDate))) {
    return Promise.reject('批改生效日期不能小于保险起期，请更正');
  }
  return Promise.resolve();
};

defineExpose({
  validate: async () => {
    const validateArr = [formRef.value?.validateFields()];
    if (showReason.value) {
      validateArr.push(reasonFormRef.value?.validateFields());
    }
    const results = await Promise.all(validateArr);
    if (results) {
    // 所有表单校验通过
      return { valid: true, errors: [] };
    } else {
      return { valid: false, errors: ['校验不通过'] };
    }
  },
});
</script>

  <style scoped>
  .form-title {
    position: relative;
    font-size: 14px;
    font-weight: 600;
    padding-left: 7px;
    margin-bottom: 10px;
    width: fit-content;

    &::before {
      position: absolute;
      left: 0;
      top: 6px;
      content: '';
      width: 3px;
      height: 10px;
      background: #07c160;
    }
  }
  :deep(.ant-form-item) {
    margin: 0
  }
  </style>
