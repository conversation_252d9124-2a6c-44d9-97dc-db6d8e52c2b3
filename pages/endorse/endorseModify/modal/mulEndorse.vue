<template>
  <a-modal v-model:open="visible" title="批量批改" :width="pxToRem(800)" centered :confirm-loading="confirmLoading" :mask-closable="false" @ok="fileImport">
    <div class="mb-[18px]">
      <div class="flex items-center mb-[9px]">
        <VueIcon :icon="IconPingtaijiekouFont" />
        <span class="ml-[3px]">上传文件</span>
      </div>
      <div class="bg-gray-100 rounded p-14px">
        <a-upload :multiple="false" :show-upload-list="false" :action="handleFileChange" @change="handleFileChange">
          <a-button :loading="uploading">
            <template #icon>
              <VueIcon :icon="IconTongyongShangchuanFont" />
            </template>
            <span class="ml-[2px]">上传修改结果</span>
          </a-button>
        </a-upload>
        <div v-if="currentFile" class="text-12px text-[#4E6085] mt-[10px]">
          <VueIcon :icon="IconAttachmentFont" />
          <span class="ml-[5px] cursor-pointer" @click="downloadFileByKey(currentFile.fileKey)">{{ currentFile.fileName }}({{ currentFile.fileSize }})</span>
          <a-button type="link" @click="removeFile">删除</a-button>
        </div>
      </div>
    </div>
    <div>
      <div class="flex items-center mb-[9px]">
        <VueIcon :icon="IconJichuxinxiFont" />
        <span class="ml-[3px]">导出模板</span>
      </div>
      <div class="bg-gray-100 rounded p-14px overflow-x-auto">
        <a-button :loading="exportUploading" @click.stop="handleExportTemplate()">
          <template #icon>
            <VueIcon :icon="IconTongyongXiazaiFont" />
          </template>
          <span class="ml-[2px]">导出修改模板</span>
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { IconJichuxinxiFont, IconPingtaijiekouFont, IconTongyongXiazaiFont, IconTongyongShangchuanFont, IconAttachmentFont } from '@pafe/icons-icore-agr-an';
import { notification, Button } from 'ant-design-vue';
import { get, set } from 'lodash-es';
import { pxToRem, downloadFile } from '@/utils/tools';
import { $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

const visible = defineModel<boolean>('visible', { required: true, default: false });
const props = defineProps<{
  endorseApplyNo: string;
  refresh: () => void;
}>();
// 当前上传清单附件
const currentFile = ref<{
  farmerListName: string;
  fileKey: string;
  fileName: string;
  fileSize: string;
  templateId: string;
}>();

const { gateWay, service } = useRuntimeConfig().public || {};

// 删除附件
const removeFile = async () => {
  await $getOnClient(`/gateway${service.farmer}/templateInfo/deleteFile`, {
    fileKey: currentFile.value?.fileKey || '',
  });
  currentFile.value = undefined;
};

const downloadFileByKey = async (fileKey: string, key?: string) => {
  const res = await $getOnClient(`/gateway${service.farmer}/templateInfo/queryFileDownloadUrl`, { fileKey });
  const { msg = '' } = res || {};
  const url = get(res, 'data.url');
  if (url) {
    downloadFile(url);
    if (key) {
      notification.close(key);
    }
  } else {
    message.error(msg);
  }
};

const confirmLoading = ref(false);
const fileImport = async () => {
  if (!currentFile.value) {
    message.warning('请先上传修改结果!');
    return;
  }
  try {
    confirmLoading.value = true;
    const res = await $postOnClient(`/gateway${service.farmer}/file/edrExcelUpload`, {
      fileKey: currentFile.value?.fileKey || '',
      endorseApplyNo: props.endorseApplyNo,
    });
    confirmLoading.value = false;
    if (res?.code === SUCCESS_CODE) {
      visible.value = false;
      props.refresh();
    } else {
      message.error(res?.msg || '');
    }
  } catch {
    confirmLoading.value = false;
  }
};

const uploading = ref(false);
const handleFileChange = (file: File) => {
  uploading.value = true;
  const formData = new FormData();
  formData.append('file', file);
  formData.append('endorseApplyNo', props.endorseApplyNo);
  $postOnClient(`/gateway${service.farmer}/templateInfo/uploadEdrFarmerListFile`, formData)
    .then((res) => {
      if (res && res.code === SUCCESS_CODE) {
        const uploadResult = get(res, 'data.uploadResult');
        if (uploadResult) {
          set(currentFile, 'value', res.data);
        } else {
          const key = 'errorNotice';
          notification.error({
            message: '错误信息',
            description: () => {
              return h(
                'div',
                {
                  style: {
                    'white-space': 'pre-wrap',
                    'word-wrap': 'break-word',
                    'word-break': 'break-all',
                  },
                },
                get(res, 'data.errorMsg'),
              );
            },
            duration: null,
            key,
            btn: () =>
              h(
                Button,
                {
                  type: 'link',
                  size: 'small',
                  onClick: () => {
                    downloadFileByKey(get(res, 'data.fileKey', ''), key);
                  },
                },
                { default: () => '下载完整信息' },
              ),
          });
        }
      } else {
        message.error(get(res, 'msg', ''));
      }
    })
    .finally(() => {
      uploading.value = false;
    });
  return Promise.reject();
};

const exportUploading = ref(false);
const gatewayFarmerURL = gateWay + service.farmer;
const handleExportTemplate = async () => {
  exportUploading.value = true;
  try {
    const res = await $postOnClient<{ templateFileKey: string }>(`${gatewayFarmerURL}/templateInfo/queryEndorseApplyTemplateInfo`, { endorseApplyNo: props.endorseApplyNo });
    if (res?.data) {
      const { templateFileKey } = res.data;
      downloadFileByKey(templateFileKey);
    }
    exportUploading.value = false;
  } catch {
    exportUploading.value = false;
  }
};
</script>
