type SceneEnum = {
  apply: string;
  modify: string;
};

type SceneEnums = {
  [key: string]: SceneEnum;
};

export const sceneEnums: SceneEnums = {
  // 整单批改
  '90001': {
    apply: 'WHOLE_APPLY',
    modify: 'WHOLE_MODIFY',
  },
  // 退保
  '00006': {
    apply: 'CANCELLATION_APPLY',
    modify: 'CANCELLATION_MODIFY',
  },
  // 注销
  '00017': {
    apply: 'WRITE_OFF_APPLY',
    modify: 'WRITE_OFF_MODIFY',
  },
};
