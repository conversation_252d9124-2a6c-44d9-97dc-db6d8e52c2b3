<template>
  <a-form-item v-bind="$attrs" :colon="pageMode === 'view'" :label="data.label" :help="help" :validate-status="status" :rules="[{ required: pageMode !== 'view' && data.require, trigger: 'change' }, ...extraRules]">
    <template v-if="pageMode === 'view'">
      <span class="font-bold">{{ renderValue }}</span>
    </template>
    <template v-else>
      <slot :clear-status="clearStatus" />
    </template>
  </a-form-item>
</template>

<script setup lang="ts">
import type { RuleObject } from 'ant-design-vue/es/form/interface';

const props = withDefaults(
  defineProps<{
    data: { value?: unknown; status?: unknown; statusTips?: unknown; label?: string; require?: boolean };
    options?: Array<{ label: string; value: string }>;
    extraRules?: Array<RuleObject>;
  }>(),
  {
    data: () => ({}),
    options: () => [],
    extraRules: () => [],
  },
);
const pageMode = inject('pageMode');

const status = ref();
const help = ref();

const renderValue = computed(() => {
  if (props.options.length > 0) {
    return props.options.find((option) => option.value === props.data.value)?.label;
  } else {
    return props.data.value;
  }
});

watchEffect(() => {
  if (props.data.status) {
    status.value = props.data.status;
  } else {
    status.value = undefined;
  }
  if (props.data.statusTips) {
    help.value = props.data.statusTips;
  } else {
    help.value = undefined;
  }
});

// 清除风险
const clearStatus = () => {
  help.value = undefined;
  status.value = undefined;
};
</script>
