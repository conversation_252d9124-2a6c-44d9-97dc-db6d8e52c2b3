export interface UploadFile {
  documentName: string;
  documentId: string;
  thumbnail: string; // 缩略图，上传图片会返回
  documentFormat: string;
  uploadPath: string;
  selected: boolean;
  bucketName: string;
  storageType: string;
}

export interface FileModule {
  typeName: string;
  typeCode: string;
  docLimitFlag: string; // 00:非必填 01:出单必录 02：出单可缓， 03：纸质归档
  fileDetailList: Array<UploadFile>;
  checkedAll: boolean;
  indeterminate: boolean;
}
