export interface SearchFormState {
  departmentCode: string; // 机构编码
  containLowDepartment?: boolean; // 是否包含下级机构
  lastRiskType: string; // 标的类型
  createDate: [string, string]; // 保单生成日期
  riskCheckStatus?: string[]; // 验标状态
  policyNo: string; // 保单号
  edrStatusCodeList: string[]; // 批改状态
  endorseNoOrApplyNo: string; // 申请单号
  insuredName: string; // 被保险人
}
export interface DataType {
  records: Record<string, string>[];
  total: number;
  current: number;
  size: number;
}
export interface deleteDataType {
  data: string; // 数据
  code: string; // 状态码
  msg: string; // 提示信息
}
export type Key = string | number; // 表格勾选回调数据类型
