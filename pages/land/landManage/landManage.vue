<template>
  <iframe v-if="url" :src="url" style="margin:0;position: absolute;width: 100%;padding:12px;padding-bottom:0;height: calc(100vh - 40px);box-sizing: border-box;border: none" />
</template>

<script setup lang="ts">
import type { EcosSign } from './landQuery.d';
import { useGet } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

const { ecosHost, gateWay, service } = useRuntimeConfig().public || {};
const getEcosUserAuthParamReq = await useGet<EcosSign>(`${gateWay}${service.farmer}/ecos/getEcosUserAuthParam`);// 查询鉴权信息

const url = ref('');

const setUrl = (data: EcosSign) => {
  url.value = `${ecosHost}/#/retransmission?target=landManage&systemId=${data.systemId}&randomNumber=${data.randomNumber}&unixTimeStamp=${data.unixTimeStamp}&signature=${data.signature}&umAccount=${data.umAccount}&depId=${data.depId}&noNavbar=1&noSidebar=1`;
};

const getSign = async () => {
  try {
    const { code, data, msg } = await getEcosUserAuthParamReq.fetchData() || {};
    if (SUCCESS_CODE === code) {
      setUrl(data as EcosSign);
    } else {
      message.error(msg || '请求有误，请稍后重试');
    }
  } catch (err) {
    console.log(err);
  }
};

getSign();
</script>
