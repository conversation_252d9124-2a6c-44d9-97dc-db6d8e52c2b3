export interface BaseFarmerLand {
  farmerName: string;
  idFarmerlistCustomInfo?: string;
  identityCardNoCipher: string; // 身份证密文
  certificateNo: string; // 身份证脱敏数据
  accountNo?: string;
  linkState?: boolean;
  drawState?: boolean;
  shapeNum: number;
  shapeList: { authName: string; authCertificateNo: string; landCode: string; agrLandClassification: string; landArea: number; insuranceNums?: number; landName?: string; confirmationLandCode?: string }[];
}

export interface ChooseLand {
  areaInMu: number;
  farmerName: string;
  identityCardNo: string;
  identityCardNoCipher: string;
  landNoStr: string;
  landType: string;
  landName: string;
  confirmationLandCode: string;
}

export interface EcosSign {
  depId: string;
  randomNumber: string;
  signature: string;
  systemId: string;
  umAccount: string;
  unixTimeStamp: string;
}

export interface EcosmapInstance {
  addDistrictLayer: (options: { code: string; level: number; onlyShowBounds?: boolean }) => Promise<void>;
  addLandLayerByCode: (options: { code: string; level: number }) => Promise<void>;
  addActiveLandLayer: (options: { landNoList: string[]; needLocate: boolean; code: string; level: number }) => Promise<void>;
  addQueryLandLayer: (options: { code: string; level: number; landNoList: string[] }) => Promise<void>;
  addLandLayerByPointClick: (options: { code: string; level: number; activeLandNoList: string[]; disabledLandNoList?: string[]; needLocate?: boolean }) => Promise<void>;
  removeLandLayerByPointClick: (options: { queryType: string; code: string; level: string }) => Promise<ChooseLand[]>;
  drawPolygon: (landCodes: string[]) => void;
  handleSaveSketch: (options: { landNo: string; code: string }) => Promise<{ code: string; res: string; data: { list: { parentLandNoStr: string; landNoStr: string; areaInMu: number; current: number }[] } }>;
  addDrawLayer: (options: { code: string; close: boolean; level: number }) => Promise<void>;
  resize: () => void;
  destroy: () => void;
  queryLandInfo: (options: { queryType: string; code: string; level: number; identityCardNo?: string; farmerName?: string }) => Promise<ChooseLand[]>;
}
