export interface Banner {
  bannerId: number;
  sequence: number;
  bannerName: string;
  downloadUrl: string;
  jumpUrl: string;
}

export interface Person {
  position: string;
  name: string; // 姓名
  profilePicture: string; // 头像
  department: string; // 部门
  businessLine: string; // 业务线
  role: string; // 用户角色
  tags: string[]; // 用户标签+评分
}

export interface Notice {
  sequence: number; // 排序
  date: string; // 日期
  title: string; // 标题
  jumpUrl: string; // 跳转URL
}

export interface LinkInfo {
  sequence: number;
  linkName: string; // 链接名称
  icoPath: string; // 图标下载地址
  jumpUrl: string; // 跳转URL
}

export interface HelpInfo {
  sequence: number;
  askTitle: string; // 问题标题
  heatValue: number; // 热度值
  jumpUrl: string; // 跳转URL
}

export interface TodoData {
  due: number;
  total: number;
  todoLists: {
    module: string;
    moduleName: string;
    todoContents: {
      endTime: string | undefined;
      startTime: string | undefined;
      jumpUrl: string;
      module: string;
      newlyAdded: true;
      todoCount: number;
      todoType: string;
      todoTypeName: string;
    }[];
  }[];
}

export interface Course {
  downloadPath: string; // 视频下载地址
  courseName: string; // 视频名
  courseCoverPath: string; // 封面地址
}

export interface TodayTask {
  businessNo: number; // 业务单号或id，待定
  timeQuantum: string; // 时间段
  title: string; // 待办标题
  content: string; // 待办内容
  todoType: string;
  jumpUrl: string; // 跳转URL
}
