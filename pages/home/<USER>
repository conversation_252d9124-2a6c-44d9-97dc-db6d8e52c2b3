<template>
  <ClientOnly>
    <div class="home-layout gap-x-[14px]">
      <div class="flex flex-col w-[75%]">
        <div class="mb-[14px]">
          <ClientOnly>
            <a-carousel class="h-[120px]">
              <img v-for="banner in bannerList" :key="banner.bannerId" :src="banner.downloadUrl" class="h-full w-full rounded-[10px]" />
            </a-carousel>
          </ClientOnly>
          <!-- <div class="h-[120px] absolute z-[1]">
            <img src="@/assets/images/home/<USER>" class="h-full w-full rounded-[10px]" />
          </div> -->
        </div>
        <div class="h-full grid grid-cols-[2fr_1fr] grid-rows-[1fr_180px] gap-[14px]">
          <div class="my-todo rounded-[10px] bg-[white] px-[16px]">
            <div class="flex justify-between items-center h-[64px]">
              <div class="flex">
                <CardTitle title="我的待办" />
              </div>
            </div>
            <div v-if="mineTodo" class="grid-box grid grid-cols-2 grid-rows-[1fr_1.4fr] gap-[14px]">
              <div v-for="todo in orderTask" :key="todo.module" :class="`task-${todo.module}`">
                <div class="flex items-center h-[46px] text-[#333333] text-[16px] font-bold pl-[23px]">{{ todo.moduleName }}</div>
                <div class="content grid grid-cols-2 pl-[23px]">
                  <div v-for="content in todo.todoContents" :key="content.todoType" class="flex flex-col justify-center text-[#333333] cursor-pointer" @click="goProcess(content)">
                    <span class="leading-[24px] mb-[5px]" @click="goToPage(content.todoType, content.startTime, content.endTime)">{{ content.todoTypeName }}</span>
                    <span
                      ><span class="font-extrabold text-[22px]" @click="goToPage(content.todoType, content.startTime, content.endTime)">{{ content.todoCount }}</span
                      >个</span
                    >
                  </div>
                </div>
              </div>
            </div>
            <a-skeleton v-else active />
          </div>
          <div class="rounded-[10px] bg-[white] px-[14px] today-task">
            <div class="flex items-center justify-between mt-[25px] mb-[20px]">
              <CardTitle title="今日待办" />
              <!-- <span class="text-[12px] text-[rgba(102,102,102,0.80)]">全部待办<VueIcon :icon="IconChevronRightFont" /></span> -->
            </div>
            <ClientOnly><CusCalendar /></ClientOnly>
            <div class="task-list">
              <img :src="TipImage" class="tip-image" />
              <div class="text-[rgba(102,102,102,0.80)]">功能建设中，敬请期待</div>
            </div>
          </div>
          <div class="rounded-[10px] bg-[white]">
            <div class="flex items-center justify-between mt-[25px] px-[16px]">
              <CardTitle title="常用链接" />
              <!-- <span class="text-[12px] text-[rgba(102,102,102,0.80)]">设置<VueIcon :icon="IconChevronRightFont" /></span> -->
            </div>
            <div class="flex items-center h-[128px] px-[33px]">
              <div v-for="(link, index) in linkList" :key="link.sequence" :class="['cursor-pointer', 'flex', 'flex-col', 'items-center', { 'ml-[43px]': index > 0 }]" @click="jumpURL(link.jumpUrl)">
                <img :class="['w-[54px]', 'h-[54px]', 'rounded-[10px]', 'mb-[9px]']" :src="link.icoPath" />
                <span class="text-[#666666]">{{ link.linkName }}</span>
              </div>
            </div>
          </div>
          <div class="rounded-[10px] bg-[white]">
            <div class="flex items-center justify-between mt-[25px] px-[16px] mb-[20px]">
              <CardTitle title="安农学院" />
            </div>
            <div class="flex px-[14px] justify-around gap-x-[8px]">
              <div class="video-left" @click="openFileModal">
                <div class="font-bold ml-[12px] mt-[12px]">操作指南</div>
                <div class="text-[12px] ml-[12px]">全流程SOP</div>
              </div>
              <div class="video-right">
                <div class="font-bold ml-[12px] mt-[12px]">教学视频</div>
                <div class="text-[12px] ml-[12px]">清单管理操作</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="w-[25%] flex flex-col gap-y-[14px]">
        <div class="bg-[white] rounded-[10px] px-[14px] h-[120px] bg-image-url relative">
          <!-- <div class="text-[12px] w-[80px] absolute right-[5px] top-[0px] text-[#fff] text-center">{{ personInfo?.role || '' }}</div> -->
          <div v-if="personInfo" class="flex mt-[29px] mb-[27px]">
            <img :class="['rounded-full', 'h-[46px]', 'w-[46px]', 'mr-[10px]']" :src="personInfo.profilePicture" />
            <div class="flex flex-col justify-between">
              <div class="text-[16px] font-bold leading-[18px] text-[#000000] mb-[12px]">{{ personInfo.name }}</div>
              <div class="text-[12px] text-[#333333] mb-[14px]">
                {{ personInfo.department }}&nbsp; {{ personInfo.businessLine }}&nbsp;|&nbsp;
                <a-tooltip placement="top">
                  <template #title>
                    <div v-for="(role, index) in personInfo.positionAndRole" :key="index">{{ role }}</div>
                  </template>
                  <span> {{ personInfo.position }}</span>
                </a-tooltip>
              </div>
            </div>
          </div>
        </div>
        <div class="px-[14px] bg-[white] h-[170px] rounded-[10px]">
          <div class="flex justify-between items-center mt-[22px] mb-[21px]">
            <CardTitle title="公告" />
            <!-- <div class="text-[12px] text-[rgba(102,102,102,0.80)]">查看全部<VueIcon :icon="IconChevronRightFont" /></div> -->
          </div>
          <div class="notice-list">
            <img :src="TipImage" class="tip-image" />
            <div class="text-[rgba(102,102,102,0.80)]">功能建设中，敬请期待</div>
            <!-- <div v-for="notice in notieList" :key="notice.sequence" class="text-[#333333] font-semibold mb-[17px]">{{ notice.date }}&nbsp;&nbsp;{{ notice.title }}</div> -->
          </div>
        </div>
        <div class="bg-[white] rounded-[10px] px-[14px] pb-[10px] grow flex flex-col">
          <div class="flex justify-between items-center mt-[22px] mb-[10px]">
            <CardTitle title="互动专区" />
            <!-- <div class="text-[12px] text-[rgba(102,102,102,0.80)]">查看全部<VueIcon :icon="IconChevronRightFont" /></div> -->
          </div>
          <div class="flex gap-x-[8px] mb-[12px]">
            <div v-for="help in helpTypes" :key="help.type" :class="['cursor-pointer', 'flex', 'items-center', 'justify-center', 'w-[60px]', 'h-[28px]', 'rounded', 'border', 'border-solid', help.type === activeType ? 'text-[#11bf7e]' : 'border-[rgba(217,217,217,1)]']" @click="changeActiveType(help.type)">
              <span :class="['text-[12px]', help.type === activeType ? 'text-[#11BF7E]' : 'text-[rgba(102,102,102,0.8)]']">{{ help.name }}</span>
            </div>
          </div>
          <div v-if="activeType === '3'" class="grow flex flex-col-reverse">
            <div class="qrcode-box cursor-pointer">
              <div class="scan-box">
                <div class="mt-[10px] mb-[5px[] text-[12px] text-[#969696]">快乐平安扫一扫</div>
                <img class="w-[180px] h-[180px]" :src="scanCode" />
              </div>
              <div class="qrcode-bannner z-10 flex flex-col justify-around">
                <div class="font-bold ml-[16px] text-[#333333]">互动群</div>
                <div class="ml-[16px] text-[12px] text-[#333333]">欢迎加入互动群，有专人回复您的疑问</div>
                <div class="flex justify-between px-[16px]">
                  <img class="h-[25px]" :src="peopleTip" />
                  <div class="h-[25px] leading-[25px] px-[12px] bg-[#ffffff] rounded-[12px] text-[12px] font-semibold">立即加入</div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="grow flex flex-col justify-center items-center">
            <img :src="TipImage" class="tip-image" />
            <div class="text-[rgba(102,102,102,0.80)]">功能建设中，敬请期待</div>
          </div>
        </div>
        <div class="bg-[white] rounded-[10px] p-14px">
          <CardTitle title="智能助手" />
          <div class="mt-[16px]">
            <div class="relative flex flex-col rounded justify-around py-[14px] w-full h-[40px] bg-banner-url">
              <div class="ml-[16px] text-[12px] text-[#333333]">hi,我是农博士</div>
              <div class="ml-[16px] text-[12px] text-[#333333] font-bold">承保理赔有什么问题都来找我聊聊吧～</div>
              <img src="assets/images/home/<USER>" class="absolute w-[75px] right-0 bottom-0 cursor-pointer" @click="openDoctorNong" />
            </div>
          </div>
        </div>
      </div>
      <a-modal v-model:open="fileVisible" title="下载文件">
        <div v-for="file in helpFile?.fileDetailList" :key="file.documentId" class="text-[#576B95] cursor-pointer" @click="getFileURL(file)">
          <VueIcon :icon="IconTongyongXiazaiFont" />
          {{ file.documentName }}
        </div>
      </a-modal>
    </div>
  </ClientOnly>
</template>

<script setup lang="ts">
import CardTitle from './components/CardTitle.vue';
import CusCalendar from './components/CusCalendar.vue';
import type { Banner, Person, TodoData, Notice, LinkInfo, HelpInfo, Course, TodayTask } from './home';
import TipImage from '@/assets/images/home/<USER>';
import peopleTip from '@/assets/images/home/<USER>';
import scanCode from '@/assets/images/home/<USER>';
import { IconTongyongXiazaiFont } from '@pafe/icons-icore-agr-an';
import { $postOnClient, $getOnClient } from '~/composables/request';
import dayjs from 'dayjs';
import { SUCCESS_CODE } from '~/utils/constants';
import bannerUrl from '@/assets/images/home/<USER>';

const helpTypes = [
  { name: '常见问题', type: '1' },
  { name: '行业动态', type: '2' },
  { name: '互动交流', type: '3' },
];
const activeType = ref('3');
const changeActiveType = async (newActive: string) => {
  activeType.value = newActive;
  try {
    const { data: _essayList = [] } = await $getOnClient<HelpInfo[]>(baseUrl + '/dashboard/help', { helpType: activeType.value });
    essayList.value = _essayList;
  } catch (e) {
    console.log(e);
  }
};

const { gateWay, service } = useRuntimeConfig().public || {};
const baseUrl = gateWay + service.administrate;

const router = useRouter();
// 跳转到处理页面
const goProcess = ({ todoType, startTime, endTime }) => {
  const urlMap = {
    endorseToUnd: '/endorseVerify',
    applyToUnd: '/policyReview',
    docSuppleToUnd: '/infoModificationAudit',
  };
  if (urlMap[todoType]) {
    router.push({
      path: urlMap[todoType],
      query: { startTime: dayjs(startTime).format('YYYY-MM-DD'), endTime: dayjs(endTime).format('YYYY-MM-DD') },
    });
  }
};

const mineTodo = shallowRef<TodoData>();
const essayList = shallowRef<HelpInfo[]>([]);
const bannerList = shallowRef<Banner[]>([{ downloadUrl: bannerUrl, bannerId: -234, sequence: -234, jumpUrl: '', bannerName: '' }]);
const personInfo = shallowRef<Person>();
const notieList = shallowRef<Notice[]>([]);
const dayList = shallowRef<TodayTask[]>([]);
const linkList = shallowRef<LinkInfo[]>([]);
const courseList = shallowRef<Course[]>([]);
const helpFile = shallowRef();
const fileVisible = ref(false);

const orderTemp = ['farmerlist', 'endorse', 'applypolicy', 'policy'];
const orderTask = computed(() => {
  const temp = mineTodo.value?.todoLists || [];
  return temp.sort((a, b) => orderTemp.findIndex((v) => v === a.module) - orderTemp.findIndex((v) => v === b.module));
});

const fetchData = async () => {
  const endpoints = [
    { url: '/dashboard/ask', ref: personInfo },
    { url: '/dashboard/bulletin', ref: notieList },
    { url: '/dashboard/todoOfToday', ref: dayList, params: { date: dayjs().format('YYYY-MM-DD') } },
    { url: '/dashboard/commonLink', ref: linkList },
    { url: '/dashboard/help', ref: essayList, params: { helpType: activeType.value } },
    { url: '/dashboard/course', ref: courseList },
    { url: '/course/document/listPage', ref: helpFile, params: { pageSize: 10, pageNum: 1 } },
    { url: '/dashboard/getCarouselImages', ref: bannerList },
  ];
  await Promise.allSettled(
    endpoints.map(async ({ url, ref, params }) => {
      try {
        const { data, code } = await $getOnClient(baseUrl + url, params);
        if (code === SUCCESS_CODE) {
          ref.value = data;
        }
      } catch (e) {
        console.error(`Failed to fetch ${url}:`, e);
      }
    }),
  );
};

const getTodo = async () => {
  try {
    $getOnClient<TodoData>(baseUrl + '/dashboard/todo').then((res) => {
      if (res?.code === SUCCESS_CODE) {
        mineTodo.value = res.data;
      }
    });
  } catch (e) {
    console.log(e);
  }
};

onMounted(async () => {
  setTimeout(() => {
    // 接口图片影响到LCP指标
    fetchData();
    getTodo();
  }, 10);
});

const jumpURL = (url: string) => {
  window.open(url);
};

const openFileModal = () => {
  fileVisible.value = true;
};

const getFileURL = async ({ uploadPath, bucketName, documentName }) => {
  await $postOnClient(
    '/api/iobs/getInIobsUrl',
    { fileKey: uploadPath, storageTypeCode: '02', iobsBucketName: bucketName, downloadFlag: true, fileName: documentName },
    {
      onResponse({ response }) {
        const fileURL = response._data.data.fileUrl;
        window.open(fileURL);
      },
    },
  );
};
const goToPage = (todoType: string, startTime?: string, endTime?: string) => {
  if (todoType === 'docToSupply') {
    // 资料待补交跳转到信息修改
    router.push({
      path: '/infoModify',
      query: { docToSupply: '0', startTime, endTime },
    });
  } else if (todoType === 'mpToReport') {
    // 产品待报备跳转到最新保单;
    router.push({
      path: '/policyQuery',
      query: { mpPlReportStatus: '0', startTime, endTime },
    });
  } else if (todoType === 'endorseToAskUnd') {
    // 批单待申请核保 跳转 批改跟踪
    router.push({
      path: '/endorseTracking',
      query: {
        fromHome: '1',
        startTime,
        endTime,
      },
    });
  } else if (todoType === 'applyToAskUnd') {
    // 投保单待申请核保 跳转 投保跟踪
    router.push({
      path: '/insuranceTracking',
      query: {
        fromHome: '1',
        startTime,
        endTime,
      },
    });
  }
};

const doctorNongOpen = inject('doctorNongOpen'); // 农博士打开or关闭
const openDoctorNong = () => {
  doctorNongOpen.value = true;
};
</script>

<style lang="less" scoped>
.home-layout {
  margin: 14px;
  min-height: calc(100% - 28px - 34px);
  display: flex;
  .width-full {
    height: 100%;
    width: 100%;
    & :deep(.ant-skeleton-image) {
      width: 100%;
      height: 100%;
    }
  }
  .bg-image-url {
    background-image: url(/assets/images/home/<USER>
    background-size: cover;
  }
  .bg-banner-url {
    background-image: url(/assets/images/home/<USER>
    background-size: cover;
  }
  :deep(.ant-carousel .slick-slider) {
    height: 100%;
    .slick-list {
      height: 100%;
      .slick-track {
        height: 100%;
        .slick-slide > div {
          height: 100%;
        }
      }
    }
  }
}
.my-todo {
  .task-farmerlist {
    background-image: url('@/assets/images/home/<USER>');
    background-size: cover;
    border-radius: 4px;
    .content {
      height: calc(100% - 46px);
    }
  }
  .task-applypolicy {
    background-image: url('@/assets/images/home/<USER>');
    background-size: cover;
    border-radius: 4px;
    .content {
      height: calc(100% - 46px);
    }
  }
  .task-endorse {
    background-image: url('@/assets/images/home/<USER>');
    background-size: cover;
    border-radius: 4px;
    .content {
      height: calc(100% - 46px);
    }
  }
  .task-policy {
    background-image: url('@/assets/images/home/<USER>');
    background-size: cover;
    border-radius: 4px;
    .content {
      height: calc(100% - 46px);
    }
  }
  .grid-box {
    height: calc(100% - 78px);
  }
}

.personal-block {
  position: relative;

  .top-bg {
    height: 55px;
    opacity: 0.32;
    background-image: linear-gradient(90deg, #a7f2f4 3%, #f0fdfd 35%, #dafaf1 76%, #c2f6e4 100%);
    border-radius: 10px 10px 0px 0px;
  }
}

.today-task {
  .time-line {
    position: relative;
    padding-left: 15px;
    border-left: 1px dashed #07c160;
    padding-bottom: 15px;
  }
}

.tip-image {
  width: 60px;
  margin-bottom: 10px;
}

.task-list {
  height: calc(100% - 220px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.notice-list {
  height: calc(100% - 70px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.help-list {
  height: calc(100% - 210px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.video-left {
  width: 134px;
  height: 92px;
  background-image: url('@/assets/images/home/<USER>');
  background-size: cover;
  cursor: pointer;
}

@media (min-width: 1441px) {
  .video-left,
  .video-right {
    transform: scale(1.2);
  }
}

.video-right {
  width: 134px;
  height: 92px;
  background-image: url('@/assets/images/home/<USER>');
  background-size: cover;
  cursor: pointer;
}

.qrcode-box {
  position: relative;
  border-radius: 10px;
  padding-top: 40px;
  background: rgba(222, 244, 235, 0.4);
  .scan-box {
    text-align: center;
    width: 90%;
    border-radius: 12px 12px 0 0;
    height: 50px;
    box-shadow: 0px 0px 10px 0px rgba(62, 73, 84, 0.11);
    background: #ffffff;
    position: absolute;
    top: -10px;
    left: 0;
    right: 0;
    margin: 0 auto;
    overflow: hidden;
    transition:
      height 0.5s ease,
      transform 0.5s ease;
  }
  @media (min-width: 1441px) {
    .scan-box {
      top: -30px;
      height: 80px;
    }
  }
}
.qrcode-box:hover {
  .scan-box {
    height: 210px;
    transform: translateY(calc(-100% + 60px));
    z-index: 10;
  }
  @media (min-width: 1441px) {
    .scan-box {
      height: 220px;
      transform: translateY(calc(-100% + 80px));
    }
  }
}
.qrcode-deactive {
  height: 60px;
  width: 90%;
}
.qrcode-bannner {
  position: relative;
  border-radius: 10px;
  height: 85px;
  background-image: url('@/assets/images/home/<USER>');
  background-size: cover;
}

@media (min-width: 1441px) {
  .qrcode-bannner {
    height: 110px;
  }
}
</style>
