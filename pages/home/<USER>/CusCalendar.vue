<template>
  <div class="calendar">
    <div class="header">
      <div class="text-[#161616] text-[18px]">{{ currentYear }}/{{ currentMonth }}/{{ currentDay }}</div>
      <div class="today-btn" @click="resetToToday">回到今日</div>
    </div>
    <div class="flex justify-between items-center w-full mb-[22px]">
      <span class="text-[#D4D6D9] cursor-pointer" @click="prevWeek"><VueIcon :icon="IconChevronLeftFont" /></span>
      <div v-for="(day, index) in currentWeekDays" :key="day" class="flex flex-col justify-between">
        <div class="text-[12px] text-[#999999]">{{ days[index] }}</div>
        <div :class="['day-item', { selected: day === currentDay }]" @click="selectDate(day)">{{ day }}</div>
      </div>
      <span class="text-[#D4D6D9] cursor-pointer" @click="nextWeek"><VueIcon :icon="IconChevronRightFont" /></span>
    </div>
  </div>
</template>

<script setup>
import dayjs from 'dayjs';
import { IconChevronLeftFont, IconChevronRightFont } from '@pafe/icons-icore-agr-an';

// 当前日期
const currentDate = ref(dayjs());

// 年、月、日计算属性
const currentYear = computed(() => currentDate.value.year());
const currentMonth = computed(() => currentDate.value.month() + 1);
const currentDay = computed(() => currentDate.value.date());

const days = ['日', '一', '二', '三', '四', '五', '六'];

// 计算当前周的日期
const currentWeekDays = computed(() => {
  const startOfWeek = currentDate.value.startOf('week'); // 获取本周的开始日期（周日）

  return Array.from({ length: 7 }, (_, index) => {
    return startOfWeek.date() + index; // 返回当前周的日期
  });
});

// 回到今天
const resetToToday = () => {
  currentDate.value = dayjs(); // 重置为当前日期
};

// 上一周
const prevWeek = () => {
  currentDate.value = currentDate.value.subtract(7, 'day'); // 前进一周
};

// 下一周
const nextWeek = () => {
  currentDate.value = currentDate.value.add(7, 'day'); // 后退一周
};

// 选择日期
const selectDate = (day) => {
  const selectedDate = dayjs().year(currentYear.value).month(currentMonth.value - 1).date(day);
  console.log(`选中了日期: ${selectedDate.format('YYYY/MM/DD')}`);
  currentDate.value = selectedDate; // 选中后更新当前日期
};
</script>

<style scoped>
.calendar {
  width: 100%;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 20px;
}
.today-btn {
  color: #07c160;
  cursor: pointer;
  font-size: 12px;
}
.selected {
  background-color: #11bf7e;
  color: white;
}
.day-item {
  margin-top: 9px;
  width: 24px;
  height: 24px;
  border-radius: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
</style>
