<template>
  <div class="relative">
    <span class="relative text-[16px] leading-[18px] font-bold text-[#000000] z-10">{{ title }}</span>
    <div class="title-tag" />
  </div>
</template>

<script setup lang="ts">
const { title = '' } = defineProps<{ title: string }>();
</script>

<style lang="less" scoped>
.title-tag {
  position: absolute;
  height: 10px;
  width: 21px;
  left: 0px;
  bottom: 2px;
  background-image: linear-gradient(1deg, #33CFF0 0%, #9EFFA2 100%);
  border-radius: 5px;
}
</style>
