<template>
  <div id="file_dom" class="pl-[12px]">
    <div class="flex items-center justify-between sticky top-0 z-[300] bg-white border-b-[1px] border-[rgba(241,244,245,1)] border-solid border-x-0 border-t-0 pb-[5px]">
      <a-checkbox v-model:checked="checkAllState.checkAll" :indeterminate="checkAllState.indeterminate" @change="onCheckAllChange">全选</a-checkbox>
    </div>
    <div class="pt-[8px] flex flex-wrap">
      <a-checkbox-group v-model:value="checkAllState.checkedList" @change="checkedValue">
        <template v-for="(item, index) in fileList" :key="index">
          <div class="content bg-[#FAFAFC] border border-[rgba(241,241,241,1)] border-solid rounded-[4px] mr-[10px] mb-[12px]">
            <SingleFile :format="item.documentFormat" :url="item.thumbnail" :document-name="item.documentName" :document-format="item.documentFormat" @preview="preview(index)">
              <div class="absolute top-[6px] right-[8px] z-20">
                <a-checkbox :value="item.uploadPath" />
              </div>
            </SingleFile>
            <a-empty v-if="fileList?.length === 0 || fileList === null" :image="simpleImage" />
          </div>
        </template>
      </a-checkbox-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Empty } from 'ant-design-vue';
import type { fileListType } from '../auditDetail.d';
import SingleFile from './fileFall/SingleFile.vue';

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const checkAllState = reactive<{ // 页面全选状态
  checkAll: boolean;
  indeterminate: boolean;
  checkedList: string[];
}>({
  checkAll: false,
  indeterminate: false,
  checkedList: [],
});
const { fileList = [] } = defineProps<{
  fileList: fileListType[];
}>();
const onCheckAllChange = () => {
  if (checkAllState.checkAll) {
    checkAllState.indeterminate = false;
    checkAllState.checkedList = [];
    fileList.forEach((item) => {
      checkAllState.checkedList.push(item.uploadPath);
    });
  } else {
    checkAllState.checkedList = [];
  }
};
const emit = defineEmits(['preview']);
// 查看
const preview = (index: number) => {
  emit('preview', index);
};
  // 控制身份信息等同层级的选择checkbox的样式
const checkedValue = () => {
  if (checkAllState.checkedList?.length > 0 && checkAllState.checkedList?.length !== fileList.length) {
    checkAllState.indeterminate = true;
  } else {
    checkAllState.indeterminate = false;
  }
  checkAllState.checkAll = checkAllState.checkedList.length === fileList.length;
};
</script>
