<template>
  <a-spin :spinning="loading">
    <div class="grid grid-cols-3 gap-12px">
      <div v-for="(classify) in classifyList" :key="classify.typeCode" class="classify-container">
        <div class="flex items-center justify-between mb-[8px]">
          <div class="flex items-center">
            <span class="sub-title">{{ classify.typeName }}</span>
          </div>
        </div>
        <div class="grid-container">
          <div v-for="file in classify.fileDetailList" :key="file.documentId" class="file-box" draggable="true">
            <div class="h-full flex flex-col justify-center items-center">
              <img v-if="file.thumbnail" class="h-[80px] w-[70px]" :src="file.thumbnail" @click="handleClickFile(file, classify.fileDetailList)">
              <div v-else class="text-[50px]" @click="handleClickFile(file, classify.fileDetailList)">
                <VueIcon :icon="getFileIcon(file.documentName)" />
              </div>
              <div class="text-xs text-[#606060] mt-[8px] truncate max-w-[100px]">{{ file.documentName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-spin>
  <ImageViewer v-if="viewList.length > 0" ref="viewRef" :list="viewList" :current="viewCurrent" />
</template>

<script setup lang="ts">
import { IcPdfColor, IcWordColor, IcDianziqingdanColor, IcWenjianColor } from '@pafe/icons-icore-agr-an';
import type { UploadFile, FileModule } from '../auditDetail.d';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import ImageViewer from '@/components/ui/ImageViewer.vue';

const { gateWay, service } = useRuntimeConfig().public || {};
const loading = ref<boolean>(false);

const route = useRoute();

// 分类列表
const classifyList = ref<Array<FileModule>>([]);

const getDocList = async () => {
  loading.value = true;
  const fetchUrl = gateWay + service.endorse + '/web/infomodify/attachment/queryAttachmentDetail';
  try {
    const { code, data } = await $postOnClient(fetchUrl, { bizNo: route.query.bizNo, bizType: route.query.bizType, idEvntDocInfoMdfyDataTrc: route.query.idEvntDocInfoMdfyDataTrc });
    if (code === SUCCESS_CODE && data) {
      classifyList.value = data.uploadFileListVO.fileTypeList.map(item => ({
        ...item,
        checkedAll: false,
        indeterminate: false,
      }));
      loading.value = false;
    }
  } catch (e) {
    console.log(e);
  } finally {
    loading.value = false;
  }
};
watch(() => [route.query.bizNo, route.query.bizType, route.query.idEvntDocInfoMdfyDataTrc], () => {
  getDocList();
}, {
  immediate: true,
});
const handleClickFile = async (file: UploadFile, fileList: UploadFile[]) => {
  // 如果点击图片，则预览，否则下载
  if (file.thumbnail) {
    viewCurrent.value = fileList.findIndex(k => k.documentId === file.documentId);
    viewList.value = fileList.map(v => ({
      bucketName: v.bucketName,
      uploadPath: v.uploadPath,
      thumbnail: v.thumbnail,
      documentFormat: v.documentFormat,
      documentName: v.documentName,
      id: v.documentId,
    }));
    if (viewRef.value) {
      viewRef.value?.openPreview();
    }
  } else {
    const { data } = await $postOnClient('/api/iobs/getInIobsUrl', {
      iobsBucketName: file.bucketName,
      fileKey: file.uploadPath,
      storageTypeCode: file.storageType,
    }) || {};
    if (data?.fileUrl) {
      window.open(data?.fileUrl);
    }
  }
};
const getFileIcon = (fileName: string) => {
  const fileExtension = fileName.slice(((fileName.lastIndexOf('.') - 1) >>> 0) + 2).toLocaleUpperCase();
  return fileTypeMap.get(fileExtension) || IcWenjianColor;
};
const fileTypeMap = new Map([
  ['XLSX', IcDianziqingdanColor],
  ['XLS', IcDianziqingdanColor],
  ['DOC', IcWordColor],
  ['DOCX', IcWordColor],
  ['PDF', IcPdfColor],
]);
const viewCurrent = ref(0);
const viewList = ref<{
  bucketName: string;
  uploadPath: string;
  thumbnail: string; // 缩略图url
  documentFormat?: string; // 文件类型 img/pdf/word/excel 4种类型，非后缀名
  documentName?: string;
  id?: string; }[]>([]);
const viewRef = ref(null);
</script>

<style lang="less" scoped>
.sub-title {
  position: relative;
  font-size: 14px;
  color: #404442;
  font-weight: 600;
  padding-left: 8px;

  &::before {
    position: absolute;
    left: 0px;
    top: 4px;
    content: '';
    width: 3px;
    height: 10px;
    background: #07c160;
  }
}
.file-box {
  cursor: pointer;
  border: 1px solid rgba(241,241,241,1);
  border-radius: 4px;
  width: 100%;
  height: 130px;
  position: relative;
  background: #ffffff;
}
.classify-container {
  background: #FAFAFC;
  border: 1px solid rgba(241,241,241,1);
  border-radius: 4px;
  padding: 12px;
}
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 默认是3列布局 */
  gap: 8px; /* 网格间距 */
  max-height: 580px;
  overflow-y: auto;
  overflow-x: hidden;
}
@media (min-width: 1920px) {
  .grid-container {
    grid-template-columns: repeat(4, 1fr); /* 1920及以上为4列布局 */
  }
}
</style>
