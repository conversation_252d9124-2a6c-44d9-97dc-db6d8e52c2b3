<template>
  <div class="attachment box-border bg-white rounded-t-[4px] px-[12px] pt-[16px] flex flex-col">
    <div class="flex grow overflow-hidden">
      <div>
        <OverlayScrollbarsComponent class="left w-[240px] bg-[#FAFAFC] h-full box-border">
          <div class="px-[8px] py-[12px]">
            <FileCatalogue v-model:selected-key="treeSelectKey" v-model:spin-loading="spinLoading" :tree-data="treeData" @handle-click="handleParentClick" />
          </div>
        </OverlayScrollbarsComponent>
      </div>
      <OverlayScrollbarsComponent class="grow h-full right box-border">
        <a-spin :spinning="loading">
          <div v-if="fileLists?.length > 0" class="py-[12px]">
            <FileItem v-model:selected-key="treeSelectKey" :file-list="fileLists" @preview="preview" />
            <div class="text-right">
              <a-pagination v-model:current="pageNum" v-model:page-size="pageSize" size="small" :total="totals" :show-total="total => `总共 ${totals} 条`" @change="handleChangePage" />
            </div>
          </div>
          <div v-else class="w-[100%] h-auto mt-[20%]">
            <a-empty :image="simpleImage" />
          </div>
        </a-spin>
      </OverlayScrollbarsComponent>
      <ImageViewer ref="viewer" :list="fileLists" :current="previewCurrent" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Empty } from 'ant-design-vue';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-vue';
import type { FileInfo, FileManagerRes, fileListType, ListByPageType } from '../auditDetail.d';
import FileCatalogue from './FileCatalogue.vue';
import FileItem from './FileItem.vue';
import 'overlayscrollbars/styles/overlayscrollbars.css';
import { $postOnClient, usePost } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import ImageViewer from '@/components/ui/ImageViewer.vue';

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const { gateWay, service } = useRuntimeConfig().public || {};
const treeData = ref<FileInfo[]>([]); // 树数据
const treeSelectKey = ref(''); // 选中树节点
const fileLists = ref<fileListType[]>([]);
const route = useRoute();
const fileTotal = reactive<{
  needUploadFiles: string[];
  notUploadedFiles: string[];
  uploadedFiles: string[];
}>({
  needUploadFiles: [],
  notUploadedFiles: [],
  uploadedFiles: [],
});
const loading = ref<boolean>(false);
const fileManageReq = await usePost<FileManagerRes, { bizNo: string; bizType: string }>(`${gateWay}${service.administrate}/attachment/document/list`, { server: false }); // 附件列表接口,无需进行服务端调用接口
const spinLoading = ref<boolean>(false);
const getfileManageInfo = async () => {
  // 获取文件列表
  try {
    loading.value = true;
    const { data, code, msg } = await fileManageReq.fetchData({ bizNo: (route.query.bizNo || '') as string, bizType: (route.query.bizType || '') as string }) || {};
    // const { data, code, msg } = await fileManageReq.fetchData({ bizNo: '72104006500000015105', bizType: 'docTypeEndorse' }) || {};
    if (code === SUCCESS_CODE) {
      // 处理数据
      const fileInfoList = data?.fileTypeList || [];
      fileTotal.needUploadFiles = data?.requiredFileTypeList || [];
      fileTotal.notUploadedFiles = data?.unUploadedFileTypeList || [];
      fileTotal.uploadedFiles = data?.uploadedFileTypeList || [];
      treeData.value = fileInfoList?.map((item) => {
        (item?.children || [])?.forEach((dom) => {
          dom.title = dom.title + `（${dom.numberOfFile || 0}）`;
        });
        return {
          ...item,
          title: item.title + `（${item.numberOfFile || 0}）`,
        };
      }); // 树数据
      if (fileInfoList[0] && fileInfoList[0].children) { // 树节点默认打开
        treeSelectKey.value = fileInfoList[0].children[0].key;
      } else if (fileInfoList[0]) {
        treeSelectKey.value = fileInfoList[0].key;
      } else {
        treeSelectKey.value = '';
      }
      handleClick();
    } else {
      treeData.value = [];
      fileLists.value = [];
      message.error(msg || '请求有误，请稍后重试');
    }
    loading.value = false;
  } catch (err) {
    console.log(err);
    loading.value = false;
  }
};
  // watcheffect会调2次接口
watch(() => route.query.bizNo, (val) => {
  if (val) {
    getfileManageInfo();
  }
}, {
  immediate: true,
});
// 点击其他节点，分页为1，调用接口
const handleParentClick = () => {
  pageNum.value = 1;
  handleClick();
};
const totals = ref<number>(0);
const pageSize = ref<number>(20);
const pageNum = ref<number>(1);
// 右侧文件列表
const handleClick = async () => {
  fileLists.value = [];
  try {
    loading.value = true;
    const res = await $postOnClient<ListByPageType>(`${gateWay}${service.administrate}/attachment/document/listByPage`, {
      bizNo: route.query.bizNo || '', bizType: route.query.bizType, fileTypeCode: treeSelectKey.value, pageNum: pageNum.value, pageSize: pageSize.value,
    });
    if (res?.code === SUCCESS_CODE) {
      const { fileList, total = 0, current = 1, size } = res?.data || {};
      fileLists.value = fileList || []; // 右侧文件列表
      totals.value = total;
      pageNum.value = current || 1;
      pageSize.value = size;
    } else {
      fileLists.value = [];
    }
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
    fileLists.value = [];
  }
};
const handleChangePage = (page: number) => {
  pageNum.value = page;
  handleClick();
};
const viewer = ref();
const previewCurrent = ref(0); // 当前要预览的文件id
// 查看
const preview = (index: number) => {
  previewCurrent.value = index;
  if (viewer.value) {
    viewer.value?.openPreview();
  }
};
</script>

  <style lang="less" scoped>
  .attachment {
    height: calc(100% - 34px - 16px);
  }
  .h-full :deep(.os-scrollbar.os-scrollbar-vertical.os-scrollbar-cornerless) {
    top: 40px;
  }
  </style>
