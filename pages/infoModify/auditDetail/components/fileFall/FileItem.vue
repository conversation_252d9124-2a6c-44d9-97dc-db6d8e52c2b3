<template>
  <!-- classname file-item-dom不可删除 -->
  <div class="item block file-item-dom">
    <div class="title mb-[6px]">
      <a-checkbox
        v-model:checked="checkAll"
        :indeterminate="indeterminate"
      >
        <span class="font-medium text-[rgba(0,0,0,0.90)] text-[16px]">{{ fileItem.title || '' }}({{ fileItem.fileLength || '0' }})</span>
      </a-checkbox>
    </div>
    <a-checkbox-group
      v-model:value="checkedList"
      style="width: 100%"
      class="block"
      @change="checkedValue"
    >
      <div class="content bg-[#FAFAFC] border border-[rgba(241,241,241,1)] border-solid pt-[6px] rounded-[4px] mb-[12px]">
        <template v-if="fileItem.children && fileItem.children.length">
          <!-- classname file-item-children-dom不可删除 -->
          <div
            v-for="(child, childIndex) in fileItem.children"
            :key="childIndex"
            class="file-item-children-dom"
          >
            <div class="pl-[19px] text-[14px] mb-[1px] text-[#404442] before:content-[''] before:block before:absolute before:h-[10px] before:w-[3px] before:left-[12px] before:bg-[#07C160] before:top-[7px] relative leading-[24px]">
              {{ child.title }}({{ child.fileList?.length || 0 }})
            </div>
            <div class="file-list flex flex-wrap pl-[5px] mb-[5px]">
              <SingleFile
                v-for="(file, fileIndex) in child.fileList"
                :key="fileIndex"
                :format="file.documentFormat"
                :url="file.thumbnail"
                @preview="preview(file.documentGroupItemsId)"
              >
                <div class="absolute top-[6px] right-[8px] z-20">
                  <a-checkbox :value="file.uploadPath" />
                </div>
              </SingleFile>
              <div v-if="child.fileList?.length === 0 || child.fileList === null" class="group w-[180px] h-[130px] rounded-[4px] bg-[#fff] m-[5px] border border-[rgba(241,241,241,1)] border-solid relative flex items-center justify-center cursor-pointer">
                <a-empty :image="simpleImage" />
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="file-list flex flex-wrap pl-[5px]">
            <SingleFile
              v-for="(file, fileIndex) in fileItem.fileList"
              :key="fileIndex"
              :format="file.documentFormat"
              :url="file.thumbnail"
              @preview="preview(file.documentGroupItemsId)"
            >
              <div class="absolute top-[6px] right-[8px] z-20">
                <a-checkbox :value="file.uploadPath" />
              </div>
            </SingleFile>
            <div v-if="fileItem.fileList?.length === 0 || fileItem.fileList === null" class="group w-[180px] h-[130px] rounded-[4px] bg-[#fff] m-[5px] border border-[rgba(241,241,241,1)] border-solid relative flex items-center justify-center cursor-pointer">
              <a-empty :image="simpleImage" />
            </div>
          </div>
        </template>
      </div>
    </a-checkbox-group>
  </div>
</template>

<script lang="ts" setup>
import { Empty } from 'ant-design-vue';
import type { FileInfo } from '../attachment.d.ts';
import SingleFile from './SingleFile.vue';

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const { fileItem = {} } = defineProps<{
  fileItem: FileInfo;
}>();

const checkAll = defineModel<boolean>('checkAll'); // 是否全选
const checkedList = defineModel<string[]>('checkedList'); // 选中的文件id
const indeterminate = ref(false);

const emit = defineEmits(['preview']);

watch(checkedList, (val: FileInfo[]) => {
  if (val.length > 0 && val.length < fileItem.fileLength) {
    indeterminate.value = true;
  } else {
    indeterminate.value = false;
  }
});
// 控制身份信息等同层级的选择checkbox的样式
const checkedValue = (value: FileInfo[]) => {
  if (value?.length === 0) {
    indeterminate.value = false;
  } else {
    indeterminate.value = true;
  }
  checkAll.value = value.length === fileItem.fileLength;
};

watch(checkAll, (checked) => {
  if (checked) {
    // 勾选了全部
    if (fileItem.children && fileItem.children.length) {
      let list: FileInfo[] = [];
      for (const item of fileItem.children) {
        list = list.concat((item.fileList || []).map((val: FileInfo) => val.uploadPath));
      }
      checkedList.value = list;
    } else {
      checkedList.value = (fileItem.fileList || []).map((val: FileInfo) => val.uploadPath);
    }
  } else if (!checked && !indeterminate.value) {
    checkedList.value = [];
  }
});

const preview = (id: string) => {
  emit('preview', id);
};
</script>

<style lang="less" scoped>
.item :deep(.ant-checkbox-group) {
  cursor: default;
}
</style>
