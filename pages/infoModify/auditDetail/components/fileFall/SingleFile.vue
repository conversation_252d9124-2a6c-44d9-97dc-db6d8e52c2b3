<template>
  <div class="group w-[160px] h-[160px] bg-[#fff] relative text-center cursor-pointer">
    <div class="pt-[10px]">
      <template v-if="isWord(format)">
        <VueIcon :icon="IcWordColor" class="text-[64px]" />
      </template>
      <template v-else-if="isExcel(format)">
        <VueIcon :icon="IcDianziqingdanColor" class="text-[64px]" />
      </template>
      <template v-else-if="isPdf(format)">
        <VueIcon :icon="IcPdfColor" class="text-[64px]" />
      </template>
      <template v-else-if="isZip(format)">
        <VueIcon :icon="IcWenjianColor" class="text-[64px]" />
      </template>
      <template v-else-if="isMail(format)">
        <VueIcon :icon="IcMailColor" class="text-[64px]" />
      </template>
      <template v-else-if="isHtml(format)">
        <VueIcon :icon="IcHtmlColor" class="text-[64px]" />
      </template>
      <template v-else-if="isPpt(format)">
        <VueIcon :icon="IcPptColor" class="text-[64px]" />
      </template>
      <template v-else-if="isTxt(format)">
        <VueIcon :icon="IcTxtColor" class="text-[64px]" />
      </template>
      <template v-else>
        <img class="w-[100px] h-[80px]" :src="url" alt="">
      </template>
    </div>
    <div class="text-left pt-[15px] text-[12px] px-[5px] truncate">文件名称：{{ documentName }}</div>
    <div class="text-left pt-[10px] text-[12px] px-[5px] truncate">文件类型：{{ documentFormat }}</div>
    <slot />
    <div class="absolute left-0 right-0 top-0 bottom-0 bg-[rgba(0,0,0,0.45)] justify-center items-center z-10 group-hover:flex hidden" @click="preview">
      <VueIcon :icon="IconPreviewFont" class="text-[16px] text-[#fff]" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { IcPdfColor, IcWordColor, IcDianziqingdanColor, IconPreviewFont, IcWenjianColor, IcMailColor, IcHtmlColor, IcPptColor, IcTxtColor } from '@pafe/icons-icore-agr-an';
import { VueIcon } from '@pag/icon-vue3';
import { isWord, isPdf, isExcel, isZip, isMail, isHtml, isPpt, isTxt } from '@/utils/tools';

const { format = '', url = '', documentName = '' } = defineProps<{
  format?: string;
  url?: string;
  documentName?: string;
  documentFormat?: string;
}>();

const emit = defineEmits(['preview']);

const preview = () => {
  emit('preview');
};
</script>
