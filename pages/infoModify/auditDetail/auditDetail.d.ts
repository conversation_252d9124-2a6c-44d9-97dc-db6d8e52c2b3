// 上传信息
export interface FileList {
  documentId: string;
  selected: boolean;
  thumbnail: string;
  documentName: string;
}

export interface UploadFile {
  documentName: string;
  documentId: string;
  thumbnail: string; // 缩略图，上传图片会返回
  documentFormat: string;
  uploadPath: string;
  selected: boolean;
  bucketName: string;
  storageType: string;
}

export interface FileModule {
  typeName: string;
  typeCode: string;
  docLimitFlag: string; // 00:非必填 01:出单必录 02：出单可缓， 03：纸质归档
  fileDetailList: Array<UploadFile>;
  checkedAll: boolean;
  indeterminate: boolean;
}

export interface FileInfo {
  key: string; // 附件组类型
  title: string; // 类型名称
  fileList: DocumentInfo[]; // 附件文件列表
  children: FileInfo[]; // 附件子集
  checkAll?: boolean; // 其子目录所有文件已选中
  checkedList?: string[]; // 选中文件的id
  fileLength?: number; // 其子目录或当前目录文件个数
  numberOfFile?: number;
}

export interface DocumentInfo {
  documentGroupItemsId: string; // 附件id
  documentName: string; // 附件名称
  url: string; // 原图URL
  documentFormat: string; // 文件格式
  thumbnail: string; // 缩略图，img/pdf/word/excel 4种类型，非后缀名
}

export interface FileManagerRes {
  fileTypeList: FileInfo[];
  requiredFileTypeList: string[];
  uploadedFileTypeList: string[];
  unUploadedFileTypeList: string[];
}
export interface fileListType {
  bucketName: string;
  documentFormat: string;
  documentGroupItemsId: string;
  documentId: string;
  documentName: string;
  documentStatus: string;
  storageType: string;
  thumbnail: string;
  uploadPath: string;
  checked: boolean;
}
export interface ListByPageType {
  fileList: fileListType[];
  total: number;
  current: number;
  size: number;
}
