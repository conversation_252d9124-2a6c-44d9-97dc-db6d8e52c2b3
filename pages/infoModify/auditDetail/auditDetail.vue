<template>
  <div class="m-14px space-y-16px">
    <div class="rounded">
      <div class="rounded bg-white flex-grow p-12px mb-12px">
        <div class="text-[16px] text-[rgba(0,0,0,0.9)] font-bold mb-8px">审核详情</div>
        <a-descriptions :column="2">
          <a-descriptions-item class="font-number" label="审核结论">{{ approvalData?.approveStatus || '-' }}</a-descriptions-item>
          <a-descriptions-item class="font-number" label="审核人">{{ approvalData?.approvePerson || '-' }}</a-descriptions-item>
          <a-descriptions-item class="font-number" label="审核意见">{{ approvalData?.approveResultDesc || '-' }}</a-descriptions-item>
        </a-descriptions>
      </div>
      <div class="rounded bg-white flex-grow py-12px">
        <div class="text-[16px] text-[rgba(0,0,0,0.9)] font-bold mb-8px px-12px">审核内容</div>
        <div class="ml-[14px] mb-[8px]">上传信息</div>
        <div class="mx-[14px]">
          <UploadFiles />
        </div>
        <div class="mx-[8px] mt-[8px] mb-[8px] px-12px">审核内容-文件信息</div>
        <Attachment />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import UploadFiles from './components/UploadFiles.vue';
import Attachment from './components/Attachment.vue';
import { $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';

const { gateWay, service } = useRuntimeConfig().public || {};
const route = useRoute();
const approvalData = reactive({
  approveStatus: '', // 审批状态
  approveResultDesc: '', // 审批描述
  approvePerson: '', // 审批人
});
const result: Record<string, string> = {
  B4: '通过',
  B7: '不通过',
};
// 查询审核结论
const getResult = async () => {
  const params = {
    voucherNo: route.query.idEvntDocInfoMdfyDataTrc,
    underwriteTypeCode: '4',
  };
  const res = await $getOnClient<Record<string, string>>(gateWay + service.ums + '/underwrite/queryUnderwriteRecordByType', params);
  if (res && res?.code === SUCCESS_CODE) {
    if (res?.data) {
      approvalData.approveStatus = result[res.data.agriculturalUnderwriteStatusCode] || '';
      approvalData.approveResultDesc = res.data.approveResultDesc;
      approvalData.approvePerson = res.data.approvelUm;
    }
  } else {
    message.error(res?.msg);
  }
};
watch(() => route.query.idEvntDocInfoMdfyDataTrc, () => {
  getResult();
}, {
  immediate: true,
});
</script>

<style lang="less" scoped>
.container-height {
  height: calc(100vh - 120px);
  overflow-y: scroll;
}
</style>
