<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="searchForm" :colon="false" class="flex-grow" :model="searchFormState" :label-col="{ style: { width: pxToRem(80) } }">
          <a-row>
            <a-col span="10">
              <a-form-item label="机构" name="departmentCode" required>
                <department-search v-model:contain-child-depart="searchFormState.containLowDepartment" :dept-code="searchFormState.departmentCode" :show-child-depart="true" @change-dept-code="(val: string) => changeDeptCode(val)" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="提交资料时间" name="insureDate" :label-col="{ style: { width: pxToRem(110) } }">
                <a-range-picker v-model:value="searchFormState.insureDate" :disabled="disabled" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="0">
            <a-col span="10">
              <a-form-item label="保单号" name="policyNo">
                <a-input v-model:value.trim="searchFormState.policyNo" placeholder="请输入" @blur="handleBlurDisabled" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="审核状态" name="checkStatusList" required>
                <check-box-group v-model:checked-list="searchFormState.checkStatusList" :options="publicStatusOptions" :disabled="disabled" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button type="default" @click="reset">重置</a-button>
        <a-button type="primary" @click="search">查询</a-button>
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="mb-16px flex justify-between">
        <div class="text-[#404442] text-xl font-bold">查询结果</div>
      </div>
      <a-table
        :columns="listColumns"
        :pagination="pagination"
        :data-source="dataSource"
        :loading="loading"
        :scroll="{ x: 'max-content' }"
        row-key="idPlySummaryInfo"
        :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'table-striped' : undefined)"
        class="table-box"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'policyNo'">
            <div class="flex items-center">
              <CopyLink :text="text" @click="openDetail(text)" />
              <div v-if="record.dataSource === 'icore_agr_icp_self'" class="p-[4px] text-[#0958d9] bg-[#e6f4ff] border-[#91caff] text-[11px] rounded-[12px]" color="blue">自助</div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'insuranceBeginDate'">
            {{ (record.insuranceBeginDate ? record.insuranceBeginDate + ' 至 ' : '') + (record.insuranceEndDate ?? '') }}
          </template>
          <template v-if="['marketproductName', 'insuredName', 'departmentName', 'riskTypeName'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] truncate">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <a-space :size="1">
              <a-button type="link" :disabled="record.checkStatusCode !=='1'" @click="del(record.policyNo, record.idEvntDocInfoMdfyDataTrc)">删除</a-button>
              <a-button type="link" :disabled="record.checkStatusCode !=='2'" @click="recall(record.policyNo, record.idEvntDocInfoMdfyDataTrc)">撤回</a-button>
              <a-button type="link" @click="goToDetails(record.policyNo, record.checkStatusCode, record.idEvntDocInfoMdfyDataTrc)">审核详情</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnsType } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { SearchFormState, DataType } from './infoModificationTrack.d';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost, $getOnClient, $postOnClient } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import { pxToRem } from '@/utils/tools';
import CopyLink from '@/components/ui/CopyLink.vue';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
// 审核状态选项
const publicStatusOptions = [
  { label: '待申请', value: '1' },
  { label: '待审核', value: '2' },
  { label: '通过', value: '3' },
  { label: '不通过', value: '4' },
];
const listColumns: TableColumnsType = [
  { title: '出单机构', dataIndex: 'insureDepartmentName', fixed: 'left' },
  { title: '保单号', dataIndex: 'policyNo' },
  { title: '产品名称', dataIndex: 'productName' },
  { title: '被保险人', dataIndex: 'insuredName' },
  { title: '审核状态', dataIndex: 'checkStatusName' },
  { title: '提交资料时间', dataIndex: 'applySubmitTime' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];
const router = useRouter();
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
const searchForm = ref();
const searchFormState = reactive<SearchFormState>({
  departmentCode: defaultDeptCode.value, // 机构
  containLowDepartment: true, // 是否包含下级
  insureDate: [dayjs().subtract(1, 'month'), dayjs()], // 提交资料时间
  checkStatusList: ['1', '2'], // 审核状态
  policyNo: '', // 保单号
});
const { gateWay, service } = useRuntimeConfig().public || {};
// 改变机构
const changeDeptCode = (val: string) => {
  searchFormState.departmentCode = val;
};
const dataSource = ref<Record<string, string>[]>([]);
const loading = ref(false);
// 查询
const search = async () => {
  try {
    await searchForm.value.validate();
    pagination.current = 1;
    pagination.pageSize = 10;
    refresh();
  } catch (e) {
    console.log(e);
  }
};
const getListReq = await usePost<DataType>(`${gateWay}${service.endorse}/web/attachmentModify/trace/list`);
// 分页查询
const refresh = async () => {
  loading.value = true;
  try {
    const params = {
      policyNo: searchFormState.policyNo,
      departmentCode: searchFormState.departmentCode,
      containLowDepartment: searchFormState.containLowDepartment,
      inputStartDate: searchFormState.insureDate?.[0]?.format('YYYY-MM-DD') || '',
      inputEndDate: searchFormState.insureDate?.[1]?.format('YYYY-MM-DD') || '',
      checkStatusList: searchFormState.checkStatusList,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    const res = await getListReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data || [];
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      message.warning(res?.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const { pagination } = usePagination(refresh);
// 重置
const reset = () => {
  searchForm.value?.resetFields();
  searchForm.value?.clearValidate();
  searchFormState.checkStatusList = ['1', '2'];
  disabled.value = false;
  refresh();
};
// 打开保单详情页
const openDetail = (policyNo: string) => {
  const path = {
    path: '/policyDetails',
    query: {
      policyNo: policyNo,
    },
  };
  router.push(path);
};
  // 表格的select选择框
// type Key = string | number;
// const rowSelection: TableProps['rowSelection'] = {
//   onChange: (selectedRowKeys: Key[], selectedRows: insuranceTrackingType[]) => onSelectChange(selectedRowKeys, selectedRows),
//   getCheckboxProps: (record: insuranceTrackingType) => ({
//     disabled: false,
//     applyStatus: record.applyStatus,
//   }),
// };
// 表格选中ID
// const selectedRow = ref<insuranceTrackingType[]>([]);
// const selectedRowKey = ref<Key[]>([]);
// 选择数据
// const onSelectChange = (selectedRowKeys: Key[], selectedRows: insuranceTrackingType[]) => {
//   selectedRow.value = selectedRows;
//   selectedRowKey.value = selectedRowKeys;
// };

// 单号有值时其余搜索条件禁用,交互逻辑同投保跟踪
const disabled = ref<boolean>(false);
const handleBlurDisabled = async () => {
  if (searchFormState.policyNo) {
    disabled.value = true;
    searchFormState.insureDate = [];
    searchFormState.checkStatusList = ['1', '2', '3', '4'];
    // 动态获取单号类型，和机构code
    const res = await $getOnClient<Record<string, string>>(gateWay + service.administrate + '/public/getBizTypeByBizNo', { bizNo: searchFormState.policyNo });
    if (res && res?.code === SUCCESS_CODE && res?.data) {
      searchFormState.departmentCode = res.data?.insureDepartmentNo || '';
    }
  } else {
    disabled.value = false;
    searchFormState.departmentCode = defaultDeptCode.value;
    searchFormState.insureDate = [dayjs().subtract(1, 'month'), dayjs()];
    searchFormState.checkStatusList = ['1', '2'];
  }
};
// 跳转审核详情
const goToDetails = (policyNo: string, checkStatusCode: string, idEvntDocInfoMdfyDataTrc: string) => {
  if (checkStatusCode === '1') { // 待申请
    router.push({
      path: '/fileModify',
      query: {
        bizNo: policyNo,
        idEvntDocInfoMdfyDataTrc: idEvntDocInfoMdfyDataTrc,
      },
    });
  } else {
    router.push({
      path: '/auditDetail',
      query: {
        bizNo: policyNo,
        bizType: 'docViewTreePolicy',
        checkStatusCode: checkStatusCode,
        idEvntDocInfoMdfyDataTrc: idEvntDocInfoMdfyDataTrc,
      },
    });
  }
};
// 删除
const del = async (bizNo: string, idEvntDocInfoMdfyDataTrc: string) => {
  const params = {
    bizNo,
    idEvntDocInfoMdfyDataTrc,
  };
  const res = await $postOnClient<Record<string, string>>(gateWay + service.endorse + '/web/attachmentModify/trace/delete', params);
  if (res && res?.code === SUCCESS_CODE && res?.data) {
    message.success(res?.msg);
    search();
  } else {
    message.error(res?.msg);
  }
};
// 撤回
const recall = async (bizNo: string, idEvntDocInfoMdfyDataTrc: string) => {
  const params = {
    bizNo,
    idEvntDocInfoMdfyDataTrc,
  };
  const res = await $postOnClient<Record<string, string>>(gateWay + service.endorse + '/web/attachmentModify/trace/withdraw', params);
  if (res && res?.code === SUCCESS_CODE && res?.data) {
    message.success(res?.msg);
    search();
  } else {
    message.error(res?.msg);
  }
};
onMounted(() => {
  search();
});
</script>
