<template>
  <a-modal v-model:open="processOpen" title="核保轨迹" centered :width="pxToRem(900)">
    <div class="pb-[12px]">审批链： {{ chainStr || '-' }}</div>
    <a-table :data-source="insureInfo" :columns="columns" :pagination="false" :loading="tableLoading">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'approveStatus'">{{ approveStatusText(record.approveStatus) }}</template>
      </template>
    </a-table>
    <template #footer>
      <a-button type="primary" @click="processOpen = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import type { QueryApprovalTaskList } from '../insureProcess.d';
import { pxToRem } from '@/utils/tools';

// 表格数据
const { insureInfo, chainStr } = defineProps<{
  insureInfo: QueryApprovalTaskList[];
  chainStr: string;
}>();

// 表格loading
const tableLoading = ref<boolean>(false);
// 弹窗
const processOpen = defineModel<boolean>('processOpen', { default: false });
const columns = [
  { title: '发送人', dataIndex: 'createdBy' },
  { title: '上报时间', dataIndex: 'createdDate' },
  { title: '处理人', dataIndex: 'approveUm' },
  { title: '核保时间', dataIndex: 'approveTime' },
  { title: '核保意见', dataIndex: 'approveResultDesc' },
  { title: '核保状态', dataIndex: 'approveStatus' },
];
// B3-待审核、B4-已同意、B2-已下发、B7-拒保
const approveStatusText = (type: string) => {
  const approveTypeMap: Record<string, string> = {
    B3: '待审核',
    B4: '已同意',
    B2: '已下发',
    B7: '拒保',
    B21: '审核中',
  };
  return approveTypeMap[type] || '-';
};
</script>
