<template>
  <!-- 清单信息 -->
  <div class="mb-[10px]">
    <div class="font-[600] text-[16px] text-[#333]">清单累计农户数{{ contractModel?.baseInfo?.farmersCount || '0' }}户，客户验真认证已通过。</div>
    <div class="flex flex-start">
      <VueIcon v-if="errorDataInfo?.insureList?.length > 0" class="text-[14px] text-[#FAAD14] mr-[5px] mt-[2px]" :icon="IconErrorCircleFilledFont" />
      <div>
        <div v-for="(item, index) in errorDataInfo?.insureList" :key="index" class="text-[rgba(0,0,0,.6)]"><span>{{ index + 1 }}.</span>{{ item.promptInfo }}</div>
      </div>
    </div>
  </div>
  <a-button type="primary" :loading="loading" @click="singleFarmerListExport">导出清单数据</a-button>
</template>

<script setup lang="ts">
import { IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { ContractModelType, ErrorModelType } from '../insureProcess.d';
import { $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { downloadBlob } from '@/utils/tools';

// 获取信息大对象
const { contractModel, errorDataInfo } = defineProps<{
  contractModel: ContractModelType;
  errorDataInfo: ErrorModelType;
}>();
const { gateWay, service } = useRuntimeConfig().public || {};
const loading = ref<boolean>(false);
// 导出清单数据
const singleFarmerListExport = async () => {
  if (contractModel?.baseInfo?.farmerlistNo === null || !contractModel?.baseInfo?.farmerlistNo) {
    message.warning('未关联清单，无法下载');
    return;
  }
  try {
    loading.value = true;
    const fetchUrl = `${gateWay}${service.farmer}/file/singleFarmerListExport`;
    await $getOnClient(fetchUrl, { position: '3', farmerListNo: contractModel?.baseInfo?.farmerlistNo }, {
      onResponse({ response }) {
        if (response._data instanceof Blob) {
          const contentDisposition = response.headers.get('Content-Disposition');
          const fileData = response._data;
          const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
          if (contentDisposition) {
            const match = contentDisposition.match(/filename=(.+)/);
            const fileName = match ? decodeURI(match[1]) : '';
            downloadBlob(fileData, fileName, fileType);
          }
          message.success('导出成功');
        } else {
          const { code, data, msg = '' } = response._data;
          if (code === SUCCESS_CODE) {
            message.success(data);
          } else if (msg) {
            message.error(msg);
          }
        }
      },
    });
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
};
</script>
