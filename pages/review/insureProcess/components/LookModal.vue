<template>
  <!-- 审批链签报 -->
  <CommonApprovalModal
    v-if="visible"
    v-model:open="visible"
    title="一般临分-EOA签报申请页"
    :show-uploadbtn="true"
    eoa-type="T08"
    :department-code="route.query.departmentCode"
    :show-text="true"
    @ok="handleSubmit"
  >
    <template #content>
      <div class="bg-gray-100 rounded px-14px box-border">
        <div class="flex mb-[10px] flex-wrap">
          <p class="m-0 w-1/1 box-border">
            <span>签报主题：</span>
            <span class="text-[#262626]">{{ lookModalObj.eoaSubject }}</span>
          </p>
        </div>
        <div class="flex flex-wrap">
          <p v-for="(item, index) in titleList" :key="index" class="m-0 w-1/2 box-border">{{ item }}</p>
        </div>
      </div>
      <div class="my-[18px]">
        <div class="flex items-center base-info">
          <VueIcon :icon="SolutionfontFont" />
          <span class="ml-[3px]">核保分出方案</span>
        </div>
        <div class="bg-gray-100 rounded px-14px box-border">
          <a-table v-if="lookModalObj.disDataSource?.[0].poundage !== ''" :columns="disColumns" :data-source="lookModalObj.disDataSource" :pagination="false" class="my-[16px]" />
          <a-table v-if="lookModalObj.dataSource?.[0]?.separationRatio !== ''" :columns="compensateColumns" :data-source="lookModalObj.dataSource" :pagination="false" />
        </div>
      </div>
    </template>
  </CommonApprovalModal>
</template>

<script setup lang="ts">
import { SolutionfontFont } from '@pafe/icons-icore-agr-an';
import type { LookModalObj, EoaData } from '../insureProcess.d';
import { disColumns, compensateColumns } from './columns';
import { SUCCESS_CODE } from '@/utils/constants';
import { $postOnClient } from '@/composables/request';
import CommonApprovalModal from '@/components/ui/CommonApprovalModal.vue';

const visible = defineModel<boolean>('visible', { required: true, default: false });
const { gateWay, service } = useRuntimeConfig().public || {};
const { lookModalObj } = defineProps<{
  lookModalObj: LookModalObj;
}>();
// 签报
const titleList = computed(() => lookModalObj.eoaBody?.split('\n'));
const route = useRoute();
const emit = defineEmits(['queryReinsuranceInfo']);
const formStateRef = ref();
const loading = ref<boolean>(false);
// 发起签报
const handleSubmit = async (eoaData: EoaData) => {
  try {
    await formStateRef.value?.validate();
    loading.value = true;
    const params = {
      relationBusinessNo: route.query.applyPolicyNo,
      departmentCode: route.query.departmentCode,
      eoaType: '001',
      eoaSubject: lookModalObj.eoaSubject,
      eoaBody: lookModalObj.eoaBody,
      productCode: route.query.productCode,
      businessData: {
        idReinsurance: lookModalObj.idReinsurance,
      },
      approveChainList: eoaData.approveChainList,
    };
    const res = await $postOnClient(gateWay + service.ums + '/udw/eoa/createEoa', params);
    if (res?.code === SUCCESS_CODE) {
      message.success(res?.msg);
      emit('queryReinsuranceInfo');
      handleCancel();
    } else {
      message.error(res?.msg);
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
};
// 弹窗关闭
const handleCancel = () => {
  visible.value = false;
};
</script>

  <style lang="less">
  .base-info {
    color: rgba(0,0,0,0.9);
  }
  </style>
