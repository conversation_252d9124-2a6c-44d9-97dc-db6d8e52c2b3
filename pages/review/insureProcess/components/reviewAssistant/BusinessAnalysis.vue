<template>
  <div class="rounded p-12px bg-image-url">
    <div class="mb-12px">
      <VueIcon :icon="IcJingyingfenxiFont" />
      <span class="text-[14px] text-[#333333] font-bold mb-12px ml-[6px]">经营分析</span>
    </div>
    <div class="rounded bg-white p-12px h-[88px] flex flex-col">
      <div class="flex justify-between mb-8px">
        <div class="flex items-center">
          <div class="text-[#333333] text-xs">业务十分类评级&nbsp;&nbsp;{{ businessLevel }}级</div>
          <a-tooltip placement="right">
            <template #title>业务十分类评级为{{ businessLevel }}级</template>
            <VueIcon v-if="businessAnalysis?.isNewProduct === '1'" class="text-[12px] ml-[5px]" :icon="IconErrorCircleFilledFont" />
          </a-tooltip>
        </div>
        <div v-if="businessAnalysis?.isNewProduct !== '1'" class="text-xs text-[#576b95] cursor-pointer"><span @click="visible = true">查看明细</span><VueIcon :icon="IconChevronRightFont" /></div>
      </div>
      <div v-if="businessAnalysis?.isNewProduct !== '1'" class="flex justify-between items-center grow">
        <div class="flex flex-col justify-between h-full">
          <div class="text-xs text-font-primary">近3年平均满期赔付率</div>
          <div>
            <span class="font-number-medium text-[28px] text-[#333333] font-bold">{{ businessAnalysis?.oldProductInfo?.threeYearLossRatio || '-' }}</span>
            <span class="text-font-primary">&nbsp;%</span>
          </div>
        </div>
        <div class="divide-line" />
        <div class="flex flex-col justify-between h-full">
          <div class="text-xs text-font-primary">近三年平均跟单费用率</div>
          <div>
            <span class="font-number-medium text-[28px] text-[#333333] font-bold">{{ businessAnalysis?.oldProductInfo?.threeYearMerchandiserRatio || '-' }}</span>
            <span class="text-font-primary">&nbsp;%</span>
          </div>
        </div>
        <div class="divide-line" />
        <div class="flex flex-col justify-between h-full">
          <div class="text-xs text-font-primary">近三年平均减值损失率</div>
          <div>
            <span class="font-number-medium text-[28px] text-[#333333] font-bold">{{ businessAnalysis?.oldProductInfo?.threeYearIpmLossRatio || '-' }}</span>
            <span class="text-font-primary">&nbsp;%</span>
          </div>
        </div>
      </div>
      <div v-if="businessAnalysis?.isNewProduct === '1'" class="flex justify-between items-center grow">
        <div class="flex flex-col justify-between h-full flex-1">
          <div class="flex justify-between">
            <div class="text-xs text-font-primary">预期满期赔付率</div>
            <div class="text-xs text-[#576b95] cursor-pointer shrink-0" @click="handleVisble">编辑</div>
          </div>
          <div>
            <span class="font-number-medium text-[28px] text-[#333333] font-bold">{{ businessAnalysis?.newProductInfo?.expectLossRatio || '-' }}</span>
            <span class="text-font-primary">&nbsp;%</span>
          </div>
        </div>
        <div class="divide-line" />
        <div class="flex flex-col justify-between h-full flex-1">
          <div class="flex justify-between">
            <div class="text-xs text-font-primary">跟单费用率</div>
            <div class="text-xs text-[#576b95] cursor-pointer shrink-0" @click="handleVisble">编辑</div>
          </div>
          <div>
            <span class="font-number-medium text-[28px] text-[#333333] font-bold">{{ businessAnalysis?.newProductInfo?.orderRate || '-' }}</span>
            <span class="text-font-primary">&nbsp;%</span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="businessAnalysis?.mtrlcDisasterRisk !== null" class="rounded bg-white p-12px mt-8px h-[51px] flex flex-col justify-between">
      <div class="flex justify-between">
        <div class="text-xs text-font-primary">气象灾害风险</div>
        <div v-if="businessAnalysis?.mtrlcDisasterRisk !== null" class="text-xs text-[#576b95] cursor-pointer shrink-0">查看<VueIcon :icon="IconChevronRightFont" /></div>
      </div>
      <div>
        <span class="text-[20px] text-[#333333] font-bold">{{ businessAnalysis?.mtrlcDisasterRisk?.disasterType || '-' }}</span>
        <span class="ml-[12px] font-number-medium text-[28px] text-bold text-[#74c700]">{{ businessAnalysis?.mtrlcDisasterRisk?.disasterLevel || '-' }}</span>
        <span>&nbsp;级</span>
      </div>
    </div>
  </div>
  <a-modal v-model:open="visible" :title="`业务风险评级：${businessLevel}`" centered :width="pxToRem(900)">
    <a-table :data-source="businessAnalysis?.oldProductInfo?.ratioDetail?.ratioList" :columns="columns" :pagination="false" />
    <template #footer>
      <a-button type="primary" @click="visible = false">确定</a-button>
    </template>
  </a-modal>
  <!-- 十分类编辑 -->
  <a-modal v-model:open="riskVisible" title="编辑" centered :width="pxToRem(430)" @ok="handleOk">
    <a-form ref="formRef" :model="formState" :colon="false" :label-col="{ style: { width: pxToRem(120) } }">
      <a-form-item required label="预期满期赔付率" name="expectLossRatio" :rules="[{ required: true, message: '请输入' }]">
        <a-input v-model:value="formState.expectLossRatio" addon-after="%" />
      </a-form-item>
      <a-form-item required label="跟单费用率" name="orderRate" :rules="[{ required: true, message: '请输入' }]">
        <a-input v-model:value="formState.orderRate" addon-after="%" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { IconChevronRightFont, IcJingyingfenxiFont, IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { InfoType } from './reviewAssistant.d';
import { pxToRem } from '@/utils/tools';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

const { reviewAssistantInfo } = defineProps<{
  reviewAssistantInfo: InfoType;
}>();
const businessAnalysis = computed(() => reviewAssistantInfo?.businessAnalysis);
const visible = ref<boolean>(false);
const riskVisible = ref<boolean>(false);
const columns = [
  { title: '年份', dataIndex: 'years' },
  { title: '满期保费', dataIndex: 'expirePremium' },
  { title: '满期赔款', dataIndex: 'expireCompensation' },
  { title: '满期赔付率', dataIndex: 'lossRatio' },
  { title: '减值损失率', dataIndex: 'lpmLossRatio' },
  { title: '年保费收入', dataIndex: 'income' },
  { title: '年跟单费用', dataIndex: 'orderFee' },
  { title: '跟单费用率', dataIndex: 'orderRate' },
  { title: '评级', dataIndex: 'level' },
];
// 经营分析评级-新&旧
const businessLevel = computed(() => (isVisibleLevel.value ? level.value : businessAnalysis.value?.oldProductInfo?.businessLevel || businessAnalysis.value?.newProductInfo?.businessRiskLevel || '-'));
const route = useRoute();
const formState = ref({
  expectLossRatio: '',
  orderRate: '',
  departmentCode: route.query.departmentCode,
  marketProductNo: route.query.productCode,
});
// 点击十分类编辑
const handleVisble = () => {
  riskVisible.value = true;
  const { expectLossRatio, orderRate } = businessAnalysis.value?.newProductInfo || {};
  formState.value.expectLossRatio = expectLossRatio;
  formState.value.orderRate = orderRate;
};
const formRef = ref();
const emit = defineEmits(['handleRiskOk']);
// 判断当前十分类显示结果，为true则显示更新接口返回的值，否则显示核保助手返回的十分类值
const isVisibleLevel = ref<boolean>(false);
const level = ref<string>('');
type QueryCategoryType = {
  expectLossRatio: string;
  orderRate: string;
  businessRiskLevel: string;
};
// 十分类编辑完成-提交，更新卡片接口数据
const handleOk = async () => {
  try {
    await formRef.value.validateFields();
    const fetchUrl = `${gateWay}${service.ums}/underwrite/queryCategory`;
    const res = await $postOnClient<QueryCategoryType>(fetchUrl, formState.value);
    const { code, msg = '' } = res || {};
    if (code === SUCCESS_CODE) {
      message.success('编辑成功');
      const { expectLossRatio = '', orderRate = '', businessRiskLevel = '' } = res?.data || {};
      level.value = businessRiskLevel;
      riskVisible.value = false;
      isVisibleLevel.value = true;
      businessAnalysis.value.newProductInfo.expectLossRatio = expectLossRatio;
      businessAnalysis.value.newProductInfo.orderRate = orderRate;
      emit('handleRiskOk');
    } else {
      message.error(msg);
      isVisibleLevel.value = false;
    }
  } catch (error) {
    console.log(error);
    isVisibleLevel.value = false;
  }
};
const { gateWay, service } = useRuntimeConfig().public || {};
</script>

<style lang="less" scoped>
.divide-line {
  width: 1px;
  height: 70%;
  background: #d4d6d9;
  margin: 0 8px;
}
.bg-image-url {
  background-image: url(/assets/images/review/card-bg.png);
  background-size: cover;
}
</style>
