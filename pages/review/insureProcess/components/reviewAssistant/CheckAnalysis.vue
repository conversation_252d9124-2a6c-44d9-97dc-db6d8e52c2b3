<template>
  <div v-if="reviewAssistantInfo?.udwAnalysisVO?.promptInfo" class="rounded p-12px bg-image-url flex flex-col">
    <div class="mb-12px">
      <VueIcon :icon="QicQuanshufenxiFont" />
      <span class="text-[14px] text-[#333333] font-bold ml-[6px]">验标分析</span>
    </div>
    <div class="grid grid-cols-1 gap-8px">
      <div class="rounded bg-white p-12px mb-8px h-[69px] flex flex-col justify-between">
        <div class="flex">
          <span class="mr-[5px]"><VueIcon :icon="IconTankuangTishiColor" /></span>
          <span class="text-xs text-[12px]">验标后补</span>
        </div>
        <div>
          <span class="text-xs text-[14px]">
            保单承保后
            <span class="font-number-medium text-[28px] font-bold text-[#F7BA1E]">{{ reviewAssistantInfo.udwAnalysisVO.promptInfo }}</span>
            天内完成验标任务
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IconTankuangTishiColor, QicQuanshufenxiFont } from '@pafe/icons-icore-agr-an';
import type { InfoType } from './reviewAssistant';

const { reviewAssistantInfo } = defineProps<{
  reviewAssistantInfo: InfoType;
}>();
</script>
<style lang="less" scoped>
.bg-image-url {
  background-image: url(/assets/images/review/card-bg.png);
  background-size: cover;
}
</style>
