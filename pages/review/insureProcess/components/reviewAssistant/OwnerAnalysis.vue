<template>
  <div v-if="reviewAssistantInfo?.ownershipAnalysis?.ownershipFileVOList?.length > 0" class="rounded p-12px bg-image-url flex flex-col">
    <div class="mb-12px">
      <VueIcon :icon="QicQuanshufenxiFont" />
      <span class="text-[14px] text-[#333333] font-bold ml-[6px]">权属分析</span>
    </div>
    <div class="rounded p-12px bg-white flex items-center mb-[8px] grow">
      <div class="flex">
        <span class="text-[14px] mr-[5px]">
          <VueIcon :icon="IconTankuangTishiColor" />
        </span>
        <div>
          <div v-for="(item, index) in reviewAssistantInfo?.ownershipAnalysis?.ownershipFileVOList" :key="index" class="text-xs text-font-primary">{{ item.desc }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IconTankuangTishiColor, QicQuanshufenxiFont } from '@pafe/icons-icore-agr-an';
import type { InfoType } from './reviewAssistant';

const { reviewAssistantInfo } = defineProps<{
  reviewAssistantInfo: InfoType;
}>();
</script>
<style lang="less" scoped>
.bg-image-url {
  background-image: url(/assets/images/review/card-bg.png);
  background-size: cover;
}
</style>
