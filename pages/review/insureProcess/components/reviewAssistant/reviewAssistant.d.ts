// 经营分析
export interface BusinessAnalysisType {
  newProductInfo: NewProductInfoType; // 新产品经营分析
  oldProductInfo: OldProductInfoType; // 旧产品经营分析
  isNewProduct: string; // 是否是新产品
  mtrlcDisasterRisk: MtrlcDisasterRiskType; // 气象灾害风险
}
interface MtrlcDisasterRiskType {
  disasterType: string; // 灾害类型
  disasterLevel: string; // 灾害级别
}
// 客户分析
export interface CustomerAnalysisType {
  applyPolicyType: string; // 投保方式：2组织投保/1非组织投保
  historyUnderwriteList: HistoryUnderwriteListType[]; // 历史承保列表
  highRiskFarmer: string; // 高风险农户占比
  verifyRate: string; // 客户验真率
  selfCheckRate: string; // 自助验标率
  repeatPolicyRate: string; // 重复投保率
  repeatPolicyList: RepeatPolicyType[]; // 重复投保列表
  anbVerifyRate: string; // 爱农宝验真率
  verifyRateEoaNo: string; // 签报号
  customerVerifyRate: string; // 爱农宝核身率
}
// 重复投保列表
interface RepeatPolicyType {
  insuredName: string; // 被保险人
  idCardNo: string; // 身份证号
  applyPolicyNo: string; // 保单号
  insuredNumber: string; // 承保数量
  insuranceBeginDate: string; // 原保险起期
  insuranceEndDate: string; // 原保险止期
  oldInsuredNumber: string; // 原承保数量
}
interface HistoryUnderwriteListType {
  customerName: string;
  identityCardNo: string;
  applyPolicyNo: string;
  insuredNumber: string;
  premium: string;
  earPayAmount: string;
  lossRatio: string;
}
// 新产品经营分析
interface NewProductInfoType {
  businessRiskLevel: string; // 业务风险评级
  expectLossRatio: string; // 预期满期赔付率
  orderRate: string; // 跟单费用率
  secondDepartmentNo: string; // 二级机构编码
  marketProductNo: string; // 市场产品编码
  isShowPrompt: string; // 是否展示提示语（true展示，false不展示）
}
interface RatioDeatailType {
  businessRiskLevel: string; // 业务风险评级
  ratioList: RatioListType[]; // 费率列表
}
// 费率列表
interface RatioListType {
  years: number; // 年份
  expirePremium: number; // 满期保费
  expireCompensation: number; // 满期赔款
  lossRatio: number; // 满期赔付率
  lpmLossRatio: number; // 减值损失率
  income: number; // 保费收入
  orderFee: number; // 跟单费用
  orderRate: number; // 跟单费用率
  level: number; // 评级
}
interface OldProductInfoType {
  businessLevel: string; // 业务十分类评级
  threeYearLossRatio: string; // 近3年平均满期赔付率
  threeYearMerchandiserRatio: string; // 近3年平均跟单费用率
  threeYearIpmLossRatio: string; // 近3年平均减值损失率
  ratioDetail: RatioDeatailType;
}
// 地块分析
export interface PlotAnalysisType {
  amount: string; // 重复投保地块数量
  epidemicAreaRate: string; // 涉疫区率
  repeatPlotDetail: RepeatPlotDetailType[]; // 重复地块详情
  riskPromptList: string[]; // 地块分析风险提示
  combinedProductCode: string; // 标的明细
}
// 重复地块详情
interface RepeatPlotDetailType {
  historyVoucherNo: string; // 历史保单号
  insuranceBeginDate: string; // 保险起期
  insuranceEndDate: string; // 保险止期
  landNo: string; // 地块编号
}
// 标的分析
export interface RiskAnalysisType {
  allowLater: string; // 是否允许后补 (0:否,1:是)
  growthLevel: string; // 长势等级
  smartCount: string; // 识别数量
  riskType: string; // 标的类别
  repeatMaterial: string; // 否验标资料是否重复（0：不重复，1：重复）
  repeatMaterialDetail: RepeatMaterialDetailType[]; // 重复资料详情
  insuranceNums: string;
  smartCountPercent: string;
  cattleFaceAmount: string;
  cattleFaceAmountPercent: string;
}
interface RepeatMaterialDetailType {
  voucherNo: string; // 保单号
  pictureNo: string; // 照片编码
}
// 权属分析
interface OwnershipFileVOListType {
  desc: string;
}
interface OwnershipAnalysisType {
  ownershipFileVOList: OwnershipFileVOListType[];
}
// 其他风险
export interface OtherRiskType {
  riskList: RiskListType[];
  documentRiskList?: RiskListType[]; // 附件类风险
}
interface RiskListType {
  desc: string;
}
// 验标分析
interface udwAnalysisVOType {
  promptInfo: string; // 天数
}

interface taskReuseInfoRespVOType {
  insrCheckNo: string; //验标号
  reusedApplyPolicyNo: string; // 复用的投保单号
  reusedInsrCheckNo: string; //复用的验标号
}
// 对象合集
export interface InfoType {
  businessAnalysis: BusinessAnalysisType;
  customerAnalysis: CustomerAnalysisType;
  plotAnalysis: PlotAnalysisType;
  riskAnalysis: RiskAnalysisType;
  ownershipAnalysis: OwnershipAnalysisType;
  otherRisk: OtherRiskType;
  udwAnalysisVO: udwAnalysisVOType;
  taskReuseInfoRespVO: taskReuseInfoRespVOType;
}
