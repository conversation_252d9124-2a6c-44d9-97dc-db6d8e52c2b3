<template>
  <div v-if="isShow" class="rounded p-12px bg-image-url">
    <div class="mb-12px">
      <VueIcon :icon="IDikuaifenxiFont" />
      <span class="text-[14px] text-[#333333] font-bold ml-[6px]">{{ combinedProductCodeName || '地块分析' }}</span>
    </div>
    <div class="grid grid-cols-2 gap-8px">
      <div v-if="reviewAssistantInfo?.plotAnalysis?.amount !== null" class="rounded bg-white p-12px mb-8px h-[69px] flex flex-col justify-between">
        <div class="flex justify-between">
          <div class="flex">
            <span class="mr-[5px]"><VueIcon :icon="IconTankuangTishiColor" /></span>
            <span class="text-xs text-font-primary">重复投保地块</span>
          </div>
          <span class="text-[#576b95] text-xs cursor-pointer shrink-0" @click="visible = true">查看<VueIcon :icon="IconChevronRightFont" /></span>
        </div>
        <div>
          <span class="font-number-medium text-[28px] font-bold text-[#f03e3e]">{{ reviewAssistantInfo?.plotAnalysis?.amount || '0' }}</span>
          <span>个</span>
        </div>
      </div>
      <div v-if="reviewAssistantInfo?.plotAnalysis?.epidemicAreaRate !== null" class="rounded bg-white p-12px h-[69px] flex flex-col justify-between">
        <div class="flex justify-between">
          <span class="text-xs text-font-primary">涉疫区</span>
          <!-- <span class="text-[#576b95] text-xs cursor-pointer shrink-0">查看<VueIcon :icon="IconChevronRightFont" /></span> -->
        </div>
        <div>
          <span class="font-number-medium text-[28px] font-bold text-[#333333]">{{ reviewAssistantInfo?.plotAnalysis?.epidemicAreaRate || '-' }}</span>
          <span>%</span>
        </div>
      </div>
    </div>
    <div v-if="reviewAssistantInfo?.plotAnalysis?.riskPromptList?.length > 0" class="rounded p-12px bg-white flex items-center grow">
      <div class="flex">
        <span class="text-[14px] mr-[5px]">
          <VueIcon :icon="IconTankuangTishiColor" />
        </span>
        <div>
          <div v-for="(item, index) in reviewAssistantInfo?.plotAnalysis?.riskPromptList" :key="index" class="text-xs text-font-primary">{{ item }}</div>
        </div>
      </div>
    </div>
  </div>
  <a-modal v-model:open="visible" title="重复投保地块" centered :width="pxToRem(800)">
    <a-table :data-source="reviewAssistantInfo?.plotAnalysis?.repeatPlotDetail" :columns="columns" :pagination="false" />
    <template #footer>
      <a-button type="primary" @click="visible = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { IconChevronRightFont, IconTankuangTishiColor, IDikuaifenxiFont } from '@pafe/icons-icore-agr-an';
import type { InfoType } from './reviewAssistant';
import { pxToRem } from '@/utils/tools';

const { reviewAssistantInfo } = defineProps<{
  reviewAssistantInfo: InfoType;
}>();

const visible = ref<boolean>(false);
const columns = [
  { title: '历史保单号', dataIndex: 'historyVoucherNo' },
  { title: '保险起期', dataIndex: 'insuranceBeginDate' },
  { title: '保险止期', dataIndex: 'insuranceEndDate' },
  { title: '地块编号', dataIndex: 'landNo' },
];
// 种植业A开头，养殖业B开头，林业C开头，涉农D开头
const combine: Record<string, string> = {
  A: '地块分析',
  B: '场地分析',
  C: '林业',
  D: '涉农',
};
const combinedProductCodeName = computed(() => combine[reviewAssistantInfo?.plotAnalysis?.combinedProductCode?.slice(0, 1)]);
// 是否显示总卡片
const isShow = computed(() => {
  const { amount, epidemicAreaRate, riskPromptList } = reviewAssistantInfo?.plotAnalysis || {};
  return amount !== null || epidemicAreaRate !== null || riskPromptList?.length > 0;
});
</script>
<style lang="less" scoped>
.bg-image-url {
  background-image: url(/assets/images/review/card-bg.png);
  background-size: cover;
}
</style>
