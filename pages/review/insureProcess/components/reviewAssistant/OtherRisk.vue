<template>
  <div v-if="showOtherRisk" class="rounded p-12px bg-image-url flex flex-col">
    <div class="mb-12px">
      <VueIcon :icon="QicQuanshufenxiFont" />
      <span class="text-[14px] text-[#333333] font-bold ml-[6px]">其他风险</span>
    </div>
    <div class="grid gap-8px grid-col-1 h-full">
      <template v-for="(items, indexs) in otherRiskList" :key="indexs">
        <div v-if="items.length" class="rounded p-12px bg-white flex">
          <div class="flex grow items-center">
            <span class="text-[14px] mr-[5px]">
              <VueIcon :icon="IconTankuangTishiColor" />
            </span>
            <div>
              <div v-for="(item, index) in items" :key="index" class="text-xs text-font-primary break-all">{{ item.desc }}</div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IconTankuangTishiColor, QicQuanshufenxiFont } from '@pafe/icons-icore-agr-an';
import type { InfoType } from './reviewAssistant';

const { reviewAssistantInfo } = defineProps<{
  reviewAssistantInfo: InfoType;
}>();

const otherRiskList = computed(() => {
  const list = [];
  const documentRiskList = (reviewAssistantInfo?.otherRisk?.documentRiskList || []).map((val) => {
    return { desc: '附件风险：' + val.desc };
  }); // 附件类风险
  const otherRisk = reviewAssistantInfo?.otherRisk?.riskList || []; // 其他风险
  if (documentRiskList.length) {
    list.push(documentRiskList);
  }
  if (otherRisk.length) {
    list.push(otherRisk);
  }
  return list;
});

const showOtherRisk = computed(() => {
  return otherRiskList.value?.length;
});
</script>
<style lang="less" scoped>
.bg-image-url {
  background-image: url(/assets/images/review/card-bg.png);
  background-size: cover;
}
</style>
