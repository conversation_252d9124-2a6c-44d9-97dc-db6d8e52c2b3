<template>
  <div v-if="isShow" class="rounded p-12px bg-image-url">
    <div class="mb-12px">
      <VueIcon :icon="IcBiaodifenxiFont" />
      <span class="text-[14px] text-[#333333] font-bold ml-[6px]">标的分析</span>
    </div>
    <div class="grid grid-cols-2 gap-8px">
      <!-- <div class="rounded bg-white p-12px h-[69px] flex flex-col justify-between">
        <div class="flex justify-between">
          <div class="text-xs text-font-primary">验标是否后补</div>
        </div>
        <div class="text-[20px] font-bold text-[#333333]">{{ reviewAssistantInfo?.riskAnalysis?.allowLater === '1' ? '是' : '否' }}</div>
      </div> -->
      <div v-if="reviewAssistantInfo?.riskAnalysis?.growthLevel !== null" class="rounded bg-white p-12px h-[69px] flex flex-col justify-between">
        <div class="flex justify-between">
          <div class="text-xs text-font-primary">标的长势等级</div>
          <!-- <div class="text-xs text-[#576b95] cursor-pointer">查看<VueIcon :icon="IconChevronRightFont" /></div> -->
        </div>
        <div>
          <span class="font-number-medium text-[28px] font-bold text-[#107D9F]">{{ reviewAssistantInfo?.riskAnalysis?.growthLevel || '-' }}</span>
          <span>级</span>
        </div>
      </div>
      <div v-if="reviewAssistantInfo?.riskAnalysis?.smartCount !== null || reviewAssistantInfo?.riskAnalysis?.smartCount" class="rounded bg-white p-12px h-[69px] flex flex-col justify-between">
        <div class="text-xs text-font-primary">
          智能点数采集数
          <a-tooltip placement="right">
            <template #title>智能点数采集数量{{ reviewAssistantInfo?.riskAnalysis?.smartCount }}，该验标任务下总保险数量为{{ reviewAssistantInfo?.riskAnalysis?.insuranceNums }}，识别占比{{ reviewAssistantInfo?.riskAnalysis?.smartCountPercent }}%</template>
            <VueIcon class="text-[12px] ml-[5px]" :icon="IconErrorCircleFilledFont" />
          </a-tooltip>
        </div>
        <div class="font-number-medium text-[28px] font-bold text-[#333333]">{{ reviewAssistantInfo?.riskAnalysis?.smartCount || '0' }}</div>
      </div>
      <div v-if="reviewAssistantInfo?.riskAnalysis?.cattleFaceAmount !== null || reviewAssistantInfo?.riskAnalysis?.cattleFaceAmount" class="rounded bg-white p-12px h-[69px] flex flex-col justify-between">
        <div class="text-xs text-font-primary">
          牛脸识别数量
          <a-tooltip placement="right">
            <template #title>牛脸识别数量{{ reviewAssistantInfo?.riskAnalysis?.cattleFaceAmount }}，该验标任务下总保险数量为{{ reviewAssistantInfo?.riskAnalysis?.insuranceNums }}，识别占比{{ reviewAssistantInfo?.riskAnalysis?.cattleFaceAmountPercent }}%</template>
            <VueIcon class="text-[12px] ml-[5px]" :icon="IconErrorCircleFilledFont" />
          </a-tooltip>
        </div>
        <div class="font-number-medium text-[28px] font-bold text-[#333333]">{{ reviewAssistantInfo?.riskAnalysis?.cattleFaceAmount || '0' }}</div>
      </div>
      <!-- <div v-if="reviewAssistantInfo?.riskAnalysis?.repeatMaterial === '1'" class="rounded bg-white p-12px h-[69px] flex flex-col justify-between">
        <div class="flex justify-between">
          <div class="text-xs text-font-primary">验标资料是否重复</div>
          <div v-if="reviewAssistantInfo?.riskAnalysis?.repeatMaterial === '1'" class="text-xs text-[#576b95] cursor-pointer shrink-0" @click="visible = true">查看<VueIcon :icon="IconChevronRightFont" /></div>
        </div>
        <div class="text-[20px] font-bold text-[#333333]">{{ reviewAssistantInfo?.riskAnalysis?.repeatMaterial === '1' ? '是' : '否' }}</div>
      </div> -->
    </div>
  </div>
  <a-modal v-model:open="visible" title="验标资料" centered :width="pxToRem(700)">
    <a-table :data-source="repeatMaterialDetailList" :columns="columns" :pagination="pagination">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'thumbnailPath'">
          <a-button type="link" @click="handlePreview(record)">下载</a-button>
        </template>
      </template>
    </a-table>
    <template #footer>
      <a-button type="primary" @click="visible = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
// IconChevronRightFont,
import { IcBiaodifenxiFont, IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { InfoType } from './reviewAssistant';
import { pxToRem } from '@/utils/tools';
import { $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePagination } from '@/composables/usePagination';

const { gateWay, service } = useRuntimeConfig().public || {};

const { reviewAssistantInfo } = defineProps<{
  reviewAssistantInfo: InfoType;
}>();

const visible = ref<boolean>(false);
const columns = [
  { title: '保单号', dataIndex: 'voucherNo' },
  { title: '照片名称', dataIndex: 'imageDetail' },
  { title: '缩略图', dataIndex: 'thumbnailPath' },
];
const repeatMaterialDetailList = ref<Record<string, string>[]>([]);
const route = useRoute();
// 验标资料是否重复接口
const getInit = async () => {
  try {
    const fetchUrl = `${gateWay}${service.ums}/underwrite/queryRepeatFilePage`;
    const res = await $postOnClient(fetchUrl, { applyPolicyNo: route.query?.applyPolicyNo || '', pageSize: pagination.pageSize, pageNum: pagination.current });
    if (res?.code === SUCCESS_CODE) {
      repeatMaterialDetailList.value = res?.data?.records as unknown as Record<string, string>[];
      pagination.total = res?.data?.total;
      pagination.current = res?.data?.current;
      pagination.pageSize = res?.data?.size;
    } else {
      message.error(res?.msg || '请求失败');
    }
  } catch (error) {
    console.log(error);
  }
};
// 分页处理
const { pagination } = usePagination(getInit);
// 查看照片
const handlePreview = async (record: Record<string, string>) => {
  const { data, msg, code } =
    (await $postOnClient('/api/iobs/getInIobsUrl', {
      iobsBucketName: record.repeatBucketName,
      fileKey: record.repeatIobsKey,
      storageTypeCode: record.storageType || '02',
    })) || {};
  if (SUCCESS_CODE === code) {
    window.open(data?.fileUrl);
  } else {
    message.error(msg || '');
  }
};
// 是否显示总卡片
const isShow = computed(() => {
  // const { growthLevel, smartCount, cattleFaceAmount, repeatMaterial } = reviewAssistantInfo?.riskAnalysis || {};
  // return growthLevel !== null || smartCount !== null || cattleFaceAmount !== null || repeatMaterial === '1';
  // 临时屏蔽repeatMaterial
  const { growthLevel, smartCount, cattleFaceAmount } = reviewAssistantInfo?.riskAnalysis || {};
  return growthLevel !== null || smartCount !== null || cattleFaceAmount !== null;
});
onMounted(() => {
  getInit();
});
</script>
<style lang="less" scoped>
.bg-image-url {
  background-image: url(/assets/images/review/card-bg.png);
  background-size: cover;
}
</style>
