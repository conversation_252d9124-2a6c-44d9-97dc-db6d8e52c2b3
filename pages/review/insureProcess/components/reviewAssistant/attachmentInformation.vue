<template>
  <div v-if="reviewAssistantInfo?.taskReuseInfoRespVO?.reusedApplyPolicyNo" class="rounded p-12px bg-image-url flex flex-col">
    <div class="mb-12px">
      <VueIcon :icon="QicQuanshufenxiFont" />
      <span class="text-[14px] text-[#333333] font-bold ml-[6px]">附件资料</span>
    </div>
    <div class="grid grid-cols-1 gap-8px">
      <div class="rounded bg-white p-12px mb-8px h-[69px] flex flex-col justify-center item-center">
        <div class="flex">
          <span class="mr-[5px]"><VueIcon :icon="IconTankuangTishiColor" /></span>
          <span class="text-xs text-[14px]">
            本验标任务复用于投保单<span class="text-[#576B95] cursor-pointer font-number" @click.stop="copyText(reviewAssistantInfo.taskReuseInfoRespVO.reusedApplyPolicyNo)"> {{ reviewAssistantInfo.taskReuseInfoRespVO.reusedApplyPolicyNo }}</span
            >的验标任务
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IconTankuangTishiColor, QicQuanshufenxiFont } from '@pafe/icons-icore-agr-an';
import { copyText } from '@/utils/tools';
import type { InfoType } from './reviewAssistant';

const { reviewAssistantInfo } = defineProps<{
  reviewAssistantInfo: InfoType;
}>();
</script>
<style lang="less" scoped>
.bg-image-url {
  background-image: url(/assets/images/review/card-bg.png);
  background-size: cover;
}
</style>
