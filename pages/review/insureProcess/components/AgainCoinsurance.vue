<!-- 再次共保 -->
<template>
  <div class="flex justify-between">
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">再保信息</div>
    <a-button v-if="['0', '3', '8'].includes(formState?.status) && formState.facultativeType === '7'" type="primary" :loading="loading" @click="getRimsSysInfo('')">发起再保申请</a-button>
  </div>
  <!-- 填写与回显互斥 -->
  <div class="ml-8px">
    <a-form ref="formRef" :model="formState" :colon="false">
      <a-form-item v-if="['0', '3', '8'].includes(formState?.status)" label="是否临分" name="" :rules="[{ required: true, message: '请选择' }]">
        <a-radio-group v-model:value="formState.facultativeType" @change="changeRadio">
          <a-radio value="1">否</a-radio>
          <a-radio value="2">是（一般临分）</a-radio>
          <a-radio value="7">是（非总对总FAC）</a-radio>
          <a-radio value="4">是（关联已有再保项目）</a-radio>
        </a-radio-group>
      </a-form-item>
      <div v-show="isVisibleFlag" class="grid grid-cols-3 gap-x-16px mb-12px">
        <a-form-item label="项目名称" name="projectName" :rules="[{ required: true, message: '请输入项目名称' }]">
          <a-input v-model:value.trim="formState.projectName" placeholder="请输入项目名称" />
        </a-form-item>
      </div>
      <div v-show="isFacNoFlag" class="grid grid-cols-3 gap-x-16px mb-12px">
        <a-form-item label="批复/询价编号" name="facultativeNo" :rules="[{ required: true, message: '请输入批复/询价编号' }]">
          <a-input v-model:value.trim="formState.facultativeNo" placeholder="请输入批复/询价编号" @blur="handleFacultativeNo" />
        </a-form-item>
      </div>
    </a-form>
    <!-- 分出比列，手续费，为必填项 -->
    <a-form ref="formDisRef" :model="disDataSource" :colon="false">
      <a-form-item v-if="isVisibleFlag" label="临分方式" name="" :rules="[{ required: true, message: '请选择临分方式' }]">
        <div :class="ratioChecked ? 'mt-[5px]' : ''" class="inline-block">
          <a-checkbox v-model:checked="ratioChecked">比例临分</a-checkbox>
        </div>
        <!-- 因为ui要求这样的样式，所以这里多写一个 -->
        <div class="inline-block ml-[18px]">
          <a-checkbox v-show="!checked" v-model:checked="checked">超赔临分</a-checkbox>
        </div>
        <a-table v-show="ratioChecked" :columns="disColumns" :data-source="disDataSource" :pagination="false" class="mt-[16px]">
          <template #bodyCell="{ column, index }">
            <!-- 分出比列 -->
            <template v-if="column.dataIndex === 'separationRatio'">
              <a-form-item :name="[index, 'separationRatio']" :rules="[{ validator: insuredRateValid }]">
                <a-input v-model:value.trim="disDataSource[index].separationRatio" style="width: 100%" placeholder="请输入" />
              </a-form-item>
            </template>
            <!-- 手续费 -->
            <template v-if="column.dataIndex === 'poundage'">
              <a-form-item :name="[index, 'poundage']" :rules="[{ validator: insuredRateValid }]">
                <a-input v-model:value.trim="disDataSource[index].poundage" style="width: 100%" placeholder="请输入" />
              </a-form-item>
            </template>
            <!-- 免赔 -->
            <template v-if="column.dataIndex === 'abatement'">
              <a-form-item :name="[index, 'abatement']" :rules="[{ validator: numberValid }]">
                <a-input v-model:value.trim="disDataSource[index].abatement" style="width: 100%" placeholder="请输入" />
              </a-form-item>
            </template>
            <!-- 其他说明 -->
            <template v-if="column.dataIndex === 'otherExplain'">
              <a-form-item :name="[index, 'otherExplain']" :rules="[{ validator: validOtherExplain }]">
                <a-input v-model:value.trim="disDataSource[index].otherExplain" style="width: 100%" placeholder="请输入" />
              </a-form-item>
            </template>
          </template>
        </a-table>
        <div :class="ratioChecked ? 'mt-[16px]' : ''" class="inline-block">
          <a-checkbox v-show="checked" v-model:checked="checked">超赔临分</a-checkbox>
        </div>
      </a-form-item>
    </a-form>
    <div v-if="isVisibleFlag" class="ml-[76px]">
      <a-form v-show="checked" ref="formModelRef" :model="dataSource" :colon="false">
        <a-form-item name="dataSource">
          <a-table :columns="compensateColumns" :data-source="dataSource" :pagination="false">
            <template #bodyCell="{ column, index }">
              <!-- 起赔点 -->
              <template v-if="column.dataIndex === 'franchise'">
                <a-form-item :name="[index, 'franchise']" :rules="[{ validator: numberValid }]">
                  <a-input v-model:value.trim="dataSource[index].franchise" style="width: 100%" placeholder="请输入" />
                </a-form-item>
              </template>
              <!-- 分出比例（%） -->
              <template v-if="column.dataIndex === 'separationRatio'">
                <a-form-item :name="[index, 'separationRatio']" :rules="[{ validator: insuredRateValid }]">
                  <a-input v-model:value.trim="dataSource[index].separationRatio" style="width: 100%" placeholder="请输入" />
                </a-form-item>
              </template>
              <!-- 赔偿限额 -->
              <template v-if="column.dataIndex === 'compensateLimit'">
                <a-form-item :name="[index, 'compensateLimit']" :rules="[{ validator: numberValid }]">
                  <a-input v-model:value.trim="dataSource[index].compensateLimit" style="width: 100%" placeholder="请输入" />
                </a-form-item>
              </template>
              <!-- 分出净保费 -->
              <template v-if="column.dataIndex === 'separationPremium'">
                <a-form-item :name="[index, 'separationPremium']" :rules="[{ validator: numberValid }]">
                  <a-input v-model:value.trim="dataSource[index].separationPremium" style="width: 100%" placeholder="请输入" />
                </a-form-item>
              </template>
              <!-- 其他说明 -->
              <template v-if="column.dataIndex === 'otherExplain'">
                <a-form-item :name="[index, 'otherExplain']" :rules="[{ validator: validOtherExplain }]">
                  <a-input v-model:value.trim="dataSource[index].otherExplain" style="width: 100%" placeholder="请输入" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <a-space>
                  <a-button v-if="index > 0" type="link" @click="handleRemove(index)">删除</a-button>
                  <a-button type="link" @click="handleAdd">新增</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-form-item>
      </a-form>
    </div>
    <div class="text-center">
      <a-button v-if="isApplyVisible" type="primary" :loading="againLoading" @click="getApply">发起再保申请</a-button>
    </div>
  </div>
  <!-- 是（关联已有再保项目）-显示查看状态 -->
  <div v-if="!['0', '3', '8'].includes(formState?.status) && formState.relatedFacultativeNo" class="mb-[18px]">
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">是否临分</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">是（关联已有再保项目）</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">临分批复编号</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ formState?.facultativeNo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">状态</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ getStatusText(formState?.status) }}</div>
        </div>
      </div>
    </div>
  </div>
  <!-- 是（非总对总FAC）显示查看状态 -->
  <div v-if="!['0', '3', '8'].includes(formState?.status) && formState.facultativeType === '7'" class="mb-[18px]">
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">是否临分</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">是（非总对总FAC）</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">临分批复编号</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ formState?.facultativeNo }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">状态</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ getStatusText(formState?.status) }}</div>
        </div>
      </div>
    </div>
    <div class="text-center mt-[10px]">
      <a-button type="primary" @click="getRimsSysInfo('1')">查看分出方案</a-button>
    </div>
  </div>
  <!-- 回显与上面填写互斥-一般临分情况 -->
  <div v-if="isLookFlag" class="mb-[18px]">
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">是否临分</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]"> 是（一般临分）</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">项目名称</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ formState?.projectName }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">临分批复编号</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ formState?.facultativeNo }}</div>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">状态</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ getStatusText(formState?.status) }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">签报号</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ formState?.signNo || '-' }}</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">临分方式</div>
        <div class="pl-[10px]">
          <div class="text-[#333333] flex">
            <span class="text-[#262626]">{{ disDataSource?.length > 0 ? '比例临分' : '' }}</span>
            <span v-if="disDataSource?.length > 0 && dataSource?.length > 0"> + </span>
            <span class="text-[#262626]">{{ dataSource?.length > 0 ? '超赔临分' : '' }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="rounded p-14px box-border">
      <a-table v-if="disDataSource?.length > 0" :columns="disColumns" :data-source="disDataSource" :pagination="false" class="mt-[16px]" />
      <a-table v-if="dataSource?.length > 0" :columns="compensateColumns" :data-source="dataSource" :pagination="false" class="mt-[16px]" />
    </div>
    <div class="text-center mt-[10px]">
      <a-space>
        <a-button v-if="formState?.status === '4'" type="primary" :loading="acceptLoading" @click="updateReinsurance('1')">采纳</a-button>
        <a-button v-if="formState?.status === '4'" type="primary" :loading="noAcceptLoading" @click="updateReinsurance('2')">不采纳</a-button>
        <a-button v-if="['0', '2', '3', '5'].includes(formState?.status)" type="primary" :loading="acceptLoading" @click="updateReinsurance('3')">取消</a-button>
        <a-button v-if="formState?.signNo && formState?.signNo !== null" type="primary" :loading="signLoading" @click="queryEoaInfo">查看签报</a-button>
      </a-space>
    </div>
  </div>
  <a-divider />
  <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">共保信息</div>
  <div v-for="(item, index) in errorDataInfo?.coinsurance" :key="index" class="text-[12px] pb-[4px] pl-[8px]" :class="colorText(item.promptLevel)">{{ item.promptInfo }}<a-button v-if="item.extendDesc" :href="item.extendDesc" target="_blank" size="small" class="ml-[8px]" type="primary">查看签报</a-button></div>
  <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">共保类型</div>
      <div class="pl-[10px]">
        <div class="text-[#333333]">{{ coinsuranceInfo?.innerCoinsuranceMarkChName || '-' }}</div>
      </div>
    </div>
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">共保协议代码</div>
      <div class="pl-[10px]">
        <div class="text-[#333333]">{{ coinsuranceInfo?.innerCoinsuranceAgreement || '-' }}</div>
      </div>
    </div>
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">总保额</div>
      <div class="pl-[10px]">
        <div class="text-[#333333] font-number">{{ coinsuranceInfo?.totalInsuredAmount || '-' }}</div>
      </div>
    </div>
  </div>
  <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">总保费</div>
      <div class="pl-[10px]">
        <div class="text-[#333333] font-number">{{ coinsuranceInfo?.totalPremium || '-' }}</div>
      </div>
    </div>
  </div>
  <!-- 共保列表 -->
  <a-table v-if="contractModel?.baseInfo?.coinsuranceMark === '1'" :columns="coinsuranceColumns" :data-source="contractModel?.coinsuranceInfo?.coinsuranceDetailList" :pagination="false" class="my-[16px]">
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'acceptInsuranceFlag'"> {{ record.acceptInsuranceFlag === '1' ? '是' : '否' }}</template>
    </template>
  </a-table>
  <!-- 查看签报 -->
  <LookModal v-if="lookVisible" v-model:visible="lookVisible" :look-modal-obj="lookModalObj" @query-reinsurance-info="queryReinsuranceInfoId" />
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form/interface';
import type { ContractModelType, ErrorModelType, ResType, ReinsuranceInfoType, ReinsuranceDetailListType, EoaResType } from '../insureProcess.d';
import LookModal from './LookModal.vue';
import { disColumns, compensateColumns, coinsuranceColumns } from './columns';
import { $postOnClient, $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

const { gateWay, service } = useRuntimeConfig().public || {};
// 获取信息大对象
const { contractModel, errorDataInfo } = defineProps<{
  contractModel: ContractModelType;
  errorDataInfo: ErrorModelType;
}>();
const reinsuranceInfoData = ref();
const route = useRoute();
// 优先以relatedFacultativeNo的值，其次facultativeType的值，facultativeType只有一般临分传2，非总对总FAC传7
const formState = ref<ReinsuranceInfoType>({
  idReinsurance: '',
  projectName: '',
  facultativeType: '1', // 临分类型 2-一般性临分 7-非总对总fac
  facultativeNo: '', // 临分编码(再保批复编号)
  applyPolicyNo: route.query.applyPolicyNo || '', // 投保单号
  schemeNo: route.query.applyPolicyNo || '', // 再保方案号
  signNo: '', // eoa签报号
  relatedFacultativeNo: false, // 是否关联已有临分批复编号 - 仅关联操作传true，其余传false
  reinsuranceDetailList: [], // 再保详细信息
  status: '0',
  id: '',
});
const lookModalObj = computed(() => {
  return {
    idReinsurance: formState.value?.idReinsurance,
    eoaSubject: eoaSubjects.value,
    eoaBody: eoaBodys.value,
    disDataSource: disDataSource.value,
    dataSource: dataSource.value,
  };
});
interface type {
  [key: number | string]: number | string;
};
// 是（关联已有再保项目）--查看状态
const isFacNoFlag = computed(() => (formState.value.facultativeType === '4' || formState.value?.relatedFacultativeNo) && ['0', '3', '8'].includes(formState.value?.status));
const commonHandTypeList: type = { 0: '待确认', 1: '待审批', 2: '签报审批通过', 3: '签报审批不通过', 4: '再保询价成功', 5: '再保询价失败', 6: '核保确认分出方案', 7: '核保拒绝分出方案', 8: '取消再保' };
const getStatusText = (type: string) => commonHandTypeList[Number(type)];
// 一般临分方式显示编辑状态status：0或者8或者3，否则显示查看状态
const isVisibleFlag = computed(() => formState.value.facultativeType === '2' && ['0', '3', '8'].includes(formState.value.status));
const isLookFlag = computed(() => formState.value.facultativeType === '2' && !['0', '3', '8'].includes(formState.value.status));
// 一般临分，发起再保申请按钮显示
const isApplyVisible = computed(() => (ratioChecked.value || checked.value) && formState.value.facultativeType === '2' && ['0', '3', '8'].includes(formState.value.status));
const dataSource = ref<ReinsuranceDetailListType[]>([
  {
    facultativeMode: '2', // 临分方式（1比例临分;2 超赔临分）
    franchise: '', // 起赔点
    separationRatio: '', // 分出比例（前端不用转换百分比，下同）
    compensateLimit: '', // 赔偿限额
    separationPremium: '', // 分出净保费
    otherExplain: '', // 其他说明
  },
]);
const disDataSource = ref<ReinsuranceDetailListType[]>([
  {
    facultativeMode: '1',
    separationRatio: '',
    poundage: '', // 手续费
    abatement: '', // 免赔
    otherExplain: '',
  },
]);
// 超赔临分-新增
const handleAdd = () => {
  dataSource.value.push({
    facultativeMode: '2',
    franchise: '',
    separationRatio: '',
    compensateLimit: '',
    separationPremium: '',
    otherExplain: '',
  });
};
// 超赔临分-删除
const handleRemove = (index: number) => {
  dataSource.value.splice(index, 1);
};
// 超赔临分-表格是否显示
const checked = ref<boolean>(false);
// 比例临分-表格是否显示
const ratioChecked = ref<boolean>(false);
const lookVisible = ref<boolean>(false);
// 共保信息数据处理
const coinsuranceInfo = computed(() => contractModel?.coinsuranceInfo);
// 颜色判断
const colorText = (type: string) => {
  return type === 'green' ? 'green' : 'red ';
};
// 选择是否临分时，重置校验
const changeRadio = async () => {
  formState.value.relatedFacultativeNo = false;
  // status为切换时
  if (['0', '3', '8'].includes(formState.value.status) && reinsuranceInfoData.value) {
    await updateReinsurance('4');
    // 由于点了取消，返回数据则撤销，所以需要调用接口重新赋值空
    queryReinsuranceInfo();
  }
};
const queryReinsuranceInfo = async () => {
  try {
    const res = await $getOnClient(`${gateWay}${service.ums}/reinsurance/queryReinsuranceInfo`, { applyPolicyNo: route.query.applyPolicyNo });
    reinsuranceInfoData.value = res?.data || null;
    if (!res?.data || res?.data === null) {
      dataSource.value = [{
        facultativeMode: '2', // 临分方式（1比例临分;2 超赔临分）
        franchise: '', // 起赔点
        separationRatio: '', // 分出比例（前端不用转换百分比，下同）
        compensateLimit: '', // 赔偿限额
        separationPremium: '', // 分出净保费
        otherExplain: '', // 其他说明
      }];
      disDataSource.value = [{
        facultativeMode: '1',
        separationRatio: '',
        poundage: '', // 手续费
        abatement: '', // 免赔
        otherExplain: '',
      }];
      checked.value = false;
      ratioChecked.value = false;
      formState.value.projectName = '';
    }
  } catch (error) {
    console.log(error);
  }
};
// 发起再保申请
const facultativeNo = ref<string>(''); // 分出方案需要传的批复编号
const loading = ref<boolean>(false);
const getRimsSysInfo = async (type?: string) => {
  try {
    loading.value = true;
    const fetchUrl = `${gateWay}${service.ums}/reinsurance/getRimsSysInfo`;
    const res = await $getOnClient<ResType>(fetchUrl, { voucherNo: contractModel?.baseInfo?.applyPolicyNo });
    const { httpAppUrl, sysId, timeStamp, code, virtualUsername, userId, deptCode } = res?.data || {};
    if (res?.code === SUCCESS_CODE) {
      if (type === '1') {
        facultativeNo.value = formState.value.facultativeNo;
      }
      // 延续老项目的逻辑跳转
      const href = `${httpAppUrl}/docc/confirmshareinfo/confirmShareInfoAdd.jsp?requestSysName=ICORE_AGR&outSysSessionId=REINSURANCE&confirmNoType=02&schemeNo=${contractModel?.baseInfo?.applyPolicyNo}` +
        `&confirmNO=${facultativeNo.value}&quotationNo=${contractModel?.baseInfo?.applyPolicyNo}&callbackSysUrl=${location.origin}/reinsuranceFacIframeResult.html&userId=` +
        `${userId}&timeStamp=${timeStamp}&sysUser=${virtualUsername}&sysId=${sysId}&ciphertext=${code}&deptCode=${deptCode}&agrAnFlag=1`;
      window.open(href);
    } else {
      message.error(res?.msg || '');
    }
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
};
// 校验-起赔点，赔偿限额，分出净保费，免赔，不校验必填，只校验输入是否符合规则
const numberValid = (rule: Rule, value: string) => {
  const reg = /^\d{0,20}(\.\d{1,2})?$/;
  if (value && !reg.test(value)) {
    return Promise.reject(new Error('请输入最多20位正数且小数位不能超过2位！'));
  }
  return Promise.resolve();
};
// 校验-分出比例（%），手续费
const insuredRateValid = (rule: Rule, value: string) => {
  const reg = /^\d+(\.\d{1,4})?$/;
  if (!reg.test(value)) {
    return Promise.reject(new Error('请输入正数且小数位不能超过4位！'));
  }
  if (Number(value) > 100) {
    return Promise.reject(new Error('请输入0~100范围内的数字且小数位不能超过4位！'));
  }
  return Promise.resolve();
};
// 校验-其它说明
const validOtherExplain = (rule: Rule, value: string) => {
  if (value && value.length > 1000) {
    return Promise.reject(new Error('请输入数字/字母/汉字且长度不超过1000位！'));
  }
  return Promise.resolve();
};
const formRef = ref();
const formModelRef = ref();
const formDisRef = ref();
const againLoading = ref<boolean>(false);
// 一般临分方式发起再保申请
const getApply = async () => {
  const validateArr = [formRef.value.validateFields('projectName')];
  if (checked.value) validateArr.push(formModelRef.value.validateFields());
  if (ratioChecked.value) validateArr.push(formDisRef.value.validateFields());
  Promise.all(validateArr).then(() => {
    apply();
  });
};
// 批复号失焦提交接口
const handleFacultativeNo = async () => {
  formState.value.relatedFacultativeNo = true;
  await formRef.value.validateFields('facultativeNo');
  apply();
};
const eoaSubjects = ref<string>(''); // 再保签报标题-仅一般性临分返回
const eoaBodys = ref<string>(''); // 签报正文-仅一般性临分返回
// 发起再保申请接口
const apply = async () => {
  try {
    againLoading.value = true;
    const params = {
      ...formState.value,
      facultativeType: !['2', '7'].includes(formState.value.facultativeType) ? '' : formState.value.facultativeType,
      reinsuranceDetailList: [...dataSource.value, ...disDataSource.value],
    };
    const res = await $postOnClient<EoaResType>(`${gateWay}${service.ums}/reinsurance/apply`, params);
    if (res?.code === SUCCESS_CODE) {
      const { eoaSubject, eoaBody, idReinsurance } = res?.data || {};
      formState.value.idReinsurance = idReinsurance;
      eoaSubjects.value = eoaSubject;
      eoaBodys.value = eoaBody;
      if (formState.value.relatedFacultativeNo) {
        queryReinsuranceInfoId();
        formState.value.relatedFacultativeNo = false;
      }
      if (formState.value.facultativeType === '7') {
        queryReinsuranceInfoId();
      }
      if (formState.value.facultativeType === '2') lookVisible.value = true;
    } else {
      message.error(res?.msg || '');
    }
    againLoading.value = false;
  } catch (error) {
    console.log(error);
    againLoading.value = false;
  };
};
// 查询再保信息
const queryReinsuranceInfoId = async () => {
  try {
    const res = await $getOnClient<ReinsuranceInfoType>(`${gateWay}${service.ums}/reinsurance/queryReinsuranceInfoById`, { idReinsurance: formState.value.idReinsurance });
    if (res?.code === SUCCESS_CODE) {
      formState.value = res?.data || {};
      reinsuranceInfoData.value = res?.data || {};
      getCommon();
    } else {
      message.error(res?.msg || '');
    }
  } catch (error) {
    console.log(error);
  };
};
const acceptLoading = ref<boolean>(false); // 采纳
const noAcceptLoading = ref<boolean>(false);
// 采用-1，不采用-2,再保方案
const updateReinsurance = async (operateType: string) => {
  try {
    if (['1', '3'].includes(operateType)) {
      acceptLoading.value = true;
    } else {
      noAcceptLoading.value = true;
    }
    const res = await $postOnClient(`${gateWay}${service.ums}/reinsurance/updateReinsurance`, { idReinsurance: formState.value.idReinsurance, operateType });
    if (res?.code === SUCCESS_CODE) {
      if (operateType !== '4') {
        message.success(res?.msg || '');
        queryReinsuranceInfoId();
      }
    } else {
      message.error(res?.msg || '');
    }
    acceptLoading.value = false;
    noAcceptLoading.value = false;
  } catch (error) {
    console.log(error);
    acceptLoading.value = false;
    noAcceptLoading.value = false;
  };
};
// 公共方法
const getCommon = () => {
  if (formState.value?.relatedFacultativeNo) {
    formState.value.facultativeType = '4';
  } else {
    if (formState.value.reinsuranceDetailList?.length > 0 && ['0', '3', '8'].includes(formState.value.status)) {
      checked.value = true;
      ratioChecked.value = true;
    }
    dataSource.value = formState.value.reinsuranceDetailList?.filter(item => item.facultativeMode === '2');
    disDataSource.value = formState.value.reinsuranceDetailList?.filter(item => item.facultativeMode === '1');
  }
};
type EoaDetailUrl = {
  eoaDetailUrl: string;
};
const signLoading = ref<boolean>(false);
// 一般性临分查看签报
const queryEoaInfo = async () => {
  try {
    signLoading.value = true;
    const res = await $getOnClient<EoaDetailUrl>(gateWay + service.ums + '/udw/eoa/queryEoaInfo', { eoaNo: formState.value.signNo });
    if (res?.code === SUCCESS_CODE) {
      const { eoaDetailUrl } = res?.data || {};
      window.open(eoaDetailUrl);
    } else {
      message.error(res?.msg || '');
    }
    signLoading.value = false;
  } catch (error) {
    console.log(error);
    signLoading.value = false;
  }
};
// 监听再保是否有数据，有数据赋值给formState
watch(() => contractModel, (val) => {
  reinsuranceInfoData.value = val?.reinsuranceInfo;
  if (val?.reinsuranceInfo !== null && val?.reinsuranceInfo) {
    formState.value = val?.reinsuranceInfo;
    getCommon();
  }
});
onMounted(() => {
  // 非总对总FAC-点击发起再保申请，跳转到再保系统，再保系统发起签报成功后，调用此方法，关闭再保系统，拿到对应参数，回调接口
  window.facCallbackParamStr = null;
  // 定义回调函数
  window.facCallbackParamStr = function (parameters: { facultativeNo: string; schemeNo: string; quotationNo: string }) {
    formState.value.facultativeNo = parameters.facultativeNo; // 获取到回传的临分批复编号
    formState.value.schemeNo = parameters.schemeNo;
    formState.value.facultativeType = '7';
    formState.value.applyPolicyNo = parameters.quotationNo;
    apply();
  };
});
</script>

<style lang="less" scoped>
.red {
  color: #F03E3E;
}
.green {
  color: #07C160;
}
</style>
