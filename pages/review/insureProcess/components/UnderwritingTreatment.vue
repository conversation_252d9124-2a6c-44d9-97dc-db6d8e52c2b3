<template>
  <!-- 核保处理 -->
  <a-form ref="formRef" :colon="false" :model="formState">
    <a-form-item label="核保结论" name="approveStatus" :rules="[{ required: true, message: '请选择' }]">
      <a-radio-group v-model:value="formState.approveStatus" @change="handleChange">
        <a-radio value="B2">下发</a-radio>
        <a-radio value="B4">同意</a-radio>
        <a-radio v-if="!hiddenReject" value="B7">拒保</a-radio>
      </a-radio-group>
    </a-form-item>
    <div v-if="['B2', 'B7'].includes(formState.approveStatus)">
      <div class="ml-[77px]">
        <!-- mode="multiple" -->
        <a-form-item name="approveResult" :rules="[{ required: true, message: `请选择${formState.approveStatus === 'B2' ? '下发' : '拒保'}原因` }]">
          <a-select v-model:value="formState.approveResult" placeholder="请选择原因" :options="options" allow-clear @change="handleChangeLabel" />
        </a-form-item>
      </div>
    </div>
    <a-form-item label="核保意见" name="approveResultDesc" :rules="[{ required: true, message: '请填写核保意见' }]">
      <a-textarea v-model:value="formState.approveResultDesc" :maxlength="4000" :rows="2" />
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select';
import type { UdwProcessType } from '../insureProcess.d';

const formState = defineModel<UdwProcessType>('udwProcess', { default: {} });
const { hiddenReject = false } = defineProps<{ hiddenReject?: boolean }>();
// 下发时- option
const issueOptions = [
  { value: 'C1', label: '承保资料上传不完整' },
  { value: 'C2', label: '承保资料不准确与系统信息不对应' },
  { value: 'C3', label: '承保资料存在不真实情况' },
  { value: 'C4', label: '未按要求进行公示' },
  { value: 'C5', label: '其他项' },
];
// 拒保时- option
const rejectOptions = [
  { value: 'C6', label: '高风险' },
  { value: 'C7', label: '重复录单' },
  { value: 'C5', label: '其他' },
];
const options = computed(() => formState.value?.approveStatus === 'B2' ? issueOptions : rejectOptions);
const formRef = ref();
// 核保结论选择时，清空选项值
const handleChange = async () => {
  formState.value.approveResult = undefined;
  if (formState.value.approveStatus === 'B4') {
    formState.value.approveResultDesc = '同意';
  } else {
    formState.value.approveResultDesc = '';
  }
  // 防止第一次未触发
  nextTick(() => {
    formRef.value.validateFields('approveResult');
    formRef.value.validateFields('approveResultDesc');
  });
};
// 支持多选
const handleChangeLabel = async (val: SelectValue, option: DefaultOptionType | DefaultOptionType[]) => {
  formState.value.approveResultDesc = option.label;
  // const str: string[] = [];
  // option?.forEach((item: { label: string }) => str.push(item.label));
  // formState.value.approveResultDesc = str?.length > 1 ? str?.join(';') : str.join();
  nextTick(() => {
    formRef.value.validateFields('approveResultDesc');
  });
};
const validate = async () => {
  if (formRef.value) {
    try {
      await formRef.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};
defineExpose({
  validate,
});
</script>
