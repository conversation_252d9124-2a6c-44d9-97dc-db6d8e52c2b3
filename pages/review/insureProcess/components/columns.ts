import type { ColumnsType } from 'ant-design-vue/es/table';

// 比例临分列表
export const disColumns: ColumnsType<Record<string, string>> = [
  { title: '分出比例（%）', dataIndex: 'separationRatio' },
  { title: '手续费（%）', dataIndex: 'poundage' },
  { title: '免赔', dataIndex: 'abatement' },
  { title: '其他说明', dataIndex: 'otherExplain' },
];
// 超赔临分列表
export const compensateColumns: ColumnsType<Record<string, string>> = [
  { title: '起赔点', dataIndex: 'franchise' },
  { title: '分出比例（%）', dataIndex: 'separationRatio' },
  { title: '赔偿限额', dataIndex: 'compensateLimit' },
  { title: '分出净保费', dataIndex: 'separationPremium' },
  { title: '其他说明', dataIndex: 'otherExplain' },
  { title: '操作', dataIndex: 'action' },
];
// 共保列表
export const coinsuranceColumns: ColumnsType<Record<string, string>> = [
  { title: '共保公司代码', dataIndex: 'reinsureCompanyCode' },
  { title: '共保公司名称', dataIndex: 'reinsureCompanyName' },
  { title: '类型', dataIndex: 'coinsuranceChName' },
  { title: '保险金额', dataIndex: 'insuredAmount' },
  { title: '保费金额', dataIndex: 'premium' },
  { title: '共保比例（%）', dataIndex: 'reinsureScale' },
  { title: '是否主承保', dataIndex: 'acceptInsuranceFlag' },
];
