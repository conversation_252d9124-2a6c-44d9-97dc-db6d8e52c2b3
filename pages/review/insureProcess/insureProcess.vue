<template>
  <div ref="scrollWrapper" class="page-container">
    <main class="main-wrapper">
      <div class="flex-1 border-right-line">
        <div class="h-[56px] w-ful bg-image-url">
          <span class="text-[24px] text-[#00190c] leading-[56px] ml-[16px]">核保处理</span>
        </div>
        <a-spin :spinning="loading">
          <div class="flex bg-white h-[54px] items-center pl-[10px] pr-[24px] py-[12px] mr-[14px] justify-between flex-wrap">
            <div class="text-[#333]"><span class="text-[rgba(0,0,0,.6)]">投保单号：</span>{{ contractModel?.baseInfo?.applyPolicyNo || '-' }}</div>
            <div class="text-[#333]"><span class="text-[rgba(0,0,0,.6)]">产品：</span>{{ contractModel?.baseInfo?.productName || '-' }}</div>
            <div class="text-[#333]"><span class="text-[rgba(0,0,0,.6)]">补贴类型：</span>{{ contractModel?.baseInfo?.govSubsidyTypeChName || '-' }}</div>
            <div class="text-[#333]"><span class="text-[rgba(0,0,0,.6)]">清单编号：</span>{{ contractModel?.baseInfo?.farmerlistNo || '-' }}</div>
            <div class="text-[#333]"><span class="text-[rgba(0,0,0,.6)]">投保单申请号：</span>{{ contractModel?.extendInfo?.preRiskNo || '-' }}</div>
          </div>
          <div class="relative mr-[14px] mt-[14px] form-content">
            <!-- 核保助手 -->
            <ReviewAssistant v-model:business-rating="businessRating" />
            <!-- 基础信息 -->
            <InfoGroupBox id="base-info" title="基础信息">
              <BaseInfo ref="baseInfoRef" v-model:agr-commercial-model-info="contractModel.agrCommercialModel" :contract-model="contractModel" :error-data-info="errorDataInfo" />
            </InfoGroupBox>
            <!-- 清单信息 -->
            <InfoGroupBox id="farmer-info" title="清单信息">
              <FarmerInfo :contract-model="contractModel" :error-data-info="errorDataInfo" />
            </InfoGroupBox>
            <!-- 保险方案 -->
            <InfoGroupBox id="insure-info" title="保险方案">
              <InsureInfo ref="insureInfoRef" v-model:base-info="contractModel.baseInfo" :contract-model="contractModel" :error-data-info="errorDataInfo">
                <!-- 费用信息组件 -->
                <CostInfo ref="costInfoRef" v-model:cost-info="contractModel.costInfo" :contract-model="contractModel" />
              </InsureInfo>
            </InfoGroupBox>
            <!-- 保费信息 -->
            <InfoGroupBox id="cost-info" title="保费信息">
              <InsureCostInfo :contract-model="contractModel" :error-data-info="errorDataInfo" />
            </InfoGroupBox>
            <!-- 再次共保 -->
            <InfoGroupBox id="again-coinsurance-info" title="再/共保信息">
              <AgainCoinsurance :contract-model="contractModel" :error-data-info="errorDataInfo" />
            </InfoGroupBox>
            <!-- 渠道信息 -->
            <InfoGroupBox id="channel-info" title="渠道信息">
              <ChannelInfo :contract-model="contractModel" :error-data-info="errorDataInfo" />
            </InfoGroupBox>
            <!-- 核保处理 -->
            <InfoGroupBox id="underwriting-treatment" title="核保处理">
              <UnderwritingTreatment ref="underwritingTreatmentRef" v-model:udw-process="udwProcess" />
            </InfoGroupBox>
          </div>
        </a-spin>
      </div>
      <!-- 侧边锚点导航 -->
      <div class="right-sider">
        <div class="sticky top-[25px]">
          <span class="text-[#404442] font-semibold">大纲</span>
        </div>
        <a-anchor :offset-top="70" :get-container="getContainer" :items="anchorItems" @click="handleClick" />
      </div>
    </main>
    <!-- 操作按钮区域 -->
    <footer class="footer-wrapper space-x-8px">
      <a-button type="primary" ghost @click="handleGoLink">附件管理</a-button>
      <a-button type="primary" ghost @click="goInspectionInfo">验标信息</a-button>
      <a-button
        type="primary"
        ghost
        @click="
          processOpen = true;
          queryUnderwriteAssistant();
        "
        >核保轨迹</a-button
      >
      <a-button type="primary" :loading="btnLoading" @click="handleOk">确定</a-button>
    </footer>
    <!-- 点击核保信息按钮 -->
    <InsureTrack v-model:process-open="processOpen" :insure-info="insureInfo" :chain-str="chainStr" />
  </div>
</template>

<script setup lang="ts">
import BaseInfo from './components/BaseInfo.vue';
import FarmerInfo from './components/FarmerInfo.vue';
import InsureInfo from './components/InsureInfo.vue';
import InsureCostInfo from './components/InsureCostInfo.vue';
import AgainCoinsurance from './components/AgainCoinsurance.vue';
import ChannelInfo from './components/ChannelInfo.vue';
import UnderwritingTreatment from './components/UnderwritingTreatment.vue';
import ReviewAssistant from './components/reviewAssistant/index.vue';
import CostInfo from './components/CostInfo.vue';
import InsureTrack from './components/InsureTrack.vue';
import type { ContractModelType, UdwProcessType, QueryApprovalTaskList, ResType, ErrorModelType, ContractVOType, agrCommercialModelType } from './insureProcess.d';
import InfoGroupBox from '@/components/ui/InfoGroupBox.vue';
import { $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

const loading = ref<boolean>(false);
// 关闭页签
const { deletPageTabListItem } = inject('pageTab');
// 定位锚点
const scrollWrapper = ref();
const getContainer = () => scrollWrapper.value || window;
const anchorItems = ref([
  { key: '1', href: '#review-assistant', title: '核保助手' },
  { key: '2', href: '#base-info', title: '基础信息' },
  { key: '3', href: '#farmer-info', title: '清单信息' },
  { key: '4', href: '#insure-info', title: '保险方案' },
  { key: '5', href: '#cost-info', title: '保费信息' },
  { key: '6', href: '#again-coinsurance-info', title: '再/共保信息' },
  { key: '7', href: '#channel-info', title: '渠道信息' },
  { key: '8', href: '#underwriting-treatment', title: '核保信息' },
]);
const infoDataObj = {
  personnelType: [], // 客户属性
  name: [], // 姓名
  certificateNo: [], // 身份证号
  certificateIssueDate: [], // 证件有效期开始日期（发证日期）
  certificateValidDate: [], // 证件有效期截止日期（失效日期
  address: [], // 地址
  postcode: [], // 邮政编码
  sexCode: [], // 性别
  mobileTelephone: [], // 手机号码
  poorSymbol: [], // 贫困户标识
  belongOrganizationNo: [], // 企业归属
  professionName: [], // 职业名称
  subjectName: [], // 农业主体类型
  nationality: [], // 国籍
  organizerName: [], // 组织者名称
  certificateTypeChName: [], // 证件类型
  businessScope: [], // 经营范围
  completeIndustryChName: [], // 行业
  organizationType: [], // 组织机构类型
  email: [],
  postalCode: [],
  contactTelephone: [],
  organizationTypeChName: [],
};
// 点击核保信息按钮，打开弹窗
const processOpen = ref<boolean>(false);
const data: ContractModelType = {
  baseInfo: {
    assisterInfoList: [],
    coinsuranceMark: '',
    applyPolicyType: '',
    farmerlistNo: '',
    farmersCount: '',
    disputedSettleModeChName: '',
    renewalTypeName: '',
    lastPolicyNo: '',
    insuranceBeginDate: '',
    shortTimeCoefficient: '',
    insuranceEndDate: '',
    timeRange: '',
    applyPolicyNo: '',
    govSubsidyTypeChName: '',
    inputBy: '',
    policyNo: '',
    productName: '',
    insuranceBeginEndDateType: '',
    deductionDesc: '',
    farmerlistFarmerNum: '',
    farmerlistRiskNum: '',
    verifyRate: '',
    unitName: '',
    disputedSettleMode: '',
    applyPolicyTypeChName: '',
    insuredNumber: '',
    arbitralDepartment: '',
    renewalType: '',
    totalInsuredAmount: '',
    totalStandardPremium: '',
    insurancePeriod: '',
    totalAgreePremium: '',
    totalActualPremium: '',
  }, // 基础信息
  extendInfo: {
    tenderBusiness: '', // 项目来源
    tenderBusinessName: '', // 项目名称
    lossRate: '', // 风险程度
    customerType: '', // 客户类型
    customerAbbrName: '', // 客户简称
    isTenderBusinessChName: '',
    preRiskNo: '',
    futureCompanyName: '',
  },
  extendGroupInfo: {
    productFactoryPlanType: '',
  },
  applicantInfoList: [], // 投保人
  insurantInfoList: [], // 被保人
  beneficaryInfoList: [], // 收益人
  riskAddressInfoList: [], // 标的
  riskGroupInfoList: [], // 标的
  sumRiskGroupAmount: {}, // 保费计划合计
  coinsuranceInfo: {
    innerCoinsuranceAgreement: '',
    totalPremium: '',
    totalInsuredAmount: '',
    coinsuranceDetailList: [],
    innerCoinsuranceMarkChName: '',
  }, // 共保
  noclaimInfoList: [], // 免赔
  payInfoList: [], // 收费计划
  costInfo: {
    calamitySecurityRate: '', // 防灾防损费
    isPolicyBeforePayfee: '', // 财务标识
    assisterCharge: '', // 协办费
    commissionBrokerChargeProportion: '', // 手续费/经纪费
    managementFees: '', // 工作经费
    coinsuranceInsureFeeRatio: '', // 共保出单
    performanceValue1Default: '', // 农险补贴
    totalSumFeeLimit: '', //  string; // 总费用之和上限值
    assisterChargeLimit: '', // 协办费上限值
    commissionBrokerChargeProportionLimit: '', // 手续费/经纪费上限
    managementFeesLimit: '', // 工作经费上限-  // 管理费(业务员的工作经费比例
    calamitySecurityRateLimit: '', // 防灾防损费上限值
    performanceValue1DefaultLimit: '', // 农险补贴上限值
    coinsuranceInsureFeeRatioLimit: '',
    isPolicyBeforePayfeeChName: '',
  }, // 费用信息
  specialPromiseList: [], // 特约
  saleInfo: {
    departmentName: '',
    channelSourceDetailName: '',
    channelSourceName: '',
    saleagentIntroducerCode: '',
    employeeInfoList: [],
    developFlg: '',
    businessSourceCode: '',
    channelSourceCode: '',
    channelSourceDetailCode: '',
    primaryIntroducerInfo: {},
    brokerInfoList: [],
    agentInfoList: [],
    departmentCodeAndName: '',
    developFlgChName: '',
  },
  reinsuranceInfo: {
    idReinsurance: '',
    projectName: '',
    facultativeType: '',
    facultativeNo: '',
    applyPolicyNo: '',
    schemeNo: '',
    signNo: '',
    relatedFacultativeNo: false,
    reinsuranceDetailList: [],
    status: '',
    id: '',
  },
  organizerInfo: {},
};
const errorData = {
  insureList: [],
  riskAgrInfoExtend: [],
  insurePlan: [],
  payInfo: [],
  applicant: infoDataObj,
  beneficary: infoDataObj,
  insurant: infoDataObj,
  organizerInfo: infoDataObj,
  channelInfo: {
    channelSourceName: [], // 渠道名称
    channelSourceDetailName: [], // 渠道细分名称
    departmentCode: [], // 出单机构
    assisterId: [], // 农保id
    employeeInfo: [], // 业务员信息
    departmentName: [],
  },
  coinsurance: {
    innerCoinsuranceMark: [],
    innerCoinsuranceAgreement: [], // 共保协议码
    totalPremium: [], // 总保费
    totalInsuredAmount: [], // 总保额
    coinsuranceDetailList: [],
  },
  feeInfo: {
    calamitySecurityRate: [], // 防灾防损费
    isPolicyBeforePayfee: [], // 财务标识
    assisterCharge: [], // 协办费
    commissionBrokerChargeProportion: [], // 手续费/经纪费
    managementFees: [], // 工作经费
    coinsuranceInsureFeeRatio: [], // 共保出单
    performanceValue1Default: [], // 农险补贴
  },
  productInfo: {
    insuranceBeginDate: [], // 保险起期
    insuranceEndDate: [], // 保险止期
    insurancePeriod: [], // 保险期限
    shortTimeCoefficient: [], // 年限系数
    timeRange: [], // 起止时间段
    substitute: [], // 农户保费是否代缴
    reductionCoefficient: [], // 减免系数
    totalInsuredAmount: [], // 保单保险金额
    totalAgreePremium: [], // 减免后保单保费金额
    totalStandardPremium: [], // 减免前保单保费金额
    insuredNumber: [], // 保险数量
    farmersCount: [], // 参保户数
    totalActualPremium: [], // 复核保费
  },
  projectInfo: {
    tenderBusiness: [], // 项目来源
    tenderBusinessName: [], // 项目名称
    lossRate: [], // 风险程度
    customerType: [], // 客户类型
    customerAbbrName: [], // 客户简称
    isTenderBusinessChName: '',
    futureCompanyName: '', // 期货公司名称
  },
  riskAgrInfo: {
    centralFinance: [], // 保费来源(中央财政)%
    provincialFinance: [], // 保费来源(省级财政)%
    cityFinance: [], // 保费来源(地市财政)%
    countyFinance: [], // 保费来源(县财政)%
    farmersFinance: [], // 保费来源(农户)%
    otherFinance: [], // 保费来源(其他来源)%
  },
  riskInfo: {
    insuredNumber: [], // 承保数量
    farmersCount: [], // 承保农户数
    riskTypeDetailName: [], // 标的名称明细中文名称
    unitInsuredAmount: [], // 单位保险金额
    insuredRate: [], // 保险费率
    unitPremium: [], // 单位保费
    insuredAmount: [], // 保险金额
    premium: [], // 保费金额
    riskAddress: [], // 标的地址
    insuredUnit: [], // 保险单位
  },
  specialPromise: {
    promiseType: [], // 特约类型
    promiseDesc: [], // 特约描述
  },
  renewalInsuranceInfo: {
    renewalInsuranceVoucherNo: [],
    isBelongRenewalInsurance: [],
  },
  isPolicyBeforePayfeeChName: [],
};
const initContractModel = (data: ContractVOType) => {
  const contract = data?.contractVO || {};
  return {
    taskId: data?.taskId,
    baseInfo: contract?.baseInfo,
    applicantInfoList: contract?.applicantInfoList || [],
    beneficaryInfoList: contract?.beneficaryInfoList || [],
    insurantInfoList: contract?.insurantInfoList || [],
    organizerInfo: contract?.organizerInfo,
    extendInfo: contract?.extendInfo,
    extendGroupInfo: contract?.extendGroupInfo,
    riskGroupInfoList: contract?.riskGroupInfoList || [], // 标的
    costInfo: contract?.costInfo,
    riskAddressInfoList: contract?.riskAddressInfoList || [],
    reinsuranceInfo: contract?.reinsuranceInfo || null,
    noclaimInfoList: contract?.noclaimInfoList || [],
    specialPromiseList: contract?.specialPromiseList || [],
    saleInfo: contract?.saleInfo,
    payInfoList: contract?.payInfoList,
    coinsuranceInfo: contract?.coinsuranceInfo,
    attachmentGroupList: contract?.attachmentGroupList || [],
    agrCommercialModel: handleAgrCommercialModel(data?.agrCommercialModel),
  };
};

const handleAgrCommercialModel = (data: agrCommercialModelType | undefined) => {
  if (data?.agrCommercialRelatedPolicyNo) {
    return { ...data, agrCommercialRelatedPolicyNo: data?.agrCommercialRelatedPolicyNo?.split(',') };
  }
  if (data) {
    return {
      ...data,
      agrCommercialRelatedPolicyNo: data.agrCommercialRelatedPolicyNo || [],
      attachmentNo: data.attachmentNo || [],
    };
  }
  return { agrCommercialRelatedPolicyNo: [], attachmentNo: [] };
};
const initErrorModel = (data: ErrorModelType) => {
  return {
    insureList: data.insureList, // 列表清单
    applicant: data.applicant,
    beneficary: data.beneficary,
    insurant: data.insurant,
    organizerInfo: data?.organizerInfo,
    channelInfo: data.channelInfo,
    coinsurance: data.coinsurance,
    productInfo: data.productInfo,
    projectInfo: data.projectInfo,
    riskAgrInfo: data.riskAgrInfo,
    riskInfo: data.riskInfo,
    specialPromise: data.specialPromise,
    renewalInsuranceInfo: data.renewalInsuranceInfo,
    riskAgrInfoExtend: data.riskAgrInfoExtend,
    insurePlan: data.insurePlan,
    payInfo: data.payInfo,
    baseInfo: data.baseInfo,
    isPolicyBeforePayfeeChName: data.isPolicyBeforePayfeeChName,
  };
};
// 初始化-默认值
const contractModel = ref(initContractModel(data));
const errorDataInfo = ref(initErrorModel(errorData));
const route = useRoute();
// 初始化接口请求
const getInit = async () => {
  loading.value = true;
  try {
    const fetchUrl = `${gateWay}${service.ums}/underwrite/queryUnderwriteHandlerInfo`;
    const res = await $getOnClient(fetchUrl, { applyPolicyNo: route.query?.applyPolicyNo || '' });
    const { code, msg = '', data = { contractVO: {}, taskId: '' } } = res || {};
    if (code === SUCCESS_CODE) {
      contractModel.value = initContractModel(data as ContractVOType);
    } else {
      message.error(msg || '请求失败');
    }
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
};
const router = useRouter();
// 跳转附件管理页面
const handleGoLink = () => {
  router.push({
    path: '/attachment',
    query: {
      bizNo: route.query?.applyPolicyNo,
      bizType: 'docViewTreeApply',
    },
  });
};
const handleClick = (e: { preventDefault: () => void }, link: { href: string; title: string }) => {
  e.preventDefault();
  if (link.href) {
    const el = document.querySelector(link.href);
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    el && el.scrollIntoView({ block: 'start', behavior: 'smooth' });
  }
};
// 表单校验
const baseInfoRef = ref();
const underwritingTreatmentRef = ref();
const costInfoRef = ref();
const insureInfoRef = ref();
const handleOk = async (e: { preventDefault: () => void }) => {
  // 子组件的校验方法
  const validateArr = [insureInfoRef.value?.validate(), costInfoRef.value?.validate(), underwritingTreatmentRef.value?.validate(), baseInfoRef.value?.validate()];
  const results = await Promise.all(validateArr);
  const isValid = results.every((result) => result.valid);
  if (contractModel.value.extendInfo.govSubsidyType === '3' && !results[3].valid) {
    handleClick(e, { href: '#base-info', title: '基础信息' });
  } else if (!results[0].valid || !results[1].valid) {
    handleClick(e, { href: '#insure-info', title: '保险方案' });
  } else {
    if (!results[2].valid) {
      handleClick(e, { href: '#underwriting-treatment', title: '核保信息' });
    }
  }
  // 所有表单校验通过
  if (isValid) handleUnderwrite(); // 接口
};
// 核保意见信息字段
const udwProcess = reactive<UdwProcessType>({
  taskId: '',
  voucherNo: '',
  approveStatus: '',
  approveResult: undefined,
  approveResultDesc: '',
});
// 业务十分类字段
const businessRating = ref<string>('');
// 点击确定按钮
const btnLoading = ref<boolean>(false);
const handleUnderwrite = async () => {
  btnLoading.value = true;
  try {
    const fetchUrl = `${gateWay}${service.ums}/underwrite/underwriteProcess`;
    const params = {
      feeInfo: contractModel.value.costInfo,
      dataSource: 'ICORE-AGR-AN',
      renewalInsuranceInfo: {
        isBelongRenewalInsurance: contractModel.value?.baseInfo?.renewalType === '1',
        renewalInsuranceVoucherNo: contractModel.value?.baseInfo?.lastPolicyNo || '',
      },
      udwProcess: {
        ...udwProcess,
        taskId: contractModel.value?.taskId,
        voucherNo: contractModel.value?.baseInfo?.applyPolicyNo,
      },
      businessRating: businessRating.value,
      agrCommercialModel:
        contractModel.value.extendInfo.govSubsidyType === '3'
          ? {
              ...contractModel.value?.agrCommercialModel,
              taskNo: contractModel.value?.taskId,
              businessNo: contractModel.value?.baseInfo?.applyPolicyNo,
              agrCommercialRelatedPolicyNo: contractModel.value?.agrCommercialModel?.agrCommercialInsuranceModeCategory === '02' ? contractModel.value?.agrCommercialModel.agrCommercialRelatedPolicyNo?.join(',') : null,
              attachmentNo: contractModel.value?.agrCommercialModel?.attachmentNo.join(','),
            }
          : null,
    };
    const res = await $postOnClient(fetchUrl, params);
    const { code, msg = '' } = res || {};
    if (code === SUCCESS_CODE) {
      message.success(msg);
      router.replace({
        path: '/policyReview',
      });
      deletPageTabListItem('/insureProcess');
    } else {
      message.error(msg);
    }
  } catch (error) {
    console.log(error);
  }
  btnLoading.value = false;
};
// 核保轨迹
const insureInfo = ref<QueryApprovalTaskList[]>([]);
const chainStr = ref<string>('');
const queryUnderwriteAssistant = async () => {
  try {
    const fetchUrl = `${gateWay}${service.ums}/evntApprovalTaskRecord/queryApprovalTaskList`;
    const res = await $getOnClient<ResType>(fetchUrl, { voucherNo: route.query.applyPolicyNo });
    const { taskRecordVOList = [], chainList = [] } = res?.data || {};
    if (res?.code === SUCCESS_CODE) {
      insureInfo.value = taskRecordVOList || [];
      chainStr.value = chainList.join('->');
    } else {
      message.error(res?.msg || '');
    }
  } catch (error) {
    console.log(error);
  }
};
// 风险提示
const queryRiskWarnInfo = async () => {
  try {
    const fetchUrl = `${gateWay}${service.ums}/parmUdwCheckResult/queryRiskWarnInfo`;
    const res = await $postOnClient(fetchUrl, { insuranceBusinessNo: route.query.applyPolicyNo });
    const { code, msg = '', data } = res || {};
    if (code === SUCCESS_CODE) {
      errorDataInfo.value = initErrorModel(data as ErrorModelType) || [];
    } else {
      message.error(msg);
    }
  } catch (error) {
    console.log(error);
  }
};
// 跳转验标页面
const goInspectionInfo = () => {
  router.push({
    path: '/inspectionInfo',
    query: {
      applyPolicyNo: contractModel.value?.baseInfo?.applyPolicyNo,
      isHiddenProcess: 'Y',
    },
  });
};

const routerQueryApplyPolicyNo = ref('');

onActivated(() => {
  if (route.query.applyPolicyNo !== routerQueryApplyPolicyNo.value) {
    // 投保单号不一样，发起请求
    getInit();
    queryRiskWarnInfo();
  }

  routerQueryApplyPolicyNo.value = route.query.applyPolicyNo;
});
const { gateWay, service } = useRuntimeConfig().public || {};
</script>

<style lang="less" scoped>
.page-container {
  position: relative;
  height: calc(100vh - 40px);
  overflow: auto;

  .main-wrapper {
    padding: 14px;
    display: flex;
    .bg-image-url {
      background-image: url(/assets/images/content-head.png);
      background-size: cover;
    }
    .border-right-line {
      border-right: 1px solid #e6e8eb;
    }
    .border-bottom-line {
      border-bottom: 1px solid #e6e8eb;
      margin-bottom: 16px;
    }

    .right-sider {
      background: #fff;
      width: 100px;
      padding-left: 32px;
    }
  }
  .footer-wrapper {
    z-index: 100;
    height: 50px;
    background: #fff;
    position: sticky;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  }
  :deep(.ant-divider) {
    margin: 14px 0;
  }
}
</style>
