<template>
  <div>
    <div class="form-title">{{ title }}</div>
    <div class="grid grid-cols-3 gap-y-[12px] text-[#333333]">
      <div>
        <span class="label">名称</span>
        <CompareRender :value-path="`${personType}.organizerName`" :value="data.organizerName" />
      </div>
      <div>
        <span class="label">证件类型</span>
        <CompareRender :value-path="`${personType}.certificateTypeChName`" :value="data.certificateTypeChName" />
      </div>
      <div>
        <span class="label">证件号码</span>
        <CompareRender :value-path="`${personType}.certificateNo`" :value="data.certificateNo" />
      </div>
      <div>
        <span class="label">证件有效期</span>
        <CompareRender
          :value-path="`${personType}.certificateIssueDate`"
          :value="`${data.certificateIssueDate || '-'} - ${data.certificateValidDate || '-'}`"
        />
      </div>
      <div>
        <span class="label">地址</span>
        <CompareRender :value-path="`${personType}.address`" :value="data.address" />
      </div>
      <div>
        <span class="label">邮政编码</span>
        <CompareRender :value-path="`${personType}.postalCode`" :value="data.postalCode" />
      </div>
      <div>
        <span class="label">联系人</span>
        <CompareRender :value-path="`${personType}.contactPerson`" :value="data.contactPerson" />
      </div>
      <div>
        <span class="label">联系电话</span>
        <CompareRender :value-path="`${personType}.contactTelephone`" :value="data.contactTelephone" />
      </div>
      <div>
        <span class="label">电子邮箱</span>
        <CompareRender :value-path="`${personType}.email`" :value="data.email" />
      </div>
      <div>
        <span class="label">经营范围</span>
        <CompareRender :value-path="`${personType}.businessScope`" :value="data.businessScope" />
      </div>
      <div>
        <span class="label">行业</span>
        <CompareRender :value-path="`${personType}.industryCode`" :value="data.industryCode" />
      </div>
      <div>
        <span class="label">组织机构类型</span>
        <CompareRender :value-path="`${personType}.organizationTypeChName`" :value="data.organizationTypeChName" />
      </div>
      <!-- <div>
        <span class="label">国籍</span>
        <CompareRender :value-path="`${personType}.nationalityZh`" :value="data.nationalityZh" />
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import CompareRender from './CompareRender.vue';

defineProps<{ title: string; personType: string; data: Record<string, string> }>();
</script>

<style lang="less" scoped>
.label {
  margin-right: 12px;
}
</style>
