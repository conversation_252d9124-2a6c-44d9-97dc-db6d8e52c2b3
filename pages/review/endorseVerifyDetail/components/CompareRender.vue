<template>
  <div class="inline-block">
    <a-tooltip v-if="compareObj" placement="topLeft">
      <template #title>
        <span>批改前: {{ compareObj.oldValue }}</span>
      </template>
      <span class="text-[#07C160]">{{ props.value || '-' }}</span>
    </a-tooltip>
    <span v-else>{{ props.value || '-' }}</span>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  value: string;
  valuePath: string;
}>();

const compareValueMap = inject<Map<string, { oldValue: string }>>('compareValueMap');
const compareObj = computed(() => {
  return compareValueMap?.get(props.valuePath);
});
</script>
