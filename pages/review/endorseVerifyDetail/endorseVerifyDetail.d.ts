// 被保险人信息,投保人信息,受益人信息，ApplicantVO、BeneficaryVO、InsurantVO
export interface ApplicantInfoType {
  [key: string | number]: string | number;
  personnelType: string; // 客户属性
  name: string; // 姓名
  certificateNo: string; // 身份证号
  certificateIssueDate: string; // 证件有效期开始日期（发证日期）
  certificateValidDate: string; // 证件有效期截止日期（失效日期
  address: string; // 地址
  postcode: string; // 邮政编码
  sexCode: string; // 性别
  mobileTelephone: string; // 手机号码
  poorSymbol: string; // 贫困户标识
  belongOrganizationNo: string; // 企业归属
  professionName: string; // 职业名称
  subjectName: string; // 农业主体类型
  nationality: string; // 国籍
  organizerName: string; // 组织者名称
  certificateTypeChName: string; // 证件类型
  businessScope: string; // 经营范围
  completeIndustryChName: string; // 行业
  organizationType: string; // 组织机构类型
  email: string;
}
export interface ErrorApplicantInfoType {
  personnelType: RiskInfoData[]; // 客户属性
  name: RiskInfoData[]; // 姓名
  certificateNo: RiskInfoData[]; // 身份证号
  certificateIssueDate: RiskInfoData[]; // 证件有效期开始日期（发证日期）
  certificateValidDate: RiskInfoData[]; // 证件有效期截止日期（失效日期
  address: RiskInfoData[]; // 地址
  postcode: RiskInfoData[]; // 邮政编码
  sexCode: RiskInfoData[]; // 性别
  mobileTelephone: RiskInfoData[]; // 手机号码
  poorSymbol: RiskInfoData[]; // 贫困户标识
  belongOrganizationNo: RiskInfoData[]; // 企业归属
  professionName: RiskInfoData[]; // 职业名称
  subjectName: RiskInfoData[]; // 农业主体类型
  nationality: RiskInfoData[]; // 国籍
  organizerName?: RiskInfoData[]; // 组织者名称
  certificateTypeChName?: RiskInfoData[]; // 证件类型
  businessScope?: RiskInfoData[]; // 经营范围
  completeIndustryChName: RiskInfoData[]; // 行业
  organizationType?: RiskInfoData[]; // 组织机构类型
  email?: RiskInfoData[];
}
// 渠道信息
export interface ChannelInfoType {
  [key: string | number]: string | number;
  businessSourceCode: string; // 业务来源编码
  channelSourceCode: string; // 渠道来源编码
  channelSourceName: string; // 渠道名称
  channelSourceDetailName: string; // 渠道细分名称
  departmentCode: string; // 出单机构
  assisterIdList: string[]; // 农保id
  employeeInfo: string; // 业务员信息
  departmentName: string; // 机构名称
}
export interface ErrorChannelInfoType {
  channelSourceName: RiskInfoData[]; // 渠道名称
  channelSourceDetailName: RiskInfoData[]; // 渠道细分名称
  departmentCode: RiskInfoData[]; // 出单机构
  assisterId: RiskInfoData[]; // 农保id
  employeeInfo: RiskInfoData[]; // 业务员信息
  departmentName: RiskInfoData[]; // 机构名称
}
// 共保信息
export interface CoinsuranceType {
  innerCoinsuranceMark: string; // 共保属性:0-系统外共保,1-系统内共保
  innerCoinsuranceAgreement: string; // 共保协议码
  totalPremium: string; // 总保费
  totalInsuredAmount: string; // 总保额
  coinsuranceDetailList: CoinsuranceDetailType[];
}
export interface ErrorCoinsuranceType {
  innerCoinsuranceMark: RiskInfoData[]; // 共保属性:0-系统外共保,1-系统内共保
  innerCoinsuranceAgreement: RiskInfoData[]; // 共保协议码
  totalPremium: RiskInfoData[]; // 总保费
  totalInsuredAmount: RiskInfoData[]; // 总保额
  coinsuranceDetailList: CoinsuranceDetailType[];
}
// 共保列表
export interface CoinsuranceDetailType {
  reinsureCompanyCode: string; // 共保公司编码
  reinsureCompanyName: string; // 共保公司名称
  reinsureScaleBigDecimal: string; // 共保比例
  insuredAmountBigDecimal: string; // 保额
  premiumBigDecimal: string; // 保费
  onlyselfScaleBigDecimal: string; // 净自留比例
  callPremiumBigDecimal: string; // 复核保费
  acceptInsuranceFlag: string; // 是 // 主承保：0 // 否,1是
  employeeCode: string; // 业务员
  idchannelSourceCode: string; // 渠道
  channelSourceName: string; // 渠道名称
  coinsuranceInsureFeeRatioBigDecimal: string; // 共保出单费比例
  coinsureCompanyFinanceDeptCode: string; // 分出管理机构编码
  coinsureCompanyMgDeptCode: string; // 分出财务机构编码
  employeeName: string; // 业务员名称
  totalValueAddedTaxBigDecimal: string; // 增值税
  totalVATExcludedPremiumBigDecimal: string; // 不含增值税保费
}
// 费用信息
export interface FeeInfoType {
  [key: string]: string;
  calamitySecurityRate: string; // 防灾防损费
  isPolicyBeforePayfee: string; // 财务标识
  assisterCharge: string; // 协办费
  commissionBrokerChargeProportion: string; // 手续费/经纪费
  managementFees: string; // 工作经费
  coinsuranceInsureFeeRatio: string; // 共保出单
  performanceValue1Default: string; // 农险补贴
  totalSumFeeLimit: string; //  string; // 总费用之和上限值
  assisterChargeLimit: string; // 协办费上限值
  commissionBrokerChargeProportionLimit: string; // 手续费/经纪费上限
  managementFeesLimit: string; // 工作经费上限-  // 管理费(业务员的工作经费比例
  calamitySecurityRateLimit: string; // 防灾防损费上限值
  performanceValue1DefaultLimit: string; // 农险补贴上限值
  coinsuranceInsureFeeRatioLimit: string; // 共保出单费上限值
}
// 免赔信息
export interface NoclaimInfoType {
  noclaimItem: string; // 免赔项目
  noclaimDesc: string; // 免赔项目描述
  noclaimType: string; // 免陪类别
  lossNoclaimRate: string; // 损失免赔率
}
// 应收信息
export interface PayInfoType {
  termNo: string; // 期次
  paymentPersonName: string; // 付款人姓名
  payerType: string; // 付款人类别
  agreePremium: string; // 应交保费
  paymentBeginCircDate: string; // 缴费起始时间 (平台)
  paymentEndCircDate: string; // 缴费截止时间 (平台)
  paymentPath: string; // 收付途径/ 支付方式
}
// 产品信息-列表
export interface PlanInfoType {
  planCode?: string; // 险种代码
  planName?: string; // 险种名称
  eachCompensationMaxAmount?: string; // 每次赔偿限额
  unitInsuredAmount?: string; // 单位保额
  totalInsuredAmount?: string; // 保险金额
  expectPremiumRate?: string; // 费率
  unitPrimium?: string; // 单位保费
  totalStandardPremium?: string; // 基准保费
  totalAgreePremium?: string; // 减免后保费金额
  totalActualPremium?: string; // 复核保费
  dutyInfoList?: dutyInfoListType[]; // 责任列表
}
export interface dutyInfoListType {
  dutyCode: string; // 责任编码
  dutyName: string; // 责任名称
}
// 产品信息-详情
export interface ProductInfoType {
  [key: string | number]: string | number;
  insuranceBeginDate: string; // 保险起期
  insuranceEndDate: string; // 保险止期
  insurancePeriod: string; // 保险期限
  shortTimeCoefficient: string; // 年限系数
  timeRange: string; // 起止时间段
  substitute: string; // 农户保费是 // 否代缴
  reductionCoefficient: string; // 减免系数
  totalInsuredAmount: string; // 保单保险金额
  totalAgreePremium: string; // 减免后保单保费金额
  totalStandardPremium: string; // 减免前保单保费金额
  insuredNumber: string; // 保险数量
  farmersCount: string; // 参保户数
  totalActualPremium: string; // 复核保费
}
export interface ErrorProductInfoType {
  insuranceBeginDate: RiskInfoData[]; // 保险起期
  insuranceEndDate: RiskInfoData[]; // 保险止期
  insurancePeriod: RiskInfoData[]; // 保险期限
  shortTimeCoefficient: RiskInfoData[]; // 年限系数
  timeRange: RiskInfoData[]; // 起止时间段
  substitute: RiskInfoData[]; // 农户保费是 // 否代缴
  reductionCoefficient: RiskInfoData[]; // 减免系数
  totalInsuredAmount: RiskInfoData[]; // 保单保险金额
  totalAgreePremium: RiskInfoData[]; // 减免后保单保费金额
  totalStandardPremium: RiskInfoData[]; // 减免前保单保费金额
  insuredNumber: RiskInfoData[]; // 保险数量
  farmersCount: RiskInfoData[]; // 参保户数
  totalActualPremium: RiskInfoData[]; // 复核保费
}
// 项目信息
export interface ProjectInfoType {
  [key: string]: string;
  tenderBusiness: string; // 项目来源
  tenderBusinessName: string; // 项目名称
  lossRate: string; // 风险程度
  customerType: string; // 客户类型
  customerAbbrName: string; // 客户简称
}
export interface ErrorProjectInfoType {
  tenderBusiness: RiskInfoData[]; // 项目来源
  tenderBusinessName: RiskInfoData[]; // 项目名称
  lossRate: RiskInfoData[]; // 风险程度
  customerType: RiskInfoData[]; // 客户类型
  customerAbbrName: RiskInfoData[]; // 客户简称
}
// 保费来源
export interface RiskAgrInfoType {
  [key: string | number]: string | number;
  centralFinance: string; // 保费来源(中央财政)%
  provincialFinance: string; // 保费来源(省级财政)%
  cityFinance: string; // 保费来源(地市财政)%
  countyFinance: string; // 保费来源(县财政)%
  farmersFinance: string; // 保费来源(农户)%
  otherFinance: string; // 保费来源(其他来源)%
}
export interface ErrorRiskAgrInfoType {
  centralFinance: RiskInfoData[]; // 保费来源(中央财政)%
  provincialFinance: RiskInfoData[]; // 保费来源(省级财政)%
  cityFinance: RiskInfoData[]; // 保费来源(地市财政)%
  countyFinance: RiskInfoData[]; // 保费来源(县财政)%
  farmersFinance: RiskInfoData[]; // 保费来源(农户)%
  otherFinance: RiskInfoData[]; // 保费来源(其他来源)%
}
// 标的信息
export interface RiskInfoType {
  [key: string | number]: string | number;
  insuredNumber: string; // 承保数量
  farmersCount: string; // 承保农户数
  riskTypeDetailName: string; // 标的名称明细中文名称
  unitInsuredAmount: string; // 单位保险金额
  insuredRate: string; // 保险费率
  unitPremium: string; // 单位保费
  insuredAmount: string; // 保险金额
  premium: string; // 保费金额
  riskAddress: string; // 标的地址
  insuredUnit: string; // 保险单位
}
export interface ErrorRiskInfoType {
  insuredNumber: RiskInfoData[]; // 承保数量
  farmersCount: RiskInfoData[]; // 承保农户数
  riskTypeDetailName: RiskInfoData[]; // 标的名称明细中文名称
  unitInsuredAmount: RiskInfoData[]; // 单位保险金额
  insuredRate: RiskInfoData[]; // 保险费率
  unitPremium: RiskInfoData[]; // 单位保费
  insuredAmount: RiskInfoData[]; // 保险金额
  premium: RiskInfoData[]; // 保费金额
  riskAddress: RiskInfoData[]; // 标的地址
  insuredUnit: RiskInfoData[]; // 保险单位
}
// 特约信息
export interface SpecialPromiseType {
  promiseType: string; // 特约类型
  promiseDesc: string; // 特约描述
}
export interface ErrorSpecialPromiseType {
  promiseType: RiskInfoData[]; // 特约类型
  promiseDesc: RiskInfoData[]; // 特约描述
}
// 续保信息
export interface RenewalInsuranceInfoType {
  renewalInsuranceVoucherNo: string;
  isBelongRenewalInsurance: boolean;
}
export interface ErrorRenewalInsuranceInfoType {
  renewalInsuranceVoucherNo: RiskInfoData[];
  isBelongRenewalInsurance: RiskInfoData[];
}
// 意见
export interface UdwProcessType {
  taskId: string;
  approveStatus: string;
  approveResult: string | undefined;
  approveResultDesc: string;
  voucherNo: string;
};
// 核保轨迹
export interface QueryApprovalTaskList {
  approveStatus: string;
  approveResultDesc: string;
  approveUm: string;
  approveTime: string;
  createdBy: string;
  createdDate: string;
}
export interface ResType {
  code: string;
  msg: string;
  data: QueryApprovalTaskList[];
  httpAppUrl?: string;
  sysId?: string;
  timeStamp?: string;
  code?: string;
  virtualUsername?: string;
  userId?: string;
  deptCode?: string;
}
// 风险提示
export interface RiskInfoData {
  keyName: string;
  promptInfo: string; // 描述
  promptLevel: string; // green-绿色，red-红色
  resultType: string;
  showKey: string;
  insuranceBusinessNo: string;
  origin: string;
  eoaNo: string;
  extendDesc: string;
}
// 再保一般临分列表
interface ReinsuranceDetailListType {
  facultativeMode?: string; // 临分方式（1比例临分;2 超赔临分）
  franchise?: string; // 起赔点
  separationRatio?: string; // 分出比例（前端不用转换百分比，下同）
  compensateLimit?: string; // 赔偿限额
  separationPremium?: string; // 分出净保费
  otherExplain?: string; // 其他说明
  poundage?: string; // 手续费
  abatement?: string; // 免赔
}
// 再保
export interface ReinsuranceInfoType {
  idReinsurance: string;
  projectName: string;
  facultativeType: string; // 临分类型 2-一般性临分 7-非总对总fac
  facultativeNo: string; // 临分编码(再保批复编号)
  applyPolicyNo: string | LocationQueryValue[]; // 投保单号
  schemeNo: string | LocationQueryValue[]; // 再保方案号
  signNo: string; // eoa签报号
  relatedFacultativeNo: boolean; // 是否关联已有临分批复编号 - 仅关联操作传true，其余传false
  reinsuranceDetailList: ReinsuranceDetailListType[]; // 再保详细信息
  status: string; // 再保状态（0待确认;1待审批；2签报审批通过；3签报审批不通过；4再保询价成功（确认分出方案）；5再保询价失败；6:核保确认分出方案； 7：核保拒绝分出方案; 8:取消再保）
}
// 总集合
export interface contractModelType {
  taskId: string;
  coinsuranceMark: string; // 是否为共保，0-否，1-是
  arbitralDepartment: string;
  productName: string; // 产品名称
  applyPolicyNo: string; // 投保单号
  govSubsidyType: string; // 补贴类型
  acceptInsuranceFlag: string; // 是否主承
  govSubsidyName: string; // 补贴类型名称
  preRiskNo: string; // 投保单申请号
  applyPolicyType: string; // 投保方式
  disputedSettleModeName: string; // 争议解决方式名称
  farmersCount: string; // 清单农户数
  farmerListNo: string;
  applicant: ApplicantInfoType;
  beneficary: ApplicantInfoType;
  insurant: ApplicantInfoType;
  organizerInfo: ApplicantInfoType;
  channelInfo: ChannelInfoType;
  coinsurance: CoinsuranceType;
  feeInfo: FeeInfoType;
  noclaimInfo: NoclaimInfoType[];
  payInfo: PayInfoType[];
  plan: PlanInfoType[];
  productInfo: ProductInfoType;
  projectInfo: ProjectInfoType;
  riskAgrInfo: RiskAgrInfoType;
  riskInfo: RiskInfoType;
  specialPromise: SpecialPromiseType;
  renewalInsuranceInfo: RenewalInsuranceInfoType;
  reinsuranceInfo: ReinsuranceInfoType;
}
// 错误信息集合
export interface ErrorModelType {
  insureList: RiskInfoData[]; // 清单列表
  applicant: ErrorApplicantInfoType;
  beneficary: ErrorApplicantInfoType;
  insurant: ErrorApplicantInfoType;
  organizerInfo: ErrorApplicantInfoType;
  channelInfo: ErrorChannelInfoType;
  coinsurance: ErrorCoinsuranceType;
  productInfo: ErrorProductInfoType;
  projectInfo: ErrorProjectInfoType;
  riskAgrInfo: ErrorRiskAgrInfoType;
  riskInfo: ErrorRiskInfoType;
  specialPromise: ErrorSpecialPromiseType;
  renewalInsuranceInfo: ErrorRenewalInsuranceInfoType;
  riskAgrInfoExtend: RiskInfoData[]; // 保费来源大类提示
  insurePlan: RiskInfoData[]; // 保险方案大类提示
  payInfo: RiskInfoData[]; // 应收信息大类提示
}

export interface FlowOwnerType {
  parameterCode: string;
  parameterName: string;
}
export interface RecordType {
  departmentName: string;
  flowOwnerTypeName: string;
}
export interface ValueType {
  value: string;
  label: string;
}

export interface OptionsType {
  flow_owner_type: FlowOwnerType[];
  eoa_type: FlowOwnerType[];
}
// 签报内容
export interface EoaResType {
  idReinsurance: string; // 签报id
  eoaBody: string; // 签报内容
  eoaSubject: string; // 签报主题
}
// 审批链
export interface EoaData {
  approveChainList: EoaChainsType[]; // 审批链
}
interface OptionType {
  um: string;
  name: string;
  option?: OptionType;
}
// 审批链
interface EoaChainsType {
  flowOwnerNameList: string[];
  handType: string;
  flowOwnerTypeList: string[] | OptionType[];
  seq: number;
  flowOwnerOptions: OptionType[][];
  flowOwner: (OptionType | null)[];
}
export interface LookModalObj {
  eoaSubject: string;
  eoaBody: string;
  disDataSource: ApplyType[];
  dataSource: ApplyType[];
  idReinsurance: string;
}
