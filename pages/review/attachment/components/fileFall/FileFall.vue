<template>
  <OverlayScrollbarsComponent
    class="h-full box-border"
    @os-scroll="onScroll"
  >
    <!-- id file_dom 不可删除 -->
    <div id="file_dom" class="pl-[12px]">
      <div class="flex items-center justify-between sticky top-0 z-[300] bg-white border-b-[1px] border-[rgba(241,244,245,1)] border-solid border-x-0 border-t-0 pb-[5px]">
        <a-checkbox
          v-model:checked="checkAllState.checkAll"
          :indeterminate="checkAllState.indeterminate"
          @change="onCheckAllChange"
        >
          全选
        </a-checkbox>
        <!-- 后端接口尚未实现，后续放开 -->
        <a-button class="group" :loading="downLoading" style="display:none;" @click="download">
          <template #icon>
            <VueIcon :icon="IconDownloadFont" class="text-[16px] text-[rgba(0,0,0,0.55)] mr-4px group-hover:text-[#07C160]" />
          </template>
          <span>下载</span>
        </a-button>
      </div>
      <div class="pt-[8px]">
        <template v-for="(item, index) in newFileList" :key="index">
          <FileItem v-model:check-all="item.checkAll" v-model:checked-list="item.checkedList" :file-item="item" @preview="preview" />
        </template>
      </div>
    </div>
  </OverlayScrollbarsComponent>
  <ImageViewer ref="viewer" v-model:selectedkey="selectedKey" :list="fileList" :current="previewCurrent" />
</template>

<script lang="ts" setup>
import { OverlayScrollbarsComponent } from 'overlayscrollbars-vue';
import 'overlayscrollbars/styles/overlayscrollbars.css';
import { IconDownloadFont } from '@pafe/icons-icore-agr-an';
import { VueIcon } from '@pag/icon-vue3';
import { cloneDeep, throttle } from 'lodash-es';
import type { FileInfo } from '../attachment.d.ts';
import FileItem from './FileItem.vue';
import ImageViewer from '@/components/ui/ImageViewer.vue';
import { $postOnClient } from '~/composables/request.js';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { downloadBlob } from '@/utils/tools';

const { gateWay, service } = useRuntimeConfig().public || {};
interface ClientRect {
  left?: number;
  right?: number;
  top?: number;
  bottom?: number;
  width?: number;
}

const { fileList = [] } = defineProps<{
  fileList: FileInfo[];
}>();

const selectedKey = defineModel<string>('selectedKey'); // 文件目录的唯一key，用来渲染某一级目录被选中
const lockScroll = ref(false); // 停止scroll事件执行，防止scroll事件触发和selectedKey变化导致循环执行
const cacheKey = ref(''); // 缓存selectedKey
const newFileList = ref<FileInfo[]>([]); // props.fileList的备份数据，新增了checkAll，checkedList，记录FileItem的勾选选中情况

const viewer = ref(null); // 预览组件的ref

const containerDomClientRect = ref<ClientRect>(); // 右侧文件列表的可视区

const scrollDom = shallowRef<Element | null>(null); // 滚动组件的dom，OverlayScrollbarsComponent不支持设置id,OverlayScrollbarsComponent的getElement()并不能准确获取设置scroll属性的dom

const checkAllState = reactive<{ // 页面全选状态
  checkAll: boolean;
  indeterminate: boolean;
  checkedList: string[];
}>({
  checkAll: false,
  indeterminate: false,
  checkedList: [],
});

const onCheckAllChange = (e) => {
  for (const item of newFileList.value) {
    item.checkAll = e.target.checked;
  }
};

// 重置页面状态
const reset = () => {
  lockScroll.value = false;
  cacheKey.value = '';
  containerDomClientRect.value = undefined;
};

// 根据selectedKey查找其及父亲节点的index
const findNodeAndParentsIndex = (tree: FileInfo[], targetId: string, path: number[] = []): number[] | null => {
  for (let i = 0; i < tree.length; i++) {
    // 将当前节点添加到路径中
    path.push(i);
    if (tree[i].key === targetId) {
      // 找到了目标节点
      return path.slice();
    }

    if (tree[i].children && tree[i].children.length > 0) {
      // 当前节点有子节点，递归查找
      const result = findNodeAndParentsIndex(tree[i].children, targetId, path);
      if (result) {
        return result;
      }
    }

    // 回溯：从路径中移除当前节点，因为它不是目标节点也不是目标节点的父节点
    path.pop();
  }
  // 如果没有找到目标节点，则返回null
  return null;
};

watch(newFileList, async (val) => {
  // 监听newFileList，实时更新文件勾选情况
  if (val && val.length) {
    // 收集选中的文件id
    let list: FileInfo[] = [];
    for (const item of val) {
      list = [...list, ...item.checkedList];
    }
    // 判断新旧值，才进行赋值
    // checkbox只会新增或减少，长度必然发生变化
    if (checkAllState.checkedList.length !== list.length) {
      checkAllState.checkedList = list;
    }

    await nextTick();
    let isAllCheck = true;
    for (const item of val) {
      if (!item.checkAll) {
        isAllCheck = false;
        break;
      }
    }
    checkAllState.checkAll = isAllCheck;
  }
}, {
  deep: 2,
});

watch(() => checkAllState.checkedList, (val) => {
  checkAllState.indeterminate = !!val.length;
});

watch(() => checkAllState.checkAll, async (val) => {
  await nextTick();
  checkAllState.indeterminate = !val;
});

const previewCurrent = ref(''); // 当前要预览的文件id

const preview = (id: string) => {
  previewCurrent.value = id;
  if (viewer.value) {
    viewer.value?.openPreview();
  }
};
// 修改文件类目名称，将文件数量加到文件类目名称上
const findNodesToResetTitle = (tree: FileInfo, path: number[]) => {
  if (tree.fileList) {
    tree.fileLength = tree.fileList.length;
    path[0] = path[0] + tree.fileLength;
  }

  if (tree.children) {
    for (const item of tree.children) {
      findNodesToResetTitle(item, path);
    }
  }
};
const copyFileList = (val: FileInfo) => {
  const list = cloneDeep(val);
  for (const item of list) {
    // 新增2个字段
    item.checkAll = false;
    item.checkedList = [];
    const path = [0];
    findNodesToResetTitle(item, path);
    item.fileLength = path[0];
  }
  return list;
};
// 监听props.fileList更新右侧文件列表，支持fileList重置、push、pop等数组操作
watch(() => fileList, (val) => {
  newFileList.value = copyFileList(val);
  reset();
}, {
  immediate: true,
  deep: 1, // 深度监听层次为1，vue 3.5+支持，支持fileList数组修改时能监听得到，fileList数组里面的数据则监听不到
});

const updatecontainerDomClientRect = () => {
  if (!containerDomClientRect.value) {
    const dom = document.querySelector('#file_dom');
    if (dom) {
      containerDomClientRect.value = dom.getBoundingClientRect();
    }
  }
};

const getScrollDom = () => {
  if (scrollDom.value) return;
  const dom = document.querySelector('#file_dom');
  if (dom) {
    scrollDom.value = dom.parentNode as Element;
  }
};

const resetScroll = (indexList: number[]) => {
  const domList = document.querySelectorAll('#file_dom .file-item-dom');
  if (domList && domList.length) {
    // 文件列表已渲染完成
    updatecontainerDomClientRect();
    const containerTop = containerDomClientRect.value?.top || 0;
    const parentDom: Element = domList[indexList[0]];
    let targetDom: Element | null = null;
    if (parentDom) {
      if (indexList.length > 1) {
        // 目录定位是二级目录
        const childDoms = parentDom.querySelectorAll('.file-item-children-dom');
        if (childDoms) {
          targetDom = childDoms[indexList[1]];
        }
      } else {
        // 目录定位是一级目录
        targetDom = parentDom;
      }

      if (targetDom) {
        const rect = targetDom.getBoundingClientRect();
        if (rect.top - containerTop > 100 || rect.bottom - containerTop < 100) {
          getScrollDom();
          // 不在可视区内
          if (rect.top - containerTop > 100) {
            // 滚动条需要往下移
            scrollDom.value?.scrollTo({
              top: scrollDom.value.scrollTop + (rect.top - containerTop - 100),
              // behavior: 'smooth', 不知smooth耗时多少，距离越长耗时越长，手动滚动不能触发scroll事件，这里不能使用smooth
            });
          } else {
            // 滚动条往上移
            scrollDom.value?.scrollTo({
              top: scrollDom.value.scrollTop - (containerTop - rect.top) - 100,
              // behavior: 'smooth',
            });
          }
        }
      }
    }
  }
};
// 监听selectedKey发生变化，更新滚动条位置
watch(selectedKey, async (val) => {
  if (val && val !== cacheKey.value) {
    lockScroll.value = true; // selectedKey引发滚动条移动，停止滚动条触发selectedKey发生变化
    const keys = findNodeAndParentsIndex(fileList, val);
    cacheKey.value = val;
    resetScroll(keys || []);
    setTimeout(() => {
      lockScroll.value = false;
    }, 100);
  }
});
const onScroll = throttle(() => {
  // 滚动条滚动，左侧文件目录需要实时定位到具体的某一目录
  if (lockScroll.value) return;
  const domList = document.querySelectorAll('#file_dom .file-item-dom');
  if (domList && domList.length) {
    const length = domList.length;
    updatecontainerDomClientRect();
    const containerTop = containerDomClientRect.value?.top || 0;
    const containerBottom = containerDomClientRect.value?.bottom || 0;
    const list = [];
    for (let i = 0; i < length; i++) {
      const rect = domList[i].getBoundingClientRect();
      if ((rect.top > containerTop && rect.top < containerBottom) || (rect.bottom < containerBottom && rect.bottom > containerTop)) {
        // 识别哪一类文件在可视区里面
        list.push({
          index: i,
          rect: rect,
          dom: domList[i],
        });
      }
    }

    // 从上往下寻找，第一个符合条件即可，实现更精准的定位
    let key = '';
    for (const item of list) {
      const rect = item.rect;
      if (fileList[item.index].children) {
        const children = item.dom.querySelectorAll('.file-item-children-dom');
        let findedKey = false;
        if (children && children.length) {
          const length = children.length;

          for (let i = 0; i < length; i++) {
            const childRect = children[i].getBoundingClientRect();
            if (childRect.top - containerTop < 100 && childRect.bottom - containerTop > 100) {
              key = fileList[item.index].children[i].key || '';
              findedKey = true;
              break;
            }
          }
        }
        if (findedKey) break;
      } else {
        // 没有子目录
        if (rect.top - containerTop < 100 && rect.bottom - containerTop > 100) {
          key = fileList[item.index].key;
          break;
        }
      }
    }

    if (key && selectedKey.value !== key) {
      cacheKey.value = key;
      selectedKey.value = key;
    }
  }
}, 200);
const route = useRoute();
const downLoading = ref<boolean>(false);
// 下载
const download = async () => {
  console.log(checkAllState.checkedList, newFileList.value, selectedKey.value);
  if (checkAllState.checkedList?.length === 0) {
    message.warning('暂无附件可下载');
  } else {
    try {
      downLoading.value = true;
      await $postOnClient(`${gateWay}${service.ums}/underwrite/downZip`,
        { uploadPathList: checkAllState.checkedList, applyPolicyNo: route.query.applyPolicyNo, docGroupType: 'docTypeApply' }, {
          onResponse({ response }) {
            if (response._data instanceof Blob) {
              const contentDisposition = response.headers.get('Content-Disposition');
              const fileData = response._data;
              const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
              if (contentDisposition) {
                const match = contentDisposition.match(/fileName=(.+)/);
                const fileName = match ? decodeURI(match[1]) : '';
                downloadBlob(fileData, fileName, fileType);
              }
              message.success('导出成功');
              downLoading.value = false;
            } else {
              const { code, data, msg = '' } = response._data;
              if (code === SUCCESS_CODE) {
                message.success(data);
              } else if (msg) {
                message.error(msg);
              }
            }
          },
        });
      downLoading.value = false;
    } catch (error) {
      console.log(error);
      downLoading.value = false;
    }
  }
};
</script>

<style lang="less" scoped>
:deep(.block) {
  display: block;
}
</style>
