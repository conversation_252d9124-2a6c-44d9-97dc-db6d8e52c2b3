<template>
  <div class="h-[49px] flex attachment-board pl-[18px] items-center">
    <p class="text-[#1C2438] text-[14px] p-0 mr-[12px]">
      安农小看板
    </p>
    <div class="flex h-[38px] items items-center rounded-[4px] px-[10px]">
      <div class="flex items-baseline mr-[23px] text-[12px] text-[#495060]">
        <span>需上传资料</span><span class="font-number-medium text-[18px] mx-[2px] leading-[24px]">{{ needUploadFilesLength }}</span><span>类</span>
      </div>
      <div class="flex items-baseline item mr-[7px] h-[26px] text-[12px] text-[#495060] px-[12px]">
        <span>已缴资料</span><span class="font-number-medium text-[18px] mx-[2px] leading-[24px]">{{ uploadedFilesLength }}</span><span>类</span>
      </div>
      <div class="flex items-baseline item h-[26px] text-[13px] text-[#495060] px-[12px]">
        <span>待缴资料</span><span class="font-number-medium text-[18px] mx-[2px] leading-[24px]">{{ notUploadedFilesLength }}</span><span>类</span>
        <a-tooltip v-if="notUploadedFilesLength > 0">
          <template #title>待缴材料包含：{{ (fileTotal?.notUploadedFiles || []).join('，') }}</template>
          <VueIcon :icon="IconTankuangTishiColor" class="text-[12px] ml-[3px] cursor-pointer" />
        </a-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IconTankuangTishiColor } from '@pafe/icons-icore-agr-an';

const { fileTotal } = defineProps<{
  fileTotal: {
    needUploadFiles: string[];
    notUploadedFiles: string[];
    uploadedFiles: string[];
  };
}>();

const needUploadFilesLength = computed(() => {
  return (fileTotal?.needUploadFiles || []).length;
});

const notUploadedFilesLength = computed(() => {
  return (fileTotal?.notUploadedFiles || []).length;
});

const uploadedFilesLength = computed(() => {
  return (fileTotal?.uploadedFiles || []).length;
});
</script>

<style lang="less" scoped>
.attachment-board {
  background-image: url('@/assets/images/review/attachment-board.png');
  .items {
    background-color: rgba(255, 255, 255, 0.7);
    .item {
      background: #F0FAF6;
      border: 1px solid rgba(129,222,172,0.18);
      border-radius: 4px;
    }
  }
}
</style>
