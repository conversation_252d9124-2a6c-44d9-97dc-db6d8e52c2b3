import type { TableColumnType } from 'ant-design-vue';
import type { ApplyPayInfo } from './paymentManage';

export const STATUS_TYPE = {
  NOBILLS: '00', // 未开单
  BILLED: '51', // 已开单
  RECEIVED: '71', // 已到账
  APPROVAL: '54', // 开单审批中
  DRAWN: '62', // 已抽档
  PAID: '73', // 已支付
  PAYMENTFAILED: '74', // 支付失败
  RTICKET: '82', // 退票
};

// 单号类型
export const documentTypeOptions = [
  { label: '投保单号', value: '1' },
  { label: '通知单号', value: '2' },
  { label: '保单号', value: '3' },
  { label: '批单申请号', value: '4' },
  { label: '批单号', value: '5' },
];

// 保费来源
export const SOURCEPREMIUM_OPTIONS = [
  { label: '中央财政', value: '1' },
  { label: '省财政', value: '2' },
  { label: '市财政', value: '3' },
  { label: '区县财政', value: '4' },
  { label: '农户', value: '5' },
  { label: '其他', value: '6' },
];

// 通知单状态
export const NOTICESTATUS_OPTIONS = [
  { label: '待开单', value: '00' },
  { label: '开单审批中', value: '54' },
  { label: '已开单', value: '51' },
  { label: '已抽档', value: '62' },
  { label: '已到账', value: '71' },
  { label: '已实收', value: '72' },
  { label: '已支付', value: '73' },
  { label: '支付失败', value: '74' },
  { label: '退票', value: '82' },
  // { label: '部分开单', value: '52' },
  // { label: '部分支付', value: '72' },
  // { label: '部分到账', value: '75' },
];

export const columns: TableColumnType<ApplyPayInfo>[] = [
  { title: '期次', dataIndex: 'termNo', fixed: 'left' },
  { title: '保费来源', dataIndex: 'premiumSourceName' },
  { title: '客户名称', dataIndex: 'customName' },
  { title: '出单机构', dataIndex: 'insureDepartmentName' },
  { title: '单号', dataIndex: 'voucherNo' },
  { title: '通知单号', dataIndex: 'noticeNo' },
  { title: '被保险人', dataIndex: 'insurantName' },
  { title: '金额', dataIndex: 'termAmount' },
  { title: '见费/非见费', dataIndex: 'isPolicyBeforePayfee' },
  { title: '收付途径', dataIndex: 'paymentPathName' },
  { title: '通知单状态', dataIndex: 'noticeStatusName' },
  { title: '缴费止期', dataIndex: 'paymentEndDate' },
  { title: '反洗钱信息', dataIndex: 'antiMoneyInfo' },
  { title: '操作', dataIndex: 'operate', fixed: 'right' },
];
