// 表单筛选项
export interface FormState {
  documentType?: string; // 开单类型
  departmentCode?: string; // 机构编码
  containChildDepart?: boolean; // 是否包含下级机构
  dateValue?: [string, string]; // 保险起期
  documentNo?: string; // 单号
  documentNoType?: string; // 单号 select
  personName?: string; // 被保险人
  project?: string; // 项目
  lastRiskType?: string; // 标的类型
  riskCode?: string; // 标的
  payerType?: Array<string | number>;// 保费来源
  noticeStatus?: Array<string | number>; // 通知单状态
}

// 表格数据
export interface ApplyPayInfo {
  key?: string;
  termNo: string; // 期次
  premiumSourceCode?: string; // 保费来源
  premiumSourceName: string; // 保费来源
  customName: string; // 客户名称
  insureDepartmentNo?: string; // 出单机构
  insureDepartmentName: string; // 出单机构
  applyPolicyNo: string; // 单号
  noticeNo: string; // 通知单号
  insurantName: string; // 被保险人
  termAmount: string; // 金额
  isPolicyBeforePayfee: string; // 见费/非见费
  paymentPath: string; // 收付途径
  paymentPathName: string; // 收付途径
  noticeStatusName: string; // 通知单状态
  paymentEndDate: string; // 缴费止期
  operate?: string; // 操作
  productCode?: string; // 产品编码
  productName?: string; // 产品名称
  marketTechnicProductCode?: string;
  noticeStatus: string;
  payerType?: string;
  documentNo?: string;
  documentType?: string;
  _checked?: boolean;
  noticeId?: string; // 通知单
  ourtermAmount?: string; // 通知单
  // 调整金额
  adjustedFlag?: string; // 未调整  1 已调整
  adjustedAmount?: number | string; // 调整后金额
  isCoinsuranceFullPay?: string; // 0 非保非我司代收 1 共保我司代收 2 非共保
  policyNo?: string;// 保单号
  voucherNo?: string; // 单号
  idApplyPayInfo?: string; // 唯一标识
  excludeAddedTaxPremium: string; // 税后金额
  endorseNo?: string;
  actualPremium: string;
  paymentPersonRelationType?: string; // 领款人关系类型
  certificateIssueDate?: string; // 证件有效期起期
  certificateValidDate?: string; // 证件有效期止期
  mobileTelephone?: string; // 联系方式
}

// 缴费平台
export interface QueryApplyPayInfoListRes {
  total: number;
  current: number;
  size: number;
  pages: number;
  records: ApplyPayInfo[];
}
// 跳转财务支付form表单对象
export interface NoticeFormDataType {
  onlinePayUrl?: string;
  amount?: string;
  businessNo?: string;
  businessTranChnl?: string;
  businessTranCode?: string;
  businessType?: string;
  certPubKey?: string;
  currencyNo?: string;
  customerName?: string;
  dataSource?: string;
  documentNo?: string;
  regionCode?: string;
  signData?: string;
  tellerNo?: string;
}

export interface FormDataType {
  customerName: string; // 客户名称
  totalAmount: number | string; // 合计
};

export interface ApplyPayInfoDetail {
  noticeNo: string; // 通知单号
  clientName: string; // 客户名称
  policyNo: string; // 单号
  collectPayType: string; // 收付标志
}

export interface NoticeInfo {
  noticeNo: string; // 通知单号
  documentInfoList: Array<{
    clientName: string; // 客户名称
    documentNo: string; // 业务号
  }>;
}
// 调整金额modal表格
export interface AdjustTableDataType {
  key?: string; //
  payerTypeName: string; // 保费来源
  paymentPersonName: string; // 客户名称
  policyNo: string; // 单号
  insuredPerson: string; // 被保险人
  actualPremium: number; // 原金额
  reinsureScale: number; // 共保比例
  ourActualPremium: number; // 调整后的开单金额
  noticeStatusName?: string; // 通知单状态
  noticeId?: string; // 通知单id
  currencyCode?: string; // 金额
  currencyName?: string; // 金额 code
  productName?: string; // 产品名称
}
export interface OptionType {
  value?: string;
  label?: string;
  province?: string;
  provinceCode?: string | undefined;
  cityCode?: string | undefined;
  city?: string;
  bankTypeName?: string;
  bankTypeCode?: string | undefined;
  bankCode?: string | undefined;
  bankName?: string;
  keys?: string;
}
export interface RefundDocumentInfoListType {
  documentNo: string | undefined; // 业务号
  payerType: string | undefined;
  termNo: string;
  endorseNo?: string;
  policyNo?: string;
  actualPremium: string;
  applyPolicyNo: string;
}
export interface PremiumObjType {
  actualPremium?: string;
  excludeAddedTaxPremium?: string;
  paymentPersonName?: string;
  refundDocumentInfoList: RefundDocumentInfoListType[] & ApplyPayInfo [];
  name?: string[];
  refundCheckFlag?: string; // 校验标识 Y-仅校验；N-正常开退费单
  clientName?: string;
  bankAccountAttribute?: string; // 领款人帐号类型(0 团体，1个人)
  bankAccountNo?: string; // 收款人账号
  bankCode?: string; // 收款人开户行编码(支行)
  bankName?: string; // 银行分支机构名称
  bankHeadquartersCode?: string; // 收款人开户银行编码(总行)
  bankHeadquartersName?: string; // 总行名称
  clientName?: string; // 付款人名称
  existRedWord?: string; // 是否存在红字信息 1有 0无
  paymentPath?: string; // 收款途径 01 公司柜面-->出纳柜面 02 实时支付-->实时收付 03 银行转帐-->批量收付 04 原路退回
  paymentPersonName?: string; // 收款人姓名
  redWordAmount?: string; // 红字信息金额
  redWordNum?: string; // 红字信息号
  bankProvinceCode?: string | undefined; // 省份
  bankCityCode?: string | undefined; // 市
  certificateNo?: string; // 身份证
  paymentPathName?: string;
  bankAccountAttributeName?: string;
}
// 签报eoadata
export interface EoaData {
  documentGroupId: string;
  eoaTitle?: string; // 签报主题
  eoaContent?: string; // 签报内容
  fileInfos?: { fileKey: string; fileName: string; fileType: string }[]; // 签报附件
  approvalChain?: string; // 跳转eoa审批链
  templateChainKey?: string; // 跳转eoa审批链key
  approveChainList: EoaChainsType[]; // 审批链
}
