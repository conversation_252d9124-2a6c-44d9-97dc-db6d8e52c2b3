<template>
  <a-modal
    v-model:open="visible"
    title="开单成功！"
    :width="pxToRem(500)"
    centered
    :confirm-loading="confirmLoading"
    @ok="handleOk"
  >
    <div class="edit-name-body">
      <VueIcon :icon="IconCheckCircleFilledFont" />
      是否确定打印通知单？
    </div>

    <template #footer>
      <a-button @click="handleCancel"> 取消 </a-button>
      <a-button type="primary" @click="handleOk"> 确认 </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { IconCheckCircleFilledFont } from '@pafe/icons-icore-agr-an';
import { pxToRem } from '@/utils/tools';

const visible = defineModel<boolean>('visible', {
  required: true,
  default: false,
});
const emit = defineEmits(['handleOk']);

const confirmLoading = ref(false);

const handleOk = () => {
  setVisible(false);
  emit('handleOk');
};
const handleCancel = () => {
  setVisible(false);
};
const setVisible = (flag: boolean) => {
  visible.value = flag;
};
</script>
