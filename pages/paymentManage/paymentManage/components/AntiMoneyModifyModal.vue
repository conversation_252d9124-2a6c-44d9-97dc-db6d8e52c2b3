<template>
  <a-modal v-model:open="visible" title="反洗钱信息修改" centered :confirm-loading="confirmLoading" :after-close="afterClose" @ok="confirm">
    <a-form ref="formRef" :model="formState" :colon="false" :label-col="{ style: { width: pxToRem(120) } }">
      <a-form-item label="领款人关系类型" name="paymentPersonRelationType" :rules="[{ required: true, message: '请选择领款人关系类型' }]">
        <a-select v-model:value="formState.paymentPersonRelationType" :options="options" />
      </a-form-item>
      <a-form-item label="证件有效期起期" name="certificateIssueDate" :rules="[{ required: true, message: '请选择证件有效期起期' }]">
        <a-date-picker v-model:value="formState.certificateIssueDate" :disabled-date="disabledIssueDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
      </a-form-item>
      <a-form-item label="证件有效期止期" name="certificateValidDate" :rules="[{ required: true, message: '请选择证件有效期止期' }]">
        <div class="flex items-center">
          <a-date-picker v-model:value="formState.certificateValidDate" :disabled-date="disabledValidDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled="disabled" />
          &nbsp;
          <a-form-item-rest>
            <a-checkbox v-model:checked="checked" class="w-[80px]" @change="handleChange">长期</a-checkbox>
          </a-form-item-rest>
        </div>
      </a-form-item>
      <a-form-item label="联系方式" name="mobileTelephone" :rules="[{ required: true, message: '请输入联系方式' }, { validator: phoneRule }]">
        <a-input v-model:value="formState.mobileTelephone" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import type { CheckboxChangeEvent } from 'ant-design-vue/es/checkbox/interface';
import dayjs, { type Dayjs } from 'dayjs';
import type { Rule } from 'ant-design-vue/es/form';
import type { ApplyPayInfo } from '@/pages/paymentManage/paymentManage/paymentManage.d';
import { pxToRem } from '@/utils/tools';
import { SUCCESS_CODE } from '@/utils/constants';
import { $postOnClient } from '@/composables/request';

const { selectData } = defineProps<{
  selectData: ApplyPayInfo;
}>();
const formRef = ref();
const formState = ref({
  paymentPersonRelationType: '', // 领款人关系类型
  certificateIssueDate: '', // 证件有效期起期
  certificateValidDate: '', // 证件有效期止期
  mobileTelephone: '', // 联系方式
});
const visible = defineModel<boolean>('visible', {
  required: true,
  default: false,
});
const options = ref<Record<string, string>[]>([]);
const confirmLoading = ref(false);
const { gateWay, service } = useRuntimeConfig().public || {};
const emit = defineEmits(['handleQuery']);
// 确定修改
const confirm = async () => {
  await formRef.value.validate();
  const params = {
    ...formState.value,
    documentInfoList: [
      {
        documentNo: selectData.voucherNo,
        termNoList: [selectData.termNo],
      },
    ],
  };
  const url = gateWay + service.accept + '/notice/updatePayForAML';
  try {
    const res = await $postOnClient(url, params);
    const { code, msg } = res || {};
    if (code === SUCCESS_CODE) {
      message.success(msg);
      visible.value = false;
      emit('handleQuery');
    } else {
      message.error(msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    confirmLoading.value = false;
  }
};
// 是否禁用证件有效止期
const disabled = ref<boolean>(false);
// 是否勾选长期
const checked = ref<boolean>(false);
// 长期切换
const handleChange = (e: CheckboxChangeEvent) => {
  if (e?.target?.checked) {
    disabled.value = true;
    formState.value.certificateValidDate = '9999-12-31';
    formRef.value.validateFields(['certificateValidDate']);
  } else {
    disabled.value = false;
  }
};
// 证件有效起期禁止选择当天之前的日期
const disabledIssueDate = (current: Dayjs) => current && current > dayjs();
// 证件有效止期禁止选择当天之后的日期
const disabledValidDate = (current: Dayjs) => current && current < dayjs().subtract(1, 'day');
// 关闭弹窗清空数据
const afterClose = () => {
  formState.value = {
    paymentPersonRelationType: '', // 领款人关系类型
    certificateIssueDate: '', // 证件有效期起期
    certificateValidDate: '', // 证件有效期止期
    mobileTelephone: '', // 联系方式
  };
};
// 联系方式校验规则
const phoneRule = async (rule: Rule, value: string) => {
  const phoneRegex = /^(1[3-9]\d{9}|(\d{3,4}-\d{7,8}))$/; // 正则表达式，匹配手机号或带区号的座机号
  if (!value || phoneRegex.test(value)) {
    return Promise.resolve();
  }
  return Promise.reject(new Error('请输入有效的手机号或座机号'));
};
interface OptionType {
  children: Record<string, string>[];
}
// 获取领款人下拉选项
const getOptions = async () => {
  const url = `${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`;
  try {
    const res = await $postOnClient<OptionType[]>(url, ['AGRLKRLX']);
    if (res && res.code === SUCCESS_CODE) {
      options.value = res.data?.[0]?.children || [];
    }
  } catch (error) {
    console.log(error);
  }
};
watch(visible, (val) => {
  if (val) {
    formState.value = {
      paymentPersonRelationType: selectData.paymentPersonRelationType || '', // 领款人关系类型
      certificateIssueDate: selectData.certificateIssueDate || '', // 证件有效期起期
      certificateValidDate: selectData.certificateValidDate || '', // 证件有效期止期
      mobileTelephone: selectData.mobileTelephone || '', // 联系方式
    };
    checked.value = false;
    disabled.value = false;
    formRef.value?.clearValidate();
  }
});
onMounted(() => {
  getOptions();
});
</script>
