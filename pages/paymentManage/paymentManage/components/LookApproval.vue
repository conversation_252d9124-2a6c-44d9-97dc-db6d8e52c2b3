<template>
  <div class="bg-gray-100 rounded box-border">
    <div class="flex mb-[10px]">
      <p class="m-0 w-1/1 shrink-0 box-border">
        <span>签报主题 </span>
        <span class="text-[#262626]">{{ eoaTitle }}</span>
      </p>
    </div>
    <div class="flex flex-wrap">
      <p class="m-0 w-1/1 shrink-0 box-border">
        <span>签报内容 </span>
        <span class="text-[rgba(0,0,0,.6)]">涉及的保单/批单：【{{ premiumObj.refundDocumentInfoList[0]?.documentNo }}】</span>
      </p>
    </div>
    <!-- 旧银行信息 -->
    <div class="ml-[60px]">
      <p class="m-0 w-1/1 shrink-0 box-border text-[#262626]">
        <span>原退款信息：</span>
      </p>
      <div class="flex flex-wrap">
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>收款人名称：</span>
          <span>{{ premiumObj.clientName }}</span>
        </p>
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>支付方式：</span>
          <span>{{ premiumObj.refundDocumentInfoList[0]?.paymentPathName }}</span>
        </p>
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>账号类型：</span>
          <span>-</span>
        </p>
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>身份证：</span>
          <span>-</span>
        </p>
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>开户银行：</span>
          <span>-</span>
        </p>
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>保费变化：</span>
          <span>{{ premiumObj.excludeAddedTaxPremium }}</span>
        </p>
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)]">
          <span>开户银行名称：</span>
          <span>-</span>
        </p>
        <p class="m-0 w-1/2 shrink-0 box-border text-[rgba(0,0,0,.6)]">
          <span>银行账号：</span>
          <span>-</span>
        </p>
      </div>
    </div>
    <!-- 新银行信息 -->
    <div class="ml-[60px]">
      <p class="m-0 w-1/1 shrink-0 box-border text-[#262626]">
        <span>新退款信息：</span>
      </p>
      <div class="flex flex-wrap">
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>收款人名称：</span>
          <span>{{ premiumObj?.paymentPersonName || '-' }}</span>
        </p>
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>支付方式：</span>
          <span>{{ premiumObj?.paymentPathName || '-' }}</span>
        </p>
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>账号类型：</span>
          <span>{{ premiumObj?.bankAccountAttributeName || '-' }}</span>
        </p>
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>身份证：</span>
          <span>{{ premiumObj?.certificateNo || '-' }}</span>
        </p>
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>开户银行：</span>
          <span>{{ premiumObj?.bankHeadquartersName || '-' }}</span>
        </p>
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>保费变化：</span>
          <span>{{ premiumObj?.actualPremium || '-' }}</span>
        </p>
        <p class="m-0 w-1/3 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>银行账号：</span>
          <span>{{ premiumObj?.bankAccountNo || '-' }}</span>
        </p>
        <p class="m-0 w-1/2 shrink-0 box-border text-[rgba(0,0,0,.6)] pr-[3px]">
          <span>开户银行名称：</span>
          <span>{{ premiumObj?.bankName || '-' }}</span>
        </p>
      </div>
    </div>
  </div>
  <div class="text-[rgba(0,0,0,.6)] my-[10px] pl-[60px]">注：签报审批完成后，系统会自动开通知单</div>
</template>

<script lang="ts" setup>
import type { PremiumObjType } from '../paymentManage';

const { premiumObj, eoaTitle } = defineProps<{
  premiumObj: PremiumObjType;
  eoaTitle: string;
}>();
</script>
