<template>
  <div class="flex">
    <div>
      <a-select v-model:value="documentType" placeholder="请选择" style="width: 113px" allow-clear>
        <a-select-option v-for="item in documentTypeOptions" :key="item.value" :value="item.value">
          {{ item.label }}
        </a-select-option>
      </a-select>
    </div>
    <div class="ml-8px flex-1">
      <a-input v-model:value="documentNo" class="no-input" placeholder="可输入多条数据，用逗号隔开，不支持模糊" :maxlength="1000" :title="documentNo" @change="blurPolicyNo" />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ChangeEvent } from 'ant-design-vue/es/_util/EventInterface';
import { useGet } from '@/composables/request';
import { documentTypeOptions } from '@/pages/paymentManage/paymentManage/constants';

const emit = defineEmits(['blur', 'depCodeChange']);

const documentType = defineModel<string>('documentType');
const documentNo = defineModel<string | undefined>('documentNo', { default: undefined });

const { gateWay, service } = useRuntimeConfig().public || {};

const gatewayAcceptURL = gateWay + service.accept;

const formData = reactive({
  documentType: '1',
  documentNo: '',
});

watchEffect(() => {
  if (documentType.value) {
    formData.documentType = documentType.value;
  }
  if (documentNo.value) {
    formData.documentNo = documentNo.value;
  }
});

const blurPolicyNo = (e: ChangeEvent) => {
  const docNo = e.target.value || '';
  let noType = documentType.value;

  if (docNo.length === 20) {
    noType = determineNoType(docNo);

    nextTick(() => {
      documentType.value = noType;
      // 查机构
      getDepartmentByNumCode(docNo);
    });
  }

  if (docNo) {
    // 资料补全跟踪/审核的审核状态
    nextTick(() => {
      verifyDocNo(docNo);
    });
  }

  emit('blur', formData);
};

// 确定单号类型的函数
const determineNoType = (docNo: string): string => {
  const typeMapping: { [key: string]: string } = {
    5: '1',
    3: '5',
    7: '4',
    1: '3',
  };
  return typeMapping[docNo[0]] || '2';
};

// 投保查询页面保单号输入校验逻辑
const verifyDocNo = (val: string) => {
  if (!val) return;

  const arr = val.split(',');
  const firstNo = arr[0][0];
  documentType.value = determineNoType(val);

  // 只要78位是58 就是通知单号
  if (val.substring(6, 8) === '58') {
    documentType.value = '2';
  }

  // 校验是否为相同类型的单号
  if (arr.some(no => no[0] !== firstNo)) {
    message.error('只能对相同类型的单号进行查询，请更正');
  }
};

/**
 * 根据单号查机构
 */
const getDepartmentByNumCode = async (docNo: string) => {
  const res = await getDepartmentByNumAPI({ num: docNo });
  if (res?.data) {
    emit('depCodeChange', res.data.departmentCode);
  }
};

// 根据单号查机构
const { fetchData: getDepartmentByNumAPI } = await useGet<{ departmentCode: string }>(`${gatewayAcceptURL}/applyPayInfo/getDepartmentByNum`);
</script>
