<template>
  <a-modal
    v-model:open="visible"
    title="批量开单"
    :width="pxToRem(800)"
    centered
    :confirm-loading="confirmLoading"
    @ok="handleOk"
  >
    <div class="mb-[18px]">
      <div class="flex items-center mb-[9px]">
        <VueIcon :icon="IconJichuxinxiFont" />
        <span class="ml-[3px]">模板下载</span>
      </div>
      <div class="bg-gray-100 rounded p-14px text-sky-500">
        <VueIcon :icon="IconDownloadFont" />

        <a-button
          type="link"
          @click="downloadImportModel"
        >
          下载导入模板
        </a-button>
      </div>
    </div>

    <div class="mb-[15px]">
      <div class="flex items-center justify-between mb-[9px]">
        <div class="flex items-center">
          <VueIcon :icon="IconPingtaijiekouFont" />
          <span class="ml-[3px]">清单导入</span>
        </div>
        <div class="text-[#576B95] flex items-center justify-end space-x-14px">
          <div
            v-for="temp in templateList"
            :key="temp.templateFileKey"
            class="cursor-pointer"
            @click="downloadFileByKey(temp.templateFileKey)"
          >
            <VueIcon :icon="IconTongyongXiazaiFont" />
            {{ temp.templateName }}
          </div>
        </div>
      </div>
      <div class="bg-gray-100 rounded p-14px">
        上传文件
        <a-upload
          ref="uploadRef"
          :multiple="false"
          :show-upload-list="true"
          :action="action"
          :before-upload="handleBeforeChange"
          :file-list="fileInfo.fileList"
          :max-count="1"
          @change="handleFileChange"
        >
          <a-button type="primary" ghost :disabled="disabledUploadButton" :loading="uploading">
            <template #icon>
              <VueIcon :icon="IconTongyongShangchuanFont" />
            </template>
            <span class="ml-[2px]">附件上传</span>
          </a-button>
        </a-upload>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { IconJichuxinxiFont, IconPingtaijiekouFont, IconTongyongXiazaiFont, IconTongyongShangchuanFont, IconDownloadFont } from '@pafe/icons-icore-agr-an';
import type { UploadChangeParam } from 'ant-design-vue';
import { pxToRem, downloadFile, downloadBlob } from '@/utils/tools';
import { $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

// TODO 此页面还未重构

const visible = defineModel<boolean>('visible', {
  required: true,
  default: false,
});

const emit = defineEmits(['okBtnCb']);

const { gateWay, service } = useRuntimeConfig().public || {};

const gatewayAcceptURL = gateWay + service.accept;
// 模版清单
const templateList = ref<Array<{ templateFileKey: string; templateName: string }>>([]);

// 禁用上传附件
const disabledUploadButton = ref(false);

const downloadFileByKey = async (fileKey: string) => {
  const res = await $getOnClient<{ url: string }>(
    `${gatewayAcceptURL}/notice/import/file/createNotice`,
    { fileKey },
  );
  if (res?.data?.url) {
    downloadFile(res.data.url);
  }
};
// /notice/import/file/createNotice
const action = ref(``);
const confirmLoading = ref(false);
// 防止接口调多次
const handleBeforeChange = () => false;
const uploadRef = ref();
const uploading = ref(false);

const fileInfo = ref<UploadChangeParam>(
  { file: {
    uid: '',
    name: '',
  },
  fileList: [] },
);
watchEffect(() => {
  if (visible.value) {
    fileInfo.value = {
      file: {
        uid: '',
        name: '',
      },
      fileList: [],
    };
  }
});
// 上传change
const handleFileChange = async (info: UploadChangeParam) => {
  fileInfo.value = info;
};
// 确定
const handleOk = async () => {
  const file = fileInfo.value?.file;
  const formData = new FormData();
  formData.append('file', file as unknown as Blob);
  uploading.value = true;

  try {
    const res = await $postOnClient(
      `${gatewayAcceptURL}/notice/import/file/createNotice`,
      formData,
    );
    if (res?.code === SUCCESS_CODE) {
      message.success('上传成功');
      visible.value = false;
      emit('okBtnCb', res?.data);
    } else {
      message.error(res?.msg || '');
    }
    uploading.value = false;
  } catch {
    uploading.value = false;
  }
};

// 下载模版
const downloadImportModel = async () => {
  const { xPortalToken } = useRuntimeConfig().public || {};
  await $fetch(`${gatewayAcceptURL}/notice/down/template/openNotice`, {
    method: 'get',
    headers: {
      'X-Portal-Token': xPortalToken,
      'Content-Type': 'application/json',
    },
    query: { },
    async onResponse({ response }) {
      let fileName = '';
      const contentDisposition = response.headers.get('Content-Disposition');
      const fileData = response._data;
      const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
      if (contentDisposition) {
        const match = contentDisposition.match(/filename=(.+)/);
        if (match) {
          fileName = decodeURI(match[1]);
        }
      }
      downloadBlob(fileData, fileName, fileType);
    },
  });
};
</script>
