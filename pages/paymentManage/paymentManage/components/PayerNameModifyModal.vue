<template>
  <a-modal v-model:open="visible" title="付款人名称修改" :width="pxToRem(800)" centered :confirm-loading="confirmLoading" ok-text="确定修改" @ok="confirm">
    <a-form ref="formRef" :model="formState">
      <a-form-item label="上传修改内容" name="uploadFile" :rules="[{ required: true, message: '请上传文件' }]">
        <a-form-item-rest>
          <a-button type="link" @click="downloadTemplate('1')">下载修改模版</a-button>
        </a-form-item-rest>
        <a-upload-dragger v-model:file-list="fileInfo.fileList" name="file" :before-upload="beforeUpload" :max-count="1" :show-upload-list="false" @change="handleChange">
          <p class="ant-upload-drag-icon">
            <VueIcon :icon="IconTongyongShangchuanFont" />
          </p>
          <p class="ant-upload-text">点击或将文件拖拽到这里上传</p>
        </a-upload-dragger>
        <p>
          待上传文件： <a href="#" class="text-cyan-600"> {{ formState.uploadFile }}</a>
        </p>
      </a-form-item>
      <a-form-item label="填写修改原因" name="reason" :rules="[{ required: true, message: '请填写修改原因' }]">
        <a-textarea v-model:value="formState.reason" show-count :maxlength="1200" />
      </a-form-item>
    </a-form>
    <!-- 错误提示弹窗 -->
    <a-modal v-model:open="warningOpen" :width="pxToRem(450)" centered>
      <div class="mb-[10px]">
        <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px] ml-[5px]">提醒</span>
      </div>
      <div>{{ modalText }}</div>
      <template #footer>
        <a-button type="primary" @click="warningConfirm">确定</a-button>
      </template>
    </a-modal>
  </a-modal>
</template>

<script setup lang="ts">
import { IconTongyongShangchuanFont, IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';
import { pxToRem, downloadBlob } from '@/utils/tools';
import { $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';

const formRef = ref();
const formState = ref({
  uploadFile: '', // 文件名称
  reason: '', // 原因
});
// 文件格式错误弹窗
const warningOpen = ref<boolean>(false);
const modalText = ref<string>('');
const visible = defineModel<boolean>('visible', {
  required: true,
  default: false,
});
const confirmLoading = ref(false);
watchEffect(() => {
  if (visible.value) {
    formRef.value?.clearValidate();
    formState.value.reason = '';
    formState.value.uploadFile = '';
  }
});
// 提示弹窗类型 1: 格式错误提示 2: 数据错误提示
const warningType = ref<string>('');
// 上传前检查格式
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isExcel = /\.(xlsx|xls)$/i.test(file.name);
  if (!isExcel) {
    modalText.value = `文件${file.name}格式不正确，请上传 Excel 文件。`;
    warningType.value = '1';
    warningOpen.value = true;
    return false;
  } else {
    formState.value.uploadFile = file.name;
    return true;
  }
};
const fileInfo = ref<UploadChangeParam>({
  file: {
    uid: '',
    name: '',
  },
  fileList: [],
});
// 上传文件时
const handleChange = (file: UploadChangeParam) => {
  fileInfo.value = file;
};
// 错误信息文件名
const errorFileName = ref<string>('');
// 确定修改
const confirm = async () => {
  await formRef.value.validate();
  const file = fileInfo.value?.file?.originFileObj;
  const formData = new FormData();
  formData.append('file', file as unknown as Blob);
  formData.append('modifyReason', formState.value.reason);
  confirmLoading.value = true;
  const uploadUrl = gateWay + service.accept + '/notice/infoModify/import/uploadFile';
  try {
    const res = await $postOnClient<{ resultFlag: boolean; resultMsg: string; fileName: string }>(uploadUrl, formData);
    const { code, msg, data } = res || {};
    if (code === SUCCESS_CODE && data) {
      visible.value = false;
      if (data.resultFlag) {
        message.success('上传成功');
      } else {
        modalText.value = `部分数据有误，请更正后重新上传 \n 点击确定显示错误详情`;
        warningType.value = '2';
        warningOpen.value = true;
        errorFileName.value = data.fileName;
      }
    } else {
      modalText.value = msg || '失败';
      warningType.value = '3';
      warningOpen.value = true;
    }
  } catch (error) {
    console.log(error);
  } finally {
    confirmLoading.value = false;
  }
};
// 提示确定
const warningConfirm = () => {
  warningOpen.value = false;
  if (warningType.value === '2') {
    downloadTemplate('2'); // 下载错误信息
  }
};
const { gateWay, service } = useRuntimeConfig().public || {};
// 下载模版
const downloadTemplate = async (type: string) => {
  const url = ref<string>('');
  const params = ref({});
  if (type === '1') {
    // 下载模版
    url.value = '/notice/infoModify/download/modifyPayNameTemplate';
  } else {
    url.value = '/notice/infoModify/export/exportModifyErrorInfo';
    params.value = { fileName: errorFileName.value };
  }
  const downloadUrl = gateWay + service.accept + url.value;
  try {
    await $getOnClient(downloadUrl, params.value, {
      onResponse({ response }) {
        if (response._data instanceof Blob) {
          const contentDisposition = response.headers.get('Content-Disposition');
          const fileData = response._data;
          const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
          if (contentDisposition) {
            const match = contentDisposition.match(/filename=(.+)/);
            const fileName = match ? decodeURI(match[1]) : '';
            downloadBlob(fileData, fileName, fileType);
          }
          message.success('下载成功');
        } else {
          const { code, data, msg = '' } = response._data;
          if (code === SUCCESS_CODE) {
            message.success(data);
          } else if (msg) {
            message.error(msg);
          }
        }
      },
    });
  } catch (error) {
    console.log(error);
  }
};
</script>
