<template>
  <a-modal v-model:open="visible" title="开我司份额保费" :width="pxToRem(800)" centered :confirm-loading="confirmLoading">
    <div class="adjust-amount-body">
      <p>请检查“调整后的待开单金额”的数值是否正确，如无异议，请点击【确定调整】，如需回复为按原金额开单，请点击【撤销调整】</p>
      <a-table :columns="columns" :data-source="tableData" :loading="loading" :scroll="{ x: 'max-content' }">
        <template #bodyCell="{ column, index }">
          <template v-if="column.dataIndex === 'index'">
            {{ index + 1 }}
          </template>
        </template>
      </a-table>
    </div>
    <template #footer>
      <a-button type="primary" @click="handleOk('1')"> 确定调整</a-button>
      <a-button type="primary" @click="handleOk('0')"> 撤销调整 </a-button>
      <a-button key="back" @click="handleCancel"> 取消 </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { TableColumnType } from 'ant-design-vue';
import { pxToRem } from '@/utils/tools';
import type { ApplyPayInfo } from '@/pages/paymentManage/paymentManage/paymentManage.d';

const visible = defineModel<boolean>('visible', {
  required: true,
  default: false,
});
defineProps<{
  tableData: ApplyPayInfo[]; // 表格数据
}>();

const emit = defineEmits(['handleOk']);

const columns: TableColumnType[] = [
  { title: '保费来源', dataIndex: 'premiumSourceName', key: 'premiumSourceName' },
  { title: '客户名称', dataIndex: 'customName', key: 'customName' },
  { title: '单号', dataIndex: 'voucherNo', key: 'voucherNo' },
  { title: '被保险人', dataIndex: 'insurantName', key: 'insurantName' },
  { title: '原金额', dataIndex: 'termAmount', key: 'termAmount' },
  { title: '共保比例', dataIndex: 'reinsureScale', key: 'reinsureScale' },
  { title: '调整后的开单金额', dataIndex: 'adjustedAmount', key: 'adjustedAmount' },
];

const loading = ref<boolean>(false);

const confirmLoading = ref(false);
// 确定调整
const handleOk = (type: string) => {
  // 确定调整为0 撤销调整为1
  emit('handleOk', type);
};
// 撤销调整
const handleCancel = () => {
  setVisible(false);
};
const setVisible = (flag: boolean) => {
  visible.value = flag;
};
</script>
