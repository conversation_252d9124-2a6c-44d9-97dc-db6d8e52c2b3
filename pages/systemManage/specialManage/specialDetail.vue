<template>
  <div class="relative mx-[14px] mt-[14px] bg-white">
    <div class="h-[76px] w-ful bg-image-url">
      <span class="text-[20px] text-[#00190C] font-bold leading-[76px] ml-[16px]">特约详情</span>
    </div>
    <div class="px-[16px] py-[20px] overflow-hidden">
      <ContentSet :id="route.query.id" mode="view" />
    </div>
  </div>
</template>

<script setup lang="ts">
import ContentSet from './components/contentSet.vue';

const route = useRoute();
</script>
<style lang="less" scoped>
.bg-image-url {
  background-image: url(/assets/images/content-head.png);
  background-size: cover;
}
</style>
