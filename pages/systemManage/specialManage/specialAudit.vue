<template>
  <div class="relative mx-[14px] mt-[14px] bg-white">
    <div class="h-[76px] w-ful bg-image-url">
      <span class="text-[20px] text-[#00190C] font-bold leading-[76px] ml-[16px]">特约审核</span>
    </div>
    <div class="px-[16px] py-[20px] overflow-hidden">
      <!-- 由于子组件页面进入无刷新调用接口，因此加if -->
      <ContentSet v-if="route.query.time" :id="route.query.id" mode="audit" />
      <InfoGroup title="审核意见">
        <a-form ref="formRef" :model="modelRef" :rules="rulesRef">
          <a-form-item label="审核结论" name="actionType">
            <a-radio-group v-model:value="modelRef.actionType" :options="actionTypeList" @change="changeActionType" />
          </a-form-item>
          <a-form-item label="审核意见" name="opinionDesc">
            <a-textarea v-model:value="modelRef.opinionDesc" :rows="4" />
          </a-form-item>
        </a-form>
      </InfoGroup>
    </div>
    <div class="bg-white h-[50px] z-[100] sticky bottom-0 flex justify-center items-center space-x-8px shadow-[0_2px_8px_0_rgba(0,0,0,0.13)]">
      <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
      <a-button class="mr-8px" @click="auditInfoOpen = true">审批信息</a-button>
    </div>
  </div>
  <a-modal v-model:open="auditInfoOpen" title="特约审批信息" centered :width="pxToRem(700)">
    <div class="text-[#404442] mb-[16px]">
      审批链：<span class="font-semibold">{{ agrSpApprovalChainName }}</span>
    </div>
    <div class="text-[#404442] mb-[10px]">审批记录：</div>
    <a-table :data-source="approvalDetail" :columns="approvalColumns" :pagination="false" :loading="tableLoading" />
    <template #footer>
      <a-button type="primary" @click="auditInfoOpen = false">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { RadioChangeEvent } from 'ant-design-vue/es/radio/interface';
import type { RuleObject } from 'ant-design-vue/es/form/interface';
import ContentSet from './components/contentSet.vue';
import InfoGroup from '@/components/ui/InfoGroup.vue';
import { $postOnClient, useGet } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import type { QuerySpecialPromiseApprovalDetail, ApprovalDetailItem } from '@/apiTypes/speciaManage.d.ts';
import { message } from '@/components/ui/Message';

const formRef = ref();
const route = useRoute();
const { service } = useRuntimeConfig().public || {};

const querySpecialPromiseApprovalOpinionReq = await useGet<QuerySpecialPromiseApprovalDetail>(`/gateway${service.accept}/specialPromiseApproval/querySpecialPromiseApprovalOpinion`);
const tableLoading = ref(false);

const modelRef = ref({
  actionType: '',
  opinionDesc: '',
});
interface rulesRefType {
  [k: string]: RuleObject | RuleObject[];
}
const rulesRef = ref<rulesRefType>({
  actionType: [{ required: true, message: '请选择审核结论', trigger: 'blur' }],
  opinionDesc: [{ required: true, message: '请输入审核意见', trigger: 'blur' }],
});

const actionTypeList = [
  { value: '1', label: '下发' },
  { value: '2', label: '同意' },
  { value: '4', label: '拒绝' },
];
const changeActionType = (e: RadioChangeEvent) => {
  modelRef.value.opinionDesc = actionTypeList?.find((item) => item.value === e.target.value)?.label || '';
};
const router = useRouter();
const timer = ref();
const submitLoading = ref(false);
const { deletPageTabListItem, pageTabList } = inject('pageTab'); // 关闭页签

const submit = () => {
  formRef.value?.validate().then(() => {
    submitLoading.value = true;
    $postOnClient(`/gateway${service.accept}/specialPromiseApproval/applySpecialPromiseApproval`, {
      idSpecialPromiseDefine: route.query.id || '',
      actionType: modelRef.value.actionType,
      opinionDesc: modelRef.value.opinionDesc,
      // actionManType: 'A', // 写死数据，后续需要去掉
    })
      .then((res) => {
        const { msg = '' } = res || {};
        if (res?.code === SUCCESS_CODE) {
          message.success(res?.msg);
          deletPageTabListItem('/specialAudit'); // 关闭页签
          // 返回上一页或者首页
          if (pageTabList.value.length) {
            router.push(pageTabList.value?.[pageTabList.value.length - 1]?.fullPath);
          } else {
            router.push('/home');
          }
          // timer.value = setTimeout(() => {
          //   router.back();
          // }, 2000);
        } else {
          message.error(msg);
        }
      })
      .finally(() => {
        submitLoading.value = false;
      });
  });
};
const idCopy = ref(route.query.id);

onActivated(() => {
  formRef.value?.clearValidate();
  if (route.query.id !== idCopy.value) {
    idCopy.value = route.query.id;
    modelRef.value.actionType = '';
    modelRef.value.opinionDesc = '';
  }
});
onDeactivated(() => {
  clearTimeout(timer.value);
});

const auditInfoOpen = ref(false);
const approvalDetail = ref<ApprovalDetailItem[]>([]);
const approvalColumns = [
  { title: '操作人', dataIndex: 'name' },
  { title: '操作时间', dataIndex: 'dateTime' },
  { title: '审批结论', dataIndex: 'actionType' },
  { title: '审批意见', dataIndex: 'opinionDesc' },
];

const agrSpApprovalChainName = ref('');
watchEffect(async () => {
  if (auditInfoOpen.value) {
    try {
      tableLoading.value = true;
      const { code, data } = (await querySpecialPromiseApprovalOpinionReq.fetchData({ idSpecialPromiseDefine: route.query.id || '' })) || {};
      if (code === SUCCESS_CODE) {
        agrSpApprovalChainName.value = data?.agrSpApprovalChainName || '';
        if (data?.specialPromiseOpinionsList && data?.specialPromiseOpinionsList?.length) {
          approvalDetail.value = data.specialPromiseOpinionsList.map((val, index) => {
            return {
              key: index + 1,
              name: val.approvalName || '',
              dateTime: val.createdDate || '',
              actionType: val.approvalStatusName || '',
              opinionDesc: val.approvalOpinions || '',
            };
          });
        } else {
          approvalDetail.value = [];
        }
      }
      tableLoading.value = false;
    } catch (err) {
      tableLoading.value = false;
      console.log(err);
    }
  }
});
</script>
<style lang="less" scoped>
.bg-image-url {
  background-image: url(/assets/images/content-head.png);
  background-size: cover;
}
</style>
