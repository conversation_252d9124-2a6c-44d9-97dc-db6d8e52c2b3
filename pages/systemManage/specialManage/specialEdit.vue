<template>
  <a-spin :spinning="pageLoading">
    <div class="relative mx-[14px] mt-[14px] bg-white form-wrapper">
      <div class="h-[76px] w-ful bg-image-url">
        <span class="text-[20px] text-[#00190C] font-bold leading-[76px] ml-[16px]">{{ modeMap[mode] }}特约</span>
      </div>
      <div class="px-[16px] py-[20px] overflow-hidden">
        <a-form ref="formRef" :colon="false" :label-col="{ style: { width: pxToRem(110) } }" :model="formData">
          <InfoGroup title="基础设置" class="mb-16px">
            <div class="form-title">选机构</div>
            <div class="grid grid-cols-3 gap-x-16px">
              <a-form-item label="机构" name="specialPromiseDept" :rules="[{ required: true, message: '请选择机构' }]">
                <a-select v-model:value="formData.specialPromiseDept" show-search placeholder="请选择机构" :options="deptOptions" :filter-option="filterOption" :disabled="mode === 'edit'" @change="changeDeptCode" />
                <!-- <department-search
                  :disabled="mode === 'edit'"
                  :deptCode="formData.specialPromiseDept"
                  @changeDeptCode="changeDeptCode"
                /> -->
              </a-form-item>
            </div>
            <div class="form-title">选产品</div>
            <a-table v-if="formData.transProductList" :columns="productColumns" :data-source="formData.transProductList" :scroll="{ x: 'max-content' }" :pagination="false">
              <template #bodyCell="{ column, index }">
                <template v-if="column.dataIndex === 'orderNum'">
                  <span>{{ index + 1 }}</span>
                </template>
                <template v-if="column.dataIndex === 'technicProductCode'">
                  <a-form-item label="" :name="['transProductList', index, 'technicProductCode']" :rules="[{ required: true, message: '请选择技术产品' }]">
                    <a-select v-model:value="formData.transProductList[index].technicProductCode" :options="formData.transProductList[index].technicProductOptions" allow-clear show-search option-filter-prop="label" @change="(value, options) => changeTpCode(index, options)" />
                  </a-form-item>
                </template>
                <template v-if="column.dataIndex === 'marketProductCode'">
                  <a-form-item label="" :name="['transProductList', index, 'marketProductCodeList']" :style="{ maxWidth: pxToRem(700), maxHeight: pxToRem(150), 'overflow-y': 'auto' }" :rules="[{ required: true, message: '请选择市场产品' }]">
                    <a-select v-model:value="formData.transProductList[index].marketProductCodeList" mode="multiple" :options="formData.transProductList[index].marketProductOptions" allow-clear show-search option-filter-prop="value" :filter-option="filterOption" @change="(value, options) => changeMpCode(index, options, value)" />
                  </a-form-item>
                </template>
                <template v-if="column.dataIndex === 'operation'">
                  <a-button type="link" :disabled="index === 0" @click="removeProduct(index)">删除</a-button>
                </template>
              </template>
            </a-table>
            <div class="mt-[10px] text-[#576B95] cursor-pointer w-fit" style="border-bottom: none" @click="addProduct">
              <VueIcon :icon="IconTongyongXinzengFont" class="cursor-pointer" />
              <span class="ml-[4px]">新增</span>
            </div>
          </InfoGroup>
          <InfoGroup title="特约设置" class="mb-16px">
            <div class="form-title">选类型及名称</div>
            <div class="grid grid-cols-2 gap-x-16px">
              <a-form-item class="special-type-item" label="特约类型" name="specialPromiseType" :rules="[{ required: true, message: '请选择特约类型' }]">
                <a-radio-group v-model:value="formData.specialPromiseType" :options="specialPromiseTypeList" :disabled="mode === 'edit'" @change="changePromiseType" />
                <a-tooltip :overlay-style="{ maxWidth: pxToRem(600) }" placement="top">
                  <template #title>业务员在出单时可自由编辑自定义特约，请谨慎选择该类型</template>
                  <VueIcon :icon="IconInfoCircleFont" class="cursor-pointer text-[16px] leading-[16px] w-[16px] h-[16px] text-[rgba(0,0,0,0.55)] ml-[-4px]" />
                </a-tooltip>
              </a-form-item>
              <a-form-item
                label="特约名称"
                name="specialPromiseName"
                :rules="[
                  { required: true, message: '请输入特约名称' },
                  { min: 0, max: 40, message: '不能超过40个字符', trigger: 'change' },
                ]"
              >
                <a-input v-model:value.trim="formData.specialPromiseName" placeholder="请输入" allow-clear />
              </a-form-item>
            </div>
            <div v-if="formData.specialPromiseType !== 'F'" class="form-title">内容设置</div>
            <template v-if="formData.specialPromiseType === 'N'">
              <a-form-item label="" :name="['specialPromiseControlList', 0, 'fixedMsg']" :rules="[{ required: true, message: '请输入文本内容' }]">
                <a-textarea v-model:value="formData.specialPromiseControlList[0].fixedMsg" show-count :maxlength="maxLength" :rows="6" />
              </a-form-item>
            </template>
            <div v-if="formData.specialPromiseType === 'X'" class="bg-white rounded-[4px] px-[16px] py-[14px]">
              <div class="text-[#404442] font-semibold">特约内容预览</div>
              <div class="mt-[4px]">
                <span v-for="(item, index) in formData.specialPromiseControlList" :key="index" class="leading-[32px]">
                  <template v-if="item.type === 'F'">{{ item.fixedMsg }}</template>
                  <span v-if="item.type === 'T'" class="inline-block border border-solid border-[#D4D6D9] bg-[#F9FAFA] rounded-[4px] mx-[2px] px-[4px]"> <span class="text-[#E6AD1C] bg-[#FFF9EB]">文本</span>{{ item.dataMap.needTextMax === '1' ? `${item.dataMap.textMaxLength}位数` : '自由输入' }}</span>
                  <span v-if="item.type === 'N'" class="inline-block border border-solid border-[#D4D6D9] bg-[#F9FAFA] rounded-[4px] mx-[2px] px-[4px]">
                    <span class="text-[#576B95] bg-[#EAF1FF]">数字</span>
                    <template v-if="item.dataMap.needRange === '0' && item.dataMap.needDecimals === '0'">自由输入</template>
                    <template v-else>
                      {{ item.dataMap.needRange === '1' ? `数值范围是 ${item.dataMap.mixNum}-${item.dataMap.maxNum}` : '' }}
                      {{ item.dataMap.needRange === '1' && item.dataMap.needDecimals === '1' ? ',' : '' }}
                      {{ item.dataMap.needDecimals === '1' ? `保留${item.dataMap.decimalPlaces}位小数` : '' }}
                    </template>
                  </span>
                </span>
              </div>
              <a-table :columns="controlColumns" :data-source="formData.specialPromiseControlList" :scroll="{ x: 'max-content', y: 400 }" :pagination="false" class="mt-[16px] control-table">
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
                  <template v-if="column.dataIndex === 'type'">
                    <a-form-item label="" :name="['specialPromiseControlList', index, 'type']" :rules="[{ required: true, message: '请选择类型' }]">
                      <a-select v-model:value="formData.specialPromiseControlList[index].type" :options="controlTypeList" @change="(value) => changeControlType(value, index)" />
                    </a-form-item>
                  </template>
                  <template v-if="column.dataIndex === 'contentSet' && record.type === 'F'">
                    <a-form-item label="" :name="['specialPromiseControlList', index, 'fixedMsg']" :rules="[{ required: true, message: '请输入文本内容' }]">
                      <a-input v-model:value="formData.specialPromiseControlList[index].fixedMsg" allow-clear />
                    </a-form-item>
                  </template>
                  <template v-if="column.dataIndex === 'contentSet' && record.type === 'N'">
                    <span class="flex items-center">
                      <a-form-item label="是否限制数值范围" :label-col="{ style: { width: pxToRem(120) } }">
                        <a-radio-group v-model:value="formData.specialPromiseControlList[index].dataMap.needRange" @change="(value) => changeNeedRange(value, index)">
                          <a-radio value="0">否</a-radio>
                          <a-radio value="1">是</a-radio>
                        </a-radio-group>
                      </a-form-item>
                      <a-form-item
                        v-if="formData.specialPromiseControlList[index].dataMap.needRange === '1'"
                        label=""
                        :name="['specialPromiseControlList', index, 'dataMap', 'mixNum']"
                        :rules="[
                          { required: true, message: '请输入最大值和最小值' },
                          { validator: (rules, value) => validateNumRange(rules, { mixNum: formData.specialPromiseControlList[index].dataMap.mixNum, maxNum: formData.specialPromiseControlList[index].dataMap.maxNum }), trigger: 'change' },
                        ]"
                      >
                        仅允许输入从
                        <a-input v-model:value="formData.specialPromiseControlList[index].dataMap.mixNum" type="number" style="width: 100px; margin: 0 10px" />
                        至
                        <a-input v-model:value="formData.specialPromiseControlList[index].dataMap.maxNum" type="number" style="width: 100px; margin: 0 10px" />
                        的数字
                      </a-form-item>
                    </span>
                    <span class="flex items-center">
                      <a-form-item label="是否限制小数位数" :label-col="{ style: { width: pxToRem(120) } }" @change="(value) => changeNeedDecimals(value, index)">
                        <a-radio-group v-model:value="formData.specialPromiseControlList[index].dataMap.needDecimals">
                          <a-radio value="0">否</a-radio>
                          <a-radio value="1">是</a-radio>
                        </a-radio-group>
                      </a-form-item>
                      <a-form-item
                        v-if="formData.specialPromiseControlList[index].dataMap.needDecimals === '1'"
                        :label-col="{ style: { width: pxToRem(90) } }"
                        :name="['specialPromiseControlList', index, 'dataMap', 'decimalPlaces']"
                        :rules="[
                          { required: true, message: '请输入小数位数' },
                          { validator: validateDecimalPlaces, trigger: 'change' },
                        ]"
                      >
                        保留小数位数
                        <a-input v-model:value="formData.specialPromiseControlList[index].dataMap.decimalPlaces" type="number" style="width: 100px; margin: 0 10px" />
                        位
                      </a-form-item>
                    </span>
                  </template>
                  <span v-if="column.dataIndex === 'contentSet' && record.type === 'T'" class="flex items-center">
                    <a-form-item label="是否限制文本字数上限" :label-col="{ style: { width: pxToRem(150) } }">
                      <a-radio-group v-model:value="formData.specialPromiseControlList[index].dataMap.needTextMax" @change="(value) => changeNeedTextMax(value, index)">
                        <a-radio value="0">否</a-radio>
                        <a-radio value="1">是</a-radio>
                      </a-radio-group>
                    </a-form-item>
                    <a-form-item
                      v-if="formData.specialPromiseControlList[index].dataMap.needTextMax === '1'"
                      label=""
                      :label-col="{ style: { width: pxToRem(120) } }"
                      :name="['specialPromiseControlList', index, 'dataMap', 'textMaxLength']"
                      :rules="[
                        { required: true, message: '请输入文本字数上限' },
                        { validator: validateTextMaxLength, trigger: 'change' },
                      ]"
                    >
                      <a-input v-model:value="formData.specialPromiseControlList[index].dataMap.textMaxLength" type="number" style="width: 100px; margin-right: 10px" />
                      位
                    </a-form-item>
                  </span>
                  <template v-if="column.dataIndex === 'operation'">
                    <a-button class="p-0" type="link" :disabled="index === 0" @click="removeContent(index)">删除</a-button>
                  </template>
                </template>
              </a-table>
              <div class="mt-[10px] text-[#576B95] cursor-pointer w-fit" style="border-bottom: none" @click="addContent">
                <VueIcon :icon="IconTongyongXinzengFont" class="cursor-pointer" />
                <span class="ml-[4px]">新增</span>
              </div>
            </div>
          </InfoGroup>
          <InfoGroup title="机构设置">
            <div class="form-title">选配置机构</div>
            <a-form-item name="specialPromiseDeptRelList" :rules="[{ required: true, message: '请选择配置机构' }]">
              <!-- 由于a-checkbox-group无法做全国与其他机构互斥的逻辑 -->
              <a-checkbox v-if="isAllVisible" v-model:checked="checkAll" @change="checkAllChange">全国</a-checkbox>
              <a-checkbox-group v-model:value="chosenDepts" :options="allDeptList" style="display: grid" class="grid grid-cols-10 gap-x-16px" @change="handleChosen" />
            </a-form-item>
          </InfoGroup>
        </a-form>
      </div>
    </div>
    <div class="h-[10px]" />
    <div class="bg-white h-[50px] z-[100] sticky bottom-0 flex justify-center items-center space-x-8px shadow-[0_2px_8px_0_rgba(0,0,0,0.13)]">
      <a-button class="mr-8px" :loading="saveLoading" @click="save">暂存</a-button>
      <a-button type="primary" :loading="submitLoading" @click="submit">提交</a-button>
    </div>
  </a-spin>
</template>

<script setup lang="ts">
import { IconTongyongXinzengFont, IconInfoCircleFont } from '@pafe/icons-icore-agr-an';
import type { Rule } from 'ant-design-vue/es/form';
import type { TableColumnsType, RadioChangeEvent } from 'ant-design-vue';
import type { SelectValue, DefaultOptionType } from 'ant-design-vue/es/select';
import type { CheckboxValueType } from 'ant-design-vue/es/checkbox/interface';
import { cloneDeep } from 'lodash-es';
import InfoGroup from '@/components/ui/InfoGroup.vue';
import type { SelectOptions, BaseConstants } from '@/apiTypes/apiCommon';
import type { SpecialDetail, SpecialDeptInfo, TransProductItem, SpecialProductInfo } from '@/apiTypes/insure';
import { usePost, useGet, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import { message } from '@/components/ui/Message';

const { service } = useRuntimeConfig().public || {};
const route = useRoute();

const getParmBaseConstantConfReq = await usePost<BaseConstants>('/api/common/getParmBaseConstantConf');
const querySpecialPromiseEditDetailReq = await usePost('/api/accept/querySpecialPromiseEditDetail');
const queryTechnicMarketProductListReq = await usePost('/api/accept/queryTechnicMarketProductList');
const findDeptListByComplianceDeptCodeReq = await useGet('/api/accept/findDeptListByComplianceDeptCode');
const querySecondDepartmentListReq = await useGet(`/gateway${service.accept}/specialPromise/querySecondDepartmentList`);

const formRef = ref();
const timer = ref();
const formData: SpecialDetail = reactive({
  idSpecialPromiseDefine: '',
  lockVersion: '',
  specialPromiseDept: '',
  specialPromiseCode: '',
  specialPromiseName: '',
  specialPromiseType: '',
  specialPromiseTypeName: '',
  specialPromiseProRelList: [
    {
      marketProductCode: '',
      marketProductName: '',
      technicProductCode: '',
      technicProductName: '',
    },
  ],
  specialPromiseDeptRelList: [],
  specialPromiseControlList: [{ type: 'F', dataMap: {}, fixedMsg: '' }],
  transProductList: [
    {
      marketProductList: [],
      marketProductCodeList: [],
      technicProductCode: '',
      technicProductName: '',
      technicProductOptions: [],
      marketProductOptions: [],
    },
  ],
});

interface DeptOptionsType {
  label: string;
  value: string;
}
const deptOptions = ref<DeptOptionsType[]>([]);

const filterOption = (input: string, option: SelectOptions) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
// 全国是否选中
const checkAll = ref<boolean>(false);
// 全国默认显示，机构不是2的时候不显示
const isAllVisible = ref<boolean>(true);
// 选中全国赋值，form校验
const checkAllChange = () => {
  // 其他机构不选中
  chosenDepts.value = [];
  formData.specialPromiseDeptRelList = ['2'];
};
// 如果选择非全国，“全国”勾选，则全国取消勾选
const handleChosen = () => {
  checkAll.value = false;
  formData.specialPromiseDeptRelList = chosenDepts.value;
};
// 选择机构-选择202上海时，机构设置中的地址只显示上海，其余不显示，测试要求的
const changeDeptCode = () => {
  if (formData.specialPromiseDept !== '2') {
    chosenDepts.value = [];
    formData.specialPromiseDeptRelList = [];
    checkAll.value = false;
    // 显示当前机构时，全国不显示
    isAllVisible.value = false;
    // 筛选当前选中机构对应的配置机构的数据
    allDeptList.value = cloneDeep(allDeptListCopy.value).filter((item) => item.value === formData.specialPromiseDept);
  } else {
    // 显示全国，恢复全部数据
    isAllVisible.value = true;
    allDeptList.value = allDeptListCopy.value;
  }
  queryProductList();
  formData.transProductList.forEach((item) => {
    item.marketProductCodeList = [];
    item.marketProductList = [];
    item.technicProductCode = '';
    item.technicProductName = '';
  });
};
// 表单个性化校验
const validateNumRange = (_rule: Rule, value: { mixNum?: string; maxNum?: string }) => {
  console.log('++++', value);
  const result = Number(value.mixNum) < Number(value.maxNum);
  if (result) {
    return Promise.resolve();
  } else {
    return Promise.reject('最小值必须小于最大值');
  }
};

const validateDecimalPlaces = (_rule: Rule, value: string) => {
  const regex = /^[0-9]{1}\d*$/;
  const numVal = Number(value);
  const result = regex.test(value) && numVal > 0 && numVal < 7;
  if (result) {
    return Promise.resolve();
  } else {
    return Promise.reject('请输入大于0、小于7的正整数');
  }
};

const validateTextMaxLength = (_rule: Rule, value: string) => {
  const regex = /^[0-9]{1}\d*$/;
  const numVal = Number(value);
  const result = regex.test(value) && numVal > 0 && numVal <= 1000;
  if (result) {
    return Promise.resolve();
  } else {
    return Promise.reject('文本字数上限不能超过1000');
  }
};

// N：固定特约、X：半固定特约、F：自定义特约
const specialPromiseTypeList = ref<Array<SelectOptions>>([]);
const queryPromiseTypeList = async () => {
  try {
    const res = await getParmBaseConstantConfReq.fetchData(['specialPromiseType']);
    if (res && res.code === SUCCESS_CODE && res.data) {
      specialPromiseTypeList.value = res.data.specialPromiseTypeList || [];
      if (!formData.specialPromiseType) {
        formData.specialPromiseType = specialPromiseTypeList.value?.[0]?.value ?? '';
      }
    } else {
      specialPromiseTypeList.value = [];
      formData.specialPromiseType = '';
    }
  } catch (err) {
    console.log(err);
  }
};

// 特约类型变动，清空特约内容
const changePromiseType = () => {
  formData.specialPromiseControlList = [{ type: 'F', dataMap: {}, fixedMsg: '' }];
};

// 文本域限制长度
const maxLength = computed(() => (formData.specialPromiseType === 'X' ? 2000 : 4000));

// 已勾选的适用机构
// const chosenDepts = ref<SpecialDeptInfo[] | string[]>([]);
const chosenDepts = ref<SpecialDeptInfo[] | CheckboxValueType[] | DeptOptionsType[]>([]);

// 表单重置
const reset = () => {
  formData.idSpecialPromiseDefine = '';
  formData.lockVersion = '';
  formData.specialPromiseDept = deptOptionsClone.value?.[0]?.value;
  formData.specialPromiseCode = '';
  formData.specialPromiseName = '';
  formData.specialPromiseType = '';
  formData.specialPromiseProRelList = [
    {
      marketProductCode: '',
      marketProductName: '',
      technicProductCode: '',
      technicProductName: '',
    },
  ];
  formData.specialPromiseDeptRelList = [];
  chosenDepts.value = [];
  formData.specialPromiseControlList = [{ type: 'F', dataMap: {}, fixedMsg: '' }];
  formData.transProductList = [
    {
      marketProductList: [],
      marketProductCodeList: [],
      technicProductCode: '',
      technicProductName: '',
      technicProductOptions: JSON.parse(JSON.stringify(allProductList.value)),
      marketProductOptions: [],
    },
  ];
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

const pageLoading = ref(false);
const mode = computed(() => (route.query.id ? 'edit' : 'add'));
// 从编辑入口进入，先查询详情
const queryDetail = () => {
  if (route.query.id) {
    if (formRef.value) {
      formRef.value.clearValidate();
    }
    pageLoading.value = true;
    querySpecialPromiseEditDetailReq
      .fetchData({ idSpecialPromiseDefine: route.query.id, status: route.query.status })
      .then((res) => {
        const { msg = '' } = res || {};
        if (res?.code === SUCCESS_CODE) {
          if (res?.data) {
            formData.idSpecialPromiseDefine = res.data.idSpecialPromiseDefine;
            formData.lockVersion = res.data.lockVersion;
            formData.specialPromiseDept = res.data.specialPromiseDept;
            formData.specialPromiseName = res.data.specialPromiseName;
            formData.specialPromiseType = res.data.specialPromiseType;
            formData.specialPromiseProRelList = res.data.specialPromiseProRelList || [
              {
                marketProductCode: '',
                marketProductName: '',
                technicProductCode: '',
                technicProductName: '',
              },
            ];
            formData.specialPromiseDeptRelList = res.data.specialPromiseDeptRelList || [];
            formData.specialPromiseControlList = res.data.specialPromiseControlList || [{ type: 'F', dataMap: {}, fixedMsg: '' }];
            chosenDepts.value = formData.specialPromiseDeptRelList.map((item) => item.adaptDepartmentCode);
            // 由于全国单独写，所以需要特定赋值
            if (formData.specialPromiseDeptRelList.some((item) => item.adaptDepartmentCode === '2')) {
              checkAll.value = true;
              isAllVisible.value = true;
            } else {
              // 如果不是返回全国，则全国不勾选且不显示
              checkAll.value = false;
              isAllVisible.value = false;
            }
            // 产品配置表格数据回填
            const newList: Array<TransProductItem> = [];
            for (let i = 0; i < formData.specialPromiseProRelList.length; i++) {
              const item = formData.specialPromiseProRelList[i];
              const tempIndex = newList.findIndex((transItem) => transItem.technicProductCode === item.technicProductCode);
              if (tempIndex > -1) {
                newList[tempIndex].marketProductList.push({ marketProductCode: item.marketProductCode, marketProductName: item.marketProductName });
                newList[tempIndex].marketProductCodeList.push(item.marketProductCode);
              } else {
                const marketProductOptions = allProductList.value.find((productItem) => productItem.value === item.technicProductCode)?.children || [];
                if (marketProductOptions.length > 0) {
                  marketProductOptions.unshift({ value: 'all', label: '所有' });
                }
                newList.push({
                  marketProductList: [{ marketProductCode: item.marketProductCode, marketProductName: item.marketProductName }],
                  marketProductCodeList: [item.marketProductCode],
                  technicProductCode: item.technicProductCode,
                  technicProductName: item.technicProductName,
                  technicProductOptions: allProductList.value.filter((productItem) => !selectedTPCode.value.includes(productItem.value) || productItem.value === item.technicProductCode),
                  marketProductOptions,
                });
              }
            }
            formData.transProductList = JSON.parse(JSON.stringify(newList));
          }
        } else {
          message.error(msg);
        }
      })
      .finally(() => {
        pageLoading.value = false;
      });
  } else {
    reset();
  }
};

const modeMap: { [key: string]: string } = { add: '新增', edit: '编辑' };

// 技术产品
const allProductList = ref<SelectOptions[]>([]); // 全量产品下拉选项
const selectedTPCode = computed(() => (formData.transProductList?.filter((item) => item.technicProductCode !== '') || []).map((item) => item.technicProductCode) || []); // 已选中技术产品
const changeTpCode = (index: number, options: SelectOptions) => {
  if (formData.transProductList) {
    formData.transProductList[index].technicProductCode = options?.value || '';
    formData.transProductList[index].technicProductName = options?.label || '';
    formData.transProductList[index].marketProductList = [];
    formData.transProductList[index].marketProductCodeList = [];
    // 深拷贝，防止”所有“多次增加
    const marketProductOptions = cloneDeep(options?.children) || [];
    if (marketProductOptions.length > 0) {
      marketProductOptions.unshift({ label: '所有', value: 'all' });
    }
    formData.transProductList[index].marketProductOptions = marketProductOptions;
    formData.transProductList.map((transItem, transIndex) => {
      if (transIndex !== index) {
        transItem.technicProductOptions = allProductList.value.filter((productItem) => !selectedTPCode.value.includes(productItem.value) || productItem.value === transItem.technicProductCode);
      }
    });
  }
};

// 初始化赋值全量产品下拉选项
watch(
  () => allProductList.value,
  (val) => {
    if (formData.transProductList?.length > 0) {
      formData.transProductList.map((transItem) => {
        transItem.technicProductOptions = cloneDeep(val);
      });
    }
  },
);

// 市场产品
const changeMpCode = (index: number, options: DeptOptionsType[] | DefaultOptionType | DefaultOptionType[], value?: SelectValue) => {
  if (formData.transProductList) {
    // 支持全选
    const marketProductOptions = formData.transProductList[index].marketProductOptions;
    if (value.includes('all')) {
      formData.transProductList[index].marketProductList = [];
      for (let i = 0; i < marketProductOptions.length; i++) {
        if (marketProductOptions[i].value !== 'all') {
          formData.transProductList[index].marketProductList.push({
            marketProductCode: marketProductOptions[i].value,
            marketProductName: marketProductOptions[i].label,
          });
        }
      }
      formData.transProductList[index].marketProductCodeList = formData.transProductList[index].marketProductList.filter((item) => item.marketProductCode !== 'all').map((item) => item.marketProductCode);
    } else {
      formData.transProductList[index].marketProductList = options.map((item: DeptOptionsType) => {
        return {
          marketProductCode: item.value,
          marketProductName: item.label,
        };
      });
    }
  }
};

// 查询技术产品列表
const queryProductList = async () => {
  try {
    const res = await queryTechnicMarketProductListReq.fetchData({ departmentCode: formData.specialPromiseDept });
    if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
      allProductList.value = res.data;
    } else {
      allProductList.value = [];
    }
  } catch (err) {
    console.log(err);
  }
};

// 产品配置表格
const productColumns: TableColumnsType = [
  {
    title: '序号',
    dataIndex: 'orderNum',
    fixed: 'left',
    width: pxToRem(80),
  },
  {
    title: '技术产品',
    dataIndex: 'technicProductCode',
  },
  {
    title: '市场产品',
    dataIndex: 'marketProductCode',
    width: pxToRem(700),
  },
  {
    title: '操作',
    dataIndex: 'operation',
    fixed: 'right',
    width: pxToRem(80),
  },
];

// 新增产品
const addProduct = () => {
  formData.transProductList?.push({
    technicProductCode: '',
    technicProductName: '',
    marketProductList: [],
    marketProductCodeList: [],
    technicProductOptions: allProductList.value.filter((productItem) => !selectedTPCode.value.includes(productItem.value)),
    marketProductOptions: [],
  });
};

// 删除产品
const removeProduct = (index: number) => {
  formData.transProductList?.splice(index, 1);
};

// 特约内容
const controlColumns = ref<TableColumnsType>([
  { title: '序号', dataIndex: 'index', key: 'index' },
  { title: '类型', dataIndex: 'type', width: pxToRem(200) },
  { title: '内容设置', dataIndex: 'contentSet' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
]);

const controlTypeList = ref([
  { value: 'F', label: '固定文本' },
  { value: 'T', label: '文本参数' },
  { value: 'N', label: '数字参数' },
]);

// 切换类型后，清空已设置内容
const changeControlType = (value: string | number | SelectValue, index: number): void => {
  const typeMap: { [key: string | number]: () => void } = {
    F: () => {
      formData.specialPromiseControlList[index].fixedMsg = '';
      formData.specialPromiseControlList[index].dataMap = {};
    },
    T: () => {
      formData.specialPromiseControlList[index].dataMap = { needTextMax: '0', textMaxLength: '' };
      formData.specialPromiseControlList[index].fixedMsg = '';
    },
    N: () => {
      formData.specialPromiseControlList[index].dataMap = {
        needRange: '0',
        needDecimals: '0',
        maxNum: '',
        mixNum: '',
        decimalPlaces: '',
      };
      formData.specialPromiseControlList[index].fixedMsg = '';
    },
  };
  typeMap[value as number]();
};

// 是否限制数值范围
const changeNeedRange = (value: RadioChangeEvent | string, index: number): void => {
  if (value === '0') {
    formData.specialPromiseControlList[index].dataMap.maxNum = '';
    formData.specialPromiseControlList[index].dataMap.mixNum = '';
  }
};

// 是否限制小数位数
const changeNeedDecimals = (value: string, index: number) => {
  if (value === '0') {
    formData.specialPromiseControlList[index].dataMap.decimalPlaces = '';
  }
};

// 是否限制文本字数上限
const changeNeedTextMax = (index: number, value?: string | RadioChangeEvent) => {
  if (value === '0') {
    formData.specialPromiseControlList[index].dataMap.textMaxLength = '';
  }
};
// 新增内容
const addContent = () => {
  formData.specialPromiseControlList.push({
    type: 'F',
    fixedMsg: '',
    dataMap: {},
  });
};

// 删除内容
const removeContent = (index: number) => {
  formData.specialPromiseControlList.splice(index, 1);
};

// 适用机构列表
const allDeptList = ref<SelectOptions[]>([]);
const allDeptListCopy = ref<SelectOptions[]>([]);

// 查询全国所有二级机构
const queryDeptList = async () => {
  try {
    const res = await findDeptListByComplianceDeptCodeReq.fetchData();
    if (res && res.code === SUCCESS_CODE && res.data) {
      allDeptList.value = res.data;
      allDeptListCopy.value = cloneDeep(allDeptList.value);
    } else {
      allDeptList.value = [];
    }
  } catch (err) {
    console.log(err);
  }
};

// 根据登录用户角色返回所有二级机构和总部机构
const deptOptionsClone = ref<DeptOptionsType[]>([]);
const querySecondDepartmentList = async () => {
  try {
    const res = await querySecondDepartmentListReq.fetchData();
    if (res && res.code === SUCCESS_CODE && res.data) {
      deptOptions.value = res.data.map((item: { label: string; value: string; departmentName: string; departmentCode: string }) => {
        return {
          label: item.departmentName,
          value: item.departmentCode,
        };
      });
      // 由于后端重新提供了接口，所以这里必须有值才能请求列表接口
      deptOptionsClone.value = cloneDeep(deptOptions.value);
      formData.specialPromiseDept = deptOptionsClone.value?.[0]?.value;
      // 如果机构列表中返回value=2时，选配置机构中“全国”可选，否则全国禁用
      if (!deptOptionsClone.value.some((item) => item.value === '2')) {
        isAllVisible.value = false;
      }
    } else {
      deptOptions.value = [];
    }
  } catch (err) {
    console.log(err);
  }
};

queryDeptList();
queryPromiseTypeList();
// 查询初始化数据--由于queryProductList方法的接口改造，需传机构才能返回技术产品，因此需调用成功才能下一步
await querySecondDepartmentList();
// 需先获取技术产品接口成功后才能正常回显市场产品数据
await queryProductList();
queryDetail();

// 提交前进行数据封装
const handleFormData = () => {
  // 适用机构
  if (chosenDepts.value.length > 0 && allDeptList.value.length > 0) {
    const deptList: Array<{ adaptDepartmentCode: string; adaptDepartmentName: string }> = [];
    for (let i = 0; i < chosenDepts.value.length; i++) {
      const index = allDeptList.value.findIndex((item) => item.value === chosenDepts.value[i]);
      if (index > -1) {
        deptList.push({
          adaptDepartmentCode: allDeptList.value[index].value,
          adaptDepartmentName: allDeptList.value[index].label,
        });
      }
    }
    formData.specialPromiseDeptRelList = JSON.parse(JSON.stringify(deptList));
  }
  // 如果全国选中，则封装全国数据传给后端
  if (checkAll.value) {
    formData.specialPromiseDeptRelList = [
      {
        adaptDepartmentCode: '2',
        adaptDepartmentName: '全国',
      },
    ];
  }
  // 技术产品、市场产品
  if (formData.transProductList && formData.transProductList.length > 0) {
    const proList: Array<SpecialProductInfo> = [];
    for (let i = 0; i < formData.transProductList.length; i++) {
      const tpItem = formData.transProductList[i];
      for (let j = 0; j < tpItem.marketProductList.length; j++) {
        const mpItem = tpItem.marketProductList[j];
        // 传给后端name不需要拼接code值
        proList.push({
          technicProductCode: tpItem.technicProductCode,
          technicProductName: tpItem.technicProductName?.replace(tpItem.technicProductCode, ''),
          marketProductCode: mpItem.marketProductCode,
          marketProductName: mpItem.marketProductName?.replace(mpItem.marketProductCode, ''),
        });
      }
    }
    formData.specialPromiseProRelList = JSON.parse(JSON.stringify(proList));
  }
};

// 暂存
const saveLoading = ref(false);
const save = () => {
  formRef.value.validate().then(() => {
    handleFormData();
    saveLoading.value = true;
    $postOnClient(`/gateway${service.accept}/specialPromise/tempSpecialPromise`, {
      idSpecialPromiseDefine: route.query.id || '',
      specialPromiseCode: route.query.code || '',
      lockVersion: formData.lockVersion,
      specialPromiseDept: formData.specialPromiseDept,
      specialPromiseName: formData.specialPromiseName,
      specialPromiseType: formData.specialPromiseType,
      specialPromiseProRelList: formData.specialPromiseProRelList,
      specialPromiseDeptRelList: formData.specialPromiseDeptRelList,
      specialPromiseControlList: formData.specialPromiseControlList,
    })
      .then((res) => {
        const { msg = '' } = res || {};
        if (res?.code === SUCCESS_CODE) {
          const { lockVersion, idSpecialPromiseDefine, specialPromiseCode } = res?.data || {};
          formData.lockVersion = lockVersion || '';
          formData.idSpecialPromiseDefine = idSpecialPromiseDefine;
          formData.specialPromiseCode = specialPromiseCode;
          message.success(msg);
          checkAll.value = false;
        } else {
          message.error(msg);
        }
      })
      .finally(() => {
        saveLoading.value = false;
      });
  });
};

const submitLoading = ref(false);
const router = useRouter();
const submit = () => {
  formRef.value.validate().then(() => {
    handleFormData();
    submitLoading.value = true;
    $postOnClient(`/gateway${service.accept}/specialPromise/applySpecialPromise`, {
      idSpecialPromiseDefine: route.query.id || formData.idSpecialPromiseDefine || '',
      specialPromiseCode: route.query.code || formData.specialPromiseCode || '',
      lockVersion: formData.lockVersion,
      specialPromiseDept: formData.specialPromiseDept,
      specialPromiseName: formData.specialPromiseName,
      specialPromiseType: formData.specialPromiseType,
      specialPromiseProRelList: formData.specialPromiseProRelList,
      specialPromiseDeptRelList: formData.specialPromiseDeptRelList,
      specialPromiseControlList: formData.specialPromiseControlList,
    })
      .then((res) => {
        const { msg = '' } = res || {};
        if (res?.code === SUCCESS_CODE) {
          message.success(msg);
          checkAll.value = false;
          timer.value = setTimeout(() => {
            router.back();
          }, 2000);
        } else {
          message.error(msg);
        }
      })
      .finally(() => {
        submitLoading.value = false;
      });
  });
};

const addTypeTime = ref(route.query.time || '0');

onActivated(() => {
  if ((addTypeTime.value === '0' || addTypeTime.value !== route.query.time) && !route.query.id) {
    // 用户手动点击新增时，需要清空所有的选项
    addTypeTime.value = route.query.time || '';
    reset();
  }
});

onDeactivated(() => {
  clearTimeout(timer.value);
});
</script>

<style lang="less" scoped>
.form-wrapper {
  min-height: calc(100vh - 98px);
  .bg-image-url {
    background-image: url(/assets/images/content-head.png);
    background-size: cover;
  }
  .special-type-item {
    :deep(.ant-form-item-control-input-content) {
      display: flex;
      align-items: center;
    }
  }
  .control-table {
    :deep(.ant-form-item) {
      margin-bottom: 0;
    }
  }
}
</style>
