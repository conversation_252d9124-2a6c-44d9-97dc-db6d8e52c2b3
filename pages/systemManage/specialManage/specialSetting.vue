<template>
  <div class="p-14px space-y-14px">
    <div class="bg-white p-16px rounded-md">
      <div class="flex">
        <a-form ref="formRef" :model="formData" :colon="false" class="flex-grow" :label-col="{ style: { width: pxToRem(80) } }" :rules="formRules">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="机构" name="specialPromiseDept">
                <!-- <department-search :deptCode="formData.specialPromiseDept" @changeDeptCode="changeDeptCode" /> -->
                <a-row :gutter="24">
                  <a-col :span="8">
                    <a-input v-model:value="inputValue" placeholder="请输入" type="number" @blur="blurChange" @change="searchChange" />
                  </a-col>
                  <a-col :span="16">
                    <a-select v-model:value="formData.specialPromiseDept" show-search placeholder="请选择机构" :options="deptOptions" :filter-option="filterOption" @search="searchSelectChange" />
                  </a-col>
                </a-row>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="技术产品" name="technicProductCode">
                <a-select v-model:value="formData.technicProductCode" :options="allProductList" allow-clear show-search option-filter-prop="label" @change="handleTechnic" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="市场产品" name="marketProductCodeList">
                <a-select v-model:value="formData.marketProductCodeList" mode="multiple" :options="marketProductOptions" allow-clear show-search option-filter-prop="label" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="16">
            <a-col :span="8">
              <a-form-item label="特约名称" name="specialPromiseName">
                <a-input v-model:value.trim="formData.specialPromiseName" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="特约类型" name="specialPromiseType">
                <a-select v-model:value="formData.specialPromiseType" :options="specialPromiseTypeList" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="16">
            <a-col :span="24">
              <a-form-item label="状态" name="specialPromiseStatusList">
                <check-box-group v-model:checked-list="formData.specialPromiseStatusList" :options="specialStatus" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="w-[50px]">
          <form-fold v-model="expand" />
        </div>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button @click="resetForm">重置</a-button>
        <a-button type="primary" ghost @click="submit">查询</a-button>
        <AuthButton code="specialSettingCreate" type="primary" @click="addSpecial">新增</AuthButton>
      </div>
    </div>
    <div class="bg-white p-16px rounded-md">
      <div class="mb-16px">
        <div class="text-[#404442] text-xl font-bold">查询结果</div>
      </div>
      <a-table :loading="loading" :data-source="dataSource" :columns="columns" :pagination="pagination" :bordered="false" :scroll="{ x: 'max-content' }" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : undefined)" size="small" class="table-box">
        <template #bodyCell="{ column, record, text }">
          <template v-if="['specialPromiseDept', 'marketProductName', 'specialPromiseName', 'specialPromiseDesc'].includes(column.dataIndex as string)">
            <a-tooltip overlay-class-name="tooltipCss" placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] table-ellipsis-multiline">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'specialPromiseCode'">
            <CopyLink :text="text" @click="handleView(record)" />
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <AuthButton code="specialSettingView" type="link" size="small" @click="handleView(record)">查看</AuthButton>
            <AuthButton code="specialSettingEdit" type="link" size="small" :disabled="record.disabled || record.specialPromiseStatus === '2'" @click="handleEdit(record)">修改 </AuthButton>
            <AuthButton code="specialSettingDel" type="link" size="small" :disabled="record.disabled || ['2', '3'].includes(record.specialPromiseStatus)" @click="handleDelete(record)"> 删除</AuthButton>
            <AuthButton v-if="record.specialPromiseStatus === '4'" code="specialSettingEffInvalid" type="link" size="small" :disabled="record.disabled" @click="handleTransferTake(record)">生效</AuthButton>
            <AuthButton v-if="record.specialPromiseStatus === '3'" code="specialSettingEffInvalid" type="link" size="small" :disabled="record.disabled" @click="handleTransferLose(record)">失效</AuthButton>
          </template>
        </template>
      </a-table>
    </div>
    <a-modal v-model:open="transferInfo.transferVisible" centered title="特约失效" :width="pxToRem(500)" @ok="transferSpecialStatus">
      <div class="p-[16px]">
        <div class="mb-[10px]">请谨慎失效！</div>
        <a-form :colon="false">
          <a-form-item label="失效原因" :rules="[{ required: true, message: '请输入失效原因' }]">
            <a-input v-model:value.trim="transferInfo.operateReason" allow-clear placeholder="请输入失效原因" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
    <!-- 确认删除弹窗 -->
    <a-modal v-model:open="openVisible" :width="pxToRem(450)" @ok="handleConfirmOk">
      <div class="mb-[10px]">
        <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px]">温馨提示</span>
      </div>
      <div>即将删除，请谨慎操作！</div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep, debounce } from 'lodash-es';
import type { TableColumnsType } from 'ant-design-vue';
import { IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import FormFold from '@/components/ui/FormFold.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import { usePagination } from '@/composables/usePagination';
import { usePost, $getOnClient, $postOnClient, useGet } from '@/composables/request';
import type { SelectOptions, BaseConstants } from '@/apiTypes/apiCommon';
import { message } from '@/components/ui/Message';
import AuthButton from '@/components/ui/AuthButton.vue';
import CopyLink from '@/components/ui/CopyLink.vue';

const querySpecialPromiseAbstractListReq = await usePost('/api/accept/querySpecialPromiseAbstractList');
const getParmBaseConstantConfReq = await usePost<BaseConstants>('/api/common/getParmBaseConstantConf');
const queryTechnicMarketProductListReq = await usePost('/api/accept/queryTechnicMarketProductList');
const formRef = ref();
const { service } = useRuntimeConfig().public || {};
// 确认弹窗显示隐藏
const openVisible = ref<boolean>(false);
const specialStatus = [
  { value: '1', label: '暂存' },
  { value: '2', label: '审批中' },
  { value: '3', label: '生效' },
  { value: '4', label: '失效' },
  { value: '5', label: '已下发' },
  { value: '6', label: '已拒绝' },
];

const formData = reactive({
  specialPromiseDept: '',
  technicProductCode: '',
  marketProductCodeList: [],
  specialPromiseName: '',
  specialPromiseType: '',
  specialPromiseStatusList: ['1', '2', '3'],
});

const formRules = reactive({
  specialPromiseDept: [{ required: true, message: '请选择机构' }],
  specialPromiseStatusList: [{ required: true, message: '请选择状态' }],
});

const specialPromiseTypeList = ref<Array<SelectOptions>>([]);
const getBaseInfo = async () => {
  try {
    const res = await getParmBaseConstantConfReq.fetchData(['specialPromiseType']);
    if (res && res.code === SUCCESS_CODE && res.data) {
      specialPromiseTypeList.value = res.data.specialPromiseTypeList || [];
    } else {
      specialPromiseTypeList.value = [];
    }
  } catch (err) {
    console.log(err);
  }
};
// 查询技术产品列表
const allProductList = ref<SelectOptions[]>([]); // 全量产品下拉选项
const marketProductOptions = computed(() => allProductList.value.find((productItem) => productItem.value === formData.technicProductCode)?.children || []);
const queryProductList = async () => {
  try {
    const res = await queryTechnicMarketProductListReq.fetchData({ departmentCode: inputValue.value });
    if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
      allProductList.value = res.data;
    } else {
      allProductList.value = [];
    }
  } catch (err) {
    console.log(err);
  }
};
const handleTechnic = () => {
  formData.marketProductCodeList = [];
};

// 页面一进入就查询基础数据
getBaseInfo();

const expand = ref(true);
const columns: TableColumnsType = [
  { title: '机构', dataIndex: 'departmentName' },
  { title: '市场产品', dataIndex: 'marketProductName' },
  { title: '特约编码', dataIndex: 'specialPromiseCode' },
  { title: '特约名称', dataIndex: 'specialPromiseName' },
  { title: '特约类型', dataIndex: 'specialPromiseTypeName' },
  { title: '特约内容摘要', dataIndex: 'specialPromiseDesc' },
  { title: '修改时间', dataIndex: 'updateTime' },
  { title: '生效状态', dataIndex: 'specialPromiseStatusName' },
  { title: '创建人', dataIndex: 'createdBy' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];

const dataSource = ref([]);

// const changeDeptCode = (newVal: string) => {
//   formData.specialPromiseDept = newVal;
// };

const addSpecial = () => {
  router.push({
    path: '/specialEdit',
    query: { time: new Date().getTime() },
  });
};

const resetForm = () => {
  formRef.value?.resetFields();
};

const submit = () => {
  pagination.total = 0;
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
};

const loading = ref(false);
const refresh = () => {
  formRef.value?.validate().then(() => {
    loading.value = true;
    querySpecialPromiseAbstractListReq
      .fetchData({
        specialPromiseDept: formData.specialPromiseDept,
        technicProductCode: formData.technicProductCode,
        marketProductCodeList: formData.marketProductCodeList,
        specialPromiseName: formData.specialPromiseName,
        specialPromiseType: formData.specialPromiseType,
        specialPromiseStatusList: formData.specialPromiseStatusList,
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
      })
      .then((res) => {
        if (res && res.code === SUCCESS_CODE) {
          const { list, total = 0, pageNum = 1, pageSize = 10 } = res.data;
          dataSource.value = list;
          pagination.total = total;
          pagination.current = pageNum;
          pagination.pageSize = pageSize;
        } else {
          dataSource.value = [];
          pagination.total = 0;
          pagination.current = 1;
          pagination.pageSize = 10;
        }
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

const { pagination } = usePagination(refresh);

const initFlag = ref(false);
watchEffect(() => {
  if (formData.specialPromiseDept && !initFlag.value) {
    refresh();
    initFlag.value = true;
  }
});

const router = useRouter();

// 跳转详情页面
const handleView = (item: Record<string, string>) => {
  router.push({
    path: '/specialDetail',
    query: { id: item.idSpecialPromiseDefine },
  });
};

// 编辑
const handleEdit = (item: Record<string, string>) => {
  router.push({
    path: '/specialEdit',
    query: { id: item.idSpecialPromiseDefine, status: item.specialPromiseStatus, code: item.specialPromiseCode, time: new Date().getTime() },
  });
};

// 生效
const handleTransferTake = (item: Record<string, string>) => {
  const params = {
    idSpecialPromiseDefine: item.idSpecialPromiseDefine,
    status: item.specialPromiseStatus,
  };
  $postOnClient(`/gateway${service.accept}/specialPromise/validTransferSpecialPromise`, params).then((res) => {
    if (res?.code === SUCCESS_CODE) {
      message.success(res?.msg);
      refresh();
    } else {
      message.error(res?.msg as string);
    }
  });
};
// 失效
const transferInfo = ref({
  transferVisible: false,
  idSpecialPromiseDefine: '',
  status: '',
  operateReason: '',
});
// SpecialListItem
const handleTransferLose = (item: Record<string, string>) => {
  transferInfo.value = {
    transferVisible: true,
    idSpecialPromiseDefine: item.idSpecialPromiseDefine,
    status: item.specialPromiseStatus,
    operateReason: '',
  };
};

const transferSpecialStatus = () => {
  if (!transferInfo.value.operateReason) {
    message.error('请输入失效原因');
  } else {
    const params = {
      idSpecialPromiseDefine: transferInfo.value.idSpecialPromiseDefine,
      status: transferInfo.value.status,
      operateReason: transferInfo.value.operateReason,
    };
    $postOnClient(`/gateway${service.accept}/specialPromise/validTransferSpecialPromise`, params).then((res) => {
      if (res?.code === SUCCESS_CODE) {
        message.success(res?.msg);
        transferInfo.value.transferVisible = false;
        refresh();
      } else {
        message.error(res?.msg || '');
      }
    });
  }
};

// 删除
const itemObj = ref<Record<string, string>>({});
const handleDelete = (item: Record<string, string>) => {
  itemObj.value = item;
  openVisible.value = true;
  // Modal.confirm({
  //   title: '温馨提示',
  //   content: '即将删除，请谨慎操作！',
  //   class: 'bell-bg',
  //   centered: true,
  //   onOk() {

  //   },
  // });
};
// 确认删除
const handleConfirmOk = async () => {
  try {
    $getOnClient(`/gateway${service.accept}/specialPromise/deleteSpecialPromise`, { idSpecialPromiseDefine: itemObj.value?.idSpecialPromiseDefine }).then((res) => {
      const { code, msg = '' } = res || {};
      if (code === SUCCESS_CODE) {
        message.success(msg);
        openVisible.value = false;
        refresh();
      } else {
        message.error(msg);
      }
    });
  } catch (error) {
    console.log(error);
  }
};
interface DeptOptionsType {
  label: string;
  value: string;
}
const deptOptions = ref<DeptOptionsType[]>([]);

const filterOption = (input: string, option: DeptOptionsType) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
// 根据登录用户角色返回所有二级机构和总部机构
const deptOptionsClone = ref<DeptOptionsType[]>([]);
const querySecondDepartmentListReq = await useGet(`/gateway${service.accept}/specialPromise/querySecondDepartmentList`);
const querySecondDepartmentList = async () => {
  try {
    const res = await querySecondDepartmentListReq.fetchData();
    if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
      deptOptions.value = res.data.map((val: { departmentName: string; departmentCode: string }) => {
        return {
          label: val.departmentName,
          value: val.departmentCode,
        };
      });
      deptOptionsClone.value = cloneDeep(deptOptions.value);
      if (deptOptions.value?.length > 0) {
        // 由于后端重新提供了接口，所以这里必须有值才能请求列表接口
        formData.specialPromiseDept = deptOptionsClone.value?.[0]?.value;
      } else {
        formData.specialPromiseDept = '';
      }
    } else {
      deptOptions.value = [];
    }
  } catch (err) {
    console.log(err);
  }
};
const inputValue = ref('');
watch(
  () => formData.specialPromiseDept,
  (val) => {
    if (val) {
      inputValue.value = val;
    }
  },
  { immediate: true },
);
// 机构输入框输入值,前端做搜索，如果清空值则显示初始化值
const searchChanges = () => {
  if (inputValue.value) {
    deptOptions.value = deptOptionsClone.value.filter((item: DeptOptionsType) => item.label.toLowerCase().indexOf(inputValue.value.toLowerCase()) >= 0);
    formData.specialPromiseDept = deptOptions.value[0]?.value;
    queryProductList();
    formData.technicProductCode = '';
    formData.marketProductCodeList = [];
  }
};
const searchChange = debounce(searchChanges, 500);
const blurChange = () => {
  if (inputValue.value) {
    formData.specialPromiseDept = inputValue.value;
    formRef.value.validateFields('specialPromiseDept');
  }
  if (deptOptions.value?.length === 0) {
    formData.specialPromiseDept = '';
  }
};
// 如果机构选择框搜索值跟当前选中值不相同，则数组为初始化全部值
const searchSelectChange = (val: string) => {
  if (val !== formData.specialPromiseDept) {
    deptOptions.value = deptOptionsClone.value;
  }
};
onActivated(() => {
  refresh();
});
onMounted(async () => {
  await querySecondDepartmentList();
  queryProductList();
});
</script>

<style lang="less" scoped>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type='number'] {
  -moz-appearance: textfield;
}
</style>
<style lang="less">
.ant-tooltip.tooltipCss {
  width: inherit;
  max-width: 600px;
  .ant-tooltip-inner {
    max-height: 350px;
    overflow-y: auto;
  }
}
</style>
