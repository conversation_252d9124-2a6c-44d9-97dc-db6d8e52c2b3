export interface FormState {
  departmentNo: string; // 机构编码
  insuranceProposalName: string; // 方案名称
  planAttributeSameMain: boolean; // 附加险是否与主险一致
  planList: PlanItem[];
  marketProductNo?: string; // 市场产品
  productVersionNo?: string; // 产品版本号
  subsidyType?: string; // 补贴类型
}

export interface Option {
  label: string;
  value: string;
}

export interface PlanItem {
  planCode: string;
  planName: string;
  isMain: string;
  centralFinance: string;
  provincialFinance: string;
  cityFinance: string;
  countyFinance: string;
  farmersFinance: string;
  otherFinance: string;
  feesInfoSameFlag: boolean | string;// 费用是否同主险，保存时需要转换Y/N
  unitInsuredAmount: string;
  expectPremiumRate: string;
  unitPrimium: string;
  selected?: boolean;// 附加险是否被选中，只在前端使用，不传给后端
}
