<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="formRef" :colon="false" class="flex-grow" :model="formState" :label-col="{ style: { width: '80px' } }" :rules="formRules">
          <a-row :gutter="16">
            <a-col :span="10">
              <a-form-item label="机构" name="departmentCode">
                <department-search v-model:contain-child-depart="formState.containChildDepart" :dept-code="formState.departmentCode" :show-child-depart="true" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="16">
            <a-col :span="8">
              <a-form-item label="创建时间" name="createDate">
                <a-range-picker v-model:value="formState.createDate" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="市场产品" name="productCode">
                <ProductSelect v-model:value="formState.productCode" :department-code="formState.departmentCode" :encode-key="formState.riskCode" @product-change="handleProduct" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="补贴类型" name="subsidyType">
                <a-select v-model:value="formState.subsidyType" disabled :options="subsidyTypeOptions" :allow-clear="true" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="16">
            <a-col :span="24">
              <a-form-item label="标的类型">
                <RiskCodeSelect v-model:value="formState.riskCode" :department-code="formState.departmentCode" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="w-[50px]">
          <form-fold v-model="expand" />
        </div>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button @click="resetForm">重置</a-button>
        <a-button type="primary" ghost @click="submit">查询</a-button>
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-16px">
        <div class="leading-[32px] text-[16px] text-[rgba(0,0,0,0.9)] font-bold">查询结果</div>
        <a-button type="primary" @click="createPlan"> 新建 </a-button>
      </div>
      <a-table :columns="listColumns" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content' }" :pagination="pagination">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'operation'">
            <div>
              <a-button type="link" @click="editPlan(record.id)">查看</a-button>
              <a-button type="link" @click="deletePlan(record.id)">注销</a-button>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs, { type Dayjs } from 'dayjs';
import type { ColumnsType } from 'ant-design-vue/es/table';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import FormFold from '@/components/ui/FormFold.vue';
import { $postOnClient, $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { usePagination } from '@/composables/usePagination';
import type { SelectOptions } from '@/apiTypes/apiCommon';

const listColumns: ColumnsType<Record<string, string>> = [
  { title: '出单机构', dataIndex: 'departmentName' },
  { title: '标的', dataIndex: 'riskobjInformation' },
  { title: '方案名称', dataIndex: 'insuranceProposalName' },
  { title: '市场产品', dataIndex: 'marketProductName' },
  { title: '补贴类型', dataIndex: 'subsidyTypeName' },
  { title: '创建时间', dataIndex: 'createdDate' },
  { title: '申请人', dataIndex: 'createdBy' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];

interface FormState {
  departmentCode: string; // 机构编码
  containChildDepart: boolean; // 是否包含下级机构
  createDate: [Dayjs, Dayjs]; // 创建时间
  productCode?: string; // 市场产品
  subsidyType?: string; // 补贴类型
  riskCode: string; // 标的
}
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));

const formState = reactive<FormState>({
  departmentCode: defaultDeptCode.value,
  containChildDepart: true,
  createDate: [dayjs().subtract(1, 'month'), dayjs()],
  productCode: undefined,
  subsidyType: undefined,
  riskCode: '',
});

const productVersionNo = ref('');
const handleProduct = (value: string, option: { version: string }) => {
  if (!value) {
    productVersionNo.value = '';
    formState.subsidyType = '';
    return;
  }
  productVersionNo.value = option.version;
  getSubsidyType(value, option.version);
};

const getSubsidyType = async (productCode: string, version: string) => {
  const url = gateWay + service.accept + '/web/applicationForm/productInfos';
  const data = {
    productCode: productCode,
    version: version,
  };
  const res = await $postOnClient<{ subsidyType: string }>(url, data);
  if (res && res.code === SUCCESS_CODE) {
    formState.subsidyType = res.data.subsidyType || '';
  }
};

const formRef = ref();
const formRules = reactive({
  departmentCode: [{ required: true }],
  createDate: [{ required: true, message: '请选择创建时间' }],
});

// 搜索项是否展开
const expand = ref(true);

const changeDeptCode = (val: string) => {
  formState.departmentCode = val;
};

const resetForm = () => {
  formRef.value?.resetFields();
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
};

const submit = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
};

const router = useRouter();
// 新建方案
const createPlan = () => {
  router.push('/insurePlanEdit');
};
// 编辑方案
const editPlan = (id: string) => {
  router.push({
    path: '/insurePlanEdit',
    query: { id },
  });
};
// 删除方案
const deletePlan = async (id: string) => {
  Modal.confirm({
    title: '提醒',
    content: '是否确认删除当前保险方案',
    async onOk() {
      const fetchurl = gateWay + service.accept + '/sys/insurance/delPlan';
      const res = await $getOnClient(fetchurl, { id });
      if (res && res.code === SUCCESS_CODE) {
        message.success('删除保险方案成功');
        refresh();
      } else {
        message.error('删除保险方案失败');
      }
    },
  });
};

const dataSource = ref([]);
const loading = ref(false);
const refresh = () => {
  const getData = async () => {
    loading.value = true;
    const fetchurl = gateWay + service.accept + '/sys/insurance/qryPlanList';
    try {
      const res = await $postOnClient(fetchurl, {
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        departmentNo: formState.departmentCode,
        marketProductNo: formState.productCode,
        productVersionNo: productVersionNo.value, // 产品版本
        subsidyType: formState.subsidyType, // 补贴类型
        containChildDepart: formState.containChildDepart ? 'Y' : 'N', // Y/N 是否包含下级
        beginTime: formState.createDate[0].format('YYYY-MM-DD'),
        endTime: formState.createDate[1].format('YYYY-MM-DD'),
      });
      if (res && res.code === SUCCESS_CODE && res.data) {
        const { records = [], current = 1, size = 10, total = 0 } = res.data;
        dataSource.value = records;
        pagination.current = current;
        pagination.total = total;
        pagination.size = size;
      }
    } catch (e) {
      console.log(e);
    }
    loading.value = false;
  };
  formRef.value?.validate().then(() => {
    getData();
  });
};

const { pagination } = usePagination(refresh);

const subsidyTypeOptions = ref<SelectOptions[]>([]);

const { gateWay, service } = useRuntimeConfig().public || {};
const getSubsidyTypeOptions = async () => {
  const res = await $postOnClient<{ subsidyTypeList: Array<SelectOptions> }>('/api/common/getParmBaseConstantConf', ['subsidyType']);
  if (res && res.code === SUCCESS_CODE && res.data) {
    subsidyTypeOptions.value = res.data.subsidyTypeList || [];
  } else {
    subsidyTypeOptions.value = [];
  }
};

onMounted(() => {
  getSubsidyTypeOptions();
});
const route = useRoute();
watchEffect(() => {
  if (route.query.refresh === 'Y') {
    submit();
    router.replace({
      path: '/insurePlanManage',
      query: {},
    });
  }
});
</script>
