<template>
  <div class="p-16px m-14px bg-white rounded">
    <a-form ref="formRef" :colon="false" :model="formState" :disabled="formDisabled" :label-col="{ style: { width: '80px' } }">
      <InfoGroup title="基础信息">
        <div class="px-16px">
          <a-row :gutter="48">
            <a-col :span="8">
              <a-form-item label="机构" name="departmentNo" :rules="[{ required: true }]">
                <department-search :dept-code="formState.departmentNo" :show-child-depart="false" :disabled="formDisabled" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="48">
            <a-col :span="8">
              <a-form-item label="产品" name="marketProductNo" :rules="[{ required: true }]">
                <ProductSelect v-model:value="formState.marketProductNo" :department-code="formState.departmentNo" @product-change="handleProductChange" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="补贴分类" name="subsidyType" :rules="[{ required: true }]">
                <a-select v-model:value="formState.subsidyType" disabled allow-clear placeholder="请选择" :options="subsidyTypeOptions" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </InfoGroup>
      <InfoGroup title="方案配置" class="mt-16px">
        <div class="bg-white rounded p-16px">
          <div class="module-title">方案信息</div>
          <div class="grid grid-cols-3 mt-[12px] gap-x-[48px]">
            <a-form-item label="方案名称" name="insuranceProposalName" :rules="[{ required: true }]">
              <a-input :maxlength="10" :value="formState.insuranceProposalName" @change="handleNameChange" />
            </a-form-item>
          </div>
          <div v-for="(item, index) in formState.planList" :key="item.planCode">
            <!-- 主险名称 -->
            <div v-if="item.isMain === '1'" class="text-[rgba(0,0,0,0.9)] font-semibold mb-[12px]">{{ item.planName }}</div>
            <!-- 附加险名称 -->
            <div v-else class="flex mb-[12px]">
              <a-checkbox v-model:checked="item.selected" @change="() => handleSelected(item.selected as boolean, index)">{{ item.planName }}</a-checkbox>
              <a-checkbox v-show="item.selected" v-model:checked="item.feesInfoSameFlag as boolean" @change="() => handleSameFlag(item.feesInfoSameFlag as boolean, index)">同主险 </a-checkbox>
            </div>
            <!-- 附加险只有同主险不选中时才展示 -->
            <div v-if="item.isMain === '1' || (item.selected && !item.feesInfoSameFlag)" class="grid grid-cols-3 gap-x-[48px]">
              <a-form-item label="单位保额" :name="['planList', index, 'unitInsuredAmount']" :rules="[{ required: true }, { pattern: /^\d{1,9}(\.\d{0,4})?$/, message: '最大录入9位整数，4位小数', trigger: 'change' }]">
                <a-input v-model:value="item.unitInsuredAmount" @change="(e) => computedUnitPrimium(index)" />
              </a-form-item>
              <a-form-item label="费率" :name="['planList', index, 'expectPremiumRate']" :rules="[{ required: true }, { pattern: /^(?:100(?:\.0{1,4})?|[0-9]{1,2}(?:\.\d{1,4})?)$/, message: '费率必须在0到100之间，且小数位不能超过4位' }]">
                <a-input v-model:value="item.expectPremiumRate" @change="(e) => computedUnitPrimium(index)">
                  <template #addonAfter>%</template>
                </a-input>
              </a-form-item>
              <a-form-item label="单位保费" :name="['planList', index, 'unitPrimium']" :rules="[{ pattern: /^\d{1,9}(\.\d{0,6})?$/, message: '最大录入9位整数，6位小数', trigger: 'change' }]">
                <a-input v-model:value="item.unitPrimium" />
              </a-form-item>
            </div>
          </div>
          <div class="divide-line" />
          <div class="flex justify-between py-[20px]">
            <div class="text-[rgba(0,0,0,0.55)]">保费来源配置</div>
            <div>
              <span class="text-[rgba(0,0,0,0.55)]">附加保费来源：</span>
              <a-checkbox v-model:checked="formState.planAttributeSameMain">与主险一致</a-checkbox>
            </div>
          </div>
          <a-table :columns="sourceColumns" :data-source="dataSource" :pagination="false" :scroll="{ x: 'max-content' }">
            <template #bodyCell="{ column, index }">
              <template v-if="column.dataIndex === 'centralFinance'">
                <a-form-item label="" :validate-status="dataRules[index].centralFinance.status" :help="dataRules[index].centralFinance.help">
                  <a-input v-model:value="dataSource[index].centralFinance" :style="{ width: '100px' }" @change="() => validateFinance(dataSource[index].centralFinance, dataRules[index].centralFinance)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'provincialFinance'">
                <a-form-item label="" :validate-status="dataRules[index].provincialFinance.status" :help="dataRules[index].provincialFinance.help">
                  <a-input v-model:value="dataSource[index].provincialFinance" :style="{ width: '100px' }" @change="() => validateFinance(dataSource[index].provincialFinance, dataRules[index].provincialFinance)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'cityFinance'">
                <a-form-item label="" :validate-status="dataRules[index].cityFinance.status" :help="dataRules[index].cityFinance.help">
                  <a-input v-model:value="dataSource[index].cityFinance" :style="{ width: '100px' }" @change="() => validateFinance(dataSource[index].cityFinance, dataRules[index].cityFinance)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'countyFinance'">
                <a-form-item label="" :validate-status="dataRules[index].countyFinance.status" :help="dataRules[index].countyFinance.help">
                  <a-input v-model:value="dataSource[index].countyFinance" :style="{ width: '100px' }" @change="() => validateFinance(dataSource[index].countyFinance, dataRules[index].countyFinance)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'farmersFinance'">
                <a-form-item label="" :validate-status="dataRules[index].farmersFinance.status" :help="dataRules[index].farmersFinance.help">
                  <a-input v-model:value="dataSource[index].farmersFinance" :style="{ width: '100px' }" @change="() => validateFinance(dataSource[index].farmersFinance, dataRules[index].farmersFinance)" />
                </a-form-item>
              </template>
              <template v-if="column.dataIndex === 'otherFinance'">
                <a-form-item label="" :validate-status="dataRules[index].otherFinance.status" :help="dataRules[index].otherFinance.help">
                  <a-input v-model:value="dataSource[index].otherFinance" :style="{ width: '100px' }" @change="() => validateFinance(dataSource[index].otherFinance, dataRules[index].otherFinance)" />
                </a-form-item>
              </template>
            </template>
          </a-table>
          <!-- <div class="text-[#fa5151] mt-[12px]">
            录单时补贴比例之和需等于100%！
          </div> -->
        </div>
        <div class="bg-white rounded p-16px mt-16px">
          <div class="module-title">证明附件</div>
          <div class="rounded bg-[#f8f8f8] p-16px mt-[12px]">
            <a-form-item label="" :validate-status="fileValidStatus" :help="fileValidHelp">
              <a-upload :multiple="false" :show-upload-list="false" :action="handleFileChange" :before-upload="beforeUpload" @change="handleFileChange">
                <a-button>
                  <template #icon>
                    <VueIcon :icon="IconTongyongShangchuanFont" />
                  </template>
                  <span class="ml-[2px]">附件上传</span>
                </a-button>
              </a-upload>
            </a-form-item>
            <div class="text-xs text-[rgba(0,0,0,0.55)]">支持格式：BMP, DIB, JPG, JPEG, JPE, JFIF, GIF, TIF, TIFF, PNG, DOC, XLS, PDF, MSG, HTM, PPT, PPTX, RAR, ZIP, TXT, XLSX, DOCX</div>
            <div v-for="fileInfo in fileInfos" :key="fileInfo.uploadPath" class="text-12px text-[#4E6085] mt-[13px]">
              <VueIcon :icon="IconAttachmentFont" />
              <span class="ml-[5px] cursor-pointer" @click="downloadFile(fileInfo)">{{ fileInfo.originName }}</span>
              <a-button type="link" @click="clearFileInfos">删除</a-button>
            </div>
          </div>
        </div>
        <div class="mt-[12px] text-xs text-[rgba(0,0,0,0.55)] leading-[22px]">
          <div>方案规则说明：</div>
          <div>配置内容将默认带出到出单页面，对于数值不确定的字段，请不要填写，以免影响出单。</div>
          <div>举例：若单位保费=单位保额*费率，则单位保费留空即可，系统会自动计算，若单位保费为固定值，请如实填写。</div>
        </div>
      </InfoGroup>
    </a-form>
  </div>
  <div class="fixed-footer space-x-8px">
    <a-button @click="goBack">返回</a-button>
    <a-button type="primary" :disabled="formDisabled" @click="submit">保存</a-button>
  </div>
</template>

<script lang="ts" setup>
import { IconTongyongShangchuanFont, IconAttachmentFont } from '@pafe/icons-icore-agr-an';
import type { ChangeEvent } from 'ant-design-vue/es/_util/EventInterface';
import type { FormState, Option } from './insurePlanManage.d';
import { useUserStore } from '@/stores/useUserStore';
import InfoGroup from '@/components/ui/InfoGroup.vue';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import { $post, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { downloadBlob } from '@/utils/tools';
import type { SelectOptions } from '@/apiTypes/apiCommon';

const sourceColumns = ref([
  { title: '险种编码', dataIndex: 'planCode' },
  { title: '险种名称', dataIndex: 'planName' },
  { title: '中央补贴(%)', dataIndex: 'centralFinance' },
  { title: '省级补贴(%)', dataIndex: 'provincialFinance' },
  { title: '地方补贴(%)', dataIndex: 'cityFinance' },
  { title: '县级补贴(%)', dataIndex: 'countyFinance' },
  { title: '农户自缴(%)', dataIndex: 'farmersFinance' },
  { title: '其他补贴(%)', dataIndex: 'otherFinance' },
]);

const formDisabled = ref(false);
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));

const formRef = ref();

const formState = reactive<FormState>({
  departmentNo: defaultDeptCode.value,
  planAttributeSameMain: false,
  insuranceProposalName: '',
  marketProductNo: undefined,
  productVersionNo: undefined,
  subsidyType: undefined,
  planList: [],
});

const fileInfos = ref<
  {
    documentFormat: string;
    documentName: string;
    documentSize: string;
    originName: string;
    uploadPath: string;
  }[]
>([]);

const fileValidStatus = ref('');
const fileValidHelp = ref('');

const subsidyTypeOptions = ref<Option[]>([]);

const dataSource = computed(() => {
  const filterList = formState.planList.filter((item) => (formState.planAttributeSameMain ? item.isMain === '1' : item.isMain === '1' || item.selected));
  return filterList;
});

const dataRules = ref<{ [key: string]: { status: string; help: string } }[]>([]);

const handleNameChange = (e: ChangeEvent) => {
  formState.insuranceProposalName = e.target.value?.replace(/ /g, '') || '';
};

watchEffect(() => {
  if (dataSource.value.length > 0) {
    dataRules.value = dataSource.value.map(() => ({
      centralFinance: { status: '', help: '' },
      provincialFinance: { status: '', help: '' },
      cityFinance: { status: '', help: '' },
      countyFinance: { status: '', help: '' },
      farmersFinance: { status: '', help: '' },
      otherFinance: { status: '', help: '' },
    }));
  }
});

const tableInputValidate = computed(() => dataRules.value.every(({ centralFinance, provincialFinance, cityFinance, countyFinance, farmersFinance, otherFinance }) => centralFinance.status.length === 0 && provincialFinance.status.length === 0 && cityFinance.status.length === 0 && countyFinance.status.length === 0 && farmersFinance.status.length === 0 && otherFinance.status.length === 0));

const validateFinance = (value: string, target: { status: string; help: string }) => {
  const pattern = /^(?:100(?:\.0{1,4})?|[0-9]{1,2}(?:\.\d{1,4})?)$/;
  if (pattern.test(value) || value === '') {
    target.status = '';
    target.help = '';
  } else {
    target.status = 'error';
    target.help = '请输入0-100范围内的数字且小数位不能超过4位';
  }
};

// 允许的文件类型
const allowedFileTypes = ['image/bmp', 'image/dib', 'image/jpeg', 'image/jpg', 'image/jpe', 'image/jfif', 'image/gif', 'image/tiff', 'image/tif', 'image/png', 'application/msword', 'application/vnd.ms-excel', 'application/pdf', 'application/vnd.ms-outlook', 'text/html', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/x-rar-compressed', 'application/zip', 'text/plain', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

// 文件扩展名映射
const fileExtensions = {
  BMP: 'image/bmp',
  DIB: 'image/dib',
  JPG: 'image/jpeg',
  JPEG: 'image/jpeg',
  JPE: 'image/jpe',
  JFIF: 'image/jfif',
  GIF: 'image/gif',
  TIF: 'image/tiff',
  TIFF: 'image/tiff',
  PNG: 'image/png',
  DOC: 'application/msword',
  XLS: 'application/vnd.ms-excel',
  PDF: 'application/pdf',
  MSG: 'application/vnd.ms-outlook',
  HTM: 'text/html',
  PPT: 'application/vnd.ms-powerpoint',
  PPTX: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  RAR: 'application/x-rar-compressed',
  ZIP: 'application/zip',
  TXT: 'text/plain',
  XLSX: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  DOCX: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
};

// 文件上传前验证
const beforeUpload = (file: File) => {
  const isAllowedType = allowedFileTypes.includes(file.type);
  const fileExtension = file.name.split('.').pop()?.toUpperCase();
  const isAllowedExtension = fileExtension ? Object.keys(fileExtensions).includes(fileExtension) : false;

  if (!isAllowedType && !isAllowedExtension) {
    message.error('文件格式不支持，请上传正确格式的文件！');
    return false;
  }

  return true;
};

// 监听上传文件
const handleFileChange = (file: File) => {
  const formData = new FormData();
  formData.append('files', file);
  const url = gateWay + service.accept + '/sys/insurance/uploadFile ';
  $postOnClient(url, formData).then((res) => {
    if (res && Array.isArray(res.data)) {
      fileInfos.value = res.data || [];
    }
  });
  return Promise.reject();
};

// 删除文件
const clearFileInfos = () => {
  fileInfos.value = [];
};

// 机构赋值
const changeDeptCode = (val: string) => {
  formState.departmentNo = val;
};

// 产品变化，获取险种数据
const handleProductChange = (value: string, option: { value: string; version: string }) => {
  formState.productVersionNo = option.version;
  getSubsidyType(value, option.version);
  // 非查看，才请求险种
  if (!route.query.id) {
    fetchPlanList({ productCode: option.value, version: option.version });
  }
};

const getSubsidyType = async (productCode: string, version: string) => {
  const url = gateWay + service.accept + '/web/applicationForm/productInfos';
  const data = {
    productCode: productCode,
    version: version,
  };
  const res = await $postOnClient<{ subsidyType: string }>(url, data);
  if (res && res.code === SUCCESS_CODE) {
    formState.subsidyType = res.data.subsidyType || '';
  }
};

watchEffect(() => {
  if (fileInfos.value.length > 0) {
    fileValidStatus.value = '';
    fileValidHelp.value = '';
  }
});

// 获取险种数据函数
const fetchPlanList = async (data: { productCode: string; version: string }) => {
  const url = gateWay + service.accept + '/web/applicationForm/productInfos';
  const res = await $postOnClient(url, data);
  if (res && res.data) {
    formState.planList = res.data.planList.map((item: { planCode: string; planName: string; isMain: string }) => ({
      planCode: item.planCode,
      planName: item.planName,
      isMain: item.isMain,
      centralFinance: '',
      provincialFinance: '',
      cityFinance: '',
      countyFinance: '',
      farmersFinance: '',
      otherFinance: '',
      feesInfoSameFlag: true, // 费用是否同主险
      unitInsuredAmount: '',
      expectPremiumRate: '',
      unitPrimium: '',
      selected: false, // 辅助属性
    }));
  }
};

// 计算保费
const computedUnitPrimium = (index: number) => {
  const unitInsuredAmount = Number(formState.planList[index].unitInsuredAmount);
  const expectPremiumRate = Number(formState.planList[index].expectPremiumRate);
  if (unitInsuredAmount > 0 && expectPremiumRate > 0) {
    formState.planList[index].unitPrimium = String((unitInsuredAmount * expectPremiumRate) / 100);
  } else {
    formState.planList[index].unitPrimium = '';
  }
};

const handleSelected = (checked: boolean, index: number) => {
  const mainPlan = formState.planList.find((item) => item.isMain === '1');
  if (mainPlan) {
    if (checked && formState.planList[index].feesInfoSameFlag) {
      formState.planList[index].unitInsuredAmount = mainPlan.unitInsuredAmount;
      formState.planList[index].expectPremiumRate = mainPlan.expectPremiumRate;
      formState.planList[index].unitPrimium = mainPlan.unitPrimium;
    } else {
      formState.planList[index].unitInsuredAmount = '';
      formState.planList[index].expectPremiumRate = '';
      formState.planList[index].unitPrimium = '';
    }
  }
};

// 单位保额，费率，单位保费是否同主险
const handleSameFlag = (checked: boolean, index: number) => {
  const mainPlan = formState.planList.find((item) => item.isMain === '1');
  if (mainPlan) {
    if (checked) {
      formState.planList[index].unitInsuredAmount = mainPlan.unitInsuredAmount;
      formState.planList[index].expectPremiumRate = mainPlan.expectPremiumRate;
      formState.planList[index].unitPrimium = mainPlan.unitPrimium;
    } else {
      formState.planList[index].unitInsuredAmount = '';
      formState.planList[index].expectPremiumRate = '';
      formState.planList[index].unitPrimium = '';
    }
  }
};

const submit = async () => {
  try {
    if (fileInfos.value.length === 0) {
      fileValidStatus.value = 'error';
      fileValidHelp.value = '请上传证明附件';
    }
    await formRef.value.validate();
    if (fileInfos.value.length > 0 && tableInputValidate.value) {
      const cloneForm = {
        ...formState,
        fileDocumentVOList: fileInfos.value,
        planAttributeSameMain: formState.planAttributeSameMain ? 'Y' : 'N',
      };
      cloneForm.planList = formState.planList
        .filter((plan) => plan.isMain === '1' || plan.selected)
        .map((item) => ({
          planCode: item.planCode,
          planName: item.planName,
          isMain: item.isMain,
          centralFinance: item.centralFinance,
          provincialFinance: item.provincialFinance,
          cityFinance: item.cityFinance,
          countyFinance: item.countyFinance,
          farmersFinance: item.farmersFinance,
          otherFinance: item.otherFinance,
          unitInsuredAmount: item.unitInsuredAmount,
          expectPremiumRate: item.expectPremiumRate,
          unitPrimium: item.unitPrimium,
          feesInfoSameFlag: item.feesInfoSameFlag ? 'Y' : 'N',
        }));
      const fetchurl = `${gateWay}${service.accept}/sys/insurance/savePlan`;
      const res = await $postOnClient(fetchurl, cloneForm);

      if (res && res.code === SUCCESS_CODE) {
        message.success(res.msg);
        goBack();
      } else {
        message.error(res?.msg || '接口请求出错');
      }
    }
  } catch (error) {
    console.error('提交表单时发生错误:', error);
  }
};

// 查询详情
const queryDetail = async (id: string) => {
  const fetchurl = gateWay + service.accept + '/sys/insurance/qryPlanDetail';
  const res = await $post(fetchurl, { id });
  if (res && res.code === SUCCESS_CODE && res.data) {
    formState.departmentNo = res.data.departmentNo;
    formState.insuranceProposalName = res.data.insuranceProposalName;
    formState.marketProductNo = res.data.marketProductNo;
    formState.subsidyType = res.data.subsidyType;
    formState.planAttributeSameMain = res.data.planAttributeSameMain === 'Y';
    formState.planList = res.data.planList.map((item: { feesInfoSameFlag: string }) => {
      return {
        ...item,
        feesInfoSameFlag: item.feesInfoSameFlag === 'Y',
        selected: true,
      };
    });
    fileInfos.value = res.data.fileDocumentVOList;
  }
};

const downloadFile = (fileInfo: Record<string, string>) => {
  const url = gateWay + service.accept + '/sys/insurance/downloadFile';
  const params = {
    documentName: fileInfo.documentName,
    uploadPath: fileInfo.uploadPath,
    documentFormat: fileInfo.documentFormat,
  };
  $postOnClient(url, params).then((res: unknown) => {
    downloadBlob(res as Blob, fileInfo.originName, fileInfo.documentFormat);
  });
};

const goBack = () => {
  router.push({
    path: '/insurePlanManage',
    query: {
      refresh: 'Y',
    },
  });
};

const route = useRoute();
const router = useRouter();
const { gateWay, service } = useRuntimeConfig().public || {};

watch(
  () => formState.subsidyType,
  (subsidyType) => {
    if (subsidyType === '03') {
      sourceColumns.value = [
        { title: '险种编码', dataIndex: 'planCode' },
        { title: '险种名称', dataIndex: 'planName' },
        { title: '农户自缴(%)', dataIndex: 'farmersFinance' },
        { title: '其他补贴(%)', dataIndex: 'otherFinance' },
      ];
    } else {
      sourceColumns.value = [
        { title: '险种编码', dataIndex: 'planCode' },
        { title: '险种名称', dataIndex: 'planName' },
        { title: '中央补贴(%)', dataIndex: 'centralFinance' },
        { title: '省级补贴(%)', dataIndex: 'provincialFinance' },
        { title: '地方补贴(%)', dataIndex: 'cityFinance' },
        { title: '县级补贴(%)', dataIndex: 'countyFinance' },
        { title: '农户自缴(%)', dataIndex: 'farmersFinance' },
        { title: '其他补贴(%)', dataIndex: 'otherFinance' },
      ];
    }
  },
);

watch(
  () => formState.planAttributeSameMain,
  (value) => {
    const mainPlan = dataSource.value.find((item) => item.isMain === '1');
    if (mainPlan) {
      formState.planList.forEach((item) => {
        if (item.isMain !== '1') {
          // 附加险保费来源同主险
          if (value) {
            item.centralFinance = mainPlan.centralFinance;
            item.cityFinance = mainPlan.cityFinance;
            item.countyFinance = mainPlan.countyFinance;
            item.provincialFinance = mainPlan.provincialFinance;
            item.otherFinance = mainPlan.otherFinance;
            item.farmersFinance = mainPlan.farmersFinance;
          } else {
            item.centralFinance = '';
            item.cityFinance = '';
            item.countyFinance = '';
            item.provincialFinance = '';
            item.otherFinance = '';
            item.farmersFinance = '';
          }
        }
      });
    }
  },
);

const init = () => {
  formState.departmentNo = defaultDeptCode.value;
  formState.planAttributeSameMain = false;
  formState.insuranceProposalName = '';
  formState.marketProductNo = undefined;
  formState.productVersionNo = undefined;
  formState.subsidyType = undefined;
  formState.planList = [];
  fileInfos.value = [];
  if (route.query.id) {
    queryDetail(route.query.id as string);
    formDisabled.value = true;
  } else {
    formDisabled.value = false;
  }
};
// 获取补贴分类基础数据
const getSubsidyTypeOptions = async () => {
  const res = await $postOnClient<{ subsidyTypeList: Array<SelectOptions> }>('/api/common/getParmBaseConstantConf', ['subsidyType']);
  if (res && res.code === SUCCESS_CODE && res.data) {
    subsidyTypeOptions.value = res.data.subsidyTypeList || [];
  } else {
    subsidyTypeOptions.value = [];
  }
};

onMounted(() => {
  getSubsidyTypeOptions();
});

onActivated(() => {
  init();
});

onMounted(() => {
  init();
});
</script>

<style lang="less" scoped>
.module-title {
  font-size: 14px;
  color: #404442;
  font-weight: 600;
}
.module-title::before {
  display: inline-block;
  margin-right: 4px;
  width: 3px;
  height: 10px;
  background-color: #07c160;
  content: '';
}
.divide-line {
  height: 1px;
  width: 100%;
  background: #e6e8eb;
}
.fixed-footer {
  position: fixed;
  height: 50px;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  bottom: 0;
  z-index: 99;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
}
</style>
