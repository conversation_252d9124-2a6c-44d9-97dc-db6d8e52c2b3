<template>
  <div class="mx-[14px] my-[12px] px-[16px] pt-[16px] pb-[20px] bg-[#ffffff] rounded-[6px]">
    <div class="flex space-x-[8px] items-center mb-[30px]">
      <p class="m-0 py-0 pr-[10px]">补传类型：</p>
      <a-select v-model:value="parameterCode" :field-names="{ label: 'parameterName', value: 'parameterCode' }" :allow-clear="true" style="width: 300px" :options="options" placeholder="请选择" :filter-option="filterOption" @change="changeSelect" />
      <a-button v-if="parameterCode" :loading="downLoading" @click="handleDownloadModel">模版下载</a-button>
      <a-upload v-if="parameterCode" v-model:file-list="fileList" :show-upload-list="false" action="" :before-upload="handleBeforeChange" @change="handleUpload">
        <a-button type="primary" :loading="uploading">上传</a-button>
      </a-upload>
    </div>
  </div>
</template>

<script setup lang="ts">
import { message } from '@/components/ui/Message';
import { SUCCESS_CODE } from '@/utils/constants';
import { downloadBlob } from '@/utils/tools';
import { $getOnClient, $postOnClient } from '@/composables/request';

const parameterCode = ref<string | undefined>(undefined);
const downLoading = ref<boolean>(false);
// 下载模版
const handleDownloadModel = async () => {
  try {
    downLoading.value = true;
    await $getOnClient('/gateway/icore-agr-an.compliance/reportResult/downloadExeclTemplate', { type: parameterCode.value }, {
      async onResponse({ response }) {
        let fileName = '';
        const contentDisposition = response.headers.get('Content-Disposition');
        const fileData = response._data;
        const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
        if (contentDisposition) {
          const match = contentDisposition.match(/filename=(.+)/);
          if (match) {
            fileName = decodeURI(match[1]);
          }
        }
        downLoading.value = false;
        downloadBlob(fileData, fileName, fileType);
      },
    });
  } catch (err) {
    console.log(err);
  }
};

// 上传
const fileList = ref([]);

const uploading = ref(false);

const uploadOther = async (e: unknown) => {
  const { file } = e;
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', parameterCode.value);
  try {
    const { code, msg } = await $postOnClient('/gateway/icore-agr-an.compliance/reportResult/uploadFileByType', formData);
    if (code === SUCCESS_CODE) {
      message.success('上传成功');
    } else {
      message.error(msg);
    }
    return true;
  } catch {
    return true;
  }
};
const options = ref<Record<string, string>>();
// 初始化数据
const querySelect = async () => {
  try {
    const { code, msg, data } = await $postOnClient('/gateway/icore-agr-an.administrate/parmCommonParm/querySelect', ['excel_download_upload_type']);
    if (code === SUCCESS_CODE) {
      options.value = data?.selectMaps?.excel_download_upload_type;
    } else {
      message.error(msg);
    }
    return true;
  } catch {
    return true;
  }
};
// 模糊搜索
const filterOption = (input: string, option: Record<string, string>) => {
  return option.parameterName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const changeSelect = () => {
  fileList.value = [];
};
const handleBeforeChange = () => false;

const handleUpload = async (e: unknown) => {
  uploading.value = true;
  await uploadOther(e);
  uploading.value = false;
};
onMounted(() => {
  querySelect();
});
</script>
