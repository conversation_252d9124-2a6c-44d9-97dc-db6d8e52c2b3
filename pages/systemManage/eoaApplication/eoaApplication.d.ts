export interface DataType {
  records: RecordType[];
  total: number;
  current: number;
  size: number;
  eoaDetailUrl?: string;
}
export interface OptionsType {
  value: string;
  label: string;
}

export interface ChildrenType {
  [key: string]: string;
  children: ChildrenType[];
  idParmBaseConstantConf: number;
  label: string;
  disabled: boolean;
  value: string;
}
export interface RecordType {
  idEvntEoaBaseInfo: string; // 主键
  eoaNo: string; // 签报号
  eoaSubject: string; // 签报名称
  createdDate: string; // 申请日期
  eoaStatusCode: string; // 审批状态
  productName: string; // 市场产品
}

export interface DepartmentType {
  farmerlistNo: string;
  departmentName: string;
  departmentCode: string;
  productName: string;
  productCode: string;
  relationBusinessNo: string;
  eoaType: string;
  eoaSubject: string;
  eoaBody: string;
  documentGroupId?: string;
  departmentCode: string;
  productCode: string;
  businessData: {
    estimatedCompletionTime: string;
  };
}
