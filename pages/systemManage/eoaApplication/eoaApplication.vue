<template>
  <a-modal v-model:open="showChooseModal" title="签报场景选择" :width="pxToRem(650)" ok-text="去申请" @ok="chooseEoa">
    <a-form ref="chooseFormRef" :model="chooseData" :colon="false">
      <a-form-item label="场景选择" required name="type">
        <a-select v-model:value="chooseData.type" placeholder="请选择">
          <a-select-option value="1">验真通融申请</a-select-option>
          <!-- <a-select-option value="2">爱农宝核身豁免</a-select-option> -->
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
  <CommonApprovalModal v-if="showAuthenticityModal && chooseData.type === '1'" v-model:open="showAuthenticityModal" title="验真通融申请" approval-chain-type="0" :department-code="formState.departmentCode" eoa-type="T24" @cancel="reset" @ok="handleSubmit">
    <template #content>
      <a-form ref="modelStateRef" :colon="false" :model="modelState">
        <div class="bg-[#f8f8f8] rounded-[4px] px-[16px] py-[16px]">
          <a-form-item label="申请单号" :label-col="labelColStyle" required name="farmerlistNo">
            <a-input v-model:value.trim="modelState.farmerlistNo" placeholder="请输入" @change="debounceFetchDepartmentList" />
          </a-form-item>
          <a-form-item label="机构" required :label-col="labelColStyle" name="departmentName">
            <a-input v-model:value="modelState.departmentName" :disabled="true" placeholder="请输入申请单号获取" />
          </a-form-item>
          <a-form-item label="产品条款" required :label-col="labelColStyle" name="productName">
            <a-input v-model:value="modelState.productName" :disabled="true" placeholder="请输入申请单号获取" />
          </a-form-item>
          <a-form-item label="签报主题" required :label-col="labelColStyle" name="eoaSubject">
            <a-input v-model:value="modelState.eoaSubject" :disabled="true" placeholder="请输入申请单号获取" />
          </a-form-item>
          <a-form-item label="验真预估完成日期" required :label-col="labelColStyle" :name="['businessData', 'estimatedCompletionTime']">
            <a-date-picker v-model:value="modelState.businessData.estimatedCompletionTime" placeholder="请输入" :disabled-date="disabledDate" />
          </a-form-item>
          <a-form-item label="签报原因" required :label-col="labelColStyle" name="eoaBody">
            <a-textarea v-model:value="modelState.eoaBody" placeholder="请输入" />
          </a-form-item>
        </div>
      </a-form>
    </template>
  </CommonApprovalModal>
  <!-- 爱农宝核身豁免 -->
  <CommonApprovalModal v-if="showAuthenticityModal && chooseData.type === '2'" v-model:open="showAuthenticityModal" title="农险保单客户核身豁免申请" approval-chain-type="0" :department-code="formState.departmentCode" eoa-type="T24" @cancel="reset" @ok="handleSubmit">
    <template #content>
      <a-form ref="modelStateRef" :colon="false" :model="modelState">
        <div class="bg-[#f8f8f8] rounded-[4px] px-[16px] py-[16px]">
          <a-form-item label="签报主题" required :label-col="labelColStyle" name="eoaSubject">
            <a-input v-model:value="modelState.eoaSubject">
              <template #addonBefore>
                <span>保单号</span>
              </template>
              <template #addonAfter>
                <span>的农险保单申请核身豁免</span>
              </template>
            </a-input>
          </a-form-item>
          <a-form-item label="正文" required :label-col="labelColStyle" name="eoaBody">
            <a-textarea v-model:value="modelState.eoaBody" placeholder="请输入" />
          </a-form-item>
        </div>
      </a-form>
    </template>
  </CommonApprovalModal>

  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="formStateRef" :colon="false" class="flex-grow" :model="formState" :label-col="{ style: { width: pxToRem(80) } }" :rules="formRules">
          <a-row :gutter="16">
            <a-col :span="11">
              <a-form-item label="机构" required name="departmentCode">
                <department-search v-model:contain-child-depart="formState.containChildDepart" :dept-code="formState.departmentCode" :show-child-depart="true" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
            <a-col :span="5">
              <a-form-item label="业务号" name="businessType">
                <a-select v-model:value="formState.businessType" :options="voucherTypeList" placeholder="请选择" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item name="relationBusinessNo">
                <a-input v-model:value.trim="formState.relationBusinessNo" placeholder="请输入" @blur="handleBlurDisabled" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="16">
            <a-col :span="11">
              <a-form-item label="签报分类" name="eoaTypeCode">
                <a-select v-model:value="formState.eoaTypeCode" :filter-option="filterOption" show-search :options="flowOwnerTypeOption" placeholder="请选择" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="签报名称" name="eoaSubject">
                <a-input v-model:value.trim="formState.eoaSubject" placeholder="请输入" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="11">
              <a-form-item label="市场产品" name="productCode">
                <ProductSelect v-model:value="formState.productCode" :department-code="formState.departmentCode" :encode-key="formState.productCode" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="申请日期" name="applyDate">
                <a-range-picker v-model:value="formState.applyDate" format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="14">
              <a-form-item label="状态" name="eoaStatusCode">
                <CheckBoxGroup v-model:checked-list="formState.eoaStatusCode" :options="reviewStatusOptions" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="w-[50px]">
          <form-fold v-model="expand" />
        </div>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button @click="resetForm">重置</a-button>
        <a-button type="primary" ghost @click="submit">查询</a-button>
      </div>
    </div>
    <div class="bg-white rounded p-16px pt-0">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center py-16px">
        <div class="leading-[32px] text-[16px] text-[rgba(49,40,40,0.9)] font-bold">查询结果</div>
        <AuthButton code="eoaSceneChoose" type="primary" class="ml-[16px]" @click="showChooseModal = true">签报场景选择</AuthButton>
      </div>
      <a-table :columns="listColumns" :data-source="dataSource" :loading="loading" :pagination="pagination" :scroll="{ x: 'max-content' }" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : '')" class="table-box">
        <template #headerCell="{ column }">
          <template v-if="column.dataIndex === 'relationBusinessNo'">
            <div>
              <span class="mr-[4px]">单证号</span>
              <a-tooltip>
                <template #title>
                  <div v-for="(item, i) in headerCellList" :key="i" class="mb-[2px]">
                    <a-tag :border="false" :color="item.color">{{ item.value }}</a-tag>
                    <span>{{ item.label }}</span>
                  </div>
                </template>
                <VueIcon :icon="IconInfoCircleFilledFont" />
              </a-tooltip>
            </div>
          </template>
        </template>
        <template #bodyCell="{ column, record, index, text }">
          <template v-if="column.dataIndex === 'order'">{{ index + 1 }}</template>
          <template v-if="column.dataIndex === 'eoaNo'">
            <CopyLink :text="text" @click="goLink(text)" />
          </template>
          <template v-if="['eoaSubject', 'productName'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] truncate break-all">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'eoaStatusCode'">{{ eoaStatusCodeText(record.eoaStatusCode) }}</template>
          <template v-if="column.dataIndex === 'relationBusinessNo'">
            <div class="flex">
              <a-tag v-if="record.relationBusinessNo?.slice(0, 1) === '5'" color="orange" :border="false">投</a-tag>
              <a-tag v-if="record.relationBusinessNo?.slice(0, 1) === '1'" color="red" :border="false">保</a-tag>
              <a-tag v-if="record.relationBusinessNo?.slice(0, 2) === 'YB'" color="pink" :border="false">验</a-tag>
              <a-tag v-if="record.relationBusinessNo?.slice(0, 1) === 'Q'" color="green" :border="false">清</a-tag>
              <a-tag v-if="record.relationBusinessNo?.slice(0, 1) === '7'" color="blue" :border="false">申</a-tag>
              <a-tag v-if="record.relationBusinessNo?.slice(0, 1) === '3'" color="purple" :border="false">批</a-tag>
              <CopyLink :text="text" @click="handleGoLink(record)" />
            </div>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <a-button type="link" @click="goLink(record.eoaNo)">查看</a-button>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { IconInfoCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { ColumnsType } from 'ant-design-vue/es/table';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import debounce from 'lodash-es/debounce';
import type { ChildrenType, OptionsType, DataType, RecordType, DepartmentType } from './eoaApplication.d';
import CommonApprovalModal from '@/components/ui/CommonApprovalModal.vue';
import { usePagination } from '@/composables/usePagination';
import { pxToRem } from '@/utils/tools';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import { $post, $get, $getOnClient, $postOnClient } from '@/composables/request';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import FormFold from '@/components/ui/FormFold.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import AuthButton from '@/components/ui/AuthButton.vue';
import CopyLink from '@/components/ui/CopyLink.vue';

// 当前日期之前都不能选择
const disabledDate = (current: Dayjs) => current && current < dayjs().subtract(1, 'day').endOf('day');
const headerCellList = [
  { value: '保', label: '保单号', color: 'red' },
  { value: '投', label: '投保单号', color: 'orange' },
  { value: '验', label: '验标号', color: 'pink' },
  { value: '清', label: '清单号', color: 'green' },
  { value: '申', label: '批改申请单号', color: 'blue' },
  { value: '批', label: '批单号', color: 'purple' },
];
const { gateWay, service } = useRuntimeConfig().public || {};
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
// 搜索项是否展开
const expand = ref(false);
// 选择签报弹窗是否显示
const showChooseModal = ref<boolean>(false);
// 选择签报弹窗表单数据
const chooseData = ref({
  type: undefined,
});
// 验真
const modelState = ref<DepartmentType>({
  relationBusinessNo: '',
  eoaType: '042',
  eoaSubject: '',
  eoaBody: '',
  departmentCode: '',
  productCode: '',
  businessData: {
    estimatedCompletionTime: '',
  },
  farmerlistNo: '',
  departmentName: '',
  productName: '',
});
const eoaStatusCodeText = (type: string) => {
  const eoaStatusTypeMap: Record<string, string> = {
    0: '暂存',
    1: '审批中',
    3: '审核退回',
    2: '审批完成',
    9: '作废',
  };
  return eoaStatusTypeMap[type] || '-';
};
// 选择签报弹窗表单ref
const chooseFormRef = ref();
// 验真签报弹窗是否显示
const showAuthenticityModal = ref<boolean>(false);
// 选择机构
const changeDeptCode = (newVal: string) => {
  formState.departmentCode = newVal;
};
// 签报选择确认
const chooseEoa = async () => {
  await chooseFormRef?.value?.validate();
  showChooseModal.value = false;
  showAuthenticityModal.value = true;
};
const formRules = reactive({
  departmentCode: [{ required: true }],
  applyDate: [{ required: true, message: '请选择创建时间' }],
});
// 跳转签报页面
const goLink = async (eoaNo: string) => {
  try {
    const fetchUrl = `${gateWay}${service.administrate}/eoa/approveStatus`;
    const res = await $get(fetchUrl, { eoaNo });
    if (res && res.code === SUCCESS_CODE) {
      const { eoaDetailUrl } = res?.data as DataType;
      window.open(eoaDetailUrl, '_blank');
    }
  } catch (error) {
    console.log(error);
  }
};
const router = useRouter();
// 跳转详情页
const handleGoLink = (record: Record<string, string>) => {
  const linkTypeMap = {
    5: { id: 'applyPolicyDetails', query: { applyPolicyNo: record.relationBusinessNo } },
    1: { id: 'applyPolicyDetails', query: { applyPolicyNo: record.relationBusinessNo } },
    Y: { id: 'inspectionInfo', query: { applyPolicyNo: record.relationBusinessNo, isHiddenProcess: 'Y' } },
    Q: { id: 'editFarmerList', query: { id: record.relationBusinessNo, viewType: '1' } },
    7: { id: 'approvalTraceCheck', query: { documentNo: record.relationBusinessNo, documentType: 'ENDORSE_APPLY_NO', endorseApplyNo: record.relationBusinessNo } },
    3: { id: 'approvalTraceCheck', query: { documentNo: record.relationBusinessNo, documentType: 'ENDORSE_NO', endorseApplyNo: record.relationBusinessNo } },
  };
  const num = record.relationBusinessNo?.slice(0, 1);
  router.push({
    name: linkTypeMap[num].id,
    query: linkTypeMap[num].query,
  });
};
watch(
  () => showAuthenticityModal.value,
  (val) => {
    if (!val) {
      reset();
    }
  },
);
const reset = () => {
  chooseData.value.type = undefined;
  modelState.value.relationBusinessNo = '';
  modelState.value.eoaSubject = '';
  modelState.value.eoaBody = '';
  modelState.value.departmentCode = '';
  modelState.value.businessData.estimatedCompletionTime = '';
  modelState.value.farmerlistNo = '';
  modelState.value.departmentName = '';
  modelState.value.productName = '';
};
// 业务号失焦时，业务类型禁用且回显，5开头的是投保单，1开头的是保单,YB开头是验标号,Q开头是清单号
const relationMap: Record<string, string> = {
  5: '1',
  1: '2',
  Q: '3',
  Y: '4',
  7: '5',
  3: '6',
  N: '7',
};
const handleBlurDisabled = () => {
  const prefix = formState.relationBusinessNo?.slice(0, 1);
  formState.businessType = relationMap[prefix] || ''; // 或者其他默认值
};
// 获取验真信息
const getProductAndDepartment = async () => {
  if (modelState.value.farmerlistNo) {
    try {
      const res = await $getOnClient<DepartmentType>(gateWay + service.farmer + '/farmerList/getProductAndDepartment', { farmerListNo: modelState.value.farmerlistNo });
      console.log(res);
      if (res?.code === SUCCESS_CODE) {
        modelState.value = { ...modelState.value, ...res?.data };
        modelState.value.eoaSubject = `【关于${modelState.value.departmentName}_${modelState.value.productCode}_${modelState.value.productName}_清单号${modelState.value.farmerlistNo}的验真通融申请】`;
      } else {
        message.error(res?.msg || '获取失败');
      }
    } catch (error) {
      console.log(error);
    }
  } else {
    modelState.value.productName = '';
    modelState.value.productName = '';
  }
};
const debounceFetchDepartmentList = debounce(getProductAndDepartment, 800); // 防抖
const modelStateRef = ref();
// 提交签报
const confirmLoading = ref<boolean>(false);
const handleSubmit = async (eoaData: Record<string, string>) => {
  await modelStateRef.value.validate();
  try {
    confirmLoading.value = true;
    const res = await $postOnClient(gateWay + service.accept + '/accept/eoa/createEoa', {
      ...eoaData,
      ...modelState.value,
      relationBusinessNo: modelState.value.farmerlistNo,
      businessData: {
        ...modelState.value.businessData,
        departmentName: modelState.value.departmentName,
        productName: modelState.value.productName,
        estimatedCompletionTime: modelState.value.businessData.estimatedCompletionTime.format('YYYY-MM-DD'),
      },
      attachmentList: eoaData.fileInfos,
    });
    if (res?.code === SUCCESS_CODE) {
      message.success('发起签报成功');
      showAuthenticityModal.value = false;
      submit();
    } else {
      message.error(res?.msg || '提交失败');
    }
    confirmLoading.value = false;
  } catch (error) {
    console.log(error);
    confirmLoading.value = false;
  }
};
const labelColStyle = {
  style: {
    width: '130px',
  },
};
const reviewStatusOptions = [
  { label: '待审批', value: '0' },
  { label: '审批中', value: '1' },
  { label: '审核退回', value: '3' },
  { label: '审批完成', value: '2' },
];
// 单证类型option
const voucherTypeList = ref<OptionsType[]>([]);
const loading = ref<boolean>(false);
interface FormState {
  departmentCode: string; // 机构编码
  containChildDepart: boolean; // 是否包含下级机构
  applyDate: [Dayjs, Dayjs]; // 创建时间
  productCode: string | undefined; // 市场产品
  eoaTypeCode: undefined;
  eoaSubject: string;
  eoaStatusCode: string[];
  businessType: undefined | string;
  relationBusinessNo: string;
}
const formState = reactive<FormState>({
  departmentCode: defaultDeptCode.value,
  containChildDepart: true,
  relationBusinessNo: '', // 业务号
  eoaTypeCode: undefined, // 签报分类
  eoaSubject: '', // 签报名称
  productCode: undefined,
  eoaStatusCode: ['0', '1', '2', '3'],
  businessType: undefined,
  applyDate: [dayjs().subtract(1, 'month'), dayjs()], // 申请日期数组
});
const listColumns: ColumnsType<Record<string, string>> = [
  { title: '序号', dataIndex: 'order' },
  { title: '签报号', dataIndex: 'eoaNo' },
  { title: '签报分类', dataIndex: 'eoaTypeName' },
  { title: '签报名称', dataIndex: 'eoaSubject' },
  { title: '申请日期', dataIndex: 'createdDate' },
  { title: '审批状态', dataIndex: 'eoaStatusCode' },
  { title: '市场产品', dataIndex: 'productName' },
  { title: '业务号', dataIndex: 'relationBusinessNo' },
  { title: '机构', dataIndex: 'departmentName' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];
// 初始化列表接口
const dataSource = ref<RecordType[]>([]);
// 查询
const formStateRef = ref();
const submit = () => {
  formStateRef.value.validate().then(() => {
    pagination.pageSize = 10;
    pagination.current = 1;
    refresh();
  });
};
// 重置
const resetForm = () => {
  formStateRef.value.resetFields();
  submit();
};
// 模糊搜索
const filterOption = (input: string, option: Record<string, string>) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const initOption = async () => {
  const url = gateWay + service.administrate + '/parmBaseConstantConf/getParmBaseConstantConf';
  const res = await $post(url, ['agrAnBizType']);
  const { data = [] } = res || {};
  // 单证类型
  const findOne = (data as ChildrenType[])?.find((item: OptionsType) => item.value === 'agrAnBizType');
  if (findOne && findOne.children) {
    voucherTypeList.value = findOne.children.map((child: OptionsType) => ({
      value: child.value,
      label: child.label,
    }));
  }
};
initOption();
interface ValueType {
  value: string;
  label: string;
}
// 签报类型option
const flowOwnerTypeOption = ref<ValueType[]>([]);
interface FlowOwnerType {
  parameterCode: string;
  parameterName: string;
}
interface FlowOptionsType {
  sign_type: FlowOwnerType[];
}
// 查询枚举-签报类型
const flowTypeOption = async () => {
  try {
    const fetchUrl = `${gateWay}${service.administrate}/public/getSelect`;
    const params = ['sign_type'];
    const res = await $post(fetchUrl, params);
    const { sign_type = [] } = (res?.data as FlowOptionsType) || {};
    if (res && res.code === SUCCESS_CODE) {
      flowOwnerTypeOption.value = sign_type?.map((item: FlowOwnerType) => ({
        value: item.parameterCode,
        label: item.parameterName,
      }));
    }
  } catch (error) {
    console.log(error);
  }
};
// 初始化列表数据
const refresh = async () => {
  try {
    loading.value = true;
    const fetchUrl = `${gateWay}${service.administrate}/eoa/list`;
    // 日期数据组装
    const dateFormat = 'YYYY-MM-DD';
    const [applyBeginDate, applyEndDate] = formState.applyDate || [];
    const params = {
      ...formState,
      containChildDepart: formState.containChildDepart ? '1' : '0',
      startCreatedDate: applyBeginDate?.format(dateFormat),
      endCreatedDate: applyEndDate?.format(dateFormat),
      pageSize: pagination.pageSize,
      pageNum: pagination.current,
    };
    const res = await $post(fetchUrl, params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data as DataType;
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      pagination.total = 0;
      pagination.current = 1;
      pagination.pageSize = 10;
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
};
// 分页处理
const { pagination } = usePagination(refresh);
onMounted(() => {
  submit();
  flowTypeOption();
});
</script>
