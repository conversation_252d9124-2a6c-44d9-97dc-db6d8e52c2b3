<template>
  <a-upload v-model:file-list="list" :before-upload="handleBefore" :show-upload-list="false" @change="handleUploadChange">
    <a-button type="primary">{{ text }}</a-button>
  </a-upload>
</template>

<script lang="ts" setup>
import { message } from '@/components/ui/Message';
import { SUCCESS_CODE } from '@/utils/constants';
import { iobsUploadPro } from '@/utils/iobsUpload';

export type CusUploadListType = Array<
  Partial<
    File & {
      iobsBucketName: string;
      storageTypeCode: string;
      fileKey: string;
      fileSize: number;
      fileName: string;
      uid: string;
    }
  >
>;
const emits = defineEmits(['update:value', 'upload', 'showPageLoading']);
const props = withDefaults(
  defineProps<{
    value: Array<CusUploadListType>;
    text?: string;
  }>(),
  {
    value: () => [],
    origin: false,
    text: '上传',
  },
);
const { value } = toRefs(props);
const list = ref(value);
const tmpArr = ref<CusUploadListType>([]);
const handleBefore = () => {
  tmpArr.value = [];
  return false;
};
// 文件数据上传
const handleUploadChange = async (e: { file: File & { uid: string }; fileList: Array<CusUploadListType> }) => {
  emits('showPageLoading');
  const { file } = e;
  const { data, msg, code } = await iobsUploadPro(file);
  if (SUCCESS_CODE === code) {
    tmpArr.value.push({
      iobsBucketName: data.bucket,
      storageTypeCode: '02',
      fileKey: data.key,
      fileSize: file.size,
      fileName: file.name,
    });
    const dataAll = [...tmpArr.value];
    // 过滤完成后合并数据
    emits('update:value', dataAll);
    emits('upload');
  } else {
    // 文件上传失败不更新数据
    message.error(msg);
  }
};
</script>
