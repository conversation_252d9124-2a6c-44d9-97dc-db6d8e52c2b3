<template>
  <a-spin :spinning="pageLoading">
    <div class="m-14px space-y-16px">
      <div class="bg-white rounded p-16px">
        <div class="flex">
          <a-form ref="searchForm" :colon="false" class="flex-grow" :model="searchFormState" :label-col="{ style: { width: pxToRem(80) } }">
            <a-row>
              <a-col span="10">
                <a-form-item label="SFTP账号">
                  <a-select v-model:value="searchFormState.account" :options="selectOptions" @change="changeAccount"></a-select>
                </a-form-item>
              </a-col>
              <a-col span="10">
                <a-form-item label="路径" name="departmentCode">
                  {{ searchFormState.dir }}
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div class="flex justify-center items-center space-x-8px">
          <CusUpload v-model:value="list" text="上传" @upload="upload" @show-page-loading="pageLoading = true" />
          <a-button @click="backLastDir">返回上级目录</a-button>
          <a-button @click="search">刷新</a-button>
        </div>
      </div>
      <div class="bg-white rounded p-16px">
        <!-- 表格头部 -->
        <div class="mb-16px flex justify-between">
          <div class="text-[#404442] text-xl font-bold">查询结果</div>
        </div>
        <a-table :columns="listColumns" :pagination="false" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content', y: '50vh' }">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex === 'insuranceBeginDate'">
              {{ (record.insuranceBeginDate ? record.insuranceBeginDate + ' 至 ' : '') + (record.insuranceEndDate ?? '') }}
            </template>
            <template v-if="['marketproductName', 'insuredName', 'departmentName', 'riskTypeName'].includes(column.dataIndex as string)">
              <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
                <div class="max-w-[200px] truncate">{{ text }}</div>
              </a-tooltip>
            </template>
            <template v-if="column.dataIndex === 'operation'">
              <a-space :size="1">
                <a-button v-if="record.type === 'dir'" type="link" @click="open(record.name)">打开</a-button>
                <a-button v-if="['toFetch', 'fetchFail', 'fetched', 'uploaded'].includes(record.status)" type="link" @click="fetch(record.name)">取回最新</a-button>
                <a-button v-if="record.status === 'uploadFail'" type="link" @click="reUpload(record.name)">上传重试</a-button>
                <a-button v-if="['fetched', 'uploading', 'uploadFail', 'uploaded'].includes(record.status)" type="link" @click="downloadFile(record.name)">下载</a-button>
                <a-button v-if="['fetched', 'uploadFail', 'uploaded'].includes(record.status)" type="link" @click="deleteFile(record.name)">删除</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
      <!-- 确认删除弹窗 -->
      <a-modal v-model:open="delModalOpen" :width="pxToRem(450)" :centered="true">
        <div>
          <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
          <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px]">提醒</span>
        </div>
        <div class="mt-[10px]">删除操作不可撤销，是否确认删除？</div>
        <template #footer>
          <a-button key="back" @click="delModalOpen = false">取消</a-button>
          <a-button key="submit" type="primary" :loading="btnLoading" @click="handleDeleteOk">确定</a-button>
        </template>
      </a-modal>
    </div>
  </a-spin>
</template>

<script lang="ts" setup>
import type { TableColumnsType } from 'ant-design-vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import { usePost, $postOnClient } from '@/composables/request';
import { IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import CusUpload from './cusUpload.vue';
const btnLoading = ref(false);
const delModalOpen = ref<boolean>(false);
const listColumns: TableColumnsType = [
  { title: '文件名', dataIndex: 'name', fixed: 'left' },
  { title: '文件状态', dataIndex: 'statusDesc' },
  { title: '操作', dataIndex: 'operation', fixed: 'right', width: 320 },
];
const searchForm = ref();
const searchFormState = reactive<Record<string, string>>({
  account: '',
  dir: '',
});
// 上一层路径
const parentPath = ref<string>('');
const { gateWay, service } = useRuntimeConfig().public || {};

const dataSource = ref<Record<string, string>[]>([]);
const loading = ref<boolean>(false);
const pageLoading = ref<boolean>(false);
const getListReq = await usePost<{ files: Record<string, string>[]; parentPath: string }>(`${gateWay}${service.compliance}/sftpOperation/ls`);
// 查询
const search = async () => {
  loading.value = true;
  try {
    const params = {
      accountId: searchFormState.account,
      currentPath: searchFormState.dir,
    };
    const res = await getListReq.fetchData(params);
    dataSource.value = [];
    if (res && res.code === SUCCESS_CODE) {
      dataSource.value = res.data?.files || [];
      parentPath.value = res.data?.parentPath || '';
    } else {
      message.warning(res?.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const selectOptions = ref<Record<string, string>[]>([]);
// 获取SFTP账号列表
const getOptions = async () => {
  const res = await $postOnClient<Record<string, string>[]>(gateWay + service.compliance + '/sftpOperation/getAccounts', {});
  if (res && res?.code === SUCCESS_CODE) {
    selectOptions.value =
      res.data.map((item) => {
        return {
          label: item.name,
          value: item.id,
          initDir: item.initDir,
        };
      }) || [];
    searchFormState.account = selectOptions.value[0]?.value || '';
    searchFormState.dir = selectOptions.value[0]?.initDir || '';
    search();
  }
};
// 账号改变
const changeAccount = (val: string, options: Record<string, string>) => {
  searchFormState.dir = options.initDir;
  search();
};
// 返回上级目录
const backLastDir = () => {
  searchFormState.dir = parentPath.value;
  search();
};
// 打开
const open = (name: string) => {
  searchFormState.dir = searchFormState.dir + name;
  search();
};
// 取回最新
const fetch = async (name: string) => {
  const params = {
    accountId: searchFormState.account,
    currentPath: searchFormState.dir,
    fileName: name,
  };
  loading.value = true;
  try {
    const res = await $postOnClient<Record<string, string>[]>(gateWay + service.compliance + '/sftpOperation/fetch', params);
    if (res && res?.code === SUCCESS_CODE) {
      message.success(res.msg || '成功');
      search();
    } else {
      message.error(res?.msg || '失败');
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
// 下载
const downloadFile = async (name: string) => {
  const params = {
    accountId: searchFormState.account,
    currentPath: searchFormState.dir,
    fileName: name,
  };
  loading.value = true;
  try {
    const res = await $postOnClient<string>(gateWay + service.compliance + '/sftpOperation/download', params);
    if (res && res.code === SUCCESS_CODE && res.data) {
      window.open(res.data);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
// 普通上传
export type CusUploadListType = Array<
  Partial<
    File & {
      iobsBucketName: string;
      storageTypeCode: string;
      fileKey: string;
      fileSize: number;
      fileName: string;
      uid: string;
      attachmentClassNo: string;
    }
  >
>;
const list = ref<CusUploadListType[]>([]);
const upload = async () => {
  const params = {
    accountId: searchFormState.account,
    currentPath: searchFormState.dir,
    retryUpload: 'N',
    fileName: list.value[0].fileName,
    fileKey: list.value[0].fileKey,
  };
  try {
    const res = await $postOnClient<string>(gateWay + service.compliance + '/sftpOperation/upload', params);
    if (res && res.code === SUCCESS_CODE) {
      message.success(res.msg || '上传成功');
      search();
    } else {
      message.error(res?.msg || '上传失败');
    }
  } catch (error) {
    console.log(error);
  } finally {
    pageLoading.value = false;
  }
};
// 上传失败重试
const reUpload = async (name: string) => {
  const params = {
    accountId: searchFormState.account,
    currentPath: searchFormState.dir,
    retryUpload: 'Y',
    fileName: name,
  };
  try {
    const res = await $postOnClient<string>(gateWay + service.compliance + '/sftpOperation/upload', params);
    if (res && res.code === SUCCESS_CODE) {
      message.success(res.msg || '上传成功');
      search();
    } else {
      message.error(res?.msg || '上传失败');
    }
  } catch (error) {
    console.log(error);
  }
};
const delName = ref<string>('');
// 删除按钮
const deleteFile = (name: string) => {
  delName.value = name;
  delModalOpen.value = true;
};
// 删除弹窗-确定
const handleDeleteOk = async () => {
  btnLoading.value = true;
  try {
    const params = {
      accountId: searchFormState.account,
      currentPath: searchFormState.dir,
      fileName: delName.value,
    };
    const res = await $postOnClient<string>(gateWay + service.compliance + '/sftpOperation/delete', params);
    if (res && res.code === SUCCESS_CODE) {
      delModalOpen.value = false;
      message.success(res.msg || '删除成功！');
      search();
    } else {
      message.error(res?.msg || '删除失败！');
    }
  } catch (error) {
    console.log(error);
  } finally {
    btnLoading.value = false;
  }
};
onMounted(() => {
  getOptions();
});
</script>
