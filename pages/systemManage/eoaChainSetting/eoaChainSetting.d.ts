export interface DataType {
  records: RecordType[];
  total: number;
  current: number;
  size: number;
}
export interface RecordType {
  departmentName: string;
  flowOwnerTypeName: string;
}
export interface ValueType {
  value: string;
  label: string;
}
export interface FlowOwnerType {
  parameterCode: string;
  parameterName: string;
}
export interface OptionsType {
  eoa_type: FlowOwnerType[];
  flow_owner_type: FlowOwnerType[];
}
interface eoaChainsType {
  handType: string;
  flowOwnerTypeList: string[];
  seq: number;
}
// 弹窗表单
export interface ManageFormState {
  idParmEoaAprvLnkDef: string;
  departmentCode: string; // 机构编码
  approveChainList: eoaChainsType[];
  eoaType: string;
  defineDesc: string;
  insuranceFeeFloor: string;
  insuranceFeeCeil: string;
  intervalFloorSymbol: string | undefined;
  intervalCeilSymbol: string | undefined;
  applyPolicyTypes: string[]; // 投保方式
  riskType: string; // 标的类型
  subsidyTypes: string[]; // 补贴类型
}

// 查询条件
export interface SearchFormState {
  eoaType: string; // 签报类型
  departmentCode: string; // 机构编码
  applyPolicyTypes: string[]; // 投保方式
  riskType: string; // 标的类型
  subsidyTypes: string[]; // 补贴类型
}
