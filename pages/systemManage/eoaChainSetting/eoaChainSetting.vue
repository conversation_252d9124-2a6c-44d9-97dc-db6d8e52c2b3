<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <a-form ref="searchForm" :colon="false" :model="searchFormState">
        <a-row :gutter="24" class="items-baseline">
          <a-col :span="9">
            <a-form-item label="签报类型">
              <a-select v-model:value="searchFormState.eoaType" :filter-option="filterOption" show-search :options="flowOwnerTypeOption" @change="handleChangeEoaType" />
            </a-form-item>
          </a-col>
          <template v-if="searchFormState.eoaType === T_22">
            <a-col :span="10">
              <a-form-item label="机构编码" name="departmentCode" required>
                <DepartmentSearch :dept-code="searchFormState.departmentCode" @change-dept-code="(val: string) => changeDeptCode(val, 'searchForm')" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="标的类型" name="riskType">
                <RiskCodeSelect v-model:value="searchFormState.riskType" :department-code="searchFormState.departmentCode" />
              </a-form-item>
            </a-col>
            <a-col :span="16">
              <a-form-item label="投保方式" name="applyPolicyTypes">
                <CheckBoxGroup v-model:checked-list="searchFormState.applyPolicyTypes" :options="applyPolicyOptions" />
              </a-form-item>
            </a-col>
            <a-col :span="16">
              <a-form-item label="补贴类型" name="subsidyTypes">
                <CheckBoxGroup v-model:checked-list="searchFormState.subsidyTypes" :options="plainOptions" />
              </a-form-item>
            </a-col>
          </template>
        </a-row>
      </a-form>
      <div class="flex justify-center items-center space-x-8px mt-[16px]">
        <a-button type="primary" ghost @click="search">查询</a-button>
        <AuthButton code="eoaChainCreate" type="primary" :disabled="searchFormState.eoaType === ''" @click="showModal('add')">新增</AuthButton>
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-16px">
        <div class="leading-[32px] text-[16px] text-[rgba(0,0,0,0.9)] font-bold">查询结果</div>
      </div>
      <a-table :columns="listColumns" :pagination="pagination" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content' }" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : '')" class="table-box">
        <template #bodyCell="{ column, record, index, text }">
          <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
          <template v-if="['eoaChainDesc'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[700px] truncate">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <AuthButton code="eoaChainEdit" type="link" @click="edit(record as ManageFormState, 'edit')">编辑</AuthButton>
            <AuthButton code="eoaChainDel" type="link" @click="handleDelUpdate(record.idParmEoaAprvLnkDef)">删除</AuthButton>
          </template>
        </template>
      </a-table>
    </div>
    <!-- 弹窗 -->
    <a-modal v-model:open="open" title="审批链设置" :width="pxToRem(1036)" @ok="handleOk">
      <template v-if="formState.eoaType === T_22">
        <div class="text-[14px] text-[#0000008c] font-bold mb-[8px]">
          <VueIcon class="mr-[3px]" :icon="IconInformationFont" />
          维度
        </div>
        <div class="rounded-[4px] bg-[#F8F8F8] p-[16px]">
          <a-form ref="configForm" :colon="false" :model="formState">
            <a-form-item label="机构编码" name="departmentCode" required>
              <DepartmentSearch :dept-code="formState.departmentCode" @change-dept-code="(val: string) => changeDeptCode(val, 'configForm')" />
            </a-form-item>
            <a-form-item label="标的类型" name="riskType" required>
              <RiskCodeSelect v-model:value="formState.riskType" :department-code="formState.departmentCode" :default-value="defaultValue" />
            </a-form-item>
            <a-form-item label="投保方式" name="applyPolicyTypes" required>
              <CheckBoxGroup v-model:checked-list="formState.applyPolicyTypes" :options="applyPolicyOptions" />
            </a-form-item>
            <a-form-item label="补贴类型" name="subsidyTypes" required>
              <CheckBoxGroup v-model:checked-list="formState.subsidyTypes" :options="plainOptions" />
            </a-form-item>
          </a-form>
        </div>
        <div class="text-[14px] text-[#0000008c] font-bold mb-[8px] mt-[16px]">
          <VueIcon class="mr-[3px]" :icon="IconPingtaijiekouFont" />
          审批链
        </div>
        <div class="rounded-[4px] bg-[#F8F8F8] p-[16px]">
          <a-form ref="formStateRef" label-align="right" :colon="false" :label-col="{ style: { width: pxToRem(110) } }" :model="formState">
            <a-row>
              <a-col :span="24">
                <a-form-item>
                  <a-space>
                    <a-button block @click="handleAdd('20')">顺序批示 →</a-button>
                    <a-button block @click="handleAdd('30')">并行批示 |</a-button>
                    <a-button block @click="handleAdd('120')">协同批示 /</a-button>
                    <a-button block @click="handleAdd('1110')">传阅 §</a-button>
                    <a-button block title="将删除最末尾的审批人" @click="handleDel">删除</a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24">
                <a-form-item label="主送领导审批链" name="approveChainList">
                  <a-form-item-rest>
                    <span v-for="(item, index) in formState.approveChainList" :key="index">
                      <span v-for="(child, idx) in item.flowOwnerTypeList" :key="idx">
                        <div v-if="index !== 0 || (index === 0 && idx !== 0)" class="inline-block w-[20px] text-center">{{ showSymbol(index, idx, item.handType) }}</div>
                        <div class="inline-block w-[268px]">
                          <a-form-item :name="`flowOwnerTypeList + ${idx + 1}`" :rules="[{ required: true, validator: (rule, value) => validatorFlow(rule, value, index, idx) }]">
                            <a-select ref="selectRef" v-model:value="item.flowOwnerTypeList[idx]" placeholder="请选择" :filter-option="filterOption" show-search :options="optionsList" />
                          </a-form-item>
                        </div>
                      </span>
                    </span>
                  </a-form-item-rest>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </template>
      <template v-else>
        <a-form ref="formStateRef" label-align="right" :colon="false" :label-col="{ style: { width: pxToRem(110) } }" :model="formState">
          <a-row>
            <a-col :span="24">
              <a-form-item label="机构：2">
                <a-space>
                  <a-button block @click="handleAdd('20')">顺序批示 →</a-button>
                  <a-button block @click="handleAdd('30')">并行批示 |</a-button>
                  <a-button block @click="handleAdd('120')">协同批示 /</a-button>
                  <a-button block @click="handleAdd('1110')">传阅 §</a-button>
                  <a-button block title="将删除最末尾的审批人" @click="handleDel">删除</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="主送领导审批链" name="approveChainList">
                <a-form-item-rest>
                  <span v-for="(item, index) in formState.approveChainList" :key="index">
                    <span v-for="(child, idx) in item.flowOwnerTypeList" :key="idx">
                      <div v-if="index !== 0 || (index === 0 && idx !== 0)" class="inline-block w-[20px] text-center">{{ showSymbol(index, idx, item.handType) }}</div>
                      <div class="inline-block w-[268px]">
                        <a-form-item :name="`flowOwnerTypeList + ${idx + 1}`" :rules="[{ required: true, validator: (rule, value) => validatorFlow(rule, value, index, idx) }]">
                          <a-select ref="selectRef" v-model:value="item.flowOwnerTypeList[idx]" placeholder="请选择" :filter-option="filterOption" show-search :options="optionsList" />
                        </a-form-item>
                      </div>
                    </span>
                  </span>
                </a-form-item-rest>
              </a-form-item>
            </a-col>
          </a-row>
          <!-- 批量费用修改签报申请附加条件 -->
          <a-row v-if="formState.eoaType === 'T14'">
            <a-form-item label="保单生效时间距今" :label-col="{ style: { width: pxToRem(120) } }">
              <a-form-item-rest>
                <a-space>
                  <a-form-item name="intervalFloorSymbol" class="w-[100px]" :rules="[{ required: true, message: '请选择' }]">
                    <a-select v-model:value="formState.intervalFloorSymbol" placeholder="请选择">
                      <a-select-option value="<">&lt;</a-select-option>
                      <a-select-option value="<=">&lt;=</a-select-option>
                      <a-select-option value=">">&gt;</a-select-option>
                      <a-select-option value=">=">&gt;=</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item name="insuranceFeeFloor" class="w-[160px]" :rules="[{ required: true, message: '请输入天数' }]">
                    <a-input-number v-model:value.trim="formState.insuranceFeeFloor" style="width: 100%" :min="0" :precision="0" :maxlength="4" addon-after="天" string-mode placeholder="请输入天数" />
                  </a-form-item>
                </a-space>
              </a-form-item-rest>
            </a-form-item>
          </a-row>
          <a-row v-if="isCanAddEoaType">
            <a-col :span="24">
              <a-form-item :label="formState.eoaType === 'T10' ? '自缴保费设置' : '保费范围'">
                <a-form-item-rest>
                  <a-space>
                    <a-form-item name="defineDesc" :rules="[{ required: true, message: '请输入审批链描述' }]" class="w-[200px]">
                      <a-input v-model:value="formState.defineDesc" placeholder="请输入审批链描述" />
                    </a-form-item>
                    <a-form-item name="insuranceFeeFloor" :rules="[{ required: true, message: '请输入数值' }, { validator: validateNumber }]" class="w-[160px]">
                      <a-input v-model:value.trim="formState.insuranceFeeFloor" type="number" placeholder="请输入数值" />
                    </a-form-item>
                    <a-form-item name="intervalFloorSymbol" :rules="[{ required: true, message: '请选择' }]" class="w-[100px]">
                      <a-select v-model:value="formState.intervalFloorSymbol" placeholder="请选择">
                        <a-select-option value="<">&lt;</a-select-option>
                        <a-select-option value="<=">&lt;=</a-select-option>
                      </a-select>
                    </a-form-item>
                    <div class="w-[80px] text-center text-[rgba(0,0,0,0.60)] mt-[-10px]">保费范围</div>
                    <a-form-item name="intervalCeilSymbol" class="w-[100px]">
                      <a-select v-model:value="formState.intervalCeilSymbol" placeholder="请选择">
                        <a-select-option value="<">&lt;</a-select-option>
                        <a-select-option value="<=">&lt;=</a-select-option>
                      </a-select>
                    </a-form-item>
                    <a-form-item name="insuranceFeeCeil" class="w-[160px]" :rules="[{ validator: validateNumber }]">
                      <a-input v-model:value.trim="formState.insuranceFeeCeil" type="number" placeholder="请输入数值" />
                    </a-form-item>
                  </a-space>
                </a-form-item-rest>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
      <template #footer>
        <a-button key="back" @click="open = false">取消</a-button>
        <!-- 客户核身率豁免申请有权限控制 -->
        <AuthButton v-if="formState.eoaType === T_22" :loading="btnLoading" code="T22SubmitAuth" type="primary" @click="handleOk">确定</AuthButton>
        <a-button v-else key="submit" type="primary" :loading="btnLoading" @click="handleOk">确定</a-button>
      </template>
    </a-modal>
    <!-- 确认删除弹窗 -->
    <a-modal v-model:open="visible" :width="pxToRem(450)" @ok="handleDeleteOk">
      <div>
        <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px] ml-[5px]">提醒</span>
      </div>
      <div class="mt-[10px]">是否确认删除</div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnsType } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { IconErrorCircleFilledFont, IconInformationFont, IconPingtaijiekouFont } from '@pafe/icons-icore-agr-an';
import type { ManageFormState, DataType, RecordType, ValueType, OptionsType, FlowOwnerType, SearchFormState } from './eoaChainSetting';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { $postOnClient, $getOnClient } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import { pxToRem } from '@/utils/tools';
import { useIndexDB } from '@/composables/useIndexDB';
import AuthButton from '@/components/ui/AuthButton.vue';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import { useUserStore } from '@/stores/useUserStore';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import type { SelectOptions } from '@/apiTypes/apiCommon';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import { cloneDeep } from 'lodash-es';

const { getCacheData } = useIndexDB();

const listColumns = computed((): TableColumnsType => {
  return searchFormState.value.eoaType === T_22
    ? [
        { title: '序号', dataIndex: 'index' },
        { title: '机构', dataIndex: 'departmentName' },
        { title: '签报类型', dataIndex: 'eoaTypeName' },
        { title: '标的', dataIndex: 'riskTypeName' },
        { title: '投保方式', dataIndex: 'applyPolicyTypeNames' },
        { title: '补贴类型', dataIndex: 'subsidyTypeNames' },
        { title: '审批链', dataIndex: 'eoaChainDesc' },
        { title: '操作', dataIndex: 'operation', fixed: 'right' },
      ]
    : [
        { title: '序号', dataIndex: 'index' },
        { title: '机构', dataIndex: 'departmentName' },
        { title: '签报类型', dataIndex: 'eoaTypeName' },
        { title: '审批链', dataIndex: 'eoaChainDesc' },
        { title: '操作', dataIndex: 'operation', fixed: 'right' },
      ];
});

const T_22 = 'T22'; // 客户核身率豁免申请

const btnLoading = ref(false);
// 配置弹窗显示隐藏字段
const open = ref<boolean>(false);
const configForm = ref();
const formStateRef = ref();
const searchForm = ref();
const defaultValue = ref<string>();

// 投保方式
const applyPolicyOptions = ref<SelectOptions[]>([]);
// 补贴类型
const plainOptions = ref<SelectOptions[]>([]);

// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));

// 搜索条件
const searchFormState = ref<SearchFormState>({
  eoaType: '', // 签报类型
  departmentCode: defaultDeptCode.value, // 机构编码
  applyPolicyTypes: [], // 投保方式
  riskType: '', // 标的类型
  subsidyTypes: [], // 补贴类型
});

const formState = ref<ManageFormState>({
  idParmEoaAprvLnkDef: '',
  departmentCode: defaultDeptCode.value, // 机构编码
  approveChainList: [],
  eoaType: '',
  defineDesc: '',
  insuranceFeeFloor: '',
  insuranceFeeCeil: '',
  intervalFloorSymbol: undefined,
  intervalCeilSymbol: undefined,
  applyPolicyTypes: [], // 投保方式
  riskType: '', // 标的类型
  subsidyTypes: [], // 补贴类型
});
// 删除
const handleDel = () => {
  const list = formState.value.approveChainList;
  if (list.length === 1 && list[0].flowOwnerTypeList.length === 1) {
    message.warning('至少设置一个审批人！');
  } else {
    list[list.length - 1].flowOwnerTypeList.splice(-1, 1);
    if (list[list.length - 1].flowOwnerTypeList.length === 0) {
      list.splice(-1, 1);
    }
  }
  // 删除还剩最后一个，则handType设置为20
  if (list.length === 1 && list[list.length - 1].flowOwnerTypeList.length === 1) {
    formState.value.approveChainList[0].handType = '20';
  }
};
const { gateWay, service } = useRuntimeConfig().public || {};
// 模糊搜索
const filterOption = (input: string, option: Record<string, string>) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
// add-新增，edit-编辑
const modalType = ref<string>('');
// 显示弹窗-新增
const showModal = async (type: string) => {
  configForm.value?.clearValidate();
  formStateRef.value?.clearValidate();
  defaultValue.value = '';
  formState.value = {
    idParmEoaAprvLnkDef: '',
    departmentCode: defaultDeptCode.value, // 机构编码
    approveChainList: [],
    eoaType: searchFormState.value.eoaType,
    defineDesc: '',
    insuranceFeeFloor: '',
    insuranceFeeCeil: '',
    intervalFloorSymbol: '',
    intervalCeilSymbol: '',
    applyPolicyTypes: [], // 投保方式
    riskType: '', // 标的类型
    subsidyTypes: [], // 补贴类型
  };
  open.value = true;
  formState.value.approveChainList.push({
    handType: '20',
    flowOwnerTypeList: ['E1'],
    seq: 1,
  });
  modalType.value = type;
};
// 显示弹窗-编辑
const idParmEoaAprvLnkDef = ref<string>('');
const edit = async (record: ManageFormState, type: string) => {
  configForm.value?.clearValidate();
  formStateRef.value?.clearValidate();
  open.value = true;
  idParmEoaAprvLnkDef.value = record.idParmEoaAprvLnkDef;
  modalType.value = type;
  formState.value = cloneDeep(record);
  defaultValue.value = record.riskType;
};
/**
 * 点击弹窗中的机构-按钮
 * 1.输入框最多20个
 * 2.顺序批示(20)：1.传阅后不能添加，提示：传阅只能在最后一步审批操作；2.
 * 3.并行批示(30)：1.与协同批示互斥，提示：并行与协同不能在同一步；2.需顺序批示选择框后才能添加协同批示
 * 4.协同批示(120)：1.与并行批示互斥，提示：协同与并行不能在同一步；2.与以上第二点相反
 * 5.传阅(1110)：1.有传阅，后面都无法增加以上三种选择框,提示：
 * 6.删除：第一个不能被删除，其余均可删除
 * 7.只要是顺序批示就为一个新对象，如果顺序批示下一个不为顺序批示，则顺序批示的选中值跟下一个选择框的选中值合并为一个对象，用字段flowOwnerType:['E27','E2',...]存选择框选中值
 * 相同类型的选中框，连续，则为一个对象，区分值为flowOwnerType:['E27','E2',...]存选择框选中值
 * 8.如果为传阅，则为一个新对象，不跟顺序审批一个对象
 */
const selectRef = ref();
// 审批链添加按钮
const handleAdd = (type: string) => {
  // 选择框数量
  const len = selectRef.value.length;
  if (len < 20) {
    const _handType = formState.value.approveChainList[formState.value.approveChainList.length - 1].handType;
    const text = type === '30' ? '并行与协同' : '协同与并行';
    if (_handType === '120' && type === '30' && len > 1) {
      message.warning(`${text}不能在同一步`);
    } else if (_handType === '30' && type === '120' && len > 1) {
      message.warning(`${text}不能在同一步`);
    } else if (_handType === '1110' && type !== '1110') {
      message.warning('传阅只能在最后一步审批操作');
    } else {
      const len = formState.value.approveChainList?.length || 0;
      let seq = formState.value.approveChainList[len - 1].seq;
      if (type === '20' || (type === '1110' && formState.value.approveChainList[seq - 1].handType !== '1110')) seq++;
      if (seq === formState.value.approveChainList[len - 1].seq) {
        formState.value.approveChainList[seq - 1].flowOwnerTypeList.push('');
        formState.value.approveChainList[seq - 1].handType = type;
      } else {
        formState.value.approveChainList.push({
          flowOwnerTypeList: [''],
          handType: type,
          seq: seq,
        });
      }
    }
  } else {
    message.warning('审批链最多只能设置20个！');
  }
};
// 审批链选择框后面展示对应符号 20 →, 30 |, 120 /, 1110§
interface type {
  [key: number | string]: number | string;
}
const typeObj: type = {
  20: '→',
  30: '|',
  120: '/',
  1110: '§',
};
// 显示下拉框前面的符号
const showSymbol = (index: number, idx: number, handType: string) => {
  let symbolStr = typeObj[handType];
  if (index > 0 && formState.value.approveChainList[index].seq !== formState.value.approveChainList[index - 1].seq) {
    if (index !== 0 && idx === 0) {
      if (handType !== '1110') {
        symbolStr = '→';
      }
    }
  }
  return symbolStr;
};
// 仅"非见费出单系统管控签报申请"与"关联方投保申请"签报类型方可新增
// eoa 审批链可以新增的签报类型
const list = ['T10', 'T17', 'T18'];
// 保费设置是否显示
const isCanAddEoaType = computed(() => {
  return modalType.value === 'add' ? list.includes(searchFormState.value.eoaType) : list.includes(formState.value.eoaType);
});
// 主送领导审批链规则校验
const validatorFlow = (_rule: Rule, value: string, index: number, idx: number) => {
  if (!formState.value.approveChainList[index].flowOwnerTypeList[idx]) {
    return Promise.reject('请选择');
  } else {
    return Promise.resolve();
  }
};
// 弹窗确定
const handleOk = async () => {
  try {
    if (formState.value.approveChainList?.length === 1 && formState.value.approveChainList[0]?.flowOwnerTypeList.length < 2) {
      message.warning('请录入完整的审批链!');
      return;
    }
    await configForm.value?.validate();
    await formStateRef.value.validate();
    btnLoading.value = true;
    if (modalType.value === 'add') {
      saveApproveLinkConfig();
    } else {
      updateApproveLinkConfig();
    }
  } catch (error) {
    console.log(error);
  }
};
const dataSource = ref<RecordType[]>([]);
const loading = ref(false);
// 查询
const search = async () => {
  try {
    pagination.current = 1;
    pagination.pageSize = 10;
    refresh();
  } catch (e) {
    console.log(e);
  }
};

// 初始化数据
const refresh = async () => {
  if (searchFormState.value.eoaType === T_22) {
    await searchForm?.value?.validate();
  }
  loading.value = true;
  try {
    const params = {
      ...searchFormState.value,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    const fetchUrl = `${gateWay}${service.administrate}/eoa/getApproveLinkList`;
    const res = await $postOnClient(fetchUrl, params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data as DataType;
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      pagination.total = 0;
      pagination.current = 1;
      pagination.pageSize = 10;
    }
    loading.value = false;
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
// 新增提交
const saveApproveLinkConfig = async () => {
  try {
    const fetchUrl = `${gateWay}${service.administrate}/eoa/saveApproveLinkConfig`;
    const res = await $postOnClient(fetchUrl, formState.value);
    const { msg = '' } = res || {};
    if (res && res.code === SUCCESS_CODE) {
      message.success('新增成功');
      open.value = false;
      search();
    } else {
      message.error(msg);
    }
    btnLoading.value = false;
  } catch (error) {
    console.log(error);
    btnLoading.value = false;
  }
};
// 编辑提交
const updateApproveLinkConfig = async () => {
  try {
    const fetchUrl = `${gateWay}${service.administrate}/eoa/updateApproveLinkConfig`;
    const params = {
      ...formState.value,
      idParmEoaAprvLnkDef: idParmEoaAprvLnkDef.value,
    };
    const res = await $postOnClient(fetchUrl, params);
    const { msg = '' } = res || {};
    if (res && res.code === SUCCESS_CODE) {
      open.value = false;
      message.success('编辑成功');
      search();
    } else {
      message.error(msg);
    }
    btnLoading.value = false;
  } catch (error) {
    console.log(error);
    btnLoading.value = false;
  }
};
// 审批链option
const optionsList = ref<ValueType[]>([]);
// 签报类型option
const flowOwnerTypeOption = ref<ValueType[]>([]);
// 查询枚举
const initOption = async () => {
  try {
    const fetchUrl = `${gateWay}${service.administrate}/public/getSelect`;
    const params = ['eoa_type', 'flow_owner_type'];
    const res = await $postOnClient(fetchUrl, params);
    const { flow_owner_type = [], eoa_type = [] } = (res?.data as OptionsType) || {};
    if (res && res.code === SUCCESS_CODE) {
      optionsList.value = flow_owner_type?.map((item: FlowOwnerType) => ({
        value: item.parameterCode,
        label: item.parameterName,
      }));
      flowOwnerTypeOption.value = eoa_type?.map((item: FlowOwnerType) => ({
        value: item.parameterCode,
        label: item.parameterName,
      }));
      flowOwnerTypeOption.value.unshift({ value: '', label: '所有' });
    }
  } catch (error) {
    console.log(error);
  }
};
// 规则校验-保留4位小数
const validateNumber = (_rule: Rule, value: string) => {
  const reg = /^\d{0,20}(\.\d{1,4})?$/;
  if (!reg.test(value)) {
    return Promise.reject('请输入正数且小数位不能超过4位');
  }
  return Promise.resolve();
};
const visible = ref<boolean>(false);
// 列表数据删除
const handleDelUpdate = (id: string) => {
  visible.value = true;
  idParmEoaAprvLnkDef.value = id;
};
// 确认删除
const handleDeleteOk = async () => {
  try {
    const fetchUrl = `${gateWay}${service.administrate}/eoa/deleteApproveLinkConfig`;
    const res = await $getOnClient(fetchUrl, { idParmEoaAprvLnkDef: idParmEoaAprvLnkDef.value });
    const { msg = '' } = res || {};
    if (res && res.code === SUCCESS_CODE) {
      message.success('删除成功');
      visible.value = false;
      search();
    } else {
      message.error(msg);
    }
  } catch (error) {
    console.log(error);
  }
};

// 改变机构
const changeDeptCode = (val: string, formName: string) => {
  if (formName === 'searchForm') {
    searchFormState.value.departmentCode = val;
  } else {
    formState.value.departmentCode = val;
  }
};

// 改变签报类型
const handleChangeEoaType = () => {
  searchFormState.value = {
    ...searchFormState.value,
    departmentCode: defaultDeptCode.value, // 机构编码
    applyPolicyTypes: [], // 投保方式
    riskType: '', // 标的类型
    subsidyTypes: [], // 补贴类型
  };
};

// 获取基础信息数据
const queryBaseData = async () => {
  const requestParams = {
    url: `${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`,
    params: ['subsidyType', 'applyPolicyType'],
  };
  const res = (await getCacheData('optionData', requestParams)) as SelectOptions[];
  plainOptions.value = res?.find((item: { value: string }) => item.value === 'subsidyType')?.children || [];
  applyPolicyOptions.value = res?.find((item: { value: string }) => item.value === 'applyPolicyType')?.children || [];
};

const { pagination } = usePagination(refresh);
onMounted(() => {
  initOption();
  queryBaseData();
  search();
});
</script>
