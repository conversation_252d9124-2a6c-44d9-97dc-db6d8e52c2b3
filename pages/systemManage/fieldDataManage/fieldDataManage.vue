<template>
  <div class="mx-[14px] my-[12px] p-[16px] py-[20px] bg-[#ffffff] rounded-[6px]">
    <a-form ref="formRef" :model="modelRef" :label-col="{ span: 7 }" :colon="false" :rules="rulesRef">
      <a-row :gutter="16">
        <a-col span="8">
          <a-form-item label="保单号" name="policyNo">
            <a-input v-model:value="modelRef['policyNo']" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item name="platformConfirmCode">
            <template #label>
              <span class="mr-[2px]">投保确认码</span>
              <a-tooltip>
                <template #title>中农再预校验接口，则存的是agroReId值（中国农再投保单ID）；中农再保单接口，则存的是confirmSequenceNo值（保单投保确认码）；上海保单接口，则存的是confirmSequenceNo值（保单投保确认码）</template>
                <VueIcon :icon="IconInfoCircleFilledFont" />
              </a-tooltip>
            </template>
            <a-input v-model:value="modelRef['platformConfirmCode']" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item label="所属平台代码" name="thirdPartyPlatformNo">
            <a-input v-model:value="modelRef['thirdPartyPlatformNo']" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item label="所属接口代码" name="thirdPartyPlatformInterfaceNo">
            <a-input v-model:value="modelRef['thirdPartyPlatformInterfaceNo']" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item name="demandSequenceNo">
            <template #label>
              <span class="mr-[2px]">上传序列号</span>
              <a-tooltip>
                <template #title>中农再：orderNo值（上传序列号）；上海：orderNo值（上传序列号）；河北农业：resultId数据对比接口返回的唯一标识</template>
                <VueIcon :icon="IconInfoCircleFilledFont" />
              </a-tooltip>
            </template>
            <a-input v-model:value="modelRef['demandSequenceNo']" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item name="termNo">
            <template #label>
              <span class="mr-[2px]">通知单期次</span>
              <a-tooltip>
                <template #title>中农再：premiumTerm字段</template>
                <VueIcon :icon="IconInfoCircleFilledFont" />
              </a-tooltip>
            </template>
            <a-input v-model:value="modelRef['termNo']" placeholder="请输入数字" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="flex justify-center mt-[10px]">
      <a-button type="primary" @click="submit">确定</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IconInfoCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { Rule } from 'ant-design-vue/es/form';
import { message } from '@/components/ui/Message';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost } from '@/composables/request';

const { fetchData: updatePlatInfoRes } = await usePost('/api/compliance/updatePlatInfo');
const formRef = ref();
const modelRef = ref({
  policyNo: '',
  platformConfirmCode: '',
  thirdPartyPlatformNo: '',
  thirdPartyPlatformInterfaceNo: '',
  demandSequenceNo: '',
  termNo: undefined,
});
const rulesRef = ref<Record<string, Rule[]>>({
  policyNo: [{ required: true, message: '请输入', trigger: 'blur' }],
  thirdPartyPlatformNo: [{ required: true, message: '请输入', trigger: 'blur' }],
  thirdPartyPlatformInterfaceNo: [{ required: true, message: '请输入', trigger: 'blur' }],
  termNo: [{ required: true, message: '请输入数字', trigger: 'blur' }, { pattern: /^\d+$/, message: '请输入数字', trigger: 'blur' }],
});

const submit = () => {
  formRef.value?.validate()
    .then(async () => {
      const { code, msg } = await updatePlatInfoRes(modelRef.value);
      if (code === SUCCESS_CODE) {
        message.success('成功！');
        formRef.value?.resetFields();
      } else {
        message.error(msg);
      }
    });
};
</script>
