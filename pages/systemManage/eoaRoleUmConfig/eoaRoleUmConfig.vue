<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="searchForm" :colon="false" class="flex-grow" :model="searchFormState">
          <a-row :gutter="16">
            <a-col :span="10">
              <a-form-item label="机构" name="departmentCode" :rules="[{ required: true, trigger: 'change', message: '请选择机构' }]">
                <department-search v-model:contain-child-depart="searchFormState.containLowDepartment" :disabled-select="true" :dept-code="searchFormState.departmentCode" :show-child-depart="true" @change-dept-code="(val: string, level: number) => changeDeptCode(val, level, 'searchFormState')" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="角色" name="flowOwnerType" :rules="[{ required: true, message: '请选择角色' }]">
                <a-select v-model:value="searchFormState.flowOwnerType" :filter-option="filterOption" show-search :options="roleList" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div>注：请录入2、二级机构、三级机构</div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button type="primary" @click="search">查询</a-button>
        <a-button type="primary" @click="showModal">新增</a-button>
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-16px">
        <div class="leading-[32px] text-[16px] text-[rgba(0,0,0,0.9)] font-bold">查询结果</div>
      </div>
      <a-table :columns="listColumns" :pagination="pagination" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content' }" class="table-box">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
          <template v-if="column.dataIndex === 'operation'">
            <div>
              <a-button type="link" :title="disabledStatus(record.flowOwnerType) ? hoverText: ''" :disabled="disabledStatus(record.flowOwnerType)" @click="edit(record as ManageFormState)">编辑</a-button>
              <a-button type="link" :title="disabledStatus(record.flowOwnerType) ? hoverText: ''" :disabled="disabledStatus(record.flowOwnerType)" @click="handleDelete(record.idParmEoaAprvUser)">删除</a-button>
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <a-modal v-model:open="open" :title="modalTitle" :width="pxToRem(360)" @ok="handleOk">
      <a-form ref="manageForm" :colon="false" class="flex-grow" :model="manageFormState">
        <a-row>
          <a-col :span="24">
            <a-form-item label="机构" name="departmentCode" :rules="[{ required: true, trigger: 'change', message: '请选择机构' }]">
              <department-search :disabled="true" :dept-code="manageFormState.departmentCode" :show-child-depart="false" @change-dept-code="(val: string, level: number) => changeDeptCode(val, level, 'manageFormState')" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="角色" name="flowOwnerType" :rules="[{ required: true, trigger: 'change', message: '请选择角色' }]">
              <a-select v-model:value="manageFormState.flowOwnerType" :filter-option="filterOption" show-search :options="configRoleList" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="UM号" name="um" :rules="[{ required: true, trigger: 'blur', message: '请填写UM号' }, { validator: umRule }]">
              <a-input v-model:value="manageFormState.um" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <template #footer>
        <a-button key="back" @click="open = false">取消</a-button>
        <a-button key="submit" type="primary" :loading="btnLoading" @click="handleOk">确定</a-button>
      </template>
    </a-modal>
    <!-- 确认删除弹窗 -->
    <a-modal v-model:open="delModalOpen" :width="pxToRem(450)" :centered="true">
      <div>
        <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px]">提醒</span>
      </div>
      <div class="mt-[10px]">删除操作不可撤销，是否确认删除？</div>
      <template #footer>
        <a-button key="back" @click="delModalOpen = false">取消</a-button>
        <a-button key="submit" type="primary" :loading="btnLoading" @click="handleDeleteOk">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import type { Rule } from 'ant-design-vue/es/form';
import type { TableColumnsType } from 'ant-design-vue';
import { IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { usePost, $getOnClient } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import { pxToRem } from '@/utils/tools';

const listColumns: TableColumnsType = [
  { title: '序号', dataIndex: 'index' },
  { title: '机构', dataIndex: 'departmentName' },
  { title: '角色', dataIndex: 'flowOwnerTypeName' },
  { title: 'UM账号', dataIndex: 'um' },
  { title: '姓名', dataIndex: 'name' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];
const umRule = (rule: Rule, value: string) => {
  const req = /^[a-zA-Z]{1}([a-zA-Z0-9]|[._]){1,100}$/;
  if (value !== '' && !req.test(value)) {
    return Promise.reject('不符合UM填写规范，请更正');
  }
  if (value.length > 100) {
    return Promise.reject('不能超过100个字符长度!');
  }
  return Promise.resolve();
};
// 查询条件
interface SearchFormState {
  departmentCode: string; // 机构编码
  containLowDepartment?: boolean; // 是否包含下级机构
  flowOwnerType: string; // 角色
}
// 配置页面
interface ManageFormState extends SearchFormState {
  um: string; // um号
  idParmEoaAprvUser: string; // id
}
const modalTitle = ref<string>(''); // 弹窗标题
const btnLoading = ref(false);
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
// 配置弹窗显示隐藏字段
const open = ref<boolean>(false);
const manageForm = ref();
const searchForm = ref();
// 查询条件角色选项
const roleList = ref<Record<string, string>[]>([]);
// 配置页面角色选项
const configRoleList = ref<Record<string, string>[]>([]);
const searchFormState = ref<SearchFormState>({
  flowOwnerType: '0',
  departmentCode: defaultDeptCode.value,
  containLowDepartment: true,
});
const manageFormState = ref<ManageFormState>({
  flowOwnerType: '',
  departmentCode: '',
  um: '',
  idParmEoaAprvUser: '',
});
const { gateWay, service } = useRuntimeConfig().public || {};
// 角色搜索
const filterOption = (input: string, option: Record<string, string>) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
// 改变机构
const changeDeptCode = (val: string, level: number, formName: string) => {
  if (formName === 'manageFormState') {
    manageFormState.value.departmentCode = val;
  } else {
    if (level <= 3 || searchFormState.value.departmentCode === '2') {
      searchFormState.value.departmentCode = val;
    } else {
      searchFormState.value.departmentCode = '';
      message.error('机构输入有误，请重新输入');
    }
  }
};
// 显示弹窗-新增
const showModal = async () => {
  try {
    await searchForm.value.validate();
    modalTitle.value = '新增';
    manageFormState.value = {
      flowOwnerType: '',
      departmentCode: searchFormState.value.departmentCode,
      um: '',
      idParmEoaAprvUser: '',
    };
    manageForm.value?.clearValidate();
    open.value = true;
  } catch (e) {
    console.log(e);
  }
};
// 显示弹窗-编辑
const edit = async (record: ManageFormState) => {
  modalTitle.value = '编辑';
  manageFormState.value = {
    flowOwnerType: record.flowOwnerType,
    departmentCode: record.departmentCode,
    um: record.um,
    idParmEoaAprvUser: record.idParmEoaAprvUser,
  };
  manageForm.value?.clearValidate();
  open.value = true;
};
// 弹窗确定
const handleOk = async () => {
  try {
    await manageForm.value.validate();
    if (modalTitle.value === '新增') {
      addRole();
    } else {
      editRole();
    }
  } catch (error) {
    console.log(error);
  }
};
const saveReq = await usePost(`${gateWay}${service.administrate}/eoa/saveUser`);
// 新增保存
const addRole = async () => {
  btnLoading.value = true;
  try {
    const params = {
      departmentCode: manageFormState.value.departmentCode,
      flowOwnerType: manageFormState.value.flowOwnerType,
      um: manageFormState.value.um,
    };
    const res = await saveReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      open.value = false;
      message.success(res.msg || '新增成功！');
      search();
    } else {
      message.error(res?.msg || '新增失败！');
    }
  } catch (error) {
    console.log(error);
  } finally {
    btnLoading.value = false;
  }
};
const editReq = await usePost(`${gateWay}${service.administrate}/eoa/editUser`);
// 编辑确定
const editRole = async () => {
  btnLoading.value = true;
  try {
    const params = {
      departmentCode: manageFormState.value.departmentCode,
      flowOwnerType: manageFormState.value.flowOwnerType,
      um: manageFormState.value.um,
      idParmEoaAprvUser: manageFormState.value.idParmEoaAprvUser,
    };
    const res = await editReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      open.value = false;
      message.success(res.msg || '编辑成功！');
      search();
    } else {
      message.error(res?.msg || '编辑失败！');
    }
  } catch (error) {
    console.log(error);
  } finally {
    btnLoading.value = false;
  }
};
// 删除
const delModalOpen = ref<boolean>(false);
const idParmEoaAprvUser = ref<string>('');
const delReq = await usePost(`${gateWay}${service.administrate}/eoa/delUser`);
// 删除弹窗-确定
const handleDeleteOk = async () => {
  btnLoading.value = true;
  try {
    const params = { idParmEoaAprvUser: idParmEoaAprvUser.value };
    const res = await delReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      delModalOpen.value = false;
      message.success(res.msg || '删除成功！');
      search();
    } else {
      message.error(res?.msg || '删除失败！');
    }
  } catch (error) {
    console.log(error);
  } finally {
    btnLoading.value = false;
  }
};
// 点击删除
const handleDelete = (id: string) => {
  delModalOpen.value = true;
  idParmEoaAprvUser.value = id;
};
const dataSource = ref<ManageFormState[]>([]);
const loading = ref(false);
// 查询
const search = async () => {
  try {
    await searchForm.value.validate();
    pagination.current = 1;
    pagination.pageSize = 10;
    refresh();
  } catch (e) {
    console.log(e);
  }
};
const getListReq = await usePost<{ records: ManageFormState[]; total: number; current: number; size: number }>(`${gateWay}${service.administrate}/eoa/getApproveUserList`);
// 角色用户分页查询
const refresh = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      departmentCode: searchFormState.value.departmentCode,
      flowOwnerType: searchFormState.value.flowOwnerType === '0' ? '' : searchFormState.value.flowOwnerType,
      containLowDepartment: searchFormState.value.containLowDepartment,
    };
    const res = await getListReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data;
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const { pagination } = usePagination(refresh);
const getRuleReq = await usePost<{ flow_owner_type: Record<string, string>[] }>(`${gateWay}${service.administrate}/public/getSelect`);
// 获取角色列表
const getRoleList = async () => {
  try {
    const params = ['flow_owner_type'];
    const res = await getRuleReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      roleList.value = [];
      if (res?.data?.flow_owner_type?.length > 0) {
        res.data.flow_owner_type.forEach((item: Record<string, string>) => {
          const obj = {
            label: item.parameterName,
            value: item.parameterCode,
          };
          roleList.value.push(obj);
        });
        roleList.value?.unshift({ label: '所有', value: '0' });
      }
    }
  } catch (error) {
    console.log(error);
  }
};
// 获取配置/编辑的角色列表
const getSettingRoles = async () => {
  configRoleList.value = [];
  try {
    const fetchUrl = `${gateWay}${service.administrate}/eoa/getLimitedRoles`;
    const res = await $getOnClient(fetchUrl, {});
    const { msg = '' } = res || {};
    if (res && res.code === SUCCESS_CODE) {
      configRoleList.value = res.data as Record<string, string>[] || [];
    } else {
      message.error(msg);
    }
  } catch (error) {
    console.log(error);
  }
};
// 是否禁用编辑和删除按钮
const disabledStatus = (flowOwnerType: string) => configRoleList.value.findIndex(item => item.value === flowOwnerType) === -1;
// 禁用的编辑/删除按钮悬浮显示文字
const hoverText = ref<string>('');
const showText = () => {
  // 判断登陆用户是否有总部角色
  const isManager = userInfo.roleCodeList.some(item => item === 'R_ICORE_AGR_AN_HEAD_UNDERWRITER_MANAGER' || item === 'R_ICORE_AGR_AN_HEAD_UNDERWRITER');
  if (isManager) {
    hoverText.value = '机构角色请联系机构老师修改';
  } else {
    hoverText.value = '总部角色请联系总部老师修改';
  }
};
onMounted(() => {
  showText();
  getRoleList();
  getSettingRoles();
  search();
});
</script>
