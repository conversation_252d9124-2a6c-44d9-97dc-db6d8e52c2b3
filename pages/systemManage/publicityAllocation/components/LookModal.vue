<template>
  <a-modal v-model:open="visible" title="公示信息" :width="pxToRem(800)" centered>
    <div class="mb-[18px]">
      <div class="flex items-center mb-[9px] base-info">
        <VueIcon :icon="IconJichuxinxiFont" />
        <span class="ml-[3px]">基础信息</span>
      </div>
      <div class="bg-gray-100 rounded p-14px box-border">
        <div class="flex mb-[16px]">
          <p class="m-0 w-1/2 shrink-0 box-border">
            <span>机构：</span>
            <span class="text-[#262626]">{{ infoData.departmentName }}</span>
          </p>
          <p class="flex m-0 shrink-0 w-1/2 box-border">
            <span class="text-nowrap">标的：</span>
            <span class="text-[#262626]">{{ infoData.riskObjectDetailName }}</span>
          </p>
        </div>
        <div class="flex mb-[16px]">
          <p class="m-0 w-1/2 shrink-0 box-border">
            <span>投保方式：</span>
            <span class="text-[#262626]">{{ applyPolicyName }}</span>
          </p>
        </div>
      </div>
    </div>
    <div class="mb-[18px]">
      <div class="flex items-center mb-[9px] base-info">
        <VueIcon :icon="SolutionfontFont" />
        <span class="ml-[3px]">公示方式</span>
      </div>
      <div class="bg-gray-100 rounded p-14px box-border">
        <div v-for="(item, index) in infoData.publicityConfigList" :key="index" class="flex mb-[16px]">
          <p class="m-0 w-1/3 shrink-0 box-border">
            <span>{{ item.publicityMethodDesc }}</span>
            <span class="text-[#262626]" />
          </p>
          <p class="flex m-0 shrink-0 w-1/3 box-border">
            <span class="text-nowrap">公示天数：</span>
            <span class="text-[#262626]">{{ item.publicityDays }}天</span>
          </p>
          <p class="flex m-0 shrink-0 box-border">
            <span class="text-nowrap">必选：</span>
            <span class="text-[#262626]">{{ item.requiredFlag === 'Y' ? '是' : '否' }}</span>
          </p>
        </div>
      </div>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleCancel">返回</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { IconJichuxinxiFont, SolutionfontFont } from '@pafe/icons-icore-agr-an';
import type { PublicityDataType } from '../publicityAllocation';
import { pxToRem } from '@/utils/tools';

const visible = defineModel<boolean>('visible', { required: true, default: false });
const props = withDefaults(defineProps<{
  infoData: PublicityDataType;
}>(), {});
// 投保方式
const applyPolicyName = computed (() => props.infoData.applyPolicyType === '2' ? '组织投保' : '非组织投保');
// 弹窗关闭
const handleCancel = () => {
  visible.value = false;
};
</script>

  <style lang="less">
  .base-info {
    color: rgba(0,0,0,0.9);
  }
  </style>
