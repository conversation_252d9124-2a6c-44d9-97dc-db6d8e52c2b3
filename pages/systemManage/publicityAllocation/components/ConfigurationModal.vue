<template>
  <a-modal v-model:open="visible" title="配置页面" :width="pxToRem(880)" :confirm-loading="confirmLoading" :mask-closable="false" centered @ok="handleOk">
    <a-form ref="formStateRef" :model="formState" autocomplete="off" :colon="false" required label-align="left" :label-col="{ style: { width: pxToRem(80) } }">
      <div class="flex items-center mb-[9px]">
        <VueIcon :icon="IconJichuxinxiFont" />
        <span class="ml-[3px]">基础信息</span>
      </div>
      <div class="bg-gray-100 rounded p-14px box-border mb-8px">
        <a-form-item label="机构" name="departmentNo">
          <DepartmentSearch :dept-code="formState.departmentNo" @change-dept-code="changeDeptCode" />
        </a-form-item>
        <a-form-item label="标的类型" name="riskObjectDetailCode" required>
          <RiskCodeSelect v-model:value="formState.riskObjectDetailCode" :department-code="formState.departmentNo" @change-value="changeRiskCode" />
        </a-form-item>
        <a-form-item label="投保方式" name="applyPolicyType" :rules="[{ required: true, message: '请选择投保方式' }]">
          <a-radio-group v-model:value="formState.applyPolicyType">
            <a-radio value="2">组织投保</a-radio>
            <a-radio value="1">非组织投保</a-radio>
          </a-radio-group>
        </a-form-item>
      </div>
      <div class="flex items-center mb-[9px]">
        <VueIcon :icon="SolutionfontFont" />
        <span class="ml-[3px]">公示方式</span>
      </div>
      <div class="bg-gray-100 rounded p-14px box-border">
        <a-form-item label="公示方式" :auto-link="false" name="typeLen" label-align="left" :rules="[{ required: true, validator: validatorChecked }]">
          <a-row v-for="(item, index) in options" :key="index" class="items-baseline" :gutter="24">
            <a-col :span="colCount(item.publicityMethodCode)">
              <a-checkbox v-model:checked="item.isChecked" @change="handleChange"> {{ item.publicityTypeDesc }}</a-checkbox>
            </a-col>
            <a-col v-if="item.publicityMethodCode === 'LOCAL_PF'" :span="6">
              <a-form-item :name="`address + ${index + 1}`" :rules="[{ required: item.isChecked, validator: (rule, value) => validatorAddress(rule, value, index) }]">
                <a-select v-model:value="item.addressTypeCode" :options="addressOption" :disabled="!item.isChecked" placeholder="请选择地区" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="9">
              <a-form-item v-if="item.publicityMethodCode !== 'PA_OFFLINE'" label="公示天数" :name="`publicityDays + ${index + 1}`" :rules="[{ required: item.isChecked, validator: (rule, value) => validatorDays(rule, value, index) }]">
                <a-input v-model:value="item.publicityDays" :disabled="!item.isChecked">
                  <template #suffix>
                    <span class="text-[#D4D6D9]">天</span>
                  </template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="3">
              <a-form-item>
                <!-- 测试跟产品沟通后-需求为：中银保信，地方平台选中必选也默认选中，其余两个不联动 -->
                <a-checkbox v-if="['PA_OFFLINE', 'PA_ONLINE'].includes(item.publicityMethodCode)" v-model:checked="item.flag" :disabled="!item.isChecked"> 必选 </a-checkbox>
                <a-checkbox v-if="['ZBX', 'LOCAL_PF'].includes(item.publicityMethodCode)" v-model:checked="item.isChecked" :disabled="true"> 必选 </a-checkbox>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { IconJichuxinxiFont, SolutionfontFont } from '@pafe/icons-icore-agr-an';
import type { Rule } from 'ant-design-vue/es/form';
import type { publicityListType, formStateType, ChildrenType } from '../publicityAllocation';
import { pxToRem } from '@/utils/tools';
import { $postOnClient, $post } from '@/composables/request';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import { SUCCESS_CODE } from '@/utils/constants';

const { gateWay, service } = useRuntimeConfig().public || {};
const visible = defineModel<boolean>('visible', { required: true, default: false });
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
// 公示方式列表
const options = reactive<publicityListType[]>([
  { publicityTypeDesc: '我司线下', publicityMethodCode: 'PA_OFFLINE', flag: false, publicityDays: '', isChecked: false },
  { publicityTypeDesc: '中银保信', publicityMethodCode: 'ZBX', flag: false, publicityDays: '', isChecked: false },
  { publicityTypeDesc: '爱农宝', publicityMethodCode: 'PA_ONLINE', flag: false, publicityDays: '', isChecked: false },
  { publicityTypeDesc: '地方平台', publicityMethodCode: 'LOCAL_PF', flag: false, publicityDays: '', isChecked: false, addressTypeCode: undefined },
]);
// 表单初始化
const formState = reactive<formStateType>({
  departmentNo: defaultDeptCode.value,
  riskObjectDetailCode: '',
  applyPolicyType: '',
  publicityConfigList: [],
  typeLen: 0,
});
// 控制样式
const colCount = (val: string) => {
  return val === 'LOCAL_PF' ? 4 : 10;
};
// 初始化类型
interface Option {
  value: string;
  label: string;
}
// 地区下拉值
const addressOption = ref<Option[]>([]);
// 获取地区
const initOption = async () => {
  const url = gateWay + service.administrate + '/parmBaseConstantConf/getParmBaseConstantConf';
  const res = await $post(url, ['LOCAL_PF']);
  const { data = [] } = res || {};
  const originData = (data as ChildrenType[])?.find((item: Option) => item.value === 'LOCAL_PF');
  if (originData && originData.children) {
    addressOption.value = originData.children.map((item: Option) => ({
      value: item.value,
      label: item.label,
    }));
  }
};
initOption();

// 选择机构
const changeDeptCode = (value: string) => {
  formState.departmentNo = value;
};
// 提交
const formStateRef = ref();
const handleOk = () => {
  formStateRef.value.validate()
    .then(() => { handleSubmit(); })
    .catch(() => {});
};
// 配置提交
const emit = defineEmits(['handleSubmit']);
const confirmLoading = ref<boolean>(false);
const handleSubmit = async () => {
  confirmLoading.value = true;
  // 获取勾选的对象
  formState.publicityConfigList = options?.filter(item => item.isChecked)?.map((dom) => {
    if (['ZBX', 'LOCAL_PF'].includes(dom.publicityMethodCode)) {
      dom.flag = true;
    }
    return {
      ...dom,
      requiredFlag: dom.flag ? 'Y' : 'N',
    };
  });
  try {
    const fetchUrl = gateWay + service.accept + '/publicity/savePublicityConfig';
    const res = await $postOnClient(fetchUrl, formState);
    const { msg = '', code } = res || {};
    if (code === SUCCESS_CODE) {
      message.success('配置成功');
      visible.value = false;
      emit('handleSubmit');
    } else {
      message.error(msg);
    }
    confirmLoading.value = false;
  } catch (error) {
    confirmLoading.value = false;
    console.log(error);
  }
};
// 选择公示方式赋值
const handleChange = () => {
  formState.typeLen = options.filter(item => item.isChecked)?.length;
  formStateRef.value.validate('typeLen');
};
const validatorAddress = (_rule: Rule, value: string, index: number) => {
  if (options[index].isChecked) {
    if (!options[index].addressTypeCode) {
      return Promise.reject('请选择地区');
    }
    return Promise.resolve();
  } else {
    return Promise.resolve();
  }
};
const validatorChecked = (_rule: Rule, value: string) => {
  if (!value) {
    return Promise.reject('请选择公示方式');
  }
  return Promise.resolve();
};
// 公示天数规则校验
const validatorDays = (_rule: Rule, value: string, index: number) => {
  if (options[index].isChecked) {
    if (!options[index].publicityDays) {
      return Promise.reject('请输入天数');
    }
    // 只能输入正数，不能负数或者小数点
    const regex = /^[1-9]\d*$/;
    if (!regex.test(options[index].publicityDays)) {
      return Promise.reject('请输入正数');
    }
    if (Number(options[index].publicityDays) > 100 || Number(options[index].publicityDays) === 0) {
      return Promise.reject('公示日期范围为1-100天');
    }
    return Promise.resolve();
  } else {
    return Promise.resolve();
  }
};
const changeRiskCode = () => {
  formStateRef.value.validateFields('riskObjectDetailCode');
};
</script>
