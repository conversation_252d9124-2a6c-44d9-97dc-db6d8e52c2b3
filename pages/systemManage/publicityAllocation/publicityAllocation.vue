<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="formRef" :colon="false" class="flex-grow" :model="formState" :rules="formRules">
          <a-row :gutter="16">
            <a-col :span="10">
              <a-form-item label="机构" name="departmentNo">
                <departmentSearch v-model:contain-child-depart="formState.containChildDepart" :dept-code="formState.departmentNo" :show-child-depart="true" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="16">
            <a-col :span="24">
              <a-form-item label="标的类型">
                <RiskCodeSelect v-model:value="formState.riskObjectDetailCode" :department-code="formState.departmentNo" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="w-[50px]">
          <form-fold v-model="expand" />
        </div>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button @click="resetForm">重置</a-button>
        <a-button type="primary" ghost @click="handleSubmit">查询 </a-button>
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-16px">
        <div class="leading-[32px] text-[16px] text-[rgba(0,0,0,0.9)] font-bold">查询结果</div>
        <AuthButton code="publicityConfig" type="primary" @click="handleAdd">配置</AuthButton>
      </div>
      <a-table :columns="columns" :data-source="dataSource" :loading="loading" :pagination="pagination" :scroll="{ x: 'max-content' }">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'applyPolicyType'">
            <span v-if="record.applyPolicyType === '2'">组织投保</span>
            <span v-if="record.applyPolicyType === '1'">非组织投保</span>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <div>
              <a-button type="link" @click="handleLook(record)">查看</a-button>
              <AuthButton code="publicityWithtraw" type="link" @click="handleDelete(record)"> 注销</AuthButton>
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <!-- 配置-新增 -->
    <ConfigurationModal v-if="visible" v-model:visible="visible" @handle-submit="handleSubmit" />
    <!-- 查看 -->
    <LookModal v-model:visible="lookVisible" :info-data="infoData" />
    <!-- 确认失效弹窗 -->
    <a-modal v-model:open="open" :width="pxToRem(450)" @ok="handleDeleteOk">
      <div>
        <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px]">提醒</span>
      </div>
      <div class="mt-[10px]">是否确认失效</div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnsType } from 'ant-design-vue';
import { IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import ConfigurationModal from './components/ConfigurationModal.vue';
import LookModal from './components/LookModal.vue';
import type { PublicityDataType, DataType } from './publicityAllocation.d';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import FormFold from '@/components/ui/FormFold.vue';
import { $getOnClient } from '@/composables/request';
import { message } from '@/components/ui/Message';
import { usePagination } from '@/composables/usePagination';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import AuthButton from '@/components/ui/AuthButton.vue';

const formRef = ref();
const columns: TableColumnsType = ([
  { title: '机构', dataIndex: 'departmentName' },
  { title: '标的', dataIndex: 'riskObjectDetailName' },
  { title: '投保方式', dataIndex: 'applyPolicyType' },
  { title: '创建人', dataIndex: 'createdBy' },
  { title: '创建日期', dataIndex: 'createdDate' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
]);
interface FormState {
  departmentNo: string; // 机构编码
  containChildDepart: boolean; // 是否包含下级机构
  riskObjectDetailCode: string; // 标的
}
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
// 表单
const formState = reactive<FormState>({
  departmentNo: defaultDeptCode.value,
  containChildDepart: true,
  riskObjectDetailCode: '',
});
  // 表单规则
const formRules = reactive({
  departmentNo: [{ required: true }],
});

// 搜索项是否展开
const expand = ref(true);

const changeDeptCode = (val: string) => {
  formState.departmentNo = val;
};
  // 重置
const resetForm = () => {
  formRef.value?.resetFields();
  refresh();
};
  // 配置成功回调 & 查询
const handleSubmit = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
};

// 配置-新增
const visible = ref<boolean>(false);
const handleAdd = () => {
  visible.value = true;
};
  // 查看
const lookVisible = ref<boolean>(false);
const infoData = ref({});
const handleLook = (record: PublicityDataType) => {
  lookVisible.value = true;
  infoData.value = record;
};
  // 失效
const open = ref<boolean>(false);
const records = ref<PublicityDataType>({});
const handleDelete = async (record: PublicityDataType) => {
  open.value = true;
  records.value = record;
};
const handleDeleteOk = async () => {
  try {
    const { departmentNo, riskObjectDetailCode, applyPolicyType } = records.value || {};
    const params = {
      departmentNo,
      riskObjectDetailCode,
      applyPolicyType,
    };
    const fetchUrl = gateWay + service.accept + '/publicity/deletePublicityConfig';
    const res = await $getOnClient(fetchUrl, params);
    const { code, msg = '' } = res || {};
    if (code === SUCCESS_CODE) {
      message.success('注销成功');
      open.value = false;
      handleSubmit();
    } else {
      message.error(msg);
    }
  } catch (error) {
    console.log(error);
  }
};

const dataSource = ref<PublicityDataType[]>([]);
const loading = ref(false);
// 列表查询
const refresh = () => {
  const getData = async () => {
    loading.value = true;
    try {
      const params = {
        ...formState,
        containChildDepart: formState.containChildDepart ? '1' : '0',
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        riskObjectDetailCode: formState.riskObjectDetailCode ? formState.riskObjectDetailCode : '',
      };
      const fetchUrl = gateWay + service.accept + '/publicity/queryPublicityConfig';
      const res = await $getOnClient(fetchUrl, params);
      const { records = [], total = 0, current = 1, size = 10 } = res?.data as DataType || {};
      if (res?.code === SUCCESS_CODE) {
        dataSource.value = records;
        pagination.total = total;
        pagination.current = current;
        pagination.pageSize = size;
      } else {
        dataSource.value = [];
        pagination.total = 0;
        pagination.current = 1;
        pagination.pageSize = 10;
      }
      loading.value = false;
    } catch (error) {
      loading.value = false;
      console.log(error);
    }
  };
  formRef.value?.validate().then(() => {
    getData();
  });
};

onMounted(() => {
  refresh();
});
const { pagination } = usePagination(refresh);
const { gateWay, service } = useRuntimeConfig().public || {};
</script>
