export interface Option {
  label: string;
  value: string;
}
export interface publicityListType {
  publicityMethodCode: string;
  publicityTypeDesc?: string;
  publicityDays?: string;
  requiredFlag?: string;
  isChecked?: boolean;
  addressTypeCode?: undefined | string;
  flag?: boolean;
  publicityMethodDesc?: string;
}
export interface PublicityDataType {
  departmentNo?: string;
  departmentName?: string;
  riskObjectDetailCode?: string;
  riskObjectDetailName?: string;
  applyPolicyType?: string; // 非组织投保-组织投保
  applyPolicyName?: string;
  publicityConfigList?: publicityListType[];
  createdBy?: string;
  createdDate?: string;
  publicityMethodDesc?: string;
}
export interface formStateType {
  departmentNo: string;
  riskObjectDetailCode: string;
  applyPolicyType: string;
  publicityConfigList: publicityListType[];
  typeLen: number;
}
export interface DataType {
  records: RecordType[];
  total: number;
  current: number;
  size: number;
}
export interface ChildrenType {
  [key: string]: string;
  children: ChildrenType[];
  label: string;
  value: string;
}
