<template>
  <div class="p-14px space-y-14px">
    <div class="bg-white p-16px rounded-md">
      <div class="flex">
        <a-form ref="formRef" :model="formData" :colon="false" class="flex-grow" :label-col="{ style: { width: pxToRem(80) } }" :rules="formRules">
          <a-row :gutter="24">
            <a-col :span="10">
              <a-form-item label="机构" name="containChildDepart">
                <department-search v-model:contain-child-depart="formData.containChildDepart" :dept-code="formData.specialPromiseDept" :show-child-depart="true" @change-dept-code="changeDeptCode" />
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item label="创建日期" name="time" class="labelClassHack-80">
                <a-date-picker v-model:value="formData.time" placeholder="请选择" :format="dateFormat" :disabled-date="disabledDate" :value-format="dateFormat" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item name="marketProductCodeList" label="市场产品">
                <a-select v-model:value="formData.marketProductCodeList" mode="multiple" :options="marketProductOptions" allow-clear show-search option-filter-prop="label" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand">
            <a-col :span="24">
              <a-form-item name="riskCode" label="标的">
                <RiskCodeSelect v-model:value="formData.riskCode" :department-code="formData.departmentCode" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="w-[50px]">
          <form-fold v-model="expand" />
        </div>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button @click="resetForm">重置</a-button>
        <a-button type="primary" ghost @click="submit">查询</a-button>
      </div>
    </div>
    <div class="bg-white p-16px rounded-md">
      <div class="mb-16px flex justify-between">
        <div class="text-[#404442] text-xl font-bold">查询结果</div>
        <div class="space-x-[12px]">
          <a-button @click="importVisible = true">批量上传</a-button>
          <a-button>批量导出</a-button>
          <a-button type="primary" @click="handleSetting">配置</a-button>
        </div>
      </div>
      <a-table
        :loading="loading"
        :data-source="dataSource"
        :columns="columns"
        :pagination="pagination"
        :bordered="false"
        :scroll="{ x: 'max-content' }"
        :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'operation'">
            <a-button type="link" size="small" @click="handleView(record)">查看</a-button>
            <a-button type="link" size="small" @click="handleCancel">注销</a-button>
            <a-button type="link" size="small">导出</a-button>
          </template>
        </template>
      </a-table>
    </div>
    <!-- 批量导入 -->
    <ImportModal v-model:visible="importVisible" />
  </div>
</template>

<script setup lang="ts">
import dayjs, { type Dayjs } from 'dayjs';
import ImportModal from './components/ImportModal.vue';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import FormFold from '@/components/ui/FormFold.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { pxToRem } from '@/utils/tools';
import { usePagination } from '@/composables/usePagination';
import { usePost } from '@/composables/request';
import type { SelectOptions } from '@/apiTypes/apiCommon';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';

const formRef = ref();
const queryTechnicMarketProductListReq = await usePost('/api/accept/queryTechnicMarketProductList');
const dateFormat = 'YYYY-MM-DD';
const disabledDate = (current: Dayjs) => current && current < dayjs().subtract(1, 'day').endOf('day');
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
// 表单初始化
const formData = reactive({
  specialPromiseDept: defaultDeptCode.value,
  containChildDepart: true,
  technicProductCode: '',
  marketProductCodeList: [],
  personnel: '',
  marketProduce: '',
  time: '',
  departmentCode: defaultDeptCode.value,
  riskCode: '',
});
// 批量导入弹窗显示
const importVisible = ref<boolean>(false);
const formRules = reactive({
  specialPromiseDept: [{ required: true, message: '请选择机构' }],
});

// 选择机构
const changeDeptCode = (newVal: string) => {
  formData.specialPromiseDept = newVal;
  queryProductList();
};

const expand = ref(false);

const columns = [
  { title: '序号', dataIndex: 'index' },
  { title: '机构', dataIndex: 'departmentName' },
  { title: '标的', dataIndex: 'marketProductName' },
  { title: '市场产品', dataIndex: 'marketProduce' },
  { title: '创建日期', dataIndex: 'time' },
  { title: '配置人', dataIndex: 'personnel' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];

// 查询市场产品列表
const marketProductOptions = ref<SelectOptions[]>([]); // 全量产品下拉选项
const queryProductList = async () => {
  try {
    const res = await queryTechnicMarketProductListReq.fetchData({ departmentCode: formData.specialPromiseDept || defaultDeptCode.value });
    if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
      marketProductOptions.value = res.data;
    } else {
      marketProductOptions.value = [];
    }
  } catch (err) {
    console.log(err);
  }
};
queryProductList();
const dataSource = ref([]);
// 点击重置
const resetForm = () => {
  formRef.value?.resetFields();
};
// 点击查询
const submit = () => {
  refresh();
};

const loading = ref(false);
// 初始化列表接口
const refresh = () => {
  formRef.value?.validate().then(() => {
    // loading.value = true
  });
};
const { pagination } = usePagination(refresh);

const router = useRouter();
// 查看
const handleView = (record: Record<string, unknown>) => {
  router.push({
    path: '/costDetail',
    query: {
      id: record.id,
    },
  });
};
// 注销
const handleCancel = () => {
  Modal.confirm({
    title: '提醒',
    // 选中数据为费用兜底校验值，删除后将无系统管控，请谨慎操作
    content: `注销数据后，将以总部配置上线为准，请谨慎操作`,
    onOk() {},
  });
};
// 配置
const handleSetting = () => {
  router.push('/costSetting');
};
</script>
