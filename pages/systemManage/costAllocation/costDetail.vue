<template>
  <a-spin :spinning="loading">
    <div class="relative mx-[14px] mt-[14px] bg-white form-content">
      <div class="h-[76px] w-ful bg-image-url">
        <span class="text-[20px] text-[#00190C] font-bold leading-[76px] ml-[16px]">费用配置详情</span>
      </div>
      <div class="p-[16px] py-[20px] overflow-hidden">
        <InfoGroup title="费用配置条件" class="mb-16px">
          <a-row :gutter="16">
            <a-col :span="8">
              <p class="text-[#666]">机构：<span class="text-[#333]">210分公司</span></p>
            </a-col>
            <a-col :span="8">
              <p class="text-[#666]">标的：<span class="text-[#333]" /></p>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="8">
              <p class="text-[#666]">市场产品：<span class="text-[#333]">210分公司</span></p>
            </a-col>
            <a-col :span="8">
              <p class="text-[#666]">补贴类型：<span class="text-[#333]">210分公司</span></p>
            </a-col>
            <a-col :span="6">
              <p class="text-[#666]">投保方式：<span class="text-[#333]">210分公司</span></p>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="8">
              <p class="text-[#666]">是否共保：<span class="text-[#333]">210分公司</span></p>
            </a-col>
            <a-col :span="8">
              <p class="text-[#666]">是否主承：<span class="text-[#333]">210分公司</span></p>
            </a-col>
          </a-row>
        </InfoGroup>
        <InfoGroup title="总跟单费用率配置" class="mb-16px">
          <a-row :gutter="16">
            <a-col :span="8">
              <p class="text-[#666]">是总跟单费用率上限：<span class="text-[#333]">210分公司</span></p>
            </a-col>
          </a-row>
        </InfoGroup>
        <InfoGroup title="跟单费用细项配置" class="mb-16px">
          <a-row :gutter="24">
            <a-col :span="8">
              <p class="text-[#666]">农险补贴：<span class="text-[#333]">210分公司</span></p>
            </a-col>
            <a-col :span="8">
              <p class="text-[#666]">农险补贴：<span class="text-[#333]">210分公司</span></p>
            </a-col>
            <a-col :span="6">
              <p class="text-[#666]">农险补贴：<span class="text-[#333]">210分公司</span></p>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="8">
              <p class="text-[#666]">农险补贴：<span class="text-[#333]">210分公司</span></p>
            </a-col>
            <a-col :span="8">
              <p class="text-[#666]">农险补贴：<span class="text-[#333]">210分公司</span></p>
            </a-col>
            <a-col :span="6">
              <p class="text-[#666]">农险补贴：<span class="text-[#333]">210分公司</span></p>
            </a-col>
          </a-row>
        </InfoGroup>
      </div>
    </div>
  </a-spin>
</template>

<script setup lang="ts">
import InfoGroup from '@/components/ui/InfoGroup.vue';

const loading = ref(false);
</script>
<style lang="less" scoped>
.bg-image-url {
  background-image: url(/assets/images/content-head.png);
  background-size: cover;
}
</style>
