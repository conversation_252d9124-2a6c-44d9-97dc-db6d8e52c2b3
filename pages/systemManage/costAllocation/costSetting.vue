<template>
  <div class="relative mx-[14px] mt-[14px] bg-white form-content">
    <div class="h-[76px] w-ful bg-image-url">
      <span class="text-[20px] text-[#00190C] font-bold leading-[76px] ml-[16px]">费用配置录入</span>
    </div>
    <div class="p-[16px] py-[20px] overflow-hidden">
      <a-form ref="formDataRef" :colon="false" :model="formData" :label-col="{ style: { width: pxToRem(120) } }" :rules="formRules">
        <InfoGroup title="费用配置条件" class="mb-16px">
          <a-form-item label="机构" name="specialPromiseDept">
            <department-search v-model:contain-child-depart="formData.containChildDepart" :dept-code="formData.specialPromiseDept" :show-child-depart="true" @change-dept-code="changeDeptCode" />
          </a-form-item>
          <a-form-item label="标的">
            <RiskCodeSelect v-model:value="formData.riskCode" :department-code="formData.departmentCode" />
          </a-form-item>
          <a-row :gutter="16">
            <a-col :span="15">
              <a-form-item label="市场产品" name="marketProductCodeList">
                <a-select v-model:value="formData.marketProductCodeList" mode="multiple" :options="marketProductOptions" allow-clear show-search option-filter-prop="label" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="补贴类型" name="govSubsidyType" required>
            <a-radio-group v-model:value="formData.govSubsidyType">
              <a-radio value="0">中央补贴</a-radio>
              <a-radio value="1">地方补贴</a-radio>
              <a-radio value="2">商业性补贴</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="投保方式" name="applyPolicyType" :rules="[{ required: true, message: '请选择投保方式' }]">
            <a-radio-group v-model:value="formData.applyPolicyType">
              <a-radio value="3">所有</a-radio>
              <a-radio value="2">组织投保</a-radio>
              <a-radio value="1">非组织投保</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="是否共保" name="coinsuranceMark" :rules="[{ required: true, message: '请选择是否共保' }]">
                <a-radio-group v-model:value="formData.coinsuranceMark">
                  <a-radio value="2">所有</a-radio>
                  <a-radio value="1">是</a-radio>
                  <a-radio value="0">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item v-if="formData.coinsuranceMark === '1'" label="是否主承" name="acceptInsuranceFlag" :rules="[{ required: true, message: '请选择是否主承' }]">
                <a-radio-group v-model:value="formData.acceptInsuranceFlag">
                  <a-radio value="2">所有</a-radio>
                  <a-radio value="1">是</a-radio>
                  <a-radio value="0">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
        </InfoGroup>
        <InfoGroup title="总跟单费用率配置" class="mb-16px">
          <div class="grid grid-cols-3 gap-x-16px">
            <a-form-item
              required
              label="跟单费用率上限"
              name="brokerageFee"
              :rules="[
                { required: true, message: '请输入跟单费用率' },
                { validator: validateNumberFee, trigger: 'change' },
              ]"
            >
              <a-input v-model:value="formData.brokerageFee" addon-after="%" />
            </a-form-item>
          </div>
        </InfoGroup>
        <InfoGroup title="总跟单费用率配置" class="mb-16px">
          <div class="grid grid-cols-3 gap-x-16px">
            <a-form-item
              required
              label="农险补贴"
              name="workingFee"
              :rules="[
                { required: true, message: '请输入农险补贴' },
                { validator: validateNumberFee, trigger: 'change' },
              ]"
            >
              <a-input v-model:value="formData.workingFee" addon-after="%" />
            </a-form-item>
            <a-form-item
              required
              label="工作经费"
              name="workingFee"
              :rules="[
                { required: true, message: '请输入工作经费' },
                { validator: validateNumberFee, trigger: 'change' },
              ]"
            >
              <a-input v-model:value="formData.workingFee" addon-after="%" />
            </a-form-item>
            <a-form-item
              required
              label="协办费"
              name="spWorkRate"
              :rules="[
                { required: true, message: '请输入协办费' },
                { validator: validateNumberFee, trigger: 'change' },
              ]"
            >
              <a-input v-model:value="formData.spWorkRate" addon-after="%" />
            </a-form-item>
          </div>
          <div class="grid grid-cols-3 gap-x-16px">
            <a-form-item required label="防灾防损费" name="calamitySecurityRate">
              <a-input v-model:value="formData.calamitySecurityRate" addon-after="%" />
            </a-form-item>
            <a-form-item
              required
              label="手续费/经纪费"
              name="brokerageFee"
              :rules="[
                { required: true, message: '请输入手续费/经纪费' },
                { validator: validateNumberFee, trigger: 'change' },
              ]"
            >
              <a-input v-model:value="formData.brokerageFee" addon-after="%" />
            </a-form-item>
            <a-form-item
              required
              label="共保出单费"
              name="coinsuranceInsureFeeRatio"
              :rules="[
                { required: true, message: '请输入共保出单费' },
                { validator: validateNumberFee, trigger: 'change' },
              ]"
            >
              <a-input v-model:value="formData.coinsuranceInsureFeeRatio" addon-after="%" />
            </a-form-item>
          </div>
        </InfoGroup>
      </a-form>
    </div>
    <div class="h-[10px]" />
    <div class="bg-white h-[50px] z-[100] sticky bottom-0 flex justify-center items-center space-x-8px shadow-[0_2px_8px_0_rgba(0,0,0,0.13)]">
      <a-button class="mr-8px" @click="handleReset">重置</a-button>
      <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';
import { pxToRem } from '@/utils/tools';
import { useUserStore } from '@/stores/useUserStore';
import type { SelectOptions, BaseConstants } from '@/apiTypes/apiCommon';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost } from '@/composables/request';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import InfoGroup from '@/components/ui/InfoGroup.vue';

const queryTechnicMarketProductListReq = await usePost('/api/accept/queryTechnicMarketProductList');
const getParmBaseConstantConfReq = await usePost<BaseConstants>('/api/common/getParmBaseConstantConf');

const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
// 表单初始化
const formData = reactive({
  specialPromiseDept: defaultDeptCode.value,
  containChildDepart: true,
  technicProductCode: '',
  marketProductCodeList: [],
  personnel: '',
  marketProduce: '',
  time: '',
  departmentCode: defaultDeptCode.value,
  riskCode: '',
  govSubsidyType: '',
  applyPolicyType: '',
  acceptInsuranceFlag: '',
  coinsuranceMark: '',
  brokerageFee: '',
  workingFee: '',
  coinsuranceInsureFeeRatio: '',
  calamitySecurityRate: '',
  spWorkRate: '',
});
// 查询市场产品列表
const marketProductOptions = ref<SelectOptions[]>([]); // 全量产品下拉选项
const queryProductList = async () => {
  try {
    const res = await queryTechnicMarketProductListReq.fetchData({ departmentCode: formData.specialPromiseDept || defaultDeptCode.value });
    if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
      marketProductOptions.value = res.data;
    } else {
      marketProductOptions.value = [];
    }
  } catch (err) {
    console.log(err);
  }
};
queryProductList();
const subsidyTypeList = ref<Array<SelectOptions>>([]);
const getList = async () => {
  try {
    const res = await getParmBaseConstantConfReq.fetchData(['subsidyType', 'customerType', 'isTenderBusiness']);
    if (res && res.code === SUCCESS_CODE && res.data) {
      subsidyTypeList.value = res.data.subsidyTypeList || [];
    } else {
      subsidyTypeList.value = [];
    }
  } catch (err) {
    console.log(err);
  }
};
// 初始化下拉框数据
getList();
const formRules = reactive({
  specialPromiseDept: [{ required: true, message: '请选择机构' }],
  riskCode: [{ required: true, message: '请选择标的' }],
});
// 选择机构
const changeDeptCode = (value: string) => {
  formData.departmentCode = value;
  queryProductList();
};
// 共保出单费校验
const validateNumberFee = (_rule: Rule, value: string) => {
  const reg = /^\d+(\.\d{1,2})?$/;
  if (!reg.test(value)) {
    return Promise.reject('请输入正数且小数位不能超过4位！');
  }
  if (Number(value) > 100) {
    return Promise.reject('请输入0~100范围内的数字');
  }
  return Promise.resolve();
};
// 确定
const submitLoading = ref<boolean>(false);
const formDataRef = ref();
const submit = () => {
  formDataRef.value
    .validate()
    .then(() => {})
    .catch(() => {});
};
// 重置
const handleReset = () => {
  formDataRef.value.resetFields();
  formData.riskCode = '';
};
</script>
<style lang="less" scoped>
.bg-image-url {
  background-image: url(/assets/images/content-head.png);
  background-size: cover;
}
</style>
