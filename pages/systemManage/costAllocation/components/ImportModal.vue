<template>
  <a-modal v-model:open="visible" title="批量上传" :width="pxToRem(600)" centered :confirm-loading @ok="handleOk" @cancel="handleCancel">
    <div class="mb-[18px]">
      <div class="flex items-center mb-[9px]">
        <VueIcon :icon="IconPingtaijiekouFont" />
        <span class="ml-[3px]">费用修改模版</span>
      </div>
      <div class="bg-gray-100 rounded p-14px">
        <a-button :loading="uploading">
          <template #icon>
            <VueIcon :icon="IconTongyongXiazaiFont" />
          </template>
          <span class="ml-[2px]">模版下载</span>
        </a-button>
      </div>
    </div>
    <div class="mb-[15px]">
      <div class="flex items-center justify-between mb-[9px]">
        <div class="flex items-center">
          <VueIcon :icon="IconPingtaijiekouFont" />
          <span class="ml-[3px]">费用管控附件</span>
        </div>
      </div>
      <div class="bg-gray-100 rounded p-14px">
        <a-upload :multiple="false" :show-upload-list="false" :action="handleFile">
          <a-button :loading="uploading">
            <template #icon>
              <VueIcon :icon="IconTongyongShangchuanFont" />
            </template>
            <span class="ml-[2px]">附件上传</span>
          </a-button>
        </a-upload>
        <div v-if="currentFile" class="text-12px text-[#4E6085] mt-[13px]">
          <VueIcon :icon="IconAttachmentFont" />
          <span class="ml-[5px] cursor-pointer" @click="downloadTemplate(currentFile.fileKey)">{{ currentFile.fileName }}({{ currentFile.fileSize }})</span>
          <a-button type="link" @click="removeFile">删除</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { IconPingtaijiekouFont, IconTongyongShangchuanFont, IconAttachmentFont, IconTongyongXiazaiFont } from '@pafe/icons-icore-agr-an';
import { notification, Button } from 'ant-design-vue';
import { downloadFile, pxToRem } from '@/utils/tools';
import { $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

const visible = defineModel<boolean>('visible', { required: true, default: false });
const props = defineProps<{
  listInfo: {
    farmerlistNo: string;
    farmerlistName: string;
  };
  refresh: () => void;
}>();

const { service } = useRuntimeConfig().public || {};

const downloadTemplate = async (fileKey: string) => {
  const res = await $getOnClient(`/gateway${service.farmer}/templateInfo/queryFileDownloadUrl`, { fileKey });
  if (res?.data?.url) {
    downloadFile(res.data.url);
  }
};
// 下载模版
// const handleDownloadModel = async () => {
//   const { xPortalToken } = useRuntimeConfig().public || {};
//   await $fetch('/gateway/icore-agr-an.compliance/platCommonParameter/downPlatCommonParameterTemplate', {
//     method: 'get',
//     headers: {
//       'X-Portal-Token': xPortalToken,
//       'Content-Type': 'application/json',
//     },
//     query: { },
//     async onResponse({ response }) {
//       let fileName = '';
//       const contentDisposition = response.headers.get('Content-Disposition');
//       const fileData = response._data;
//       const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
//       if (contentDisposition) {
//         const match = contentDisposition.match(/filename=(.+)/);
//         if (match) {
//           fileName = decodeURI(match[1]);
//         }
//       }
//       downloadBlob(fileData, fileName, fileType);
//     },
//   });
// };
// 确定
const confirmLoading = ref(false);
const handleOk = async () => {
  confirmLoading.value = true;
  const res = await $postOnClient(`/gateway${service.farmer}/file/excelUpload`, {
    position: '3',
    singleApplyParam: {
      fileKey: currentFile.value?.fileKey || '',
      farmerListNo: props.listInfo.farmerlistNo,
      farmerListName: props.listInfo.farmerlistName,
      templateId: currentFile.value?.templateId, // 待定
    },
  });
  confirmLoading.value = false;
  const { msg = '', code } = res || {};
  if (code === SUCCESS_CODE) {
    if (typeof props.refresh === 'function') {
      setTimeout(() => { props.refresh(); }, 500);
    }
    visible.value = false;
    message.success('批量上传成功');
  } else {
    message.error(msg);
  }
};

// 删除附件
const removeFile = async () => {
  await $getOnClient(`/gateway${service.farmer}/templateInfo/deleteFile`, {
    fileKey: currentFile.value?.fileKey || '',
  });
  currentFile.value = undefined;
};

const handleCancel = () => {
  visible.value = false;
};

// 当前上传附件数据
const currentFile = ref<{
  farmerListName: string;
  fileKey: string;
  fileName: string;
  fileSize: string;
  templateId: string;
}>();

const downloadFileByKey = async (fileKey: string, key: string) => {
  const res = await $getOnClient(`/gateway${service.farmer}/templateInfo/queryFileDownloadUrl`, { fileKey });
  const { msg = '' } = res || {};
  if (res?.data?.url) {
    downloadFile(res.data.url);
    notification.close(key);
  } else {
    message.error(msg);
  }
};
// 点击上传附件
const uploading = ref(false);
const handleFile = (file: unknown) => {
  uploading.value = true;
  const formData = new FormData();
  formData.append('file', file);
  formData.append('farmerListNo', props.listInfo.farmerlistNo);
  $postOnClient(`/gateway${service.farmer}/templateInfo/uploadSingleFarmerListFile`, formData).then((res) => {
    const { msg = '', code } = res || {};
    if (res && code === SUCCESS_CODE) {
      if (res.data.uploadResult) {
        currentFile.value = res.data;
      } else {
        const key = `open${Date.now()}`;
        notification.error({
          message: '错误信息',
          description: res.data.errorMsg,
          duration: null,
          key,
          btn: () => h(Button, { type: 'link', size: 'small', onClick: () => { downloadFileByKey(res.data.fileKey, key); } }, { default: () => '下载完整信息' }),
        });
      }
    } else {
      message.error(msg);
    }
  }).finally(() => {
    uploading.value = false;
  });
  return Promise.reject();
};
</script>
