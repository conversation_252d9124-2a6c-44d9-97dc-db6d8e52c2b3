<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <a-form ref="searchForm" :colon="false" :model="searchFormState">
        <a-row :gutter="16">
          <a-col span="10">
            <a-form-item label="机构" name="deptCode" required :label-col="{ style: { width: '80px' } }">
              <department-search v-model:contain-child-depart="searchFormState.containChildDepart" :dept-code="searchFormState.deptCode" :show-child-depart="true" @change-dept-code="(val: string) => changeDeptCode(val)" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="创建时间" name="createDate">
              <a-range-picker v-model:value="searchFormState.createDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col span="6">
            <a-form-item label="客户类型" name="customerType" :label-col="{ style: { width: '80px' } }">
              <a-select v-model:value="searchFormState.customerType" :options="typeOptions" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex justify-center items-center space-x-8px">
        <a-button type="primary" @click="search">查询</a-button>
        <a-button type="default" @click="add">新增</a-button>
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-16px">
        <div class="leading-[32px] text-[16px] text-[rgba(0,0,0,0.9)] font-bold">查询结果</div>
      </div>
      <a-table :columns="listColumns" :pagination="pagination" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content' }" class="table-box">
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex === 'lossRate'">
            {{ text }}%
          </template>
        </template>
      </a-table>
    </div>
    <!-- 新增弹窗 -->
    <a-modal v-model:open="open" title="新增" @ok="addOk">
      <a-form ref="addForm" :colon="false" :model="addFormState">
        <a-form-item label="客户类型" name="customerType" required :label-col="{ style: { width: '80px' } }">
          <a-select v-model:value="addFormState.customerType" :options="customerTypeList" />
        </a-form-item>
        <a-form-item label="客户简称" name="customerAbbrName" :label-col="{ style: { width: '80px' } }" :rules="[{ required: true, validator: nameValid }]">
          <a-input v-model:value.trim="addFormState.customerAbbrName" />
        </a-form-item>
        <div class="col-span-12 mb-10px ml-12px text-[#F03E3E] text-[12px]">注：格式要求为大写字母（必录）+数字（非必录）的组合格式，例如SCZD2022</div>
        <a-form-item label="风险程度" name="lossRate" :label-col="{ style: { width: '80px' } }" :rules="[{ required: true, validator: lossRateValid }]">
          <a-input v-model:value.trim="addFormState.lossRate" suffix="%" />
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 二次确认弹窗 -->
    <a-modal v-model:open="openVisible" :width="pxToRem(450)" cancel-text="返回修改" @ok="handleConfirmOk">
      <div class="mb-[10px]">
        <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px] ml-[5px]">提醒</span>
      </div>
      <div>客户简称、风险程度值提交后不可修改，请确认是否提交？</div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import type { Rule } from 'ant-design-vue/es/form';
import type { SearchFormState, DataType, AddFormState } from './typeCustomerMaintenance.d';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import { usePagination } from '@/composables/usePagination';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost } from '@/composables/request';
import { pxToRem } from '@/utils/tools';

const searchForm = ref();
const addForm = ref();
const loading = ref(false);
const dataSource = ref<Record<string, string>[]>([]);
const listColumns = [
  { title: '机构名称', dataIndex: 'departmentName' },
  { title: '客户类型', dataIndex: 'customerTypeDesc' },
  { title: '客户名称', dataIndex: 'customerAbbrName' },
  { title: '风险程度', dataIndex: 'lossRate' },
  { title: '创建时间', dataIndex: 'createdDate' },
  { title: '创建人', dataIndex: 'createdBy' },
];
// 查询条件客户类型
const customerTypeList = [
  { label: '潜力客户', value: '02' },
  { label: '集团客户', value: '03' },
];
const typeOptions = [
  { label: '全部', value: '0' },
  ...customerTypeList,
];
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
const searchFormState = reactive<SearchFormState>({
  deptCode: defaultDeptCode.value, // 机构编码
  containChildDepart: true, // 是否包含下级
  createDate: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')], // 创建时间
  customerType: '0', // 客户类型
});
const addFormState = reactive<AddFormState>({
  customerType: '', // 客户类型
  customerAbbrName: '', // 客户简称
  lossRate: '', // 风险程度
});
// 改变机构
const changeDeptCode = (val: string) => {
  searchFormState.deptCode = val;
};
// 查询
const search = async () => {
  try {
    await searchForm.value.validate();
    pagination.current = 1;
    pagination.pageSize = 10;
    refresh();
  } catch (e) {
    console.log(e);
  }
};
const { gateWay, service } = useRuntimeConfig().public || {};
const getListReq = await usePost<DataType>(`${gateWay}${service.accept}/customerType/queryList`);
// 列表分页查询
const refresh = async () => {
  loading.value = true;
  try {
    const params = {
      containChildDepart: searchFormState.containChildDepart ? '1' : '0',
      deptCode: searchFormState.deptCode,
      customerType: searchFormState.customerType === '0' ? '' : searchFormState.customerType,
      startDate: searchFormState.createDate?.[0] || '',
      endDate: searchFormState.createDate?.[1] || '',
      pageNum: pagination.current, // 页码
      pageSize: pagination.pageSize, // 每页数
    };
    const res = await getListReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data || [];
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      message.warning(res?.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const { pagination } = usePagination(refresh);
const open = ref<boolean>(false);
// 新增
const add = () => {
  addFormState.customerType = '';
  addFormState.customerAbbrName = '';
  addFormState.lossRate = '';
  open.value = true;
};
// 风险程度校验条件
const lossRateValid = (_rule: Rule, value: string) => {
  const reg = /^\d{0,4}(\.\d{1,4})?$/;
  if (!value) {
    return Promise.reject('请填写风险程度');
  }
  if (reg.test(value) === false) {
    return Promise.reject(new Error('请输入4位正数且小数位不能超过4位！'));
  } else {
    return Promise.resolve();
  }
};
// 客户简称检验条件
const nameValid = (_rule: Rule, value: string) => {
  const reg = /^[A-Z]+\d*$/;
  if (!value) {
    return Promise.reject('请填写客户简称');
  }
  if (!value.match(reg)) {
    return Promise.reject(new Error('请输入规范的客户简称，格式要求为客户大写首字母手写（必录）+数字（非必录）的组合格式，例：四川正大2022，则输入SCZD2022！'));
  } else {
    return Promise.resolve();
  }
};
const addReq = await usePost<DataType>(`${gateWay}${service.accept}/customerType/save`);
// 二次确认弹窗确定
const openVisible = ref<boolean>(false);
const handleConfirmOk = async () => {
  try {
    const params = {
      ...addFormState,
      deptCode: searchFormState.deptCode,
    };
    const res = await addReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      message.success(res?.msg);
      openVisible.value = false;
      open.value = false;
      search();
    } else {
      message.warning(res?.msg);
    }
  } catch (e) {
    console.log(e);
  }
};
const addOk = async () => {
  try {
    await addForm.value.validate();
    openVisible.value = true;
  } catch (e) {
    console.log(e);
  }
};
</script>
