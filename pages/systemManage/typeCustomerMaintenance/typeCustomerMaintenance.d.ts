// 查询条件
export interface SearchFormState {
  deptCode: string; // 机构编码
  containChildDepart: boolean; // 是否包含下级机构
  createDate: [string, string]; // 创建时间
  customerType: string; // 客户类型
}
// 新增弹窗Form
export interface AddFormState {
  customerType: string; // 客户类型
  customerAbbrName: string; // 客户简称
  lossRate: string; // 风险程度
}
export interface DataType {
  records: Record<string, string>[];
  total: number;
  current: number;
  size: number;
}
