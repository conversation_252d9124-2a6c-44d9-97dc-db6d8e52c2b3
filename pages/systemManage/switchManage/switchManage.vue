<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="searchForm" :colon="false" class="flex-grow" :model="searchFormState">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="场景" name="switchCode" :rules="[{ required: true, trigger: 'change', message: '请选择场景' }]">
                <a-select v-model:value="searchFormState.switchCode" :options="switchCodeList" />
              </a-form-item>
            </a-col>
            <!-- 精准承保开关不需要机构 -->
            <a-col v-if="!precisionUnderwriting" :span="10">
              <a-form-item label="机构" name="departmentNo" :rules="[{ required: true, trigger: 'change', message: '请选择机构' }]">
                <department-search v-model:contain-child-depart="searchFormState.isContainLowDepartment" :dept-code="searchFormState.departmentNo" :show-child-depart="true" @change-dept-code="(val: string) => changeDeptCode(val, 'searchFormState')" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-if="precisionUnderwriting" :gutter="16">
            <a-col :span="24">
              <a-form-item label="标的地址" name="riskObjectCompleteAddress">
                <div class="flex gap-x-8px">
                  <a-form-item-rest>
                    <RegionSelect v-model:province="undertakeInsuranceAddress.province" v-model:city="undertakeInsuranceAddress.city" v-model:county="undertakeInsuranceAddress.coutry" v-model:town="undertakeInsuranceAddress.town" v-model:village="undertakeInsuranceAddress.village" style="width: 80%" />
                  </a-form-item-rest>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
          <!-- 精准承保开关不需要标的类型 -->
          <a-row v-else :gutter="16">
            <a-col :span="24">
              <a-form-item label="标的类型" name="agriculturalRiskObjectClassCode">
                <RiskCodeSelect v-model:value="searchFormState.agriculturalRiskObjectClassCode" :department-code="searchFormState.departmentNo" :page-scene-code="pageSceneCode" />
              </a-form-item>
            </a-col>
          </a-row>
          <!-- 精准承保开关不需要补贴类型 -->
          <a-row v-if="!precisionUnderwriting" :gutter="16">
            <a-col :span="16">
              <a-form-item label="补贴类型" name="allowanceTypeCode">
                <check-box-group v-model:checked-list="searchFormState.allowanceTypeCode" :options="plainOptions" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button type="primary" @click="search">查询</a-button>
        <AuthButton code="switchConfigSet" type="primary" @click="showModal">配置</AuthButton>
      </div>
    </div>
    <div class="bg-white rounded p-16px relative">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-16px">
        <div class="leading-[32px] text-[16px] text-[rgba(0,0,0,0.9)] font-bold">查询结果</div>
      </div>
      <a-table :columns="tableType ? undertakeInsuranceList : listColumns" :pagination="pagination" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content' }" class="table-box">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
          <template v-if="column.dataIndex === 'agriculturalRiskObjectDetailName'">
            <div v-if="record.switchCode === 'ELECTRON_AREA_CONFIG' || record.switchCode === 'CUSTOMER_TYPE_LOSS_RATE_SHOW_SWITCH'">{{ record.agriculturalRiskObjectClassName }}</div>
            <div v-else>{{ record.agriculturalRiskObjectDetailName }}</div>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <a-space :size="1">
              <a-button type="link" @click="check(record as ManageFormRecord)">查看</a-button>
              <AuthButton code="switchConfigDel" type="link" @click="deleteItem(record.idParmSwitchMultiDimension, record.switchCode)">删除</AuthButton>
            </a-space>
          </template>
          <template v-if="column.dataIndex === 'agriculturalRiskObjectClassCode'">
            <div>
              {{ riskList.find((item) => item.value === record.agriculturalRiskObjectClassCode)?.label }}
            </div>
          </template>
        </template>
      </a-table>
      <div v-if="precisionUnderwriting" class="absolute bottom-[32px]">总页数{{ totalPages }} 当前页数{{ pagination.current }} 总记录数{{ pagination.total }}</div>
    </div>
    <a-modal v-if="open" v-model:open="open" title="配置页面" :width="pxToRem(900)">
      <a-form ref="manageForm" :colon="false" class="flex-grow" :model="precisionUnderwriting ? underwritingData : manageFormState">
        <a-row>
          <a-col :span="10">
            <a-form-item label="场景" name="switchCode" :rules="[{ required: true, trigger: 'change', message: '请选择场景' }]">
              <a-select v-model:value="manageFormState.switchCode" :disabled="true" :options="switchCodeList" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="!precisionUnderwriting">
          <a-col :span="10">
            <a-form-item label="机构" name="departmentNo" :rules="[{ required: true, trigger: 'change', message: '请选择机构' }]">
              <department-search :disabled="isOnlyCheck" :dept-code="manageFormState.departmentNo" @change-dept-code="(val: string) => changeDeptCode(val, 'manageFormState')" />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 五级标的类型（非见费出单、禁止农户分期付款管控）, 精准承保开关不需要标的类型 -->
        <a-row v-if="!electronicSwitch && !precisionUnderwriting && !showRiskFlag" :gutter="16">
          <a-col :span="24">
            <a-form-item label="标的类型" name="agriculturalRiskObjectDetailCode" :rules="[{ required: true, trigger: 'change', message: '请选择标的类型' }]">
              <RiskCodeSelect v-model:value="manageFormState.agriculturalRiskObjectDetailCode" :default-value="defaultRisk" :disabled="isOnlyCheck" :department-code="manageFormState.departmentNo" :page-scene-code="pageSceneCode" />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 精准承保开关不需要补贴类型 -->
        <a-row v-if="!precisionUnderwriting">
          <a-col :span="16">
            <a-form-item label="补贴类型" name="allowanceTypeCode" :rules="[{ required: true, trigger: 'change', message: '请选择补贴类型' }]">
              <check-box-group v-model:checked-list="manageFormState.allowanceTypeCode" :disabled="isOnlyCheck" :options="plainOptions" />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 多选标的类型（电子化地区配置） -->
        <a-row v-if="electronicSwitch || showRiskFlag">
          <a-col :span="16">
            <a-form-item label="标的类型" name="agriculturalRiskObjectClassCode" :rules="[{ required: true, trigger: 'change', message: '请选择标的类型' }]">
              <check-box-group v-model:checked-list="manageFormState.agriculturalRiskObjectClassCode" :disabled="isOnlyCheck" :options="riskList" />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 技术产品（电子化地区配置） -->
        <a-row v-if="electronicSwitch">
          <a-col :span="16">
            <a-form-item label="技术产品" name="technologyProductNo">
              <a-select v-model:value="manageFormState.technologyProductNo" mode="multiple" :disabled="isOnlyCheck" :options="productOptions" :filter-option="filterOption" />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 精准承保开关不需要开关有效期 -->
        <a-row v-if="!electronicSwitch && !precisionUnderwriting && !earNeedValueSwitchFlag" :gutter="16">
          <a-col :span="10">
            <a-form-item label="开关有效期" name="dateRange">
              <a-range-picker v-model:value="manageFormState.dateRange" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :disabled="isOnlyCheck ? [true, true] : disabled" :allow-empty="[true, true]" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item>
              <a-checkbox v-model:checked="longTimeFlag" :disabled="isOnlyCheck" @change="handleChange">长期</a-checkbox>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="electronicSwitch">
          <a-col>
            <a-form-item label="是否向农户发送短信" name="whetherSendMessage" :rules="[{ required: true, trigger: 'change', message: '请选择是否向农户发送短信' }]">
              <a-radio-group v-model:value="manageFormState.whetherSendMessage" :disabled="isOnlyCheck">
                <a-radio :value="true">是</a-radio>
                <a-radio :value="false">否</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="electronicSwitch">
          <a-col :span="24">
            <a-form-item label="电子化区域" name="province">
              <RegionSelect ref="addressRef" v-model:province="manageFormState.province" v-model:city="manageFormState.city" v-model:county="manageFormState.coutry" :disabled="isOnlyCheck" :region-level="3" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="electronicSwitch">
          <a-col :span="24">
            <a-form-item label="电子化模版名称" name="isElecTemplate">
              <a-checkbox v-model:checked="manageFormState.isElecTemplate" :disabled="isOnlyCheck">通用</a-checkbox>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="!manageFormState.isElecTemplate && electronicSwitch" :gutter="16">
          <a-col :span="12">
            <a-form-item label="分户凭证模板名称" name="farmerDocumentTag" :rules="[{ required: true, trigger: 'blur', message: '请填写分户凭证模板名称' }]">
              <a-input v-model:value="manageFormState.farmerDocumentTag" :disabled="isOnlyCheck" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="电子批单模板名称" name="endorsementTag" :rules="[{ required: true, trigger: 'blur', message: '请填写电子批单模板名称' }]">
              <a-input v-model:value="manageFormState.endorsementTag" :disabled="isOnlyCheck" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="!manageFormState.isElecTemplate && electronicSwitch">
          <a-form-item label="电子保单单户投保模板名称" />
        </a-row>
        <a-row v-if="!manageFormState.isElecTemplate && electronicSwitch" :gutter="16">
          <a-col :span="8">
            <a-form-item label="种" name="policyTagPlantPersonal" :rules="[{ required: true, trigger: 'blur', message: '请填写' }]">
              <a-input v-model:value="manageFormState.policyTagPlantPersonal" :disabled="isOnlyCheck" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="养" name="policyTagCulturePersonal" :rules="[{ required: true, trigger: 'blur', message: '请填写' }]">
              <a-input v-model:value="manageFormState.policyTagCulturePersonal" :disabled="isOnlyCheck" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="林" name="policyTagForestryPersonal" :rules="[{ required: true, trigger: 'blur', message: '请填写' }]">
              <a-input v-model:value="manageFormState.policyTagForestryPersonal" :disabled="isOnlyCheck" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="!manageFormState.isElecTemplate && electronicSwitch">
          <a-form-item label="电子保单集体投保模板名称" />
        </a-row>
        <a-row v-if="!manageFormState.isElecTemplate && electronicSwitch" :gutter="16">
          <a-col :span="8">
            <a-form-item label="种" name="policyTagPlantGroup" :rules="[{ required: true, trigger: 'blur', message: '请填写' }]">
              <a-input v-model:value="manageFormState.policyTagPlantGroup" :disabled="isOnlyCheck" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="养" name="policyTagCultureGroup" :rules="[{ required: true, trigger: 'blur', message: '请填写' }]">
              <a-input v-model:value="manageFormState.policyTagCultureGroup" :disabled="isOnlyCheck" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="林" name="policyTagForestryGroup" :rules="[{ required: true, trigger: 'blur', message: '请填写' }]">
              <a-input v-model:value="manageFormState.policyTagForestryGroup" :disabled="isOnlyCheck" />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 精准承保作业模式开关 -->
        <a-row v-if="precisionUnderwriting" :gutter="16">
          <p class="m-[10px]">请选择作业模式</p>
          <a-col :span="24">
            <a-form-item label="" name="versionNum">
              <a-radio-group v-model:value="underwritingData.versionNum" name="radioGroup" :disabled="isOnlyCheck" @change="changeModal">
                <a-radio value="3.0">3.0</a-radio>
                <a-radio value="2.0">2.0</a-radio>
                <a-radio value="1.0">1.0</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <p class="m-[10px]">请选择生效范围</p>
          <a-col :span="24">
            <a-form-item label="标的" name="agriculturalRiskObjectClassCode" :rules="[{ required: true, trigger: 'change', message: '请输入标的' }]">
              <!-- <a-select v-model:value="underwritingData.agriculturalRiskObjectClassCode" style="width: 120px" :options="riskList" :disabled="isOnlyCheck" /> -->
              <RiskCodeSearch v-model:value="underwritingData.agriculturalRiskObjectClassCode" :default-value="underwritingData.agriculturalRiskObjectClassCode" :department-code="2" :disabled="isOnlyCheck" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="标的地址" name="province" :rules="[{ required: true, trigger: 'change', message: '标的地址' }]">
              <div class="flex gap-x-8px">
                <a-form-item-rest>
                  <RegionSelect v-model:province="underwritingData.province" v-model:city="underwritingData.city" v-model:county="underwritingData.county" v-model:town="underwritingData.town" v-model:village="underwritingData.village" style="width: 80%" :disabled="isOnlyCheck" />
                </a-form-item-rest>
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="是否从共" name="fromCommon" :rules="[{ required: true }]">
              <a-radio-group v-model:value="underwritingData.fromCommon" name="radioGroup" :disabled="isOnlyCheck">
                <a-radio value="1">是</a-radio>
                <a-radio value="0">否</a-radio>
                <a-radio value="00">全部</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <p class="m-[10px]">功能配置</p>
          <a-col :span="24">
            <a-form-item v-for="(item, index) in underwritingConfigList" :key="index" :label="item.functionName" :rules="[{ required: true }]" :label-col="{ style: { width: '120px', padding: '5px 0px', whiteSpace: 'normal', textAlign: 'left', marginTop: '-5px' } }">
              <p v-if="item.mustRequire" class="m-0 absolute top-[24px] text-[#f03e3e]">{{ item.functionName }}</p>
              <check-box-group v-model:checked-list="item.checkedList" :options="item.children" disabled :is-hidden-all-check="!item.allFlag" @change="handlerCheckData(item)" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row v-if="earNeedValueSwitchFlag">
          <a-col :span="16">
            <a-form-item label="验标方式" name="allowanceTypeCode1">
              <a-checkbox v-model:checked="checkMethod" :disabled="isOnlyCheck">逐头验标</a-checkbox>
              <a-tooltip placement="top" title="按照耳标号逐头采集验标照片。若标的类型为畜牧养殖中的兔、家禽（鸵鸟除外）、其他、畜禽产品；水产养殖；特种养殖业中除了鹿、貂、肉狗、孔雀、熊猫以外的，请谨慎选择逐头验标。">
                <VueIcon :icon="IconHelpCircleFont" />
              </a-tooltip>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <template #footer>
        <a-button key="back" @click="open = false">{{ isOnlyCheck ? '关闭' : '取消' }}</a-button>
        <a-button v-if="!isOnlyCheck" key="submit" type="primary" :loading="btnLoading" @click="handleOk">确定</a-button>
      </template>
    </a-modal>
    <!-- 确认删除弹窗 -->
    <a-modal v-model:open="delModalOpen" :width="pxToRem(450)" :centered="true">
      <div>
        <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px]">提醒</span>
      </div>
      <div class="mt-[10px]">删除操作不可撤销，是否确认删除？</div>
      <template #footer>
        <a-button key="back" @click="delModalOpen = false">取消</a-button>
        <a-button key="submit" type="primary" :loading="btnLoading" @click="handleDeleteOk">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import type { RadioChangeEvent, TableColumnsType } from 'ant-design-vue';
import dayjs from 'dayjs';
import { IconErrorCircleFilledFont, IconHelpCircleFont } from '@pafe/icons-icore-agr-an';
import type { SearchFormState, ManageFormStateType, ManageFormRecord, DataType, SelectOptions, UnderwritingConfigType, AddressType, UnderwritingConfigListType } from './switchManage.d';
import { RiskType } from '@/enums/insure';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import RiskCodeSearch from '@/components/selector/RiskCodeSearch.vue';

import { usePost } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import { usePagination } from '@/composables/usePagination';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import { pxToRem } from '@/utils/tools';
import AuthButton from '@/components/ui/AuthButton.vue';
import { $get } from '@/utils/request';
// 表头切换标识（false为默认表头listColumns，true为精准承担保表格列表表头undertakeInsuranceList）
const tableType = ref<boolean>(false);
const listColumns: TableColumnsType = [
  { title: '序号', dataIndex: 'index', width: 60 },
  { title: '出单机构', dataIndex: 'departmentName' },
  { title: '场景', dataIndex: 'switchName' },
  { title: '标的类型', dataIndex: 'agriculturalRiskObjectDetailName' },
  { title: '补贴类型', dataIndex: 'allowanceTypeName' },
  { title: '失效时间', dataIndex: 'invalidTime', width: 100 },
  { title: '操作人', dataIndex: 'createdBy' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];
// 精准承担保表格列表
const undertakeInsuranceList: TableColumnsType = [
  { title: '序号', dataIndex: 'index', width: 60 },
  { title: '场景', dataIndex: 'switchName' },
  { title: '标的', dataIndex: 'riskName' },
  { title: '标的地址', dataIndex: 'addressName' },
  { title: '生效时间', dataIndex: 'effectiveDate', width: 100 },
  { title: '操作人', dataIndex: 'createdBy' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];
// 标的类型
const riskList = [
  { label: '种植业', value: RiskType.Planting },
  { label: '养殖业', value: RiskType.Breeding },
  { label: '林业', value: RiskType.Forestry },
];
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
// 配置弹窗显示隐藏字段
const open = ref<boolean>(false);
const btnLoading = ref<boolean>(false);
// 场景选项
const switchCodeList = ref<SelectOptions[]>([]);
// 技术产品选项
const productOptions = ref<SelectOptions[]>([]);
// 补贴类型
const plainOptions = [
  { label: '中央补贴', value: '1' },
  { label: '地方补贴', value: '2' },
  { label: '商业', value: '3' },
];
// 初始化地址值省市区县等
const addressOrigin = () => {
  return {
    province: '', // 省
    provinceName: '', // 省
    city: '', // 市
    cityName: '', // 市
    coutry: '', // 区
    coutryName: '', // 区
    town: '', // 县
    townName: '', // 县
    village: '', // 村
    villageName: '', // 村
  };
};
const filterOption = (input: string, option: RegionInfo) => {
  if (input && option) {
    return option.label.toLowerCase().includes(input.toLowerCase());
  } else {
    return false;
  }
};
// 精准承保作业模式初始化数据
const initUnderwritingData = () => {
  return {
    fromCommon: '0', // 是否从共：00全部、1是、0否
    versionNum: '3.0', // //版本号:3.0、2.0、1.0
    switchCode: 'ACCURATE_UNDERWRITING_OPERATION_MODE', // 精准承保作业模式开关编码
    agriculturalRiskObjectClassCode: '', // 险种大类
    preciseList: [], // 用于查看列表配置
    countyName: '',
    county: '',
    ...addressOrigin(),
    switchName: '',
  };
};
const underwritingConfigList = ref<UnderwritingConfigType[]>([]); // 精准承保配置数据
const undertakeInsuranceAddress = ref<AddressType>(addressOrigin()); // 精准承保查询标界面的地址
const underwritingData = ref<UnderwritingConfigListType>(initUnderwritingData()); // 精准承保配置数据对象
// 判断配置页面是否仅查看
const isOnlyCheck = ref<boolean>(false);
// 开关有效期是否长期
const longTimeFlag = ref<boolean>(false);
const totalPages = ref<number>(0); // 总页数
// 开关有效期是否禁用
const disabled = ref<[boolean, boolean]>([false, false]);
const searchFormState = reactive<SearchFormState>({
  switchCode: 'ELECTRON_AREA_CONFIG', // 场景
  departmentNo: defaultDeptCode.value, // 机构编码
  isContainLowDepartment: true, // 是否包含下级
  allowanceTypeCode: [], // 补贴类型
  agriculturalRiskObjectClassCode: '', // 标的
});
// 验标方式是否逐头验标
const checkMethod = ref<boolean>(false);

const pageSceneCode = computed(() => {
  return searchFormState.switchCode.indexOf('EAR_NEED_VALID_SWITCH') > -1 ? 'EAR_SWITCH_CONFIG_EDIT' : '';
});
// 配置页面初始值
const resetData = () => {
  return {
    switchCode: searchFormState.switchCode, // 场景
    departmentNo: defaultDeptCode.value, // 机构编码
    dateRange: [dayjs().format('YYYY-MM-DD'), ''] as [string, string], // 开关有效期
    province: '', // 省
    city: '', // 市
    coutry: '', // 县
    isElecTemplate: true, // 电子化模版名称是否通用
    endorsementTag: '', // 电子批单模板
    farmerDocumentTag: '', // 电子分户凭证模板
    policyTagCultureGroup: '', // 集体投保电子保单模板-养殖业
    policyTagCulturePersonal: '', // 单户投保电子保单模板-养殖业
    policyTagForestryGroup: '', // 集体投保电子保单模板-林业
    policyTagForestryPersonal: '', // 单户投保电子保单模板-林业
    policyTagPlantGroup: '', // 单户投保电子保单模板-种植
    policyTagPlantPersonal: '', // 单户投保电子保单模板-种植
    agriculturalRiskObjectClassCode: [], // 标的-多选
    allowanceTypeCode: [], // 补贴类型
    agriculturalRiskObjectDetailCode: '', // 标的-五级
    whetherSendMessage: false, // 是否向农户发送短信
  };
};
const manageFormState = ref<ManageFormStateType>(resetData());
const defaultRisk = ref(''); // 五级标的默认值
// 开关有效期长期切换
const handleChange = () => {
  if (longTimeFlag.value) {
    disabled.value = [false, true];
    manageFormState.value.dateRange[1] = '9999-12-31';
  } else {
    disabled.value = [false, false];
  }
};
// 改变机构
const changeDeptCode = (val: string, formName: string) => {
  if (formName === 'manageFormState') {
    if (!isOnlyCheck.value) {
      manageFormState.value.departmentNo = val;
    }
  } else {
    searchFormState.departmentNo = val;
  }
};
const manageForm = ref();
// 点击配置显示弹窗
const showModal = () => {
  longTimeFlag.value = false;
  checkMethod.value = false;
  isOnlyCheck.value = false;
  manageFormState.value = resetData();
  manageFormState.value.switchCode = searchFormState.switchCode;
  manageFormState.value.departmentNo = searchFormState.departmentNo;
  manageForm.value?.clearValidate();
  open.value = true;
  // 精准承保查询配置，不是从详情进来
  if (precisionUnderwriting.value) {
    underwritingData.value = initUnderwritingData(); // 初始化数据先
    queryUnderwritingConfig('3.0'); // 默认3.0先
  }
};
watch(
  () => manageFormState.value.isElecTemplate,
  () => {
    if (!isOnlyCheck.value) {
      manageFormState.value.endorsementTag = ''; // 电子批单模板
      manageFormState.value.farmerDocumentTag = ''; // 电子分户凭证模板
      manageFormState.value.policyTagCultureGroup = ''; // 集体投保电子保单模板-养殖业
      manageFormState.value.policyTagCulturePersonal = ''; // 单户投保电子保单模板-养殖业
      manageFormState.value.policyTagForestryGroup = ''; // 集体投保电子保单模板-林业
      manageFormState.value.policyTagForestryPersonal = ''; // 单户投保电子保单模板-林业
      manageFormState.value.policyTagPlantGroup = ''; // 单户投保电子保单模板-种植
      manageFormState.value.policyTagPlantPersonal = ''; // 单户投保电子保单模板-种植
    }
  },
);
// 弹窗确定
const handleOk = async () => {
  try {
    // 处理精准承保配置新增开关
    if (precisionUnderwriting.value) {
      await manageForm.value.validate();
      addUndertakeInsurance();
      return;
    }
    await manageForm.value.validate();
    addSwitch();
  } catch (error) {
    console.log(error);
  }
};
const searchForm = ref();
// 查询
const search = async () => {
  try {
    await searchForm.value.validate();
    pagination.current = 1;
    pagination.pageSize = 10;
    refresh();
  } catch (error) {
    console.log(error);
  }
};
const dataSource = ref<Record<string, string>[]>([]);
const loading = ref(false);
const { gateWay, service } = useRuntimeConfig().public || {};
const queryReq = await usePost<DataType>(`${gateWay}${service.administrate}/switchMulti/queryList`);
// 查询接口
const refresh = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      switchCode: searchFormState.switchCode,
      departmentNo: searchFormState.departmentNo,
      allowanceTypeCode: searchFormState.allowanceTypeCode?.join(',') || '',
      agriculturalRiskObjectClassCode: searchFormState.agriculturalRiskObjectClassCode,
      isContainLowDepartment: searchFormState.isContainLowDepartment,
    };
    // 精准承保开关查询增加标的地址等
    if (precisionUnderwriting.value) {
      Object.assign(params, undertakeInsuranceAddress.value);
    }
    const res = await queryReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data;
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
      totalPages.value = res.data?.pages;
      tableType.value = false;
      if (precisionUnderwriting.value) {
        tableType.value = true;
      }
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const { pagination } = usePagination(refresh);
// 是否为投保告知【电子化配置】配置开关
const electronicSwitch = computed(() => {
  return ['ELECTRON_AREA_CONFIG'].includes(manageFormState.value.switchCode);
});
const precisionUnderwriting = computed(() => {
  return ['ACCURATE_UNDERWRITING_OPERATION_MODE'].includes(searchFormState.switchCode);
}); // 精准承保作业模式
// 客户类型及风险程度隐藏开关
const showRiskFlag = computed(() => {
  return ['CUSTOMER_TYPE_LOSS_RATE_SHOW_SWITCH'].includes(searchFormState.switchCode);
});
const earNeedValueSwitchFlag = computed(() => ['EAR_NEED_VALID_SWITCH'].includes(searchFormState.switchCode)); // 耳标号是否必填，显示/隐藏
const saveReq = await usePost(`${gateWay}${service.administrate}/switchMulti/save`);
// 开关配置接口
const addSwitch = async () => {
  btnLoading.value = true;
  try {
    const switchItem: { value: string; label: string } = switchCodeList.value.filter((item: { value: string; label: string }) => item.value === manageFormState.value.switchCode)?.[0];
    const switchName = switchItem.label;
    const invalidTime = longTimeFlag.value ? '' : manageFormState.value.dateRange?.[1] || '';
    const params = {
      ...manageFormState.value,
      allowanceTypeCode: manageFormState.value.allowanceTypeCode?.join(',') || '',
      agriculturalRiskObjectClassCode: manageFormState.value.agriculturalRiskObjectClassCode?.join(',') || '',
      effectiveDate: manageFormState.value.dateRange?.[0] || '',
      invalidTime,
      switchName,
      checkMethod: checkMethod.value ? '1' : '0',
    };
    // 电子化区域修改 技术产品
    if (electronicSwitch.value) {
      params.technologyProductNo = manageFormState.value.technologyProductNo?.join(',') || '';
    }
    const res = await saveReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      open.value = false;
      message.success(res.msg || '配置成功！');
      search();
    } else {
      message.error(res?.msg || '配置失败！');
    }
  } catch (error) {
    console.log(error);
  } finally {
    btnLoading.value = false;
  }
};
// 承保配置新增开关
const addUndertakeInsurance = async () => {
  btnLoading.value = true;
  try {
    // 获取参数
    const configParams: Record<string, string> = {};
    underwritingConfigList.value.forEach((item) => {
      configParams[item.functionCode] = item.checkedList.join(',');
      item.mustRequire = false; // 校验恢复初初始化
    });
    const mustRequireIndex: number = underwritingConfigList.value.findIndex((item) => item.checkedList.length === 0);
    if (mustRequireIndex !== -1) {
      underwritingConfigList.value[mustRequireIndex].mustRequire = true; // 校验必须填写
      return;
    }
    const switchItem: { value: string; label: string } = switchCodeList.value.filter((item: { value: string; label: string }) => item.value === manageFormState.value.switchCode)?.[0];
    const switchName = switchItem.label;
    const params = {
      ...underwritingData.value, // 基本数据
      ...configParams, // 配置数据对象
      coutry: underwritingData.value.county, // 区县代码
      switchName, // 开关名称
    };
    const res = await saveReq.fetchData(params);
    if (res?.code === SUCCESS_CODE) {
      open.value = false;
      message.success(res.msg || '配置成功！');
      search();
    } else {
      message.error(res?.msg || '');
    }
  } catch (error) {
    console.log(error);
  } finally {
    btnLoading.value = false;
  }
};
// 承保配置更改模式
const changeModal = (val: RadioChangeEvent) => {
  queryUnderwritingConfig(val.target.value);
};
// 查看
const check = (record: ManageFormRecord) => {
  isOnlyCheck.value = true;
  longTimeFlag.value = false;
  manageFormState.value = resetData();
  // 精准承保查询开关查看;
  if (precisionUnderwriting.value) {
    open.value = true;
    underwritingData.value = initUnderwritingData(); // 初始化数据先
    underwritingData.value = record as unknown as UnderwritingConfigListType; // 精准承保查看回填详情
    underwritingData.value.county = underwritingData.value.coutry; // 处理区县
    underwritingConfigList.value = underwritingData.value?.preciseList; // 配置界面内容
    underwritingConfigList.value.forEach((item) => {
      item.checkedList = [];
      item.children.forEach((el) => {
        if (el.checkFlag) {
          item.checkedList.push(el.value);
        }
      });
    });
    return;
  }
  // 处理其它开关
  manageFormState.value = {
    ...record,
    allowanceTypeCode: record.allowanceTypeCode?.split(',') || [],
    agriculturalRiskObjectClassCode: record.agriculturalRiskObjectClassCode?.split(',') || [],
    dateRange: [record.effectiveDate as string, record.invalidTime as string],
  };
  // 电子化区域修改 技术产品
  if (electronicSwitch.value) {
    manageFormState.value.technologyProductNo = record.technologyProductNo?.split(',');
  }
  checkMethod.value = record.checkMethod === '1';
  defaultRisk.value = '';
  nextTick(() => {
    defaultRisk.value = record.agriculturalRiskObjectDetailCode || '';
  });
  if (manageFormState.value.switchCode === '2') {
    if (record.effectiveDate && !record.invalidTime) {
      longTimeFlag.value = true;
      if (manageFormState.value.dateRange?.length === 2) {
        manageFormState.value.dateRange[1] = '9999-12-31';
      }
    }
  }
  manageForm.value?.clearValidate();
  open.value = true;
};
// 删除
const delModalOpen = ref<boolean>(false);
const idParmSwitchMultiDimension = ref<string>('');
const switchCodeParam = ref<string>('');
const delReq = await usePost(`${gateWay}${service.administrate}/switchMulti/delete`);
const handleDeleteOk = async () => {
  btnLoading.value = true;
  try {
    const params = {
      idParmSwitchMultiDimension: idParmSwitchMultiDimension.value,
      switchCode: switchCodeParam.value,
    };
    const res = await delReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      delModalOpen.value = false;
      message.success(res.msg || '删除成功！');
      search();
    } else {
      message.error(res?.msg || '删除失败！');
    }
  } catch (error) {
    console.log(error);
  } finally {
    btnLoading.value = false;
  }
};
const deleteItem = (id: string, switchCode: string) => {
  delModalOpen.value = true;
  idParmSwitchMultiDimension.value = id;
  switchCodeParam.value = switchCode;
};
const getOptionsReq = await usePost<SelectOptions[]>(`${gateWay}${service.administrate}/public/getSelectListForLevel`);
// 获取下拉框列表
const getOptions = async () => {
  try {
    const params = ['switchMulti'];
    const res = await getOptionsReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      switchCodeList.value = res.data?.[0]?.children || [];
    }
  } catch (error) {
    console.log(error);
  }
};
// 获取下拉框列表
const getProductOptions = async () => {
  try {
    const res = await $get<SelectOptions[]>(`${gateWay}${service.accept}/saleInfo/getTechnicProducts`);
    if (res && res.code === SUCCESS_CODE) {
      productOptions.value =
        res.data.map((item) => ({
          label: item.technicProductCode + item.technicProductName,
          value: item.technicProductCode,
        })) || [];
    }
  } catch (error) {
    console.log(error);
  }
};
// 精准承保作业模式开关查询配置
const queryUnderwritingConfig = async (modelValue: string) => {
  const queryReq = await usePost<UnderwritingConfigType[]>(`${gateWay}${service.administrate}/switchMulti/queryConfig`);
  try {
    const params = {
      versionNum: modelValue,
    };
    const res = await queryReq.fetchData(params);
    if (res?.code === SUCCESS_CODE) {
      const arr: UnderwritingConfigType[] = [];
      // 配置项默认全选不能去更改配置，所以调用接口的时候全部给勾选上
      res?.data.forEach((item: UnderwritingConfigType) => {
        item.checkedList = [];
        item.children.forEach((el) => {
          item.checkedList.push(el.value);
        });
        arr.push({
          functionName: item.functionName, // 名称
          allFlag: item.allFlag, // 是否全选标识
          children: item.children, // 子级
          functionCode: item.functionCode, // 名称对应code
          checkedList: item.checkedList, // 设置用于勾选数据的list
          indeterminate: false, // 设置 indeterminate 状态，只负责样式控制全选
          mustRequire: false, // 校验必须填写
        });
      });
      underwritingConfigList.value = arr;
    } else {
      message.error(res?.msg || '');
    }
  } catch (error) {
    console.log(error);
  }
};
// 精准承保勾选数据回调
const handlerCheckData = (checkItem: UnderwritingConfigType) => {
  if (checkItem.checkedList.length) {
    checkItem.mustRequire = false;
  }
};
onMounted(() => {
  getOptions();
  getProductOptions();
  search();
});
</script>
