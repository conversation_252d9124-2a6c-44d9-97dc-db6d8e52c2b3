import type { CheckboxValueType } from 'ant-design-vue/es/checkbox/interface';

// 查询条件
export interface SearchFormState {
  switchCode: string; // 场景
  departmentNo: string; // 机构编码
  isContainLowDepartment: boolean; // 是否包含下级机构
  agriculturalRiskObjectClassCode: string; // 标的
  allowanceTypeCode?: string[]; // 补贴类型
}
export interface ManageFormCommon {
  switchCode: string; // 场景
  departmentNo: string; // 机构编码
  dateRange: [string, string]; // 开关有效期
  province?: string; // 省
  city?: string; // 市
  coutry?: string; // 县
  isElecTemplate?: boolean; // 电子化模版名称是否通用
  endorsementTag?: string; // 电子批单模板
  farmerDocumentTag?: string; // 电子分户凭证模板
  policyTagCultureGroup?: string; // 集体投保电子保单模板-养殖业
  policyTagCulturePersonal?: string; // 单户投保电子保单模板-养殖业
  policyTagForestryGroup?: string; // 集体投保电子保单模板-林业
  policyTagForestryPersonal?: string; // 单户投保电子保单模板-林业
  policyTagPlantGroup?: string; // 单户投保电子保单模板-种植
  policyTagPlantPersonal?: string; // 单户投保电子保单模板-种植
  agriculturalRiskObjectDetailCode?: string; // 标的-五级
  whetherSendMessage?: boolean; // 是否向农户发送短信
  effectiveDate?: string;
  invalidTime?: string;
  checkMethod?: string; // 验标方式
}
// 配置页面
export interface ManageFormStateType extends ManageFormCommon {
  allowanceTypeCode?: string[]; // 补贴类型
  agriculturalRiskObjectClassCode?: CheckboxValueType[]; // 标的-数组
}
export interface ManageFormRecord extends ManageFormCommon {
  allowanceTypeCode: string;
  agriculturalRiskObjectClassCode: string;
}
export interface DataType {
  pages: number; // 总页数
  records: Record<string, string>[];
  total: number;
  current: number;
  size: number;
}
export interface SelectOptions {
  label: string;
  value: string;
  disabled?: boolean;
  children?: SelectOptions[];
  isLeaf?: boolean;
}
// 精准承保开关配置类型
export interface UnderwritingConfigType {
  functionName: string; // 配置title
  functionCode: string; // 配置列表code
  allFlag: boolean; // 是否展示全选
  checkedList: string[]; // 选中的数据
  indeterminate?: boolean; // 是否勾选全选样式控制
  checkAll?: boolean; // 是否全选
  children: UnderwritingConfigChildrenType[]; // 子级
  mustRequire: boolean; //  校验必须填写
}
// 精准承保开关配置子级类型
export interface UnderwritingConfigChildrenType {
  label: string; // 选中的label
  value: string; //  选中的值
  allFlag?: boolean; // 是否有全选
  checkFlag: boolean; // 是否选中
}
// 精准承保开关列表查询
export interface UnderwritingConfigListType {
  villageName: string; // 村名
  village: string; // 村code
  townName: string; // 区县名
  town: string; // 区县code
  countyName: string;
  coutryName: string;
  cityName: string;
  city: string; // 城市
  provinceName: string; // 省
  province: string; // 省
  fromCommon: string; // 是否从共：00全部、01是、02否
  agriculturalRiskObjectClassCode: string; // 险种大类
  switchCode: string; // 精准承保作业模式
  switchName: string; // 精准承保作业模式
  preciseList: UnderwritingConfigType[]; // 功能配置信息
  coutry: string; // 区县
  county: string; // 区县
  versionNum: string; // 版本
}
// 省市区
export interface AddressType {
  province: string | undefined; // 省
  provinceName: string; // 省
  city: string | undefined; // 市
  cityName: string; // 市
  coutry: string | undefined; // 区
  coutryName: string; // 区
  town: string | undefined; // 县
  townName: string; // 县
  village: string | undefined; // 村
  villageName: string; // 村
}
