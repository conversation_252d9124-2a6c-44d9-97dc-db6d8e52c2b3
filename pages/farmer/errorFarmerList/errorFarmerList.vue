<template>
  <div class="edit-farmer-list p-[14px]">
    <div class="p-[16px] bg-[white] rounded-[6px]">
      <div class="flex">
        <SearchForm v-model:form-state="formState" :expand="expand" :reset="formReset" :dept="dept" />
        <FormFold v-model="expand" class="ml-8px" />
      </div>
      <div class="flex justify-center">
        <a-button class="mr-[12px]" @click="reset">重置</a-button>
        <a-button type="primary" ghost @click="submit">查询</a-button>
      </div>
    </div>
    <div class="p-[16px] bg-[white] rounded-[6px] mt-[14px]">
      <div class="table-title flex justify-between items-center">
        <div class="text-[16px] font-bold">查询结果</div>
        <div class="space-x-8px">
          <AuthButton code="errorFarmerImport" @click="openModal">批量导入表格</AuthButton>
          <AuthButton code="errorBatchExport" :loading="exporting" @click="dataExport">批量下载清单</AuthButton>
        </div>
      </div>
      <div class="mb-[14px] text-[rgba(0,0,0,.6)]">涉及客户信息问题{{ tipState.customerRuleCount }}户，地块信息问题{{ tipState.landRuleCount }}户，其他问题{{ tipState.otherRuleCount }}户</div>
      <a-table :data-source="dataSource" :columns="columns" :pagination="pagination" :bordered="false" :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : undefined)">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key ==='farmerName'">
            <div>{{ text }} <a-tag v-if="record.accurateBigFarmer === 'Y'" color="green">大户</a-tag></div>
          </template>
          <template v-if="column.key === 'errorruleContents'">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[700px] truncate">{{ (text || '').replace(/\\n/g, '') }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.key === 'operation'">
            <AuthButton code="errorFarmerEdit" type="link" size="small" @click="handleEdit(record as ErrorFarmerList)">编辑</AuthButton>
            <AuthButton code="errorFarmerView" type="link" size="small" @click="handleView(record as ErrorFarmerList)">查看</AuthButton>
            <AuthButton code="errorFarmerDel" type="link" size="small" @click="handleDel(record as ErrorFarmerList)">删除</AuthButton>
          </template>
        </template>
      </a-table>
    </div>
    <ImportModal v-if="modalVisible" v-model:visible="modalVisible" :list-info="listInfo" :refresh />
  </div>
</template>

<script setup lang="ts">
import type { TableColumnProps } from 'ant-design-vue';
import SearchForm from '../editFarmerList/components/SearchForm.vue';
import ImportModal from '../editFarmerList/components/ImportModal.vue';
import useUpdateList from '../editFarmerList/useUpdateList';
import type { SearchFormModel } from '../farmer.d';
import FormFold from '@/components/ui/FormFold.vue';
import type { ErrorFarmerList, EditFarmerListReq, ErrorFarmerListResult } from '@/apiTypes/farmer/farmerList';
import { $postOnClient, usePost } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import { message } from '@/components/ui/Message';
import { pxToRem } from '@/utils/tools';
import { SUCCESS_CODE } from '~/utils/constants';
import AuthButton from '@/components/ui/AuthButton.vue';

const getListReq = await usePost<ErrorFarmerListResult, EditFarmerListReq>('/api/farmer/getFarmerErrorRuleList');
const deleteFarmerListReq = await usePost('/gateway/icore-agr-an.farmer/farmerList/deleteFarmerList');

const { modalVisible, listInfo, openModal, dataExport, exporting } = useUpdateList();

const route = useRoute();
const dept = computed(() => (route.query.dept as string) || '');
const expand = ref(false);
const formState = reactive<SearchFormModel>({
  farmerlistNo: undefined,
  farmerName: '',
  certificateNo: undefined,
  mobileTelephone: undefined,
  plantAddressCodeProvince: '',
  plantAddressCodeCity: '',
  plantAddressCodeCounty: '',
  plantAddressCodeTown: '',
  plantAddressCodeVillage: '',
  riskCode: '', // 标的
});
const formReset = ref(0);
const columns: TableColumnProps[] = [
  { title: '序号', dataIndex: 'index', key: 'index' },
  { title: '分户被保险人', dataIndex: 'farmerName', key: 'farmerName' },
  { title: '证件号码', dataIndex: 'certificateNo', key: 'certificateNo' },
  { title: '错误提示内容', dataIndex: 'errorruleContents', key: 'errorruleContents' },
  { title: '操作', width: pxToRem(160), dataIndex: 'operation', key: 'operation', fixed: 'right' },
];

const dataSource = ref<ErrorFarmerList[]>([]);

const refresh = async () => {
  await nextTick();
  getList();
};
const { pagination } = usePagination(refresh);

const getList = async () => {
  const params = {
    farmerlistNo: route.query.id as string,
    farmerName: formState.farmerName || '',
    certificateNo: formState.certificateNo || '',
    mobileTelephone: formState.mobileTelephone || '',
    // plantAddressCodeVillage: formState.plantAddressCodeVillage || '',
    // plantAddressCodeProvince: formState.plantAddressCodeProvince || '',
    // plantAddressCodeCity: formState.plantAddressCodeCity || '',
    // plantAddressCodeCounty: formState.plantAddressCodeCounty || '',
    // plantAddressCodeTown: formState.plantAddressCodeTown || '',
    riskCode: formState.riskCode || '',
    pageSize: pagination.pageSize || 10,
    pageNum: pagination.current || 1,
  };
  try {
    const res = await getListReq.fetchData(params);
    if (res?.code === '000000') {
      dataSource.value = res?.data?.list || [];
      pagination.current = res?.data?.current || 1;
      pagination.pageSize = res?.data?.size || 10;
      pagination.total = res?.data?.total || 0;
    } else {
      message.error(res?.msg || '请求有误，请稍后重试');
    }
  } catch (err) {
    console.log(err);
  }
};
const tipState = reactive({
  customerRuleCount: 0,
  landRuleCount: 0,
  otherRuleCount: 0,
});
const fetchTip = async () => {
  try {
    const url = '/gateway/icore-agr-an.farmer/farmerList/getFarmerListSummaryAndRuleInfo';
    const formData = new FormData();
    formData.append('farmerListNo', route.query.id);
    const res = await $postOnClient(url, formData);
    if (res && res.code === SUCCESS_CODE) {
      tipState.customerRuleCount = res.data.errorFarmerRuleInfo.customerRuleCount;
      tipState.landRuleCount = res.data.errorFarmerRuleInfo.landRuleCount;
      tipState.otherRuleCount = res.data.errorFarmerRuleInfo.otherRuleCount;
    }
  } catch (e) {
    console.log(e);
  }
};

await getList();
await fetchTip();

const reset = () => {
  formState.farmerName = '';
  formState.certificateNo = '';
  formState.mobileTelephone = '';
  formReset.value = formReset.value + 1;
};

const submit = () => {
  pagination.current = 1;
  refresh();
};

const router = useRouter();
const handleEdit = (record: ErrorFarmerList) => {
  router.push({
    path: '/farmerInfo',
    query: {
      mode: 'edit',
      farmerListNo: route.query.id,
      idFarmerlistCustomInfo: record.idFarmerlistCustomInfo,
      ruleType: '1',
    },
  });
};

const handleView = (record: ErrorFarmerList) => {
  router.push({
    path: '/farmerInfo',
    query: {
      mode: 'view',
      farmerListNo: route.query.id,
      idFarmerlistCustomInfo: record.idFarmerlistCustomInfo,
      ruleType: '1',
    },
  });
};

const handleDel = async (record: ErrorFarmerList) => {
  Modal.confirm({
    title: '提醒',
    content: `是否确认删除分户被保险人【${record.farmerName}】`,
    async onOk() {
      try {
        const res = await deleteFarmerListReq.fetchData({
          farmerlistNo: record.farmerlistNo,
          idFarmerlistCustomInfo: record.idFarmerlistCustomInfo,
        });
        if (res?.code === '000000') {
          message.success('删除成功');
          refresh();
        } else {
          message.error(res?.msg || '删除失败');
        }
      } catch (err) {
        console.log(err);
      }
    },
  });
};

onActivated(() => {
  reset();
  pagination.current = 1;
  refresh();
  fetchTip();
});
</script>
