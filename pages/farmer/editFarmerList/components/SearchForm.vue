<template>
  <a-form class="grow grid grid-cols-3 gap-x-[44px] gap-y-[2px]" :label-col="{ style: { width: pxToRem(96) } }" :colon="false">
    <a-form-item label="分户被保险人" name="username">
      <a-input v-model:value.trim="formState.farmerName" placeholder="请输入" />
    </a-form-item>
    <a-form-item label="证件号码" name="certificateNo">
      <a-input v-model:value.trim="formState.certificateNo" placeholder="请输入" />
    </a-form-item>
    <a-form-item label="联系方式" name="mobileTelephone">
      <a-input v-model:value.trim="formState.mobileTelephone" placeholder="请输入" />
    </a-form-item>
    <!-- <a-form-item v-show="expand" label="标的地址" name="plantAddressCodeVillage" class="col-span-3">
      <div class="flex">
        <RegionSelect v-model:province="province" v-model:city="city" v-model:town="town" v-model:county="county" v-model:village="village" class="w-[70%]" />
      </div>
    </a-form-item> -->
    <a-form-item v-show="expand" label="标的" name="riskCode" class="col-span-3">
      <RiskCodeSelect v-model:value="formState.riskCode" :department-code="props.dept" />
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import type { SearchFormModel } from '../../farmer.d';
import { pxToRem } from '@/utils/tools';
// import RegionSelect from '@/components/selector/RegionSelect.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';

const formState = defineModel<SearchFormModel>('formState', { required: true });

const props = defineProps<{ expand: boolean; reset: number; dept: string }>();

// const province = ref(undefined); // 省
// const city = ref(undefined); // 市
// const town = ref(undefined); // 区
// const county = ref(undefined); // 县
// const village = ref(undefined); // 村

// watch(province, (val) => {
//   if (val !== formState.value.plantAddressCodeVillage) {
//     formState.value.plantAddressCodeProvince = val || '';
//   }
// });

// watch(city, (val) => {
//   if (val !== formState.value.plantAddressCodeVillage) {
//     formState.value.plantAddressCodeCity = val || '';
//   }
// });

// watch(town, (val) => {
//   if (val !== formState.value.plantAddressCodeVillage) {
//     formState.value.plantAddressCodeTown = val || '';
//   }
// });

// watch(county, (val) => {
//   if (val !== formState.value.plantAddressCodeVillage) {
//     formState.value.plantAddressCodeCounty = val || '';
//   }
// });

// watch(village, (val) => {
//   if (val !== formState.value.plantAddressCodeVillage) {
//     formState.value.plantAddressCodeVillage = val || '';
//   }
// });

watch(() => props.reset, () => {
  // province.value = undefined;
  // city.value = undefined;
  // town.value = undefined;
  // county.value = undefined;
  // village.value = undefined;
  formState.value.plantAddressCodeCity = '';
  formState.value.plantAddressCodeCounty = '';
  formState.value.plantAddressCodeProvince = '';
  formState.value.plantAddressCodeTown = '';
  formState.value.plantAddressCodeVillage = '';
  formState.value.riskCode = '';
});
</script>
