<template>
  <a-modal v-model:open="visible" title="增量导入" :width="pxToRem(950)" centered :confirm-loading="submitLoading" @ok="handleOk" @cancel="handleCancel">
    <div class="mb-[18px]">
      <div class="flex items-center mb-[9px]">
        <VueIcon :icon="IconJichuxinxiFont" />
        <span class="ml-[3px]">基础信息</span>
      </div>
      <div class="bg-gray-100 rounded p-14px flex box-border">
        <p class="m-0 w-1/2 shrink-0 box-border">
          <span>投保清单编号：</span>
          <span class="text-[#262626]">{{ listInfo.farmerlistNo }}</span>
        </p>
        <p class="m-0 shrink-0 w-1/2 box-border">
          <span>清单名称：</span>
          <span class="text-[#262626]">{{ listInfo.farmerlistName }}</span>
        </p>
      </div>
    </div>
    <div class="mb-[15px]">
      <div class="flex items-center justify-between mb-[9px]">
        <div class="flex items-center">
          <VueIcon :icon="IconPingtaijiekouFont" />
          <span class="ml-[3px]">清单增量文件</span>
        </div>
        <div class="text-[#576B95] flex items-center justify-end space-x-14px">
          <div v-for="temp in templateList" :key="temp.templateFileKey" class="cursor-pointer" @click="downloadFileKey(temp.templateFileKey)">
            <VueIcon :icon="IconTongyongXiazaiFont" />
            {{ temp.templateName }}
          </div>
        </div>
      </div>
      <div class="bg-gray-100 rounded p-14px">
        <a-upload :multiple="false" :show-upload-list="false" :action="handleFile">
          <a-button :loading="uploading">
            <template #icon>
              <VueIcon :icon="IconTongyongShangchuanFont" />
            </template>
            <span class="ml-[2px]">增量附件上传</span>
          </a-button>
        </a-upload>
        <div v-if="currentFile" class="text-12px text-[#4E6085] mt-[13px]">
          <VueIcon :icon="IconAttachmentFont" />
          <span class="ml-[5px] cursor-pointer" @click="downloadTemplate(currentFile.fileKey)">{{ currentFile.fileName }}({{ currentFile.fileSize }})</span>
          <a-button type="link" @click="removeFile">删除</a-button>
        </div>
      </div>
    </div>
    <div>
      <div>温馨提示：</div>
      <div>1.增量附件上传是在当前投保清单的基础上进行增量导入；</div>
      <div>2.修改清单可能会影响验标要求，且投保单需要重新签字/公示，请谨慎操作;</div>
    </div>
  </a-modal>
  <a-modal v-model:open="allDeleteModal" title="提醒" @ok="handleConfirmOk">
    <div>{{ confirmText }}</div>
  </a-modal>
</template>

<script setup lang="ts">
import { IconJichuxinxiFont, IconPingtaijiekouFont, IconTongyongShangchuanFont, IconAttachmentFont, IconTongyongXiazaiFont } from '@pafe/icons-icore-agr-an';
import { get, set } from 'lodash-es';
import { downloadFile, pxToRem } from '@/utils/tools';
import { $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { notification, Button } from 'ant-design-vue';

const visible = defineModel<boolean>('visible', { required: true, default: false });
const props = defineProps<{
  listInfo: { [key: string]: unknown };
  refresh: () => void;
  farmerlistStatus: string;
  templateList: File[];
}>();

const { service } = useRuntimeConfig().public || {};

const downloadTemplate = async (fileKey: string) => {
  const res = await $getOnClient(`/gateway${service.farmer}/templateInfo/queryFileDownloadUrl`, { fileKey });
  const url = get(res, 'data.url');
  if (url) {
    downloadFile(url);
  }
};
const confirmText = ref<string>('');
const allDeleteModal = ref<boolean>(false);
const emit = defineEmits(['getPushStatus']);
const submitLoading = ref(false);
const handleOk = async () => {
  if (props.farmerlistStatus === '2') {
    allDeleteModal.value = true;
    confirmText.value = '修改清单数据会影响验标，且如投保单已完成签字确认/公示动作，需要重新触发，请谨慎操作。';
  } else {
    handleConfirmOk();
  }
};
// 二次确认
const handleConfirmOk = async () => {
  if (!currentFile.value?.fileKey) {
    message.warning('请先上传清单附件');
    return;
  }
  submitLoading.value = true;
  const res = await $postOnClient(`/gateway${service.farmer}/file/excelUpload`, {
    position: '2',
    applyExtraParam: {
      fileKey: currentFile.value?.fileKey || '',
      farmerListNo: props.listInfo.farmerlistNo,
      farmerListName: props.listInfo.farmerlistName,
      templateId: currentFile.value?.templateId, // 待定
    },
  });
  if (get(res, 'code') === SUCCESS_CODE) {
    if (typeof props.refresh === 'function') {
      setTimeout(() => {
        props.refresh();
      }, 500);
    }
    allDeleteModal.value = false;
    visible.value = false;
    message.success('增量导入成功');
    emit('getPushStatus');
  } else {
    message.error(get(res, 'msg', ''));
  }
  submitLoading.value = false;
};
// 删除附件
const removeFile = async () => {
  await $getOnClient(`/gateway${service.farmer}/templateInfo/deleteFile`, { fileKey: currentFile.value?.fileKey || '' });
  currentFile.value = undefined;
};

const handleCancel = () => {
  console.log('cancel');
};

// 当前上传清单附件
const currentFile = ref<{
  farmerListName: string;
  fileKey: string;
  fileName: string;
  fileSize: string;
  templateId: string;
}>();

const downloadFileByKey = async (fileKey: string, key: string) => {
  const res = await $getOnClient(`/gateway${service.farmer}/templateInfo/queryFileDownloadUrl`, { fileKey });
  const { msg = '' } = res || {};
  const url = get(res, 'data.url');
  if (url) {
    downloadFile(url);
    notification.close(key);
  } else {
    message.error(msg);
  }
};

const uploading = ref(false);
const handleFile = (file: File) => {
  uploading.value = true;
  const formData = new FormData();
  formData.append('file', file);
  formData.append('farmerListNo', props.listInfo.farmerlistNo as Blob);
  $postOnClient(`/gateway${service.farmer}/templateInfo/incrementUploadFarmerListFile`, formData)
    .then((res) => {
      if (res && res.code === SUCCESS_CODE) {
        const data = res.data;
        if (get(data, 'uploadResult')) {
          set(currentFile, 'value', res.data);
        } else {
          const key = `open${Date.now()}`;
          notification.error({
            message: '错误信息',
            key,
            description: () => {
              // description不支持slot，仅支持string和vnode
              const newMsg = get(data, 'errorMsg', '').split('\n');
              const paragraphs = newMsg.map((text) => h('p', { style: { margin: 0, lineHeight: 1.5 } }, text));
              return h('div', paragraphs);
            },
            duration: 10,
            btn: () =>
              h(
                Button,
                {
                  type: 'link',
                  size: 'small',
                  onClick: () => {
                    const fileKey = get(res, 'data.fileKey', '');
                    downloadFileByKey(fileKey, key);
                  },
                },
                { default: () => '下载完整信息' },
              ),
          });
        }
      } else {
        message.error(res?.msg as string);
      }
    })
    .finally(() => {
      uploading.value = false;
    });
  return Promise.reject();
};
const downloadFileKey = async (fileKey: string, key: string) => {
  const res = await $getOnClient(`/gateway${service.farmer}/templateInfo/queryFileDownloadUrl`, { fileKey });
  const { msg = '' } = res || {};
  const url = get(res, 'data.url');
  if (url) {
    downloadFile(url);
    notification.close(key);
  } else {
    message.error(msg);
  }
};
</script>
