import type { CustomRenderFunctionArg } from '@visactor/vtable';
import type { TableColumnProps } from 'ant-design-vue';
import type { CellData } from '@/components/ui/Vtable/vtable.d';

// 记录类型
interface RecordType {
  [key: string]: CellData;
  idFarmerlistBaseInfo?: CellData;
  landCode?: CellData;
  mapAreaCode?: CellData;
  landName?: CellData;
  agrLandClassification?: CellData;
  authName?: CellData;
  authCertificateNo?: CellData;
  confirmationLandCode?: CellData;
  landArea?: CellData;
  insuredQuantity?: CellData;
  animalAge?: CellData;
  startEarTagNo?: CellData;
  endEarTagNo?: CellData;
  planCode?: CellData;
  unitInsuredAmount?: CellData;
  forestCertificationNo?: CellData;
  forestSpotCode?: CellData;
  treeAge?: CellData;
  forestAverageDensity?: CellData;
  authStatus?: CellData;
  landDeviationsStatus?: CellData;
}

// 渲染函数参数类型
interface RenderTableTxtCellParams {
  args: CustomRenderFunctionArg;
  key: string;
}

// 渲染基础单元格参数类型
interface RenderBaseCellParams {
  container: unknown;
  content: unknown;
  errorTips?: string;
  status?: string;
  height?: number;
  width: number;
  disable?: boolean;
}

// 选项类型
interface Option {
  label: string;
  value: string;
}

// 验证规则类型
interface ValidationRule {
  validator: (value: unknown, unitValue?: unknown) => boolean | { valid: boolean; message: string };
  message?: string;
}

// 表格列配置类型
interface TableColumn extends TableColumnProps {
  field: string;
  title: string;
  width?: string | number;
  rule?: ValidationRule[];
  editor?: string;
  options?: Option[];
  customLayout?: (args: CustomRenderFunctionArg) => unknown;
  headerCustomLayout?: (args: CustomRenderFunctionArg) => unknown;
}

// 表格实例类型
interface TableInstance {
  records: RecordType[];
  columns: TableColumn[];
  getRecordByRowCol: (col: number, row: number) => RecordType;
  getCellRect: (col: number, row: number) => { height: number; width: number };
  getRecordShowIndexByCell: (col: number, row: number) => number;
  deleteRecords: (indexes: number[]) => void;
  renderWithRecreateCells: () => void;
  setRecords: (records: RecordType[]) => void;
  updateOption: (options: unknown) => void;
  addRecord: (record: RecordType, index: number) => void;
  scrollToCell: (params: { row: number }) => void;
  getCheckboxState: () => unknown[];
  setCellCheckboxState: (col: number, row: number, state: boolean) => void;
}

export type {
  CellData,
  RecordType,
  RenderTableTxtCellParams,
  RenderBaseCellParams,
  Option,
  ValidationRule,
  TableColumn,
  TableInstance,
};
