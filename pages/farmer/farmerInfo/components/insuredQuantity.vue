<template>
  <!-- 投保数量 -->
  <a-form-item v-bind="$attrs" ref="formItemRef" :required="true" :rules="rules" :help="help" :validate-status="status" label="">
    <span v-if="pageMode === 'view'" class="font-bold">{{ renderValue }}</span>
    <a-input v-else v-model:value="insuranceNums" @change="inputChange">
      <template #addonAfter>
        <a-select v-model:value="insuranceNumsUnit" :style="{ width: pxToRem(80) }" :options="selectOptions" allow-clear @change="selectChange" />
      </template>
    </a-input>
  </a-form-item>
</template>

<script setup lang="ts">
import { get, set } from 'lodash-es';
import type { RuleObject } from 'ant-design-vue/es/form/interface';
import type { DefaultOptionType } from 'ant-design-vue/es/select';
import { pxToRem } from '@/utils/tools';

const pageMode = inject('pageMode');

const formItemRef = ref();
const help = ref<string>();
const status = ref<string>();
const insuranceNums = defineModel('insuranceNums', { type: Number, default: 0 });
const insuranceNumsUnit = defineModel('insuranceNumsUnit', { type: String, default: '' });

const props = defineProps({
  unitOptions: {
    type: Array,
    default: () => [],
  },
  defaultStatus: {
    type: String,
    default: '',
  },
  defaultHelp: {
    type: Array,
    default: String,
  },
});

const selectOptions = computed(() => props.unitOptions as DefaultOptionType[]);
const renderValue = computed(() => {
  let res = '';
  if (insuranceNums.value) {
    res += insuranceNums.value;
  }
  if (insuranceNumsUnit.value) {
    const selected = props.unitOptions.find(item => get(item, 'value') === insuranceNumsUnit.value);
    if (selected) {
      res += get(selected, 'label');
    }
  }
  return res;
});

const validator = (_: unknown, data: { value: string; unitValue: unknown }) => {
  if (!data.value) {
    return Promise.reject('请录入投保数量');
  } else if (!/^\d{1,9}(\.\d{0,2})?$/.test(data.value)) {
    return Promise.reject('仅支持输入数字，最多9位整数，2位小数');
  }
  if (!data.unitValue) {
    return Promise.reject('请录入投保数量单位');
  }
  return Promise.resolve();
};

const rules: RuleObject[] = [{ validator: validator, trigger: 'change' }];

const inputChange = () => {
  help.value = undefined;
  status.value = undefined;
  formItemRef.value.onFieldChange();
};

const selectChange = () => {
  help.value = undefined;
  status.value = undefined;
  formItemRef.value.onFieldChange();
};
watchEffect(() => {
  if (props.defaultStatus) {
    status.value = props.defaultStatus;
  } else {
    status.value = undefined;
  }
  if (props.defaultHelp) {
    set(help, 'value', props.defaultHelp);
  } else {
    help.value = undefined;
  }
});
</script>
