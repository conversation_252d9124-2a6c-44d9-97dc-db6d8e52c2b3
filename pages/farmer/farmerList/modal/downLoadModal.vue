<template>
  <a-modal v-model:open="open" title="导出清单" :width="pxToRem(1030)" :footer="null">
    <div class="flex">
      <a-form class="grow">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="清单类型">
              <a-select v-model:value="fileTaskType" :options="fileTaskTypeOptions3" />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item label="创建时间">
              <a-range-picker v-model:value="date" format="YYYY-MM-DD" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="导出文件名称">
              <a-input v-model:value="fileName" placeholder="请输入" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="ml-[18px] space-x-[8px] shrink-0">
        <a-button @click="reset">重置</a-button>
        <a-button type="primary" @click="search">查询</a-button>
      </div>
    </div>
    <div class="table-title">导出进度</div>
    <a-table :columns="columns" :loading="loading" :pagination="pagination" :data-source="dataSource" :scroll="{ y: '24vh' }">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'operation'">
          <template v-if="record.status === 3">
            <a-button v-show="!record.loading" type="link" @click="download(record)">下载</a-button>
            <a-button v-show="record.loading" type="link" :loading="record.loading" />
          </template>
        </template>
        <template v-if="['name'].includes(column.dataIndex as string)">
          <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
            <div class="max-w-[200px] truncate">{{ text }}</div>
          </a-tooltip>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup lang="ts">
import type { Dayjs } from 'dayjs';
import { get } from 'lodash-es';
import type { TaskDataListObj } from '../../farmer.d';
import { pxToRem, downloadBlob } from '@/utils/tools';
import { usePagination } from '@/composables/usePagination';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost, $getOnClient } from '@/composables/request';

const { gateWay, service } = useRuntimeConfig().public || {};

// 获取表格
const { fetchData: getQueryFileTask } = await usePost(`${gateWay}${service.farmer}/file/queryFileTask`);

const open = defineModel<boolean>('open', { required: true, default: false });

const fileTaskTypeOptions3 = ref([{ value: '1', label: '投保清单任务' }]);

const fileTaskType = ref('1');
const date = ref<[Dayjs, Dayjs]>();
const fileName = ref('');
const loading = ref(false);
const dataSource = ref<TaskDataListObj[]>([]);

const columns = [
  { title: '清单类型', dataIndex: 'typeName', key: 'typeName' },
  { title: '导出文件名称', dataIndex: 'name', key: 'name' },
  { title: '创建时间', dataIndex: 'time', key: 'time' },
  { title: '下载队列状态', dataIndex: 'statusText', key: 'statusText' },
  { title: '操作', dataIndex: 'operation', key: 'operation' },
];

const refresh = async () => {
  const createdDateStart = date.value && date.value[0] && date.value[0].format('YYYY-MM-DD');
  const createdDateEnd = date.value && date.value[1] && date.value[1].format('YYYY-MM-DD');
  const params = {
    fileTaskType: fileTaskType.value,
    createdDateStart: createdDateStart || '',
    createdDateEnd: createdDateEnd || '',
    fileName: fileName.value,
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
  };
  loading.value = true;
  try {
    const response = await getQueryFileTask(params);
    const code = get(response, 'code');
    const data = get(response, 'data', {});
    loading.value = false;
    if (code === SUCCESS_CODE) {
      const records = get(data, 'records', []);
      if (records && records.length) {
        dataSource.value = records.map((val) => {
          return {
            type: get(val, 'fileTaskType', ''),
            typeName: get(val, 'fileTaskTypeName', ''),
            name: get(val, 'fileName', ''),
            time: get(val, 'createdDate', ''),
            status: get(val, 'fileTaskStatus'),
            statusText: get(val, 'fileTaskStatusName'),
            idEvntFileTask: get(val, 'idEvntFileTask', ''),
            loading: false,
          };
        });
      } else {
        dataSource.value = [];
      }
      pagination.total = get(data, 'total');
      pagination.current = get(data, 'current');
      pagination.pageSize = get(data, 'size');
    } else {
      dataSource.value = [];
      pagination.total = 0;
      pagination.current = 1;
      pagination.pageSize = 10;
    }
  } catch {
    loading.value = false;
  }
};
const { pagination } = usePagination(refresh);

const search = () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  refresh();
};

watch(open, (value) => {
  if (value) {
    // 打开弹窗，列表没有数据时，重新发起请求
    search();
  }
}, {
  immediate: true,
});

const download = async (record: { [key: string]: unknown }) => {
  record.loading = true;
  try {
    await $getOnClient(`${gateWay}${service.farmer}/file/downloadTaskFile`, { idEvntFileTask: record.idEvntFileTask }, {
      async onResponse({ response }) {
        let fileName = '';
        const contentDisposition = response.headers.get('Content-Disposition');
        const fileData = response._data;
        const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
        if (contentDisposition) {
          const match = contentDisposition.match(/filename=(.+)/);
          if (match) {
            fileName = decodeURI(match[1]);
          }
        }
        downloadBlob(fileData, fileName, fileType);
      },
    });
    record.loading = false;
  } catch {
    record.loading = false;
  }
};

const reset = () => {
  fileTaskType.value = '';
  date.value = undefined;
  fileName.value = '';
};
</script>

<style>
.table-title {
  margin-top: 8px;
  padding: 16px 0;
  color: rgba(0,0,0,0.9);
  font-size: 16px;
  border-top: 1px solid rgba(230,232,235,1);
}
</style>
