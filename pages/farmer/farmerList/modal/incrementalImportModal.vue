<template>
  <a-modal v-model:open="visible" title="增量导入" :width="pxToRem(950)" centered :confirm-loading="submitLoading" @ok="handleOk" @cancel="handleCancel">
    <div class="mb-[18px]">
      <div class="flex items-center mb-[9px]">
        <VueIcon :icon="IconJichuxinxiFont" />
        <span class="ml-[3px]">基础信息</span>
      </div>
      <div class="bg-gray-100 rounded p-14px flex box-border">
        <p class="m-0 w-1/2 shrink-0 box-border">
          <span>投保清单编号：</span>
          <span class="text-[#262626]">{{ listInfo.farmerlistNo }}</span>
        </p>
        <p class="m-0 shrink-0 w-1/2 box-border">
          <span>清单名称：</span>
          <span class="text-[#262626]">{{ listInfo.farmerlistName }}</span>
        </p>
      </div>
    </div>
    <div class="mb-[15px]">
      <div class="flex items-center justify-between mb-[9px]">
        <div class="flex items-center">
          <VueIcon :icon="IconPingtaijiekouFont" />
          <span class="ml-[3px]">清单增量文件</span>
        </div>
      </div>
      <div class="bg-gray-100 rounded p-14px">
        <a-upload :multiple="false" :show-upload-list="false" :action="handleFile">
          <a-button :loading="uploading">
            <template #icon>
              <VueIcon :icon="IconTongyongShangchuanFont" />
            </template>
            <span class="ml-[2px]">增量附件上传</span>
          </a-button>
        </a-upload>
        <div v-if="currentFile" class="text-12px text-[#4E6085] mt-[13px]">
          <VueIcon :icon="IconAttachmentFont" />
          <span class="ml-[5px] cursor-pointer" @click="downloadTemplate(currentFile.fileKey)">{{ currentFile.fileName }}({{ currentFile.fileSize }})</span>
          <a-button type="link" @click="removeFile">删除</a-button>
        </div>
      </div>
    </div>
    <div>
      <div>温馨提示：</div>
      <div>1.附件上传是根据已有多投保清单进行增量部分的导入；</div>
      <div>2.删除是删除本次已经导入的增量部分；</div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { IconJichuxinxiFont, IconPingtaijiekouFont, IconTongyongShangchuanFont, IconAttachmentFont } from '@pafe/icons-icore-agr-an';
import { get, set } from 'lodash-es';
import { downloadFile, pxToRem } from '@/utils/tools';
import { $getOnClient, $postOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';

const visible = defineModel<boolean>('visible', { required: true, default: false });
const props = defineProps<{
  listInfo: { [key: string]: unknown };
}>();

const { service } = useRuntimeConfig().public || {};

const downloadTemplate = async (fileKey: string) => {
  const res = await $getOnClient(`/gateway${service.farmer}/templateInfo/queryFileDownloadUrl`, { fileKey });
  const url = get(res, 'data.url');
  if (url) {
    downloadFile(url);
  }
};

const submitLoading = ref(false);
const handleOk = async () => {
  submitLoading.value = true;
  const res = await $postOnClient(`/gateway${service.farmer}/file/excelUpload`, {
    position: '2',
    applyExtraParam: {
      fileKey: currentFile.value?.fileKey || '',
      farmerListNo: props.listInfo.farmerlistNo,
      farmerListName: props.listInfo.farmerlistName,
      templateId: currentFile.value?.templateId, // 待定
    },
  });
  if (get(res, 'code') === SUCCESS_CODE) {
    visible.value = false;
  } else {
    message.error(get(res, 'msg', ''));
  }
  submitLoading.value = false;
};

// 删除附件
const removeFile = async () => {
  await $getOnClient(`/gateway${service.farmer}/templateInfo/deleteFile`, { fileKey: currentFile.value?.fileKey || '' });
  currentFile.value = undefined;
};

const handleCancel = () => {
  console.log('cancel');
};

// 当前上传清单附件
const currentFile = ref<{
  farmerListName: string;
  fileKey: string;
  fileName: string;
  fileSize: string;
  templateId: string;
}>();

const uploading = ref(false);
const handleFile = (file: File) => {
  uploading.value = true;
  const formData = new FormData();
  formData.append('file', file);
  formData.append('farmerListNo', props.listInfo.farmerlistNo as Blob);
  $postOnClient(`/gateway${service.farmer}/templateInfo/incrementUploadFarmerListFile`, formData).then((res) => {
    if (res && res.code === SUCCESS_CODE) {
      const data = res.data;
      if (get(data, 'uploadResult')) {
        set(currentFile, 'value', res.data);
      } else {
        message.error(get(data, 'errorMsg', ''));
      }
    } else {
      message.error(res?.msg as string);
    }
  }).finally(() => {
    uploading.value = false;
  });
  return Promise.reject();
};
</script>
