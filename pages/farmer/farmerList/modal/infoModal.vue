<template>
  <a-modal v-model:open="visible" title="清单信息" :width="pxToRem(800)" centered @cancel="handleCancel">
    <div class="mb-[18px]">
      <div class="flex items-center mb-[9px] base-info">
        <VueIcon :icon="IconJichuxinxiFont" />
        <span class="ml-[3px]">基础信息</span>
      </div>
      <div class="bg-gray-100 rounded p-14px box-border">
        <div class="flex mb-[16px]">
          <p class="m-0 w-1/2 shrink-0 box-border">
            <span>清单编号：</span>
            <span class="text-[#262626]">{{ item.farmerlistNo }}</span>
          </p>
          <p class="flex m-0 shrink-0 w-1/2 box-border">
            <span class="text-nowrap">清单名称：</span>
            <span class="text-[#262626]">{{ item.farmerlistName }}</span>
          </p>
        </div>
        <div class="flex mb-[16px]">
          <p class="m-0 w-1/2 shrink-0 box-border">
            <span>机构：</span>
            <span class="text-[#262626]">{{ item.departmentName }}</span>
          </p>
          <p class="m-0 w-1/2 shrink-0 box-border">
            <span>投保单号：</span>
            <span class="text-[#262626]">{{ item.applyPolicyNo }}</span>
          </p>
        </div>
        <div class="flex mb-[16px]">
          <p class="flex m-0 shrink-0 box-border">
            <span class="text-nowrap">产品：</span>
            <span class="text-[#262626]">{{ item.productName }}</span>
          </p>
        </div>
        <div class="flex mb-[16px]">
          <p class="m-0 w-1/2 shrink-0 box-border">
            <span>清单状态：</span>
            <span class="text-[#262626]">{{ item.farmerlistStatusDesc }}</span>
          </p>
          <p class="flex m-0 shrink-0 w-1/2 box-border">
            <span class="text-nowrap">投保区域：</span>
            <span class="text-[#262626]">{{ item.address }}</span>
          </p>
        </div>
        <div class="flex mb-[16px]">
          <p class="m-0 w-1/2 shrink-0 box-border">
            <span>操作人员类型：</span>
            <span class="text-[#262626]">{{ item.oprRoleName }}</span>
          </p>
          <p class="m-0 shrink-0 w-1/2 box-border">
            <span>操作人员：</span>
            <span class="text-[#262626]">{{ item.createdBy }}</span>
          </p>
        </div>
        <div class="flex mb-[16px]">
          <p class="m-0 w-1/2 shrink-0 box-border">
            <span>导入条数：</span>
            <span class="text-[#262626]">{{ item.importCount }}</span>
          </p>
          <p class="m-0 shrink-0 w-1/2 box-border">
            <span>导入日期：</span>
            <span class="text-[#262626]">{{ item.importDate }}</span>
          </p>
        </div>
        <div class="flex mb-[16px]">
          <p class="m-0 w-1/2 shrink-0 box-border">
            <span>错误条数：</span>
            <span v-if="item.errorCount === 0 || item.errorCount === '0'" class="text-[#262626]">{{ item.errorCount }}</span>
            <span v-else class="text-[#DB3939] cursor-pointer" @click="redirectToError">{{ item.errorCount }}</span>
          </p>

          <p class="m-0 shrink-0 w-1/2 box-border">
            <span>风险条数：</span>
            <span v-if="item.riskCount === 0 || item.riskCount === '0'" class="text-[#262626]">{{ item.riskCount }}</span>
            <span v-else class="text-[#E6AD1C] cursor-pointer" @click="redirectToRisk">{{ item.riskCount }}</span>
          </p>
        </div>
        <div class="m-0 w-1/1 shrink-0 box-border">
          <span>错误日志：</span>
          <span v-if="item.errorruleContents" class="text-[#262626]">{{ item.errorruleContents }}</span>
        </div>
      </div>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleCancel">返回</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { IconJichuxinxiFont } from '@pafe/icons-icore-agr-an';
import { pxToRem } from '@/utils/tools';

const visible = defineModel<boolean>('visible', { required: true, default: false });
const router = useRouter();
const props = defineProps<{ item: { [key: string]: unknown } }>();

const handleCancel = () => {
  visible.value = false;
};

const redirectToRisk = () => {
  visible.value = false;
  router.push({
    path: '/dangerFarmerList',
    query: {
      id: (props.item.farmerlistNo || '') as string,
      dept: props.item.departmentCode as string,
    },
  });
};

const redirectToError = () => {
  visible.value = false;
  router.push({
    path: '/errorFarmerList',
    query: {
      id: (props.item.farmerlistNo || '') as string,
      dept: props.item.departmentCode as string,
    },
  });
};
</script>

<style lang="less">
.base-info {
  color: rgba(0,0,0,0.9);
}
</style>
