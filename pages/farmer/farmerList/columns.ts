import type { ColumnsType } from 'ant-design-vue/es/table';
import { pxToRem } from '@/utils/tools';

export const columns: ColumnsType<Record<string, string>> = [
  { title: '投保分户清单编号', dataIndex: 'farmerlistNo', fixed: 'left' },
  { title: '清单名称', dataIndex: 'farmerlistName' },
  { title: '承保数量', align: 'center', dataIndex: 'insuredNumber', width: pxToRem(80) },
  { title: '承保户数', align: 'center', dataIndex: 'farmersCount', width: pxToRem(80) },
  { title: '错误条数', align: 'center', dataIndex: 'errorCount', width: pxToRem(110) },
  { title: '风险条数', align: 'center', dataIndex: 'riskCount', width: pxToRem(110) },
  { title: '导入条数', align: 'center', dataIndex: 'importCount', width: pxToRem(110) },
  { title: '投保区域', dataIndex: 'addressName' },
  { title: '清单状态', dataIndex: 'farmerlistStatusDesc', width: pxToRem(100) },
  { title: '产品名称', dataIndex: 'productName' },
  { title: '操作人员', dataIndex: 'createdBy' },
  { title: '更新时间', dataIndex: 'updatedDate', sorter: (a: Record<string, string>, b: Record<string, string>) => (new Date(a.updatedDate).getTime()) - (new Date(b.updatedDate).getTime()) },
  { title: '操作', fixed: 'right', dataIndex: 'operation' },
];
