<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="searchForm" :colon="false" class="flex-grow" :model="searchFormState" :label-col="{ style: { width: '80px' } }">
          <a-row :gutter="0">
            <a-col>
              <a-form-item label="机构" name="insureDepartmentNo" :rules="[{ required: true, trigger: 'change', message: '请选择机构' }]">
                <department-search v-model:contain-child-depart="searchFormState.containChildDepart" :dept-code="searchFormState.insureDepartmentNo" :show-child-depart="true" @change-dept-code="(val: string) => changeDeptCode(val)" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="单号" name="voucherNo">
                <a-input-group>
                  <a-row :gutter="8">
                    <a-col :span="5">
                      <a-select v-model:value="searchFormState.voucherType" :options="voucherTypeOpitons" :style="{ width: '100%' }" allow-clear placeholder="请选择" />
                    </a-col>
                    <a-col :span="19">
                      <a-input v-model:value.trim="searchFormState.voucherNo" placeholder="请输入" @blur="blurInput" />
                    </a-col>
                  </a-row>
                </a-input-group>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="16">
            <a-col :span="24">
              <a-form-item label="投保区域" name="riskObjectCompleteAddress">
                <div class="flex gap-x-8px">
                  <a-form-item-rest>
                    <RegionSelect v-model:province="address.province" v-model:city="address.city" v-model:county="address.county" v-model:town="address.town" v-model:village="address.village" :disabled="disabled" style="width: 65%" @change-selected="regionChange" />
                  </a-form-item-rest>
                  <a-input v-model:value="searchFormState.riskObjectCompleteAddress" :disabled="disabled" placeholder="请输入" style="width: 35%" />
                </div>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="16">
            <a-col :span="24">
              <a-form-item label="标的类型" name="agriculturalRiskObjectDetailCode">
                <RiskCodeSelect v-model:value="searchFormState.agriculturalRiskObjectDetailCode" :disabled="disabled" :department-code="searchFormState.insureDepartmentNo" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="16">
            <a-col :span="8">
              <a-form-item label="产品名称" name="productVersionNo">
                <ProductSelect v-model:value="searchFormState.productVersionNo" :disabled="disabled" :department-code="searchFormState.insureDepartmentNo" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="保险起期" name="insureDate">
                <a-range-picker v-model:value="searchFormState.insureDate" :disabled="disabled" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="公示起止期" name="publicDate">
                <a-range-picker v-model:value="searchFormState.publicDate" :disabled="disabled" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="公示状态" name="publicityStatusCode" required>
                <check-box-group v-model:checked-list="searchFormState.publicityStatusCode" :disabled="disabled" :options="publicStatusOptions" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="公示方式" name="applyPublicityMethodCode" required>
                <check-box-group v-model:checked-list="searchFormState.applyPublicityMethodCode" :disabled="disabled" :options="publicStyleOptions" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="24">
              <a-form-item label="异议状态" name="complaintStatusCode">
                <check-box-group v-model:checked-list="searchFormState.complaintStatusCode" :disabled="disabled" :options="complaintStatusOptions" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="w-[50px]">
          <form-fold v-model="expand" />
        </div>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button type="default" @click="reset">重置</a-button>
        <a-button type="primary" @click="search">查询</a-button>
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="flex justify-between align-center pb-16px">
        <div class="leading-[32px] text-[16px] text-[rgba(0,0,0,0.9)] font-bold">查询结果</div>
      </div>
      <a-table :columns="listColumns" :pagination="pagination" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content' }" class="table-box">
        <template #headerCell="{ column }">
          <template v-if="column.dataIndex === 'applyPolicyNo'">
            <div>
              <span class="mr-[4px]">单证号</span>
              <a-tooltip>
                <template #title>
                  <!-- <div class="mb-[2px]">
                    <a-tag color="red">保</a-tag>
                    <span>保单号</span>
                  </div> -->
                  <div class="mb-[2px]">
                    <a-tag color="orange" class="type-tag">投</a-tag>
                    <span>投保单号</span>
                  </div>
                </template>
                <VueIcon :icon="IconInfoCircleFilledFont" />
              </a-tooltip>
            </div>
          </template>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'applyPolicyNo'">
            <div class="flex items-center">
              <a-tag color="orange" class="type-tag">投</a-tag>
              <!-- <a-tag v-if="record.documentTypeCode === '2'" color="red">保</a-tag> -->
              <CopyLink :text="text" @click="openDetail(text)" />
            </div>
          </template>
          <template v-if="column.key === 'status' && text">
            <div v-if="text !== '4'">
              <a-tag :bordered="false" :color="tagMap[text].color">
                <template #icon>
                  <VueIcon :icon="tagMap[text].icon" />
                </template>
                {{ tagMap[text].text }}
              </a-tag>
              <!-- 中保信公示异议数量 -->
              <a-tooltip v-if="column.dataIndex === 'zbxPublicityStatusCode' && record.zbxPublicityFeedbackFlag === '1'">
                <template #title>存在{{ record.zbxPublicityFeedbackCount }}项异议反馈，请及时处理</template>
                <span class="text-[red]">({{ record.zbxPublicityFeedbackCount }})</span>
              </a-tooltip>
              <!-- 地方平台公示异议数量 -->
              <a-tooltip v-if="column.dataIndex === 'localPfPublicityStatusCode' && record.localPfPublicityFeedbackFlag === '1'">
                <template #title>存在{{ record.localPfPublicityFeedbackCount }}项异议反馈，请及时处理</template>
                <span class="text-[red]">({{ record.localPfPublicityFeedbackCount }})</span>
              </a-tooltip>
              <!-- 爱农宝线上公示异议数量 -->
              <a-tooltip v-if="column.dataIndex === 'paOnlinePublicityStatusCode' && record.paOnlinePublicityFeedbackFlag === '1'">
                <template #title>存在{{ record.paOnlinePublicityFeedbackCount }}项异议反馈，请及时处理</template>
                <span class="text-[red]">({{ record.paOnlinePublicityFeedbackCount }})</span>
              </a-tooltip>
              <!-- 我司线下公示异议数量 -->
              <a-tooltip v-if="column.dataIndex === 'paOfflinePublicityStatusCode' && record.paOfflinePublicityFeedbackFlag === '1'">
                <template #title>存在{{ record.paOfflinePublicityFeedbackCount }}项异议反馈，请及时处理</template>
                <span class="text-[red]">({{ record.paOfflinePublicityFeedbackCount }})</span>
              </a-tooltip>
            </div>
            <div v-else></div>
          </template>
          <template v-if="column.key === 'feedbackFlag'">
            {{ text === '1' ? '有异议' : '无异议' }}
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <a-space :size="1">
              <a-button type="link" :disabled="btnDisabled(record)" @click="showModal(record.applyPolicyNo, record.documentGroupId)">查看</a-button>
              <a-button type="link" :disabled="btnDisabled(record)" @click="downloadPdf(record.documentGroupId, record.applyPolicyNo)">下载</a-button>
              <a-button type="link" :disabled="btnDisabled(record)" @click="showPhotoModal(record)">公示照片</a-button>
              <a-button :disabled="record.publicityExistFeedbackAllCount === 0" type="link" @click="handleObjections(record.applyPolicyNo, record.departmentCode)">异议处理</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
    <!-- 查看弹窗 -->
    <a-modal v-model:open="open" :title="`投保单号：${applyPolicyNo}`" :width="pxToRem(1000)">
      <div class="bg-white rounded p-16px">
        <a-table :columns="modalColumns" :pagination="modalPagination" :data-source="modalDataSource" :loading="loading" :scroll="{ x: 'max-content' }">
          <template #bodyCell="{ column, index }">
            <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
          </template>
        </a-table>
      </div>
      <template #footer>
        <a-button key="back" @click="open = false">取消</a-button>
        <a-button key="submit" type="primary" @click="downloadPdf(documentGroupId, applyPolicyNo)">下载</a-button>
      </template>
    </a-modal>
    <!-- 公示照片弹窗 -->
    <a-modal v-model:open="photoOpen" title="公示照片结果" :width="pxToRem(920)" :confirm-loading="confirmLoading" @ok="handleOk" @cancel="handleCancel">
      <div v-if="isShow">
        <a-form ref="modalRef" :colon="false" class="flex-grow" label-align="left" :model="modalState" :label-col="{ style: { width: '70px' } }">
          <div class="m-0 w-1/3 shrink-0 box-border inline-block">
            <a-form-item label="公示起期" name="beginDate" :rules="[{ required: true, message: '请选择公示起期' }]">
              <a-date-picker v-model:value="modalState.beginDate" placeholder="选择起期" format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }" allow-clear />
            </a-form-item>
          </div>
          <div class="m-0 w-1/3 shrink-0 box-border inline-block ml-[24px]">
            <a-form-item label="公示止期" name="endDate" :rules="[{ required: true, message: '请选择公示止期' }]">
              <a-date-picker v-model:value="modalState.endDate" placeholder="选择止期" :disabled-date="disabledDate" format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: dayjs('23:59:00', 'HH:mm:ss') }" allow-clear />
            </a-form-item>
          </div>
        </a-form>
        <div class="flex flex-wrap">
          <a-upload :multiple="true" :before-upload="handleFileChange" :show-upload-list="false">
            <div class="file-box flex justify-center items-center gap-x-[2px] text-[#606060]">
              <loading-outlined v-if="uploading" />
              <div v-else>
                <VueIcon :icon="IconUploadFont" />
                上传文件
              </div>
            </div>
          </a-upload>
          <div v-for="file in currentFile" :key="file.uploadPath" class="file-content mb-[8px]">
            <div class="h-full text-center flex flex-col justify-center items-center relative">
              <div class="absolute right-[-7px] top-[2px]" @click="handleDelete(file.uploadPath)">
                <VueIcon :icon="IconDelFont" />
              </div>
              <img v-if="isIcon(file.documentName)" class="h-[80px] w-[70px]" :src="file.thumbnail" @click="handleClickFile(file)" />
              <div v-else class="text-[50px]" @click="handleClickFile(file)">
                <VueIcon :icon="IcPdfColor" />
              </div>
              <div class="text-xs text-[#606060] mt-[8px] truncate max-w-[100px]" @click="handleClickFile(file)">{{ file.documentName }}</div>
            </div>
          </div>
        </div>
        <div class="pt-[16px]">1.格式说明：图片附件的格式为：BMP、DIB、JPG、JPEG、JFIF、GIF、TIF、PNG，文档附件的格式为PDF</div>
        <div>2.张数说明：至少三张</div>
        <a-divider />
        <ImageViewer v-if="currentFile?.length > 0" ref="viewRef" :list="currentFile" :current="viewCurrent" />
      </div>
      <div class="flex flex-wrap">
        <div v-for="(item, index) in publicityImages" :key="index" class="file-content mb-[8px]">
          <div class="h-full text-center flex flex-col justify-center items-center relative">
            <div class="absolute right-[-7px] top-[2px]" @click="handleImgDelete(item.documentId)">
              <VueIcon :icon="IconDelFont" />
            </div>
            <img v-if="isIcon(item.documentName)" class="h-[80px] w-[70px]" :src="item.fileUrl" @click="preview(index)" />
            <div v-else class="text-[50px]" @click="preview(index)">
              <VueIcon :icon="IcPdfColor" />
            </div>
            <div class="text-xs text-[#606060] mt-[8px] truncate max-w-[100px]" @click="preview(index)">{{ item.documentName }}</div>
          </div>
        </div>
      </div>
      <ImageViewer v-if="publicityImages.length > 0" ref="imgViewer" :list="publicityImages" :current="previewCurrent" />
      <a-empty v-else :image="simpleImage" />
    </a-modal>
    <a-modal v-model:open="allDeleteModal" title="提醒" @ok="handleDeleteFile">
      <div>确认删除？</div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { IconInfoCircleFilledFont, IconCheckCircleFilledFont, IconErrorCircleFilledFont, IconUploadFont, IcPdfColor, IconDelFont } from '@pafe/icons-icore-agr-an';
import type { TableColumnsType } from 'ant-design-vue';
import { Empty } from 'ant-design-vue';
import type { IconDefinition } from '@pafe/icons-icore-agr-an/lib/types';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import type { SearchFormState, AddressType, DataType, SelectOptions, UploadFile } from '../farmer.d';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost, useGet, $getOnClient, $postOnClient } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import { pxToRem, downloadFile } from '@/utils/tools';
import ImageViewer from '@/components/ui/ImageViewer.vue';
import CopyLink from '@/components/ui/CopyLink.vue';
import FormFold from '@/components/ui/FormFold.vue';

const expand = ref(false);
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const tagMap: Record<string, { color: string; text: string; icon: IconDefinition }> = {
  0: { color: 'warning', text: '待公示', icon: IconErrorCircleFilledFont },
  1: { color: 'processing', text: '公示中', icon: IconErrorCircleFilledFont },
  2: { color: 'success', text: '公示完成', icon: IconCheckCircleFilledFont },
  3: { color: 'default', text: '无需公示', icon: IconErrorCircleFilledFont },
  5: { color: 'orange', text: '需重新公示', icon: IconErrorCircleFilledFont },
};

const listColumns: TableColumnsType = [
  { title: '出单机构', dataIndex: 'departmentName' },
  { title: '投保单号', dataIndex: 'applyPolicyNo' },
  { title: '投保区域', dataIndex: 'riskObjectCompleteAddress' },
  { title: '保险起期', dataIndex: 'insuranceBeginDate' },
  { title: '公示起止期', dataIndex: 'publicityStartEndTime' },
  { title: '中保信公示状态', dataIndex: 'zbxPublicityStatusCode', key: 'status' },
  { title: '地方平台公示状态', dataIndex: 'localPfPublicityStatusCode', key: 'status' },
  { title: '爱农宝线上公示状态', dataIndex: 'paOnlinePublicityStatusCode', key: 'status' },
  { title: '我司线下公示状态', dataIndex: 'paOfflinePublicityStatusCode', key: 'status' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];
const modalColumns: TableColumnsType = [
  { title: '序号', dataIndex: 'index' },
  { title: '被保险人姓名', dataIndex: 'farmerName' },
  { title: '身份证号', dataIndex: 'certificateNo' },
  { title: '银行账号', dataIndex: 'accountNo' },
  { title: '联系方式', dataIndex: 'mobileTelephone' },
  { title: '投保数量', dataIndex: 'insuredNumber' },
  { title: '总保费（元）', dataIndex: 'totalActualPremium' },
  { title: '自缴保费（元）', dataIndex: 'farmerPremium' },
  { title: '种植地点', dataIndex: 'riskObjectCompleteAddress' },
];
// 公示状态选项
const publicStatusOptions = [
  { label: '待公示', value: '0' },
  { label: '公示中', value: '1' },
  { label: '公示完成', value: '2' },
  { label: '无需公示', value: '3' },
  { label: '需重新公示', value: '5' },
];
// 公示方式选项
const publicStyleOptions = [
  { label: '中保信公示', value: 'ZBX' },
  { label: '地方平台', value: 'LOCAL_PF' },
  { label: '爱农宝线上公示', value: 'PA_ONLINE' },
  { label: '我司线下公示', value: 'PA_OFFLINE' },
];
// 异议状态选项
const complaintStatusOptions = ref<SelectOptions[]>([]);
// 业务号类型选项
const voucherTypeOpitons = ref<SelectOptions[]>([]);
// 保单号
const applyPolicyNo = ref('');
const documentGroupId = ref('');
const router = useRouter();
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
const searchForm = ref();
// 地址初始值
const addressOrigin = () => {
  return {
    province: undefined, // 省
    provinceName: '', // 省
    city: undefined, // 市
    cityName: '', // 市
    county: undefined, // 区
    countyName: '', // 区
    town: undefined, // 县
    townName: '', // 县
    village: undefined, // 村
    villageName: '', // 村
  };
};
const address = ref<AddressType>(addressOrigin());
const searchFormState = reactive<SearchFormState>({
  insureDepartmentNo: defaultDeptCode.value, // 机构
  containChildDepart: true, // 是否包含下级
  riskObjectCompleteAddress: '', // 地址
  agriculturalRiskObjectDetailCode: '', // 标的类型
  productVersionNo: '', // 产品名称
  insureDate: ['', ''], // 保险起止期
  publicDate: ['', ''], // 公示起止期
  publicityStatusCode: ['0', '1', '2', '3', '5'], // 公示状态
  applyPublicityMethodCode: ['ZBX', 'LOCAL_PF', 'PA_ONLINE', 'PA_OFFLINE'], // 公示方式
  complaintStatusCode: [], // 异议状态
  // publicResult: [], // 公示结果
  voucherType: [], // 业务类型
  voucherNo: '', // 业务号
});
const { gateWay, service } = useRuntimeConfig().public || {};
// 改变机构
const changeDeptCode = (val: string) => {
  searchFormState.insureDepartmentNo = val;
};
// 公示列表数据
const dataSource = ref<Record<string, string>[]>([]);
// 查看弹窗列表数据
const modalDataSource = ref<Record<string, string>[]>([]);
const loading = ref(false);
// 查询
const search = async () => {
  try {
    await searchForm.value.validate();
    pagination.current = 1;
    pagination.pageSize = 10;
    refresh();
  } catch (e) {
    console.log(e);
  }
};
// 公示清单分页查询
const getListReq = await usePost<DataType>(`${gateWay}${service.accept}/publicity/pagePublicityFarmerList`);
const refresh = async () => {
  loading.value = true;
  try {
    const params = {
      insureDepartmentNo: searchFormState.insureDepartmentNo,
      containChildDepart: searchFormState.containChildDepart ? '1' : '0',
      riskObjectCompleteAddress: searchFormState.riskObjectCompleteAddress,
      agriculturalRiskObjectDetailCode: searchFormState.agriculturalRiskObjectDetailCode,
      productVersionNo: searchFormState.productVersionNo,
      publicityStatusCode: searchFormState.publicityStatusCode.length > 0 ? searchFormState.publicityStatusCode.join(',') : '',
      applyPublicityMethodCode: searchFormState.applyPublicityMethodCode.length > 0 ? searchFormState.applyPublicityMethodCode.join(',') : '',
      complaintStatusCode: searchFormState.complaintStatusCode.length > 0 ? searchFormState.complaintStatusCode.join(',') : '',
      insuranceBeginDate: searchFormState.insureDate?.[0] || '',
      insuranceEndDate: searchFormState.insureDate?.[1] || '',
      publicityStartTime: searchFormState.publicDate?.[0] || '',
      publicityEndTime: searchFormState.publicDate?.[1] || '',
      voucherType: searchFormState.voucherType.length > 0 ? searchFormState.voucherType[0] : '',
      voucherNo: searchFormState.voucherNo,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    const res = await getListReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res?.data || {};
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      message.error(res?.msg || '');
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const { pagination } = usePagination(refresh);
// 重置
const reset = () => {
  searchForm.value.resetFields();
  address.value = addressOrigin();
  disabled.value = false;
  refresh();
};
// 地址拼接
const regionChange = (selected: { label: string }, type: string) => {
  if (['province', 'city', 'county', 'town', 'village'].includes(type)) {
    address.value[`${type}Name` as 'provinceName' | 'cityName' | 'countyName' | 'townName' | 'village'] = selected?.label || '';
  }
  searchFormState.riskObjectCompleteAddress = address.value.provinceName + address.value.cityName + address.value.countyName + address.value.townName + address.value.villageName;
};
// 查看弹窗表格查询
const getModalListReq = await useGet<DataType>(`${gateWay}${service.accept}/publicity/pagePublicityFarmerCustList`);
const getModalList = async () => {
  loading.value = true;
  try {
    const params = {
      applyPolicyNo: applyPolicyNo.value,
      pageNum: modalPagination.current,
      pageSize: modalPagination.pageSize,
    };
    const res = await getModalListReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data;
      modalDataSource.value = records || [];
      modalPagination.total = total;
      modalPagination.current = current;
      modalPagination.pageSize = size;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const { pagination: modalPagination } = usePagination(getModalList);

// 查看弹窗打开标识
const open = ref<boolean>(false);
// 打开查看弹窗
const showModal = (policyNo: string, id: string) => {
  modalPagination.current = 1;
  modalPagination.pageSize = 10;
  open.value = true;
  applyPolicyNo.value = policyNo;
  documentGroupId.value = id;
  getModalList();
};
// 公示照片弹窗打开标识
const photoOpen = ref<boolean>(false);
// 公示照片
const publicityImages = ref<Record<string, string>[]>([]);
// 获取公示照片
const getImageListReq = await usePost<Record<string, string>[]>(`${gateWay}${service.administrate}/iobs/getScFileListShowByGroupId`);
const getImage = async (id: string) => {
  loading.value = true;
  try {
    const params = {
      documentGroupId: id,
      documentTypeList: ['A04'],
    };
    const res = await getImageListReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      publicityImages.value = (res.data || [])?.map((item) => {
        return {
          ...item,
          thumbnail: item.fileUrl,
          uploadPath: item.fileKey,
        };
      });
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
// 下载公示清单
const getPdfReq = await usePost<string>(`${gateWay}${service.accept}/publicity/downloadPublicityFile`);
const downloadPdf = async (id: string, applyPolicyNo: string) => {
  loading.value = true;
  try {
    const params = {
      documentGroupId: id,
      documentType: 'E12',
      downloadFlag: true,
      applyPolicyNo,
    };
    const res = await getPdfReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE && res?.data) {
      downloadFile(res.data);
    } else {
      message.error(res?.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const paOfflinePublicityStatusCode = ref<string>(''); // 公示状态1 公示中， 2 公示结束； 0 未公示
const applyStatus = ref<string>(''); // 投保状态
// 打开公示照片查看弹窗
const showPhotoModal = (record: Record<string, string>) => {
  getImage(record.documentGroupId);
  photoOpen.value = true;
  applyPolicyNo.value = record.applyPolicyNo;
  paOfflinePublicityStatusCode.value = record?.paOfflinePublicityStatusCode || '';
  applyStatus.value = record.applyStatus;
  documentGroupId.value = record.documentGroupId;
  if (record?.offlineBeginDate && record?.offlineEndDate) {
    // 公示时间赋值
    modalState.value.beginDate = dayjs(record.offlineBeginDate) as unknown as string;
    modalState.value.endDate = dayjs(record.offlineEndDate) as unknown as string;
  } else {
    modalState.value.beginDate = '';
    modalState.value.endDate = '';
  }
};
// 打开投保单详情页
const openDetail = (applyPolicyNo: string) => {
  const path = {
    path: '/applyPolicyDetails',
    query: {
      applyPolicyNo: applyPolicyNo,
    },
  };
  router.push(path);
};
// 公示照片预览
const imgViewer = ref();
const previewCurrent = ref(0); // 当前要预览的文件id
const preview = (id: number) => {
  previewCurrent.value = id;
  if (imgViewer.value) {
    imgViewer.value?.openPreview();
  }
};
// 获取选项
const getOptionsReq = await usePost<SelectOptions[]>(`${gateWay}${service.administrate}/public/getSelectListForLevel`);
const initOption = async () => {
  try {
    const params = ['policyQueryNoType'];
    const res = await getOptionsReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      voucherTypeOpitons.value = res?.data?.[0]?.children || [];
    }
  } catch (error) {
    console.log(error);
  }
};
const disabled = ref<boolean>(false);
// 单号输入后调接口获取单号类型和机构,禁用其他组件
const blurInput = async (e: FocusEvent) => {
  const target = e.target as HTMLInputElement;
  if (target.value) {
    disabled.value = true;
    const fetchUrl = gateWay + service.administrate + '/public/getBizTypeByBizNo';
    const params = {
      bizNo: target.value,
    };
    const res = await $getOnClient<Record<string, string>>(fetchUrl, params);
    if (res && res?.code === SUCCESS_CODE && res?.data) {
      searchFormState.voucherType = res.data?.bizType ? [res.data?.bizType] : [];
      searchFormState.insureDepartmentNo = res.data?.insureDepartmentNo || '';
      searchFormState.complaintStatusCode = [];
      complaintStatusOptions.value.forEach((item) => {
        searchFormState.complaintStatusCode.push(item.value);
      });
    }
  } else {
    disabled.value = false;
    searchFormState.voucherType = [];
    searchFormState.insureDepartmentNo = defaultDeptCode.value;
  }
};
// 如果公示状态是待公示，无需公示，则置灰，否则不置灰可以继续点击 公示状态1 公示中， 2 公示结束； 0 未公示 3.无需公示, 四个中一个是1，2状态，就可以点击
const btnDisabled = (record: Record<string, string>) => !(['1', '2'].includes(record?.paOfflinePublicityStatusCode) || ['1', '2'].includes(record?.zbxPublicityStatusCode) || ['1', '2'].includes(record?.paOnlinePublicityStatusCode) || ['1', '2'].includes(record?.localPfPublicityStatusCode));
// 公示日期
const modalState = ref({
  beginDate: '',
  endDate: '',
});
// 上传后图标显示
const isIcon = (fileName: string) => {
  const fileExtension = fileName.slice(((fileName.lastIndexOf('.') - 1) >>> 0) + 2).toLocaleUpperCase();
  if (fileExtension === 'PDF') {
    return false;
  }
  return true;
};
const currentFile = ref<UploadFile[]>([]);
// 上传文件
const uploading = ref<boolean>(false);
const acceptType = ['BMP', 'DIB', 'JPG', 'JPEG', 'JFIF', 'GIF', 'TIF', 'PNG', 'PDF'];
const handleFileChange = async (file: File & { uid: string }) => {
  // 文件类型控制
  const fileExtension = file.name.slice(((file.name.lastIndexOf('.') - 1) >>> 0) + 2).toLocaleUpperCase();
  if (acceptType.indexOf(fileExtension) === -1) {
    message.warning(`不能上传${fileExtension}该类型的文件，请重新上传`);
    return false;
  }
  // 文件大小控制
  const MAX_SIZE = 10 * 1024 * 1024; // 4MB
  if (file.size > MAX_SIZE) {
    message.warning('文件大小不能超过10MB');
    return false;
  }
  uploading.value = true;
  try {
    const fetchUrl = gateWay + service.administrate + '/iobs/uploadFileByIOBS';
    const formData = new FormData();
    formData.append('file', file);
    const res = await $postOnClient<UploadFile>(fetchUrl, formData);
    if (res?.code === SUCCESS_CODE) {
      message.success('上传成功');
      currentFile.value.push({ ...res?.data, documentType: 'A04' });
    } else {
      message.error('文件上传失败，请重试');
    }
    uploading.value = false;
  } catch (e) {
    console.log(e);
    uploading.value = false;
  }
};
// 如果点击图片，则预览，否则下载
const viewCurrent = ref(0);
const viewRef = ref();
const handleClickFile = async (file: UploadFile) => {
  if (isIcon(file.documentName)) {
    viewCurrent.value = currentFile.value.findIndex((k) => k.documentId === file.documentId);
    if (viewRef.value) {
      viewRef.value?.openPreview();
    }
  } else {
    const { data } =
      (await $postOnClient<{ fileUrl: string }>('/api/iobs/getInIobsUrl', {
        iobsBucketName: file.bucketName,
        fileKey: file.uploadPath,
        storageTypeCode: file.storageType,
      })) || {};
    if (data?.fileUrl) {
      window.open(data?.fileUrl);
    }
  }
};
// 查看照片弹窗关闭清空上传照片
const handleCancel = () => {
  currentFile.value = [];
};
const modalRef = ref();
const confirmLoading = ref<boolean>(false);
// 公示照片弹窗确定
const handleOk = async () => {
  if (!isShow.value) {
    photoOpen.value = false;
  } else {
    await modalRef.value.validate();
    confirmLoading.value = true;
    try {
      const res = await $postOnClient(gateWay + service.accept + '/apply/attachment/savePublicityFileList', {
        applyPolicyNo: applyPolicyNo.value,
        beginDate: modalState.value.beginDate && dayjs(modalState.value.beginDate).format('YYYY-MM-DD HH:mm:ss'),
        endDate: modalState.value.endDate && dayjs(modalState.value.endDate).format('YYYY-MM-DD HH:mm:ss'),
        fileList: currentFile.value,
      });
      if (res?.code === SUCCESS_CODE) {
        message.success('提交成功');
        photoOpen.value = false;
        refresh();
        currentFile.value = [];
        modalState.value.beginDate = '';
        modalState.value.endDate = '';
      } else {
        message.error(res?.msg || '');
      }
      confirmLoading.value = false;
    } catch (error) {
      console.log(error);
      confirmLoading.value = false;
    }
  }
};
// 1.起始时间与结束时间不能为同一天,2.公示天数不能大于100天
const disabledDate = (current: Dayjs) => current && (current.subtract(1, 'day').endOf('day').isBefore(modalState.value.beginDate) || current.subtract(100, 'day').endOf('day').isAfter(modalState.value.beginDate));
// 如果公示起期删除则止期也删除
watch(
  () => modalState.value.beginDate,
  (val) => {
    if (!val || dayjs(modalState.value.endDate).diff(dayjs(modalState.value.beginDate), 'day', true) < 0) {
      modalState.value.endDate = '';
    }
  },
);
// 点击图片删除-前端删除
const handleDelete = (uploadPath: string) => {
  currentFile.value = cloneDeep(currentFile.value).filter((item) => item.uploadPath !== uploadPath);
};
const allDeleteModal = ref<boolean>(false);
// 是否显示公示上传附件模块-包含我司线下，且状态是公示中，公示完成，且投保单状态是暂存，待申请，待修改
const isShow = computed(() => paOfflinePublicityStatusCode.value && ['1', '2'].includes(paOfflinePublicityStatusCode.value) && ['A0', 'B1', 'A2', 'B8', 'B2'].includes(applyStatus.value));
// 公示照片删除-后端删除
const documentId = ref<string>('');
const handleImgDelete = (id: string) => {
  allDeleteModal.value = true;
  documentId.value = id;
};
// 确定删除
const handleDeleteFile = async () => {
  try {
    const res = await $postOnClient(gateWay + service.accept + '/apply/attachment/deleteListByDocumentId', { documentId: documentId.value });
    if (res?.code === SUCCESS_CODE) {
      allDeleteModal.value = false;
      message.success('删除成功');
      getImage(documentGroupId.value);
    } else {
      message.error(res?.msg);
    }
  } catch (error) {
    console.log(error);
  }
};
// 异议处理
const handleObjections = (applyPolicyNo: string, departmentCode: string) => {
  const path = {
    path: '/objectionsHandling',
    query: {
      applyPolicyNo,
      departmentCode,
    },
  };

  router.push(path);
};
// 获取基础数据
const getBaseData = async () => {
  const params = ['complaintStatusCode'];
  const res = await $postOnClient<SelectOptions[]>(`${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`, params);
  if (res && res.code === SUCCESS_CODE) {
    complaintStatusOptions.value = res.data?.[0]?.children || [];
    searchFormState.complaintStatusCode = [];
    complaintStatusOptions.value.forEach((item) => {
      searchFormState.complaintStatusCode.push(item.value);
    });
  }
};

onMounted(async () => {
  initOption();
  await getBaseData();
  search();
});
</script>

<style lang="less" scoped>
.type-tag.ant-tag-orange {
  color: #d46b08;
  background-color: #fff7e6;
  border-color: #ffd591;
}
.ant-tag-orange {
  color: #ff5b00;
  background: #ffeee5;
}
.ant-tag-default {
  color: rgba(0, 0, 0, 0.4);
  background: #f2f3f5;
}
.file-box,
.file-content {
  cursor: pointer;
  border: 1px solid rgba(241, 241, 241, 1);
  border-radius: 4px;
  width: 130px;
  height: 130px;
  position: relative;
  background: #ffffff;
  padding-right: 12px;
}
.file-content {
  display: inline-block;
  margin-left: 12px;
  box-sizing: border-box;
}
</style>
