<template>
  <a-form ref="formRef" :colon="false" :model="form">
    <a-form-item label="处理方式" name="feedbackResultCode" :rules="{ required: true, message: '请选择处理方式' }">
      <a-radio-group v-model:value="form.feedbackResultCode" :disabled="publicityStatusCode === '2'">
        <a-radio v-for="item in feedbackResultData" :key="item.value" :value="item.value">{{ item.label }}</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item
      label="处理描述"
      name="problemSolveSuggest"
      :rules="[
        { required: true, message: '请输入处理描述' },
        { min: 15, message: '至少输入15个字' },
      ]"
    >
      <a-textarea v-model:value="form.problemSolveSuggest" :disabled="publicityStatusCode === '2'" :maxlength="1000" show-count style="height: 64px" placeholder="请输入" />
    </a-form-item>
  </a-form>
  <div class="flex justify-end">
    <a-button type="primary" :disabled="publicityStatusCode === '2'" @click="submit">确定</a-button>
  </div>
</template>

<script setup lang="ts">
import { SUCCESS_CODE } from '@/utils/constants';
import { $postOnClient } from '@/composables/request';
import type { SelectOptions, FormState } from '../objectionsHandling.d';
const emits = defineEmits(['refresh']);

const { gateWay, service } = useRuntimeConfig().public || {};
const { idAplyPublicityFeedback = '', applyPolicyNo = '', feedbackResultData = [], publicityStatusCode = '' } = defineProps<{ idAplyPublicityFeedback: string; applyPolicyNo: string; feedbackResultData: SelectOptions[]; publicityStatusCode: string }>();
const formRef = ref();
const form = ref<FormState>({
  feedbackResultCode: '1',
  problemSolveSuggest: '',
});
// 处理异议
const submit = async () => {
  await formRef.value.validate();
  const params = {
    ...form.value,
    applyPolicyNo,
    idAplyPublicityFeedback,
  };
  const res = await $postOnClient(`${gateWay}${service.accept}/publicity/handlePublicityFeedbackInfo`, params);
  if (res && res.code === SUCCESS_CODE) {
    form.value = {
      feedbackResultCode: '1',
      problemSolveSuggest: '',
    };
    message.success(res.msg);
    emits('refresh');
  } else {
    message.error(res?.msg || '失败');
  }
};
</script>

<style lang="less" scoped>
:deep(.ant-form-item .ant-form-item-label > label) {
  color: rgba(0, 0, 0, 0.6);
}
</style>
