export interface ObjectionsData {
  farmerlistNo: string; // 清单编号
  applyPolicyNo: string; // 投保单号
  riskPlanTypeCnName: string; // 险类
  productName: string; // 产品
  riskObjectCompleteAddress: string; // 投保区域
  insuranceBeginDate: string; // 保险起期
  insuranceEndDate: string; // 保险止期
  publicityStartTime: string; // 公示起期
  publicityEndTime: string; // 公示止期
  insuredNumber: string; // 保险数量
  insuredUnitCnName: string; // 保险数量单位
  premiumRate: string; // 费率
  totalInsuredAmount: string; // 总保额
  amountCurrencyUnitName: string; // 总保额单位
  totalActualPremium: string; // 总保费
  premiumCurrencyUnitName: string; // 总保费单位
  unFeedbackDetailList: FeedbackDetail[]; // 异议未处理列表
  didFeedbackDetailList: FeedbackDetail[]; // 异议已处理列表
  unFeedbackDetailCount: number; // 待处理数量
  didFeedbackDetailCount: number; // 已处理数量
}
export interface FeedbackDetail {
  farmerName: string; // 被保险人姓名
  certificateNo: string; // 身份证号
  mobileTelephone: string; // 联系方式
  accountNo: string; // 银行账号
  insuredNumber: string; // 投保数量
  insuredUnitCnName: string; // 投保数量单位
  totalActualPremium: string; // 总保费
  farmerPremium: string; // 自缴保费
  dataSourceCnName: string; // 异议来源
  customerFeedbackContent: string; // 异议内容
  expand?: boolean; // 是否展开
  problemSolveSuggest: string; // 处理描述
  feedbackResultCode: string; // 处理方式
  feedbackResultCnName: string; // 处理方式中文
  idAplyPublicityFeedback: string; // id
  applyPolicyNo: string; // 投保单号
  publicityStatusCode: string; // 公示状态
}
export interface SelectOptions {
  label: string;
  value: string;
  disabled?: boolean;
  children?: SelectOptions[];
  isLeaf?: boolean;
}
export interface FormState {
  feedbackResultCode: string;
  problemSolveSuggest: string;
}
