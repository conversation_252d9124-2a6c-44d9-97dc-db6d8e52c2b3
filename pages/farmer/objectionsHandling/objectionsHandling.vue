<template>
  <div ref="scrollWrapper" class="page-container">
    <main class="main-wrapper">
      <div class="flex-1 border-right-line">
        <div class="h-[46px] w-ful bg-image-url">
          <span class="text-[16px] font-[500] text-[#00190c] leading-[46px] ml-[24px]">异议处理</span>
        </div>
        <div class="bg-white h-[46px] flex">
          <div class="h-[46px] leading-[46px] ml-[24px] text-[rgba(0,0,0,0.60)]">
            分户清单编号：
            <span class="font-number">
              <a-button type="link" @click="goToFarmerlist(data.farmerlistNo)">{{ data.farmerlistNo }}</a-button>
            </span>
          </div>
          <div class="h-[46px] leading-[46px] ml-[24px] text-[rgba(0,0,0,0.60)]">
            投保单号：
            <span class="font-number">
              <a-button type="link" @click="goToApplyPolicyDetail(data.applyPolicyNo)">{{ data.applyPolicyNo }}</a-button>
            </span>
          </div>
        </div>
        <a-spin :spinning="loading">
          <div id="objections-handling-content" class="space-y-12px">
            <InfoGroupBox title="基础信息">
              <a-descriptions :column="4">
                <a-descriptions-item label="险类">{{ data.riskPlanTypeCnName }}</a-descriptions-item>
                <a-descriptions-item label="产品">{{ data.productName }}</a-descriptions-item>
                <a-descriptions-item label="投保区域">{{ data.riskObjectCompleteAddress }}</a-descriptions-item>
              </a-descriptions>
              <a-descriptions :column="4">
                <a-descriptions-item class="font-number" label="保险起期">{{ data.insuranceBeginDate }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="保险止期">{{ data.insuranceEndDate }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="公示起期">{{ data.publicityStartTime }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="公示止期">{{ data.publicityEndTime }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="保险数量">{{ data.insuredNumber }}{{ data.insuredUnitCnName }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="费率">{{ data.premiumRate }}%</a-descriptions-item>
                <a-descriptions-item class="font-number" label="总保额">{{ data.totalInsuredAmount }}{{ data.amountCurrencyUnitName }}</a-descriptions-item>
                <a-descriptions-item class="font-number" label="总保费">{{ data.totalActualPremium }}{{ data.premiumCurrencyUnitName }}</a-descriptions-item>
              </a-descriptions>
            </InfoGroupBox>
            <InfoGroupBox title="异议处理">
              <a-tabs v-model:active-key="activeKey" type="card">
                <a-tab-pane key="1" :tab="`待处理(${data.unFeedbackDetailCount})`">
                  <div v-if="data.unFeedbackDetailList?.length > 0">
                    <div v-for="(item, index) in data.unFeedbackDetailList" :key="index">
                      <div class="flex justify-between">
                        <div class="form-title">
                          <span>异议{{ index + 1 }}</span>
                        </div>
                        <div class="w-[50px]">
                          <form-fold v-model="item.expand" />
                        </div>
                      </div>
                      <div v-if="item.expand">
                        <div class="bg-[#F9F9F9] rounded-[4px] p-[14px] pb-[6px] mb-[14px]">
                          <a-descriptions :column="4">
                            <a-descriptions-item class="font-number" label="被保险人姓名">{{ item.farmerName }}</a-descriptions-item>
                            <a-descriptions-item class="font-number" label="身份证号">{{ item.certificateNo }}</a-descriptions-item>
                            <a-descriptions-item class="font-number" label="联系方式">{{ item.mobileTelephone }}</a-descriptions-item>
                            <a-descriptions-item class="font-number" label="银行账号">{{ item.accountNo }}</a-descriptions-item>
                            <a-descriptions-item class="font-number" label="投保数量">{{ item.insuredNumber }}{{ item.insuredUnitCnName }}</a-descriptions-item>
                            <a-descriptions-item class="font-number" label="总保费">{{ item.totalActualPremium }}元</a-descriptions-item>
                            <a-descriptions-item class="font-number" label="自缴保费">{{ item.farmerPremium }}元</a-descriptions-item>
                            <a-descriptions-item class="font-number" label="异议来源">{{ item.dataSourceCnName }}</a-descriptions-item>
                            <a-descriptions-item class="font-number" label="异议内容">{{ item.customerFeedbackContent }}</a-descriptions-item>
                          </a-descriptions>
                        </div>
                        <objections-handling-form :publicity-status-code="item.publicityStatusCode" :feedback-result-data="feedbackResultData" :id-aply-publicity-feedback="item.idAplyPublicityFeedback" :apply-policy-no="item.applyPolicyNo" @refresh="getInfo" />
                      </div>
                      <div v-if="index !== data.unFeedbackDetailList?.length - 1" class="h-px bg-[#E6E8EB] mb-[16px] mt-[14px]" />
                    </div>
                  </div>
                  <a-empty v-else :image="simpleImage" />
                </a-tab-pane>
                <a-tab-pane key="2" :tab="`已处理(${data.didFeedbackDetailCount})`">
                  <div v-if="data.didFeedbackDetailList?.length > 0">
                    <div v-for="(item, index) in data.didFeedbackDetailList" :key="index">
                      <div class="flex justify-between">
                        <div class="form-title">
                          <span>异议{{ index + 1 }}</span>
                        </div>
                        <div class="w-[50px]">
                          <form-fold v-model="item.expand" />
                        </div>
                      </div>
                      <div v-if="item.expand" class="bg-[#F9F9F9] rounded-[4px] p-[14px] pb-[6px]">
                        <a-descriptions :column="4">
                          <a-descriptions-item class="font-number" label="被保险人姓名">{{ item.farmerName }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="身份证号">{{ item.certificateNo }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="联系方式">{{ item.mobileTelephone }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="银行账号">{{ item.accountNo }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="投保数量">{{ item.insuredNumber }}{{ item.insuredUnitCnName }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="总保费">{{ item.totalActualPremium }}元</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="自缴保费">{{ item.farmerPremium }}元</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="异议来源">{{ item.dataSourceCnName }}</a-descriptions-item>
                        </a-descriptions>
                        <a-descriptions :column="1">
                          <a-descriptions-item class="font-number" label="异议内容">{{ item.customerFeedbackContent }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="处理方式">{{ item.feedbackResultCnName }}</a-descriptions-item>
                          <a-descriptions-item class="font-number" label="处理描述">{{ item.problemSolveSuggest }}</a-descriptions-item>
                        </a-descriptions>
                      </div>
                    </div>
                  </div>
                  <a-empty v-else :image="simpleImage" />
                </a-tab-pane>
              </a-tabs>
            </InfoGroupBox>
          </div>
        </a-spin>
      </div>
    </main>
    <!-- 操作按钮区域 -->
    <footer class="footer-wrapper space-x-8px">
      <a-button @click="cancel">返回</a-button>
      <a-button v-if="activeKey === '1'" type="primary" :loading="btnLoading" @click="farmerListExport">异议下载</a-button>
    </footer>
  </div>
</template>

<script setup lang="ts">
import objectionsHandlingForm from './components/objectionsHandlingForm.vue';
import InfoGroupBox from '@/components/ui/InfoGroupBox.vue';
import FormFold from '@/components/ui/FormFold.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { $getOnClient, $postOnClient } from '@/composables/request';
import type { ObjectionsData, SelectOptions } from './objectionsHandling.d';
import { downloadBlob } from '@/utils/tools';
import { Empty } from 'ant-design-vue';
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const { deletPageTabListItem, pageTabList } = inject('pageTab'); // 关闭页签

const btnLoading = ref<boolean>(false);
const scrollWrapper = ref();
const loading = ref(false);
const activeKey = ref('1');
const { gateWay, service } = useRuntimeConfig().public || {};
const router = useRouter();
const route = useRoute();
const dataInit = () => {
  return {
    farmerlistNo: '', // 清单编号
    applyPolicyNo: '', // 投保单号
    riskPlanTypeCnName: '', // 险类
    productName: '', // 产品
    riskObjectCompleteAddress: '', // 投保区域
    insuranceBeginDate: '', // 保险起期
    insuranceEndDate: '', // 保险止期
    publicityStartTime: '', // 公示起期
    publicityEndTime: '', // 公示止期
    insuredNumber: '', // 保险数量
    insuredUnitCnName: '', // 保险数量单位
    premiumRate: '', // 费率
    totalInsuredAmount: '', // 总保额
    amountCurrencyUnitName: '', // 总保额单位
    totalActualPremium: '', // 总保费
    premiumCurrencyUnitName: '', // 总保费单位
    unFeedbackDetailList: [], // 异议未处理列表
    didFeedbackDetailList: [], // 异议已处理列表
    unFeedbackDetailCount: 0, // 待处理数量
    didFeedbackDetailCount: 0, // 已处理数量
  };
};
const data = ref<ObjectionsData>(dataInit());
// 获取页面信息
const getInfo = async () => {
  const params = {
    applyPolicyNo: route.query.applyPolicyNo,
  };
  loading.value = true;
  const res = await $getOnClient<ObjectionsData>(`${gateWay}${service.accept}/publicity/getPublicityFeedbackSummaryInfo`, params);
  if (res && res.code === SUCCESS_CODE) {
    loading.value = false;
    data.value = res.data || {};
    data.value.didFeedbackDetailList = data.value.didFeedbackDetailList.map((item) => {
      return {
        ...item,
        expand: true,
      };
    });
    data.value.unFeedbackDetailList = data.value.unFeedbackDetailList.map((item) => {
      return {
        ...item,
        expand: true,
      };
    });
  } else {
    loading.value = false;
  }
};
// 下载异议
const farmerListExport = async () => {
  try {
    btnLoading.value = true;
    const fetchUrl = `${gateWay}${service.accept}/apply/attachment/file/excelCommonExport`;
    await $postOnClient(
      fetchUrl,
      { exportType: 'PUBLICITY_FEEDBACK_FARMER_LIST', applyPolicyNo: route.query.applyPolicyNo },
      {
        onResponse({ response }) {
          if (response._data instanceof Blob) {
            const contentDisposition = response.headers.get('Content-Disposition');
            const fileData = response._data;
            const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
            if (contentDisposition) {
              const match = contentDisposition.match(/filename=(.+)/);
              const fileName = match ? decodeURI(match[1]) : '';
              downloadBlob(fileData, fileName, fileType);
            }
            message.success('导出成功');
          } else {
            const { code, data, msg = '' } = response._data;
            if (code === SUCCESS_CODE) {
              message.success(data);
            } else if (msg) {
              message.error(msg);
            }
          }
        },
      },
    );
    btnLoading.value = false;
  } catch (error) {
    console.log(error);
    btnLoading.value = false;
  }
};
const feedbackResultData = ref<SelectOptions[]>([]);
// 获取基础数据
const getBaseData = async () => {
  const params = ['feedbackResultCode'];
  const res = await $postOnClient<SelectOptions[]>(`${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`, params);
  if (res && res.code === SUCCESS_CODE) {
    feedbackResultData.value = res.data?.[0]?.children || [];
  }
};
// 点取消按钮
const cancel = () => {
  deletPageTabListItem('/objectionsHandling'); // 关闭页签
  // 返回上一页或者首页
  if (pageTabList.value.length) {
    router.push(pageTabList.value?.[pageTabList.value.length - 1]?.fullPath);
  } else {
    router.push('/home');
  }
};
// 跳转投保单详情
const goToApplyPolicyDetail = (applyPolicyNo: string) => {
  const path = {
    path: '/applyPolicyDetails',
    query: {
      applyPolicyNo,
    },
  };
  router.push(path);
};
// 跳转分户清单
const goToFarmerlist = (farmerlistNo: string) => {
  const path = {
    path: '/editFarmerList',
    query: {
      id: farmerlistNo,
      viewType: '1',
      dept: route.query.departmentCode,
    },
  };
  router.push(path);
};
onMounted(() => {
  getBaseData();
});
onActivated(() => {
  getInfo();
});
</script>

<style lang="less" scoped>
.page-container {
  position: relative;
  height: calc(100vh - 40px);
  overflow: auto;

  .main-wrapper {
    padding: 14px;
    display: flex;
    .bg-image-url {
      background-image: url(/assets/images/content-head.png);
      background-size: cover;
    }
    .border-right-line {
      border-right: 1px solid #e6e8eb;
    }
    .border-bottom-line {
      border-bottom: 1px solid #e6e8eb;
      margin-bottom: 16px;
    }
    #objections-handling-content {
      :deep(.ant-descriptions-item) {
        padding-bottom: 8px;
        .ant-descriptions-item-label {
          color: rgba(0, 0, 0, 0.6);
        }
        .ant-descriptions-item-content {
          color: #333;
        }
      }
    }
  }
  .footer-wrapper {
    z-index: 100;
    height: 50px;
    background: #fff;
    position: sticky;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
  }
}
</style>
