// import type { Rule } from 'ant-design-vue/es/form';

// 业务员共展比例校验
// const validateEmployeeScale = (_rule: Rule, value: string) => {
//   const regex = /^[0-9]+(\.[0-9]{1,4})?$/;
//   const numVal = parseFloat(value);
//   const result = regex.test(value) && numVal > 0 && numVal < 100;
//   if (result) {
//     return Promise.resolve();
//   } else {
//     return Promise.reject("请输入0～100范围内的数字，且小数位不超过4位");
//   }
// };

// 风险程度校验
// const validateLossRate = (_rule: Rule, value: string) => {
//   const regex = /^[0-9]+(\.[0-9]{1,2})?$/;
//   const numVal = parseFloat(value);
//   const result = regex.test(value) && numVal >= 0 && numVal <= 100;
//   if (result) {
//     return Promise.resolve();
//   } else {
//     return Promise.reject("请输入0～100范围内的数字，且小数位不超过2位");
//   }
// };

// 疾病观察期校验
// const validateObservation = (_rule: Rule, value: string) => {
//   const regex = /^\d{1,3}$/;
//   const numVal = parseFloat(value);
//   const result = regex.test(value) && numVal >= 0 && numVal <= 100;
//   if (result) {
//     return Promise.resolve();
//   } else {
//     return Promise.reject("请输入0～100范围内的整数");
//   }
// };

// 承保数量校验
// const validateInsuredNumber = (_rule: Rule, value: string) => {
//   const regex = /^\d{0,9}(\.\d{1,2})?$/;
//   const numVal = parseFloat(value);
//   const result = regex.test(value) && numVal > 0;
//   if (result) {
//     return Promise.resolve();
//   } else {
//     return Promise.reject("请输入大于0的数字，整数位不超过9位，小数位不超过2位");
//   }
// };

// 承保户次校验
// const validateFarmersCount = (_rule: Rule, value: string) => {
//   const regex = /^[1-9]{1}\d*$/;
//   const result = regex.test(value);
//   if (result) {
//     return Promise.resolve();
//   } else {
//     return Promise.reject("请输入整数，必须大于0");
//   }
// };

// 费率校验
// const validateInsuranceRate = (_rule: Rule, value: string) => {
//   const regex = /^[0-9]+(\.[0-9]{1,4})?$/;
//   const numVal = parseFloat(value);
//   const result = regex.test(value) && numVal > 0 && numVal <= 100;
//   if (result) {
//     return Promise.resolve();
//   } else {
//     return Promise.reject("请输入数字且小数位不超过4位，必须大于0，最大值100");
//   }
// };

// 总保额校验
// const validateTotalInsuredAmount = (_rule: Rule, value: string) => {
//   const regex = /^\d{0,12}(\.\d{1,2})?$/;
//   const numVal = parseFloat(value);
//   const result = regex.test(value) && numVal > 0;
//   if (result) {
//     return Promise.resolve();
//   } else {
//     return Promise.reject("请输入大于0的数字，整数位不超过12位，小数位不超过2位");
//   }
// };

// 总跟单费用率校验
// const validateYearOrderRateAll = (_rule: Rule, value: string) => {
//   const regex = /^[0-9]+(\.[0-9]{1,4})?$/;
//   const numVal = parseFloat(value);
//   const result = regex.test(value) && numVal >= 0 && numVal <= 100;
//   if (result) {
//     return Promise.resolve();
//   } else {
//     return Promise.reject("请输入0～100范围内的数字，且小数位不超过4位");
//   }
// };

export {
  // validateEmployeeScale,
  // validateLossRate,
  // validateObservation,
  // validateInsuredNumber,
  // validateFarmersCount,
  // validateInsuranceRate,
  // validateTotalInsuredAmount,
  // validateYearOrderRateAll,
};
