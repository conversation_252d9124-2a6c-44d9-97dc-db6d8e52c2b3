<template>
  <a-form-item label="代理人" :name="['saleInfo', 'agentInfoList', 0, 'agentCode']" :rules="[{ required: true, message: '请选择代理人' }]">
    <a-select
      v-model:value="agentInfoList[0].agentCode"
      :options="agentList"
      allow-clear
      show-search
      option-filter-prop="label"
      placeholder="请选择"
      :disabled="hasSubmit"
      :style="{ width: '100%' }"
      @change="changeAgent"
    />
  </a-form-item>
  <a-form-item label="代理协议" :name="['saleInfo', 'agentInfoList', 0, 'agentAgreementNo']" :rules="[{ required: true, message: '请选择代理协议' }]">
    <a-select
      v-model:value="agentInfoList[0].agentAgreementNo"
      :options="conferList"
      allow-clear
      show-search
      option-filter-prop="label"
      placeholder="请选择"
      :disabled="hasSubmit"
      :style="{ width: '100%' }"
    />
  </a-form-item>
  <a-form-item label="中介人员名称" :name="['saleInfo', 'agentInfoList', 0, 'agencySaleName']">
    <a-input v-model:value.trim="agentInfoList[0].agencySaleName" placeholder="请输入" allow-clear :disabled="hasSubmit" />
  </a-form-item>
  <a-form-item label="中介执业证号" :name="['saleInfo', 'agentInfoList', 0, 'agencySaleProfCertifNo']">
    <a-input v-model:value.trim="agentInfoList[0].agencySaleProfCertifNo" placeholder="请输入" allow-clear :disabled="hasSubmit" />
  </a-form-item>
</template>

<script setup lang="ts">
import { $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';

const props = defineProps<{
  channelSourceDetailCode: string;
  departmentCode: string;
  hasSubmit: boolean;
  validateFields: (params: unkown) => void;
}>();

const agentInfoList = defineModel<Array<{
  agentCode: string;
  agentName: string;
  agentAgreementNo: string;
  agencySaleName: string;
  agencySaleProfCertifNo: string;
}>>({ default: [] });

// 根据机构查询代理人
const agentList = ref<Array<SelectOptions>>([]);
watchEffect(() => {
  if (
    props.departmentCode &&
    props.channelSourceDetailCode
  ) {
    $getOnClient('/api/accept/getAgentListByDept', {
      departmentCode: props.departmentCode,
      channelSourceDetailCode: props.channelSourceDetailCode,
    }).then((res) => {
      if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
        agentList.value = res.data;
      } else {
        agentList.value = [];
      }
      if (!agentInfoList.value?.[0]?.agentCode && agentList.value?.findIndex(item => item.value === agentInfoList.value?.[0].agentCode) === -1) {
        agentInfoList.value[0].agentCode = agentList.value?.[0]?.value;
        agentInfoList.value[0].agentName = agentList.value?.[0]?.label;
      }
    });
  }
});
// 选择代理人
const changeAgent = (value: SelectValue, option: DefaultOptionType) => {
  agentInfoList.value[0].agentCode = option?.value as string;
  agentInfoList.value[0].agentName = option?.label || '';
};

// 根据代理人查询代理协议
const conferList = ref<Array<SelectOptions>>([]);
watchEffect(() => {
  if (agentInfoList.value[0].agentCode) {
    $getOnClient('/api/accept/getConferList', {
      agentCode: agentInfoList.value[0].agentCode,
    }).then((res) => {
      if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
        conferList.value = res.data;
      } else {
        conferList.value = [];
      }
      if (conferList.value.findIndex(item => item.value === agentInfoList.value?.[0]?.agentAgreementNo) === -1) {
        agentInfoList.value[0].agentAgreementNo = conferList.value?.[0]?.value || '';
      }
      props.validateFields([['saleInfo', 'agentInfoList', 0, 'agentAgreementNo']]);
    });
  }
});

// onMounted(() => {
//   // 组件切换要重置数据
//   agentInfoList.value = [{ agentCode: '', agentName: '', agentAgreementNo: '', agencySaleName: '', agencySaleProfCertifNo: '' }];
// });
</script>
