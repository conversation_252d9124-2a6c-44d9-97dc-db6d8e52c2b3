<template>
  <a-form-item label="经纪人" :name="['saleInfo', 'brokerInfoList', 0, 'brokerCode']" :rules="[{ required: true, message: '请选择经纪人' }]">
    <a-select
      v-model:value="brokerInfoList[0].brokerCode"
      :options="brokerList"
      allow-clear
      show-search
      option-filter-prop="label"
      placeholder="请选择"
      :disabled="hasSubmit"
      :style="{ width: '100%' }"
      @change="(value, option) => changeBroker(option)"
    />
  </a-form-item>
  <a-form-item label="中介人员名称" :name="['saleInfo', 'brokerInfoList', 0, 'agencySaleName']">
    <a-input v-model:value.trim="brokerInfoList[0].agencySaleName" placeholder="请输入" allow-clear :disabled="hasSubmit" />
  </a-form-item>
  <a-form-item label="中介执业证号" :name="['saleInfo', 'brokerInfoList', 0, 'agencySaleProfCertifNo']">
    <a-input v-model:value.trim="brokerInfoList[0].agencySaleProfCertifNo" placeholder="请输入" allow-clear :disabled="hasSubmit" />
  </a-form-item>
</template>

<script setup lang="ts">
import { $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';

const props = defineProps<{
  departmentCode: string;
  hasSubmit: boolean;
}>();

const brokerInfoList = defineModel<Array< {
  brokerCode: string;
  brokerName: string;
  agencySaleName: string;
  agencySaleProfCertifNo: string;
}>>({ default: [] });
// 选择经纪人
const changeBroker = (option: DefaultOptionType) => {
  brokerInfoList.value[0].brokerCode = option?.value as string;
  brokerInfoList.value[0].brokerName = option?.label || '';
};

// 根据机构查询经纪人
const brokerList = ref<Array<SelectOptions>>([]);
watchEffect(async () => {
  if (props.departmentCode) {
    try {
      const res = await $getOnClient('/api/accept/getBrokerList', {
        departmentCode: props.departmentCode,
      });
      if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
        brokerList.value = res.data;
      } else {
        brokerList.value = [];
      }
      if (!brokerList.value?.[0]?.agentCode && brokerList.value.findIndex(item => item.value === brokerInfoList.value[0].brokerCode) === -1) {
        // 默认选择代理人列表的第一项
        brokerInfoList.value[0].brokerCode = brokerList.value[0].value;
        brokerInfoList.value[0].brokerName = brokerList.value[0].label;
      }
    } catch (err) {
      console.log(err);
    }
  }
});

// onMounted(() => {
//   // 组件切换要重置数据
//   brokerInfoList.value = [{ brokerCode: '', brokerName: '', agencySaleName: '', agencySaleProfCertifNo: '' }];
// });
</script>
