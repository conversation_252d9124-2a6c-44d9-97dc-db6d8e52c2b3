export interface FormState {
  baseInfo: BaseInfo;
  saleInfo: SaleInfo;
  riskAddressInfoList: AddressInfo[];
  extendInfo: ExtendInfo;
  insurantInfoList: ApplicantInfo[];
  riskGroupInfoList: RiskGroupInfoList[];
  costInfo: CostInfo;
  payInfoList: PayInfo[];
}

export interface PayInfo {
  payerType: string; // 补贴类型
  otherPayerTypeDesc: string; // 备注
  paymentPersonName: string; // 付款人姓名
  premiumFinance: string; // 费用来源比例
}

export interface ExtendInfo {
  lossRate: string; // 风险程度
  isTenderBusiness?: string;
  isTenderBusinessChName?: string;
  tenderBusinessName?: string;
  tenderBusinessNo?: string;
  agrLandClassification: string;
}

export interface CostInfo {
  totalSumFeeLimit: string; // "总费用之和上限值【后端 * 100】"
  performanceValue1Default: string; // "农险补贴-【后端 * 100】"
  performanceValue1DefaultLimit?: string; // "农险补贴上限值-【后端 * 100】"
  managementFees: string; // "工作经费-【后端 * 100】"
  managementFeesLimit?: string; // "工作经费上限-【后端 * 100】"
  assisterCharge: string; // "协办费-【后端 * 100】"
  assisterChargeLimit?: string; // "协办费上限值-【后端 * 100】"
  calamitySecurityRate: string; // "防灾防损费-【后端 * 100】"
  calamitySecurityRateLimit?: string; // "防灾防损费上限值-【后端 * 100】"
  commissionBrokerChargeProportion: string; // "手续费/经纪费-【后端 * 100】"
  commissionBrokerChargeProportionLimit?: string; // "手续费/经纪费上限-【后端 * 100】"
}

export interface ApplicantInfo {
  sameInsuredPersons: string;
  personnelType: string;
  poorSymbol: string;
  subjectNumber: string;
}
export interface RiskGroupInfoList {
  combinedProductCode: string; // 标的code
  riskAgrInfo: {
    farmersCount: string; // 农户数
    reductionCoefficient: string; // 减免系数
    substitute: string; // 农户是否减免
    insuredUnit: string; // 数量单位
  };
  planInfoList: PlanInfoList[];
}
export interface PlanInfoList {
  unitInsuredAmount: string; // 单位保险金额
  expectPremiumRate: string; // 费率
  centralFinance: string; // 中央财政
  cityFinance: string; // 省级财政
  countyFinance: string;
  farmersFinance: string;
  otherFinance: string;
  provincialFinance: string;
  unitPrimium: string; // 单位保费
}
export interface SignData {
  signType: string; // 签报类型
  signDesc: string; // 签报类型说明
  signStatus: string; // 签报状态
  signStatusDesc: string; // 签报状态说明
  eoaNo: string; // 签报号
  eoaDetailUrl: string; // 签报详情url
}

export interface RuleData {
  signDataList: Record<string, string>[];
  relatedTransactionFlag: boolean;
}

// 基本信息
export interface BaseInfo {
  arbitralDepartment?: string; // 仲裁机构
  departmentCode: string; // 机构编码
  coinsuranceMark: string; // 共保标志
  govSubsidyType: string; // 补贴类型
  acceptInsuranceFlag: string; // 是否主承
  productCode: string; // 产品编码
  productName: string; // 产品名称
  customerType: string; // 客户类型
  riskCode: string; // 产品名称
  riskLevel: string; // 标的层级
  insuranceEndDate: string; // 保险止期
  insuranceBeginDate: string; // 保险起期
  disputedSettleMode: string; // 争议处理方式
  premiumOwner: string; // 应收责任人
  shortTimeCoefficient: string; // 年限系数
  assisterInfoList: AssisterInfo[]; // 协办员信息
  productVersion: string; // 产品版本
  inputBy: string; // 业务员UM
}

// 协办员信息
export interface AssisterInfo {
  assisterId: string; // 协办员id
  idCardNo: string; // 身份证号
  umCode: string; // um号码
}

export interface BaseOptionType {
  disabled?: boolean;
  [name: string]: string;
}

export interface SelectOptions {
  label: string;
  value: string;
}
