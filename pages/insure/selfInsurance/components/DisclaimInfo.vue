<template>
  <a-form ref="formRef" :model="disclaimInfo" class="fee-plan">
    <a-table v-if="disclaimInfo.length > 0" :columns="columns" :data-source="disclaimInfo" :pagination="false">
      <template #bodyCell="{ column, index }">
        <!-- 免赔项目 -->
        <template v-if="column.dataIndex === 'noclaimItemArr'">
          <a-form-item :name="[index, 'noclaimItemArr']" :rules="[{ validator: commonValidate }]">
            <a-cascader v-model:value="disclaimInfo[index].noclaimItemArr" :options="options" style="width: 400px" :disabled="disabled" @change="val => changeNoclaimItem(val, index)" />
          </a-form-item>
        </template>
        <!-- 免赔类型 -->
        <template v-if="column.dataIndex === 'noclaimType'">
          <a-form-item :name="[index, 'noclaimType']" :rules="[{ validator: typeValidate }]">
            <a-select v-model:value="disclaimInfo[index].noclaimType" style="width: 100%" :options="typeOptions" :disabled="disabled" @change="validate" />
          </a-form-item>
        </template>
        <!-- 免赔率 -->
        <template v-if="column.dataIndex === 'noclaimRate'">
          <a-form-item :name="[index, 'noclaimRate']" :rules="[{ validator: validateRate }]">
            <a-input v-model:value="disclaimInfo[index].noclaimRate" style="width: 100%" placeholder="请输入" :disabled="disabled" @blur="validate" />
          </a-form-item>
        </template>
        <!-- 免赔额 -->
        <template v-if="column.dataIndex === 'noclaimAmount'">
          <a-form-item :name="[index, 'noclaimAmount']" :rules="[{ validator: validateNumber }]">
            <a-input v-model:value="disclaimInfo[index].noclaimAmount" style="width: 100%" placeholder="请输入" :disabled="disabled" @blur="validate" />
          </a-form-item>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-button type="link" :disabled="disabled" @click="removePricingPlan(index)">删除</a-button>
        </template>
      </template>
    </a-table>
    <div v-if="!disabled" class="my-[12px] text-[#576B95] flex items-center space-x-[4px] cursor-pointer">
      <VueIcon :icon="IconTongyongXinzengFont" @click="addPricingPlan" />
      <span @click="addPricingPlan">增加一条免赔信息</span>
    </div>
    <a-form-item label="免赔描述">
      <a-textarea v-model:value="baseInfo.deductionDesc" placeholder="请输入免赔描述" :maxlength="1000" show-count :disabled="disabled" />
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';
import { IconTongyongXinzengFont } from '@pafe/icons-icore-agr-an';
import type { TableColumnsType } from 'ant-design-vue';
import { isEqual } from 'lodash-es';
import type { DisclaimInfo, BaseInfo, dutyItemType, OptionsType, PlantItemType, RiskGroupInfoType } from '@/pages/insure/insuranceFormFill/insuranceFormFill';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost } from '@/composables/request';
import type { SelectOptions } from '@/apiTypes/apiCommon';

const disclaimInfo = defineModel<DisclaimInfo[]>('disclaimInfo', { default: [] });
const baseInfo = defineModel<BaseInfo>('baseInfo', { default: {} });
// 表头
const columns: TableColumnsType = [
  {
    title: '免赔项目',
    dataIndex: 'noclaimItemArr',
    key: 'noclaimItemArr',
    width: 400,
  },
  {
    title: '免赔类型',
    dataIndex: 'noclaimType',
    key: 'noclaimType',
    width: 136,
  },
  {
    title: '免赔率(%)',
    dataIndex: 'noclaimRate',
    key: 'noclaimRate',
  },
  {
    title: '免赔额',
    dataIndex: 'noclaimAmount',
    key: 'noclaimAmount',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
  },
];
const options = ref<SelectOptions[]>([]); // 免赔项目选项
const typeOptions = ref<SelectOptions[]>([]); // 免赔类型
const props = defineProps<{
  riskGroupInfoList: RiskGroupInfoType[]; // 保险方案
  disabled: boolean; // 是否禁用
}>();
watch(
  () => props.riskGroupInfoList,
  () => {
    // 免赔项目选项组装
    if (props?.riskGroupInfoList?.length > 0 && props.riskGroupInfoList[0]?.planInfoList?.length > 0) {
      if (options.value.length > 0) {
        options.value.forEach((option) => {
          if (option.value !== '1') {
            // 免赔项目非整单的选项需要添加二级选项
            option.children = [];
            props.riskGroupInfoList[0]?.planInfoList.forEach((item: OptionsType) => {
              if (item.planCode && item.planName) {
                const plantObj: PlantItemType = {
                  label: item.planName,
                  value: item.planCode,
                  children: [],
                };
                if (option.value === '3') {
                  // 免赔项目为责任，需要添加三级选项
                  if (item.dutyInfoList && item.dutyInfoList.length > 0) {
                    plantObj.children = [];
                    item.dutyInfoList.forEach((el: dutyItemType) => {
                      if (el.dutyCode && el.dutyName) {
                        const dutyObj = {
                          label: el.dutyName,
                          value: el.dutyCode,
                        };
                        plantObj.children.push(dutyObj);
                      }
                    });
                  }
                }
                option.children?.push(plantObj);
              }
            });
          }
        });
      }
      // 判断已选的免赔项目选项是否还存在,过滤掉不存在的免赔项目
      disclaimInfo.value = disclaimInfo.value.filter((item: { planCode: string; noclaimItem: string }) => {
        const existPlanCode = props.riskGroupInfoList[0]?.planInfoList.some((el: OptionsType) => el.planCode === item.planCode);
        if (existPlanCode || item.noclaimItem === '1') { // noclaimItem为1是整单
          return item;
        }
      });
    }
  },
  { deep: true },
);

const formRef = ref();
// 表单校验
const validate = async () => {
  if (formRef.value) {
    try {
      await formRef.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};
  // 新增一条免赔信息
const addPricingPlan = () => {
  const newItem: DisclaimInfo = {
    noclaimItemArr: [], // 免赔项目
    noclaimType: '', // 免赔类型
    noclaimItem: '', // 免赔项目
    planCode: '', // 险种编码
    dutyCode: '', // 责任编码,CV0054A:火灾、CV0088A:穗发芽、CV0091A:严重干旱、...
    noclaimRate: '',
    noclaimAmount: '',
  };
  disclaimInfo.value.push(newItem);
};

// 删除一条免赔信息
const removePricingPlan = (index: number) => {
  disclaimInfo.value.splice(index, 1);
  disclaimInfo.value.forEach((item, index) => {
    formRef.value.validateFields([[index, 'noclaimItemArr']]);
  });
};
  // 免赔项目校验规则
const commonValidate = (_rule: Rule & { field?: string }) => {
  const { field } = _rule;
  const index = Number(field?.split('.')[0]);
  const itemProject = disclaimInfo.value[index].noclaimItemArr;
  // 校验不能为空
  if (itemProject?.length === 0) {
    return Promise.reject('免赔项目不能为空');
  }
  // 校验是否有重复项
  const isRepeat = disclaimInfo.value.filter((item: { noclaimItemArr: string[] }) => isEqual(itemProject, item.noclaimItemArr)).length > 1;
  if (isRepeat) {
    return Promise.reject('免赔项目不能重复选择');
  }
  return Promise.resolve();
};
  // 免赔额校验规则
const validateNumber = (_rule: Rule & { field?: string }, value: string) => {
  const { field } = _rule;
  const index = Number(field?.split('.')[0]);
  const req = /^\d{1,12}(\.\d{1,2})?$/;
  if (value && !req.test(value)) {
    return Promise.reject('整数部分最多输入12位，小数部分最多输入2位!');
  } else if (!value) {
    if (disclaimInfo.value[index].noclaimRate) {
      return Promise.resolve();
    } else {
      return Promise.reject('免赔率和免赔额必填其一');
    }
  }
  return Promise.resolve();
};
  // 免赔类型校验规则
const typeValidate = (rule: Rule, value: string) => {
  if (!value) {
    return Promise.reject('免赔类型不能为空');
  }
  return Promise.resolve();
};
  // 免赔率校验规则
const validateRate = (_rule: Rule & { field?: string }, value: string) => {
  const { field } = _rule;
  const index = Number(field?.split('.')[0]);
  const req = /^\d+(\.\d{1,4})?$/;
  if (Number(value) > 100) {
    return Promise.reject('费率不能大于100！');
  } else if (value && !req.test(value)) {
    return Promise.reject('请输入正数且小数位不能超过4位！');
  } else if (!value) {
    if (disclaimInfo.value[index].noclaimAmount) {
      return Promise.resolve();
    } else {
      return Promise.reject('免赔率和免赔额必填其一');
    }
  }
  return Promise.resolve();
};
  // 免赔项目改变时,修改模型中字段
const changeNoclaimItem = (val: unknown, index: number) => {
  disclaimInfo.value[index].noclaimItem = (val as string[])?.[0] || '';
  disclaimInfo.value[index].planCode = (val as string[])?.[1] || '';
  disclaimInfo.value[index].dutyCode = (val as string[])?.[2] || '';
  disclaimInfo.value.forEach((item, index) => {
    formRef.value.validateFields([[index, 'noclaimItemArr']]);
  });
};
defineExpose({ validate });
const { gateWay, service } = useRuntimeConfig().public || {};
const getOptionsReq = await usePost<SelectOptions[]>(`${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`);
// 获取下拉框选项
const getOptions = async () => {
  const params = ['noclaimType', 'noclaimItem'];
  try {
    const res = await getOptionsReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      typeOptions.value = res.data.find((item: { value: string }) => item.value === 'noclaimType')?.children || [];
      const firstItem = res.data.find((item: { value: string }) => item.value === 'noclaimItem')?.children || [];
      if (firstItem?.length > 0) {
        options.value = firstItem.map((item) => {
          if (item.value === '1') {
            // 整单不需要选择下级
            item.isLeaf = true;
          }
          return item;
        });
      }
    }
  } catch (error) {
    console.error(error);
  }
};
onMounted(() => {
  getOptions();
});
</script>
