<template>
  <a-form ref="formRef" :model="payInfoList" class="fee-plan">
    <a-table :columns="columns" :data-source="payInfoList" :pagination="false">
      <template #bodyCell="{ column, index, record }">
        <!-- 补贴类型 -->
        <template v-if="column.dataIndex === 'payerType'">
          <a-form-item :name="[index, 'payerType']">
            <a-select v-model:value="payInfoList[index].payerType" :options="payerTypeOptions" :disabled="true" />
          </a-form-item>
        </template>
        <!-- 费用来源比例 -->
        <template v-if="column.dataIndex === 'premiumFinance'">
          <a-form-item :name="[index, 'premiumFinance']" :rules="[{ validator: financeValidator }]">
            <a-input-number v-model:value="payInfoList[index].premiumFinance" :min="0" :disabled="disabled" />
          </a-form-item>
        </template>
        <!-- 付款人名称 -->
        <template v-if="column.dataIndex === 'paymentPersonName'">
          <a-form-item v-if="record.payerType !== '5'" :name="[index, 'paymentPersonName']" :rules="[{ required: record.premiumFinance, message: '请输入付款人名称' }]">
            <a-input v-model:value="payInfoList[index].paymentPersonName" style="width: 100%" placeholder="请输入付款人名称" :disabled="!record.premiumFinance || disabled" />
          </a-form-item>
          <a-form-item v-if="record.payerType === '5'" :name="[index, 'paymentPersonName']">
            <a-input v-model:value="payInfoList[index].paymentPersonName" style="width: 100%" disabled />
          </a-form-item>
        </template>
        <!-- 备注 -->
        <template v-if="column.dataIndex === 'otherPayerTypeDesc'">
          <a-form-item v-if="record.payerType === '6'" :name="[index, 'otherPayerTypeDesc']" :rules="[{ required: !!record.premiumFinance, message: '请填写备注' }]">
            <a-input v-model:value="payInfoList[index].otherPayerTypeDesc" style="width: 100%" placeholder="请输入备注" :maxlength="200" show-count :disabled="!record.premiumFinance || disabled" />
          </a-form-item>
          <div v-else>-</div>
        </template>
      </template>
    </a-table>
  </a-form>
</template>

<script setup lang="ts">
import type { TableColumnsType } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form/interface';
import type { PayInfo } from '../selfInsurance';
import { usePost } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import type { SelectOptions } from '@/apiTypes/apiCommon';

const payInfoList = defineModel<PayInfo[]>('payInfoList', { default: [] });
defineProps<{
  govSubsidyType: string; // 补贴类型
  disabled: boolean; // 是否禁止
}>();
const columns: TableColumnsType = [
  {
    title: '补贴类型',
    dataIndex: 'payerType',
    key: 'payerType',
  },
  {
    title: '费用来源比例(%)',
    dataIndex: 'premiumFinance',
    key: 'premiumFinance',
  },
  {
    title: '付款人名称',
    dataIndex: 'paymentPersonName',
    key: 'paymentPersonName',
  },
  {
    title: '备注',
    dataIndex: 'otherPayerTypeDesc',
    key: 'otherPayerTypeDesc',
  },
];
// 比例校验，比例录入范围0-100范围内的数字且小数位不能超过4位
const financeValidator = (_rule: Rule, value: number) => {
  // 检查是否在0到100范围内
  if (Number(value) < 0 || Number(value) > 100) {
    return Promise.reject('必须在0到100之间');
  }

  // 检查小数位是否超过4位
  const decimalPart = String(value).split('.')[1];
  if (decimalPart && decimalPart.length > 4) {
    return Promise.reject('小数位不能超过4位');
  }

  return Promise.resolve();
};
const payerTypeOptions = ref<SelectOptions[]>([]); // 补贴类型选项
const formRef = ref();
const validate = async () => {
  try {
    if (formRef.value) {
      await formRef.value.validateFields();
    }
    return { valid: true, errors: [] };
  } catch (errors) {
    return { valid: false, errors };
  }
};
defineExpose({ validate });
const { gateWay, service } = useRuntimeConfig().public || {};
const getOptionsReq = await usePost<SelectOptions[]>(`${gateWay}${service.administrate}/parmBaseConstantConf/getParmBaseConstantConf`);
// 获取下拉框选项
const getOptions = async () => {
  try {
    const params = ['agrPayerType'];
    const res = await getOptionsReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      payerTypeOptions.value = res.data?.[0]?.children || [];
    }
  } catch (error) {
    console.log(error);
  }
};
onMounted(() => {
  getOptions();
});
</script>
