<template>
  <div>
    <a-form ref="formRef" :model="costInfo" :colon="false" :label-col="{ style: { width: pxToRem(110) } }">
      <div class="grid grid-cols-3 gap-x-16px">
        <a-form-item required label="农险补贴" name="performanceValue1Default" :rules="[{ validator: validateNumberFee }]">
          <a-input v-model:value="costInfo.performanceValue1Default" addon-after="%" :disabled="disabled" />
        </a-form-item>
        <a-form-item :required="!commissionFlag" label="协办费" name="assisterCharge" :rules="[{ validator: validateNumberFee }]">
          <a-input v-model:value="costInfo.assisterCharge" :disabled="commissionFlag || disabled" addon-after="%" @change="handleAssisterCharge" />
        </a-form-item>
        <a-form-item :required="!brokerageFlag" label="手续费/经纪费" name="commissionBrokerChargeProportion" :rules="[{ validator: validateNumberFee }]">
          <a-input v-model:value="costInfo.commissionBrokerChargeProportion" :disabled="true" addon-after="%" @change="handleCommission" />
        </a-form-item>
      </div>
      <div class="grid grid-cols-3 gap-x-16px">
        <a-form-item label="工作经费" name="managementFees" :rules="[{ validator: validateNumberFee }]">
          <a-input v-model:value="costInfo.managementFees" :disabled="disabled" addon-after="%" />
        </a-form-item>
        <a-form-item :required="!disasterLossFlag" label="防灾防损费" name="calamitySecurityRate">
          <a-input v-model:value="costInfo.calamitySecurityRate" :disabled="disasterLossFlag || disabled" addon-after="%" />
        </a-form-item>
      </div>
    </a-form>
    <!-- 总费率提示 -->
    <div v-show="totalRateFlag" class="col-span-12 mt-10px ml-16px text-[#F00] text-[12px]">费用比例之和超过{{ costInfo.totalSumFeeLimit }}%，请留意</div>
  </div>
</template>

<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form/interface';
import type { CostInfo, BaseInfo } from '../selfInsurance';
import { pxToRem } from '@/utils/tools';

const costInfo = defineModel<CostInfo>('costInfo', { default: {} });
const props = defineProps<{
  baseInfo: BaseInfo; // 基础信息
  businessSourceCode: string; // 渠道code
  disabled: boolean; // 是否禁用
}>();

/** 协办费是否disabled
当非从共业务农保ID选择无时，协办费不可录入
1.系统外共保&是主承&农保id无 协办费禁用
2.系统内共保&农保id无 协办费禁用
3.系统外同时系统内共保&主承&农保id无 协办费禁用
渠道来源为代理禁用-2 */
const commissionFlag = computed(() => {
  return props.baseInfo.assisterInfoList?.length === 0 || props.baseInfo.assisterInfoList?.[0]?.assisterId === '无';
});
// 如果协办费禁用，则设置默认值为0
watch(() => commissionFlag.value, (val) => {
  if (val) {
    costInfo.value.assisterCharge = '0';
  }
});
// 防灾防损费是否disabled
const disasterLossFlag = ref<boolean>(true);

/** 手续经费是否disabled-补贴类型为政策性1,2
补贴类型为政策性：baseInfo.govSubsidyType='1' 或者 '2'
补贴类型为商业性：baseInfo.govSubsidyType='3'
渠道来源细分包含“直接”：saleInfo.businessSourceCode='1'
渠道来源细分包含“代理”&“经纪”：saleInfo.businessSourceCode='2'或'3'
当补贴类型=政策性，此项置灰不允许填写；
当补贴类型=商业性&渠道来源细分包含“直接”， 此项置灰不允许填写
当补贴类型=商业性&渠道来源细分包含“代理/经纪”，此项必录 */
const brokerageFlag = computed(() => {
  // 补贴类型
  const { govSubsidyType } = props.baseInfo;
  // 业务来源
  return (['1', '2'].includes(govSubsidyType) || (govSubsidyType === '3' && props.businessSourceCode === '1'));
});
// 禁用设置值为0
watch(() => brokerageFlag.value, (val) => {
  if (val) {
    costInfo.value.commissionBrokerChargeProportion = '0';
  } else {
    costInfo.value.commissionBrokerChargeProportion = '';
  }
});
// 协办费有值，经纪代理费为0
const handleAssisterCharge = () => {
  costInfo.value.commissionBrokerChargeProportion = '0';
};
// 手续费/经纪费有值，经纪代理费为0
const handleCommission = () => {
  costInfo.value.assisterCharge = '0';
};
// 校验
const validateNumberFee = (_rule: Rule & { field?: string }, value: string) => {
  const { field } = _rule;
  const reg = /^\d+(\.\d{1,2})?$/;
  if (!value) {
    return Promise.reject();
  }
  if (!reg.test(value)) {
    return Promise.reject('请输入正数且小数位不能超过2位');
  }
  if (Number(value) > 100) {
    return Promise.reject('请输入0~100范围内的数字');
  }
  const _name: string = field + 'Limit';
  if (!costInfo.value[_name]) {
    return Promise.reject(`费用上限未配置，无法录入，如有需要请联系核保人配置`);
  }
  if (Number(value) > Number(costInfo.value[_name])) {
    return Promise.reject(`费用上限配置为${costInfo.value[_name]}%，如需调整，请联系核保人`);
  }
  return Promise.resolve();
};
const formRef = ref();
const validate = async () => {
  if (formRef.value) {
    try {
      await formRef.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};
// 当前输入的总费率计算,如过超过上限则提示
const totalRateFlag = computed(() => {
  const { assisterCharge, commissionBrokerChargeProportion, managementFees, calamitySecurityRate, coinsuranceInsureFeeRatio, performanceValue1Default } = costInfo.value;
  const count = Number(assisterCharge) + Number(commissionBrokerChargeProportion) + Number(managementFees) + Number(calamitySecurityRate) + Number(coinsuranceInsureFeeRatio) + Number(performanceValue1Default);
  return Number(costInfo.value.totalSumFeeLimit) < count;
});
defineExpose({
  validate,
});
</script>
