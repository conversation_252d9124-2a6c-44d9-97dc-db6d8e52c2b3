<template>
  <template v-for="(item, i) in list" :key="i">
    <a-form-item label="业务员信息" :name="['saleInfo', 'employeeInfoList', i, 'employeeCode']" :rules="[{ required: true, message: '请选择业务员' }]">
      <a-select
        v-model:value="item.employeeCode"
        placeholder="请选择"
        :options="employeeList"
        allow-clear
        show-search
        :loading="employeeLoading"
        option-filter-prop="label"
        :disabled="disabled"
        @change="(value, option) => changeEmployeeInfo(option as SelectOptions, i)"
      />
    </a-form-item>
    <a-form-item label="业务员手机号" :name="['saleInfo', 'employeeInfoList', i, 'mobileTelephone']" required :rules="[{ validator: isMobilePhone }]">
      <a-input v-model:value.trim="item.mobileTelephone" placeholder="请输入" :disabled="disabled" />
    </a-form-item>
  </template>
</template>

<script setup lang="ts">
import type { SelectOptions } from '../selfInsurance';
import { $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { isMobilePhone } from '@/utils/validators';

const { gateWay, service } = useRuntimeConfig().public || {};

const props = defineProps<{
  developFlg: string;
  departmentCode: string;
  validateFields: (params: unknown) => void;
  disabled?: boolean; // 是否禁用
}>();

const isUpdateEmployee = ref<boolean>(true);
const list = defineModel<Array<{
  employeeCode: string;
  employeeName: string;
  employeeProfCertifNo: string;
  commisionScale: string;
  mainEmployeeFlag: string;
  mobileTelephone: string;
}>>({ default: [] });

const changeEmployeeInfo = (option: SelectOptions, index: number) => {
  list.value[index].employeeCode = option?.value as string;
  list.value[index].employeeName = option?.label || '';
};

// 是否共展
watch(() => props.developFlg, () => {
  if (props.developFlg === 'NS') {
    if (list.value.length > 1) {
      const firstEmployee = {
        employeeCode: list.value[0].employeeCode,
        employeeName: list.value[0].employeeName,
        employeeProfCertifNo: list.value[0].employeeProfCertifNo,
        commisionScale: '',
        mainEmployeeFlag: '1',
        mobileTelephone: '',
      };
      list.value = [firstEmployee];
    }
  }
});

const employeeLoading = ref(false);
const employeeList = ref<SelectOptions[]>([]);

watch(() => props.departmentCode, async (code) => {
  try {
    employeeLoading.value = true;
    const res = await $getOnClient(gateWay + service.accept + '/saleInfo/getSasEmployeeList', { departmentCode: code });
    if (res && res.code === SUCCESS_CODE && Array.isArray(res.data)) {
      employeeList.value = res.data.map((item) => {
        return {
          value: item.employeeCode,
          label: item.employeeName,
        };
      });
      // todo 待重构
      if (employeeList.value.length > 0 && list.value.length < 2 && (!isUpdateEmployee.value || !list?.value?.[0].employeeCode)) {
        const defaultEmployee = employeeList.value?.[0];
        for (const [index, item] of list.value.entries()) {
          item.employeeCode = defaultEmployee.value || '';
          item.employeeName = defaultEmployee.label || '';
          props.validateFields([['saleInfo', 'employeeInfoList', index, 'employeeCode']]);
        }
      }
      isUpdateEmployee.value = false;
    }
  } catch (e) {
    console.log(e, '获取业务员失败');
  } finally {
    employeeLoading.value = false;
  }
});
</script>

<style lang="less" scoped>
.percent-input {
  :deep(.ant-input-group-addon) {
    padding: 0;
  }
}
.ismain-radio {
  :deep(.ant-radio-wrapper-in-form-item) {
    margin-inline-end: 0px;
    & span + span {
      padding-inline-end: 0px;
    }
  }
}
</style>
