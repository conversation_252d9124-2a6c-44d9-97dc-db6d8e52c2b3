<template>
  <a-modal
    v-model:open="visible"
    title="凭证文件下载"
    :width="pxToRem(800)"
    :footer="null"
    :mask-closable="false"
    centered
  >
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :scroll="{ x: 'max-content' }"
      :loading="loading"
    >
      <template #bodyCell="{ column, record, index, text }">
        <template v-if="column.dataIndex === 'number'">
          {{ index + 1 }}
        </template>
        <template v-if="column.dataIndex === 'handleStatus'">
          {{ status[text] }}
        </template>
        <template v-if="column.dataIndex === 'action'">
          <a-button :disabled="record.handleStatus !== '5'" class="px-[12px]" type="primary" @click="downloadFile(record.url)">下载</a-button>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup lang="ts">
import type { TableColumnsType } from 'ant-design-vue';
import { pxToRem } from '@/utils/tools';
import { usePagination } from '@/composables/usePagination';
import { usePost } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';

const { gateWay, service } = useRuntimeConfig().public || {};
// 列表数据
const dataSource = ref<Record<string, string>[]>([]);
const loading = ref<boolean>(false);
const columns: TableColumnsType = [
  { title: '序号', dataIndex: 'number' },
  { title: '文件名称', dataIndex: 'fileName' },
  { title: '导出时间', dataIndex: 'createdDate' },
  { title: '执行状态', dataIndex: 'handleStatus' },
  { title: '操作', dataIndex: 'action', fixed: 'right' },
];
const status: Record<string, string> = {
  '-1': '正在处理',
  '4': '正在处理',
  '5': '处理成功',
  '6': '销号失败',
};
const visible = defineModel<boolean>('visible', { required: true, default: false });
// 获取列表数据
const url = gateWay + service.document + '/print/queryOneFarmerOnePolicyListPage';
const listReq = await usePost<{ records: Record<string, string>[]; total: number; current: number; size: number }>(url);
const getListData = async () => {
  loading.value = true;
  const params = {
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
  };
  try {
    const res = await listReq.fetchData(params);
    if (res?.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res?.data || {};
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      dataSource.value = [];
      pagination.total = 0;
      pagination.current = 1;
      pagination.pageSize = 10;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const { pagination } = usePagination(getListData);
watch(visible, (val) => {
  if (val) {
    getListData();
  }
});
// 下载
const downloadFile = (fileUrl: string) => {
  const url = gateWay + service.document + `/print/downloadCertificatePrint?url=${fileUrl}`;
  window.open(url);
};
</script>

<style lang="less" scoped>
.ant-table-cell .ant-btn.ant-btn-sm:first-of-type, .ant-table-cell .ant-btn:first-of-type {
  padding-left: 14px;
}
</style>
