<template>
  <a-modal v-model:open="visible" title="导出模版" ok-text="导出" :after-close="afterClose" @ok="download">
    <a-form ref="formRef" :model="formState" :label-col="{ style: { width: pxToRem(80) } }">
      <a-form-item label="单证类型" name="type">
        <a-radio-group v-model:value="formState.type" option-type="button" :options="plainOptions" @change="changeType" />
      </a-form-item>
      <a-form-item label="单证模版" name="templateNo" :rules="[{ required: true, message: '请选择单证模版' }]">
        <a-select v-model:value="formState.templateNo" :options="templateOptions" :field-names="{ label: 'printTypeName', value: 'idParmPrintTemplate' }" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { $getOnClient } from '@/composables/request';
import { pxToRem, downloadBlob } from '@/utils/tools';
import { SUCCESS_CODE } from '@/utils/constants';

const formRef = ref();
const visible = defineModel<boolean>('visible', { required: true, default: false });
// 单证类型
const plainOptions = [
  { label: '投保单', value: '02' },
  { label: '分户清单', value: '04' },
  { label: '合同变更申请书', value: '07' },
  { label: '验标单证', value: '06' },
];
// 单证模版选项
const templateOptions = ref<Record<string, string>[]>([]);
const formState = ref({
  type: '02',
  templateNo: '',
});
// 导出
const download = async () => {
  await formRef.value.validate();
  const downloadUrl = gateWay + service.document + '/print/templatePrint';
  const params = {
    printTemplateId: formState.value.templateNo,
  };
  try {
    await $getOnClient(downloadUrl, params, {
      onResponse({ response }) {
        if (response._data instanceof Blob) {
          const contentDisposition = response.headers.get('Content-Disposition');
          const fileData = response._data;
          const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
          if (contentDisposition) {
            const match = contentDisposition.match(/filename=(.+)/);
            const fileName = match ? decodeURI(match[1]) : '';
            downloadBlob(fileData, fileName, fileType);
          }
          message.success('导出成功');
        } else {
          const { code, data, msg = '' } = response._data;
          if (code === SUCCESS_CODE) {
            message.success(data);
          } else if (msg) {
            message.error(msg);
          }
        }
      },
    });
  } catch (error) {
    console.log(error);
  }
};
// 关闭
const afterClose = () => {
  formRef.value.clearValidate();
  formRef.value.resetFields();
};
const { gateWay, service } = useRuntimeConfig().public || {};
// 获取模版选项
const getTemplate = async () => {
  templateOptions.value = [];
  const fetchUrl = gateWay + service.document + '/print/queryPrintTemplate';
  const params = {
    templetType: formState.value.type,
  };
  const res = await $getOnClient<{ printTemple: Record<string, string>[] }>(fetchUrl, params);
  if (res && res?.code === SUCCESS_CODE) {
    templateOptions.value = res.data?.printTemple || [];
  } else {
    message.warning(res?.msg);
  }
};
// 改变单证类型
const changeType = () => {
  getTemplate();
  formState.value.templateNo = '';
};
watch(visible, (val) => {
  if (val) {
    getTemplate();
  }
});
</script>
