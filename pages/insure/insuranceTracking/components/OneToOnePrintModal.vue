<template>
  <a-modal v-model:open="visible" :title="title" :width="pxToRem(600)" :mask-closable="false" centered>
    <div v-if="printVisible" class="mb-[18px]">
      <a-form
        ref="formRef"
        :model="formState"
        autocomplete="off"
        :colon="false"
        required
        label-align="left"
        :label-col="{ style: { width: pxToRem(80) } }"
      >
        <a-form-item label="单证大类">保单</a-form-item>
        <a-form-item label="单证小类">格式保单</a-form-item>
        <a-form-item required label="单证模版">
          <a-select v-model:value="formState.template" :options="templateOptions" :field-names="{ label: 'printTypeName', value: 'templateName' }" />
        </a-form-item>
      </a-form>
    </div>
    <div v-if="!printVisible">请导出保单合并结果并检查是否正确，如确认合并结果正确可点击继续打印</div>
    <template #footer>
      <a-button v-if="!printVisible" type="primary" @click="downloadResult">导出合并结果</a-button>
      <a-button v-if="!printVisible" type="primary" :disabled="continuePrint" @click="printContinue">继续打印</a-button>
      <a-button v-if="printVisible" type="primary" @click="continuePrintOk">打印</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import type { insuranceTrackingType } from '../insuranceTracking.d';
import { pxToRem, downloadBlob } from '@/utils/tools';
import { useGet, $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';

const { gateWay, service } = useRuntimeConfig().public || {};

const templateOptions = ref<Record<string, string>[]>([]);
const title = ref<string>('操作提示');
// 一户一保打印弹窗
const printVisible = ref<boolean>(false);
const continuePrint = ref<boolean>(true);
const visible = defineModel<boolean>('visible', { required: true, default: false });
const formState = ref({
  template: '',
});
const props = withDefaults(defineProps<{
  selectObjArr: insuranceTrackingType[];
  sid: string;
}>(), {});
const formRef = ref();
// 继续打印
const printContinue = () => {
  title.value = '一户一保打印';
  printVisible.value = true;
};
// 继续打印弹窗确定
const continuePrintUrl = gateWay + service.document + '/print/printOneFarmerOnePolicy';
const continuePrintReq = await useGet(continuePrintUrl);
const continuePrintOk = async () => {
  const params = {
    sameBatchPrintId: props.sid,
  };
  try {
    const res = await continuePrintReq.fetchData(params);
    if (res?.code === SUCCESS_CODE) {
      message.success(res?.msg);
      visible.value = false;
    } else {
      message.error(res?.msg);
    }
  } catch (error) {
    console.log(error);
  }
};
// 导出合并结果
const downloadUrl = gateWay + service.document + '/print/exportEmpCsvList';
const downloadResult = async () => {
  const params = {
    sid: props.sid,
  };
  try {
    await $getOnClient(downloadUrl, params, {
      onResponse({ response }) {
        if (response._data instanceof Blob) {
          const contentDisposition = response.headers.get('Content-Disposition');
          const fileData = response._data;
          const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
          if (contentDisposition) {
            const match = contentDisposition.match(/filename=(.+)/);
            const fileName = match ? decodeURI(match[1]) : '';
            downloadBlob(fileData, fileName, fileType);
          }
          message.success('导出成功');
          continuePrint.value = false;
        } else {
          const { code, data, msg = '' } = response._data;
          if (code === SUCCESS_CODE) {
            message.success(data);
          } else if (msg) {
            message.error(msg);
          }
        }
      },
    });
  } catch (error) {
    console.log(error);
  }
};
// 获取单证模板选项
const getOptionsReq = await useGet<{ printTemple: Record<string, string>[] }>(`${gateWay}${service.document}/print/queryPrintTemplate`);
const getOptions = async () => {
  try {
    const params = {
      templetType: '09',
      departmentCode: props.selectObjArr[0].insureDepartmentNo,
      policyNo: props.selectObjArr[0].policyNo,
    };
    const res = await getOptionsReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      templateOptions.value = res.data?.printTemple || [];
      formState.value.template = templateOptions.value?.[0]?.templateName || '';
    }
  } catch (error) {
    console.log(error);
  }
};
watch(visible, (val) => {
  if (val) {
    printVisible.value = false;
    continuePrint.value = true;
    formState.value.template = '';
    getOptions();
  }
});
</script>
