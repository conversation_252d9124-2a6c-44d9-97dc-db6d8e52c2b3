export interface insuranceTrackingType {
  idAplyProcessTraceSummaryInfo?: string;
  /**
 *ICORE-AGR-AN COMMENT 创建人
 */
  createdBy?: string;
  /**
 *ICORE-AGR-AN COMMENT 更新人
 */
  updatedBy?: string;
  /**
 *创建时间
 */
  createdDate?: string;
  /**
 *更新时间
 */
  updatedDate?: string;
  /**
 *录入时间
 */
  inputDate?: string;
  /**
 *提交核保时间
 */
  underwriteDate?: string;
  /**
 *单号
 */
  voucherNo?: string;
  /**
 *单号类型（0?:风险申请单1?:投保单(原始保单)2?:保单）
 */
  voucherType?: string;
  /**
 *投保申请单号
 */
  preApplyNo?: string;
  /**
 *投保单号
 */
  applyPolicyNo: string;
  applyApplyFormNo: string;
  /**
 *出单机构编码
 */
  departmentCode?: string;
  /**
 *出单机构名称
 */
  departmentName?: string;
  departmentNoAndName?: string;
  /**
 *出单业务员名称
 */
  employeeNames?: string;
  /**
 *市场产品编码
 */
  marketProductCode?: string;
  /**
 *市场产品名称
 */
  marketProductName?: string;
  /**
 *标的类型
 */
  riskType?: string;
  /**
 *是否续保
 */
  renewalType?: string;
  /**
 *补贴类型
 */
  govSubsidyType?: string;
  /**
 *大流程状态
 */
  applyStatus: string;
  /**
 *关联清单标识(1已关联0未关联)
 */
  associatedListFlag?: string[];
  /**
 *清单编号(默认 空字符串)
 */
  farmersListBatchNo?: string;
  /**
 *验标状态(C0?:未验标 C1?:已验标)
 */
  insrCheckStatus?: string[];
  /**
 *被保险人名称 （多个被保险人用隔开）
 */
  insuredName?: string;
  /**
 *保险起期
 */
  insuranceBeginDate?: string;
  /**
 *保险止期
 */
  insuranceEndDate?: string;
  /**
 *保险金额
 */
  insuredAmount?: string;
  /**
 *保费金额
 */
  premium?: string;
  /**
 *标的地址（省）
 */
  riskProvince?: string;
  /**
 *标的地址（市）
 */
  riskCity?: string;
  /**
 *标的地址（区）
 */
  riskCounty?: string;
  /**
 *标的地址（镇）
 */
  riskTown?: string;
  /**
 *标的地址（全地址）
 */
  riskAddress?: string;
  /**
 * 录入开始时间
 */
  inputStartTime?: string;
  /**
 * 录入结束时间
 */
  inputEndTime?: string;
  /**
 * 提交核保开始时间
 */
  quoteStartTime?: string;
  /**
 * 提交核保结束时间
 */
  quoteEndTime?: string;
  // 投保单进度
  applyNotificationStatusCode?: string;
  // 币种
  currencyCode?: string;
  insureDepartmentNo?: string;
  salemanName?: string;
  disabled?: boolean;
  // 保单号
  policyNo?: string;
  // 批单号
  endorseNo?: string;
}
type RangeValue = [Dayjs, Dayjs];
export interface formDataType {
  departmentCode: string;
  containChildDepart: boolean;
  riskProvince?: string;
  riskCity?: string;
  riskCounty?: string;
  riskTown?: string;
  riskVillage?: string;
  riskType?: string;
  marketProductCode?: string;
  insrCheckStatus: string[];
  associatedListFlag?: string[];
  insurantNames?: string;
  govSubsidyType?: undefined | string;
  quoteEndTime?: string;
  createdDate?: RangeValue;
  renewalType?: string;
  voucherType?: string | undefined;
  voucherNo?: string | undefined;
  applyStatus?: Array<string>;
  insuranceBeginDateRange?: RangeValue;
  insuranceEndDate?: string;
}
export interface dataType {
  records: insuranceTrackingType[];
  total: number;
  current: number;
  size: number;
}
export interface OptionsType {
  value: string;
  label: string;
}

export interface ChildrenType {
  [key: string]: string;
  children: ChildrenType[];
  idParmBaseConstantConf: number;
  label: string;
  disabled: boolean;
  value: string;
}
