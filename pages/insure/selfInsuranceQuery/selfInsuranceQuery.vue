<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="searchForm" :colon="false" class="flex-grow" :model="searchFormState" :label-col="{ style: { width: pxToRem(80) } }">
          <a-row>
            <a-col span="10">
              <a-form-item label="机构" name="departmentCode" required>
                <department-search v-model:contain-child-depart="searchFormState.containChildDepart" :dept-code="searchFormState.departmentCode" :show-child-depart="true" @change-dept-code="(val: string) => changeDeptCode(val)" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="补贴分类" name="governmentSubsidyTypeCode">
                <a-select v-model:value="searchFormState.governmentSubsidyTypeCode" :options="subsidyTypeOption" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="10">
              <a-form-item label="市场产品" name="marketProductNo">
                <ProductSelect v-model:value="searchFormState.marketProductNo" :department-code="searchFormState.departmentCode" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="状态" name="checkStatusCode">
                <a-select v-model:value="searchFormState.checkStatusCode" :options="statusOption" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button type="primary" @click="search">查询</a-button>
        <a-button type="default" @click="add">新建</a-button>
        <!-- <a-button type="default">导入</a-button> -->
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="text-[#404442] text-xl font-bold">查询结果</div>
      <a-table
        :columns="listColumns"
        :pagination="pagination"
        :data-source="dataSource"
        :loading="loading"
        :scroll="{ x: 'max-content' }"
        row-key="idPlySummaryInfo"
        :row-selection="rowSelection"
        :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'table-striped' : undefined)"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'operation'">
            <div>
              <a-button type="link" @click="look(record.templateId)">查看</a-button>
              <a-button type="link" @click="edit(record.templateId)">修改</a-button>
              <!-- <a-button type="link">审核</a-button> -->
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnsType, TableProps } from 'ant-design-vue';
import type { SearchFormState, DataType } from './selfInsuranceQuery';
import type { insuranceTrackingType } from '@/pages/insure/insuranceTracking/insuranceTracking.d';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import { pxToRem } from '@/utils/tools';

const listColumns: TableColumnsType = [
  { title: '机构', dataIndex: 'departmentCodeAndName' },
  { title: '补贴分类', dataIndex: 'governmentSubsidyTypeCodeCN' },
  { title: '市场产品', dataIndex: 'marketProductName' },
  { title: '状态', dataIndex: 'checkStatusCodeCN' },
  { title: '申请人', dataIndex: 'createdBy' },
  { title: '提交时间', dataIndex: 'createdDate' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];
const statusOption = [
  {
    value: '0',
    label: '审核通过',
  },
  {
    value: '1',
    label: '审核驳回',
  },
];
// 1-中央补贴、2-地方补贴、3-商业性、4-其他补贴
const subsidyTypeOption = [
  {
    value: '1',
    label: '中央补贴',
  },
  {
    value: '2',
    label: '地方补贴',
  },
  {
    value: '3',
    label: '商业性',
  },
  {
    value: '4',
    label: '其他补贴',
  },
];
const router = useRouter();
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
const searchForm = ref();
const searchFormState = reactive<SearchFormState>({
  departmentCode: defaultDeptCode.value, // 机构
  containChildDepart: true, // 是否包含下级
  marketProductNo: '', // 产品编码
  checkStatusCode: '', // 审核状态
  governmentSubsidyTypeCode: '', // 补贴类型
});
const { gateWay, service } = useRuntimeConfig().public || {};
// 改变机构
const changeDeptCode = (val: string) => {
  searchFormState.departmentCode = val;
};
const dataSource = ref<Record<string, string>[]>([]);
const loading = ref(false);
// 查询
const search = async () => {
  try {
    await searchForm.value.validate();
    pagination.current = 1;
    pagination.pageSize = 10;
    refresh();
  } catch (e) {
    console.log(e);
  }
};
const getListReq = await usePost<DataType>(`${gateWay}${service.accept}/web/mobile/market/pageQueryProductTemplate`);
// 保单列表分页查询
const refresh = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchFormState,
      containChildDepart: searchFormState.containChildDepart ? '1' : '',
      marketProductNo: searchFormState.marketProductNo || '',
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };
    const res = await getListReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data || [];
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      message.warning(res?.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const { pagination } = usePagination(refresh);
// 表格的select选择框
type Key = string | number;
const rowSelection: TableProps['rowSelection'] = {
  onChange: (selectedRowKeys: Key[], selectedRows: insuranceTrackingType[]) => onSelectChange(selectedRowKeys, selectedRows),
};
  // 表格选中ID
const selectedRow = ref<insuranceTrackingType[]>([]);
const selectedRowKey = ref<Key[]>([]);
// 选择数据
const onSelectChange = (selectedRowKeys: Key[], selectedRows: insuranceTrackingType[]) => {
  selectedRow.value = selectedRows;
  selectedRowKey.value = selectedRowKeys;
};
// 新建跳转到自助投保配置页面
const add = () => {
  router.push({
    name: 'selfInsurance',
  });
};
// 查看
const look = (templateId: string) => {
  router.push({
    name: 'selfInsurance',
    query: {
      templateId,
      type: 'look',
    },
  });
};
// 编辑
const edit = (templateId: string) => {
  router.push({
    name: 'selfInsurance',
    query: {
      templateId,
    },
  });
};
onActivated(() => {
  refresh();
});
</script>
