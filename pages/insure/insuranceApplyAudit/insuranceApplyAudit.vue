<template>
  <div class="p-14px space-y-14px">
    <div class="bg-white p-16px rounded-md">
      <div class="flex">
        <a-form :colon="false" class="flex-grow">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item
                label="机构"
                :label-col="{ style: { width: pxToRem(65) } }"
                v-bind="validateInfos.departmentCode"
              >
                <department-search
                  v-model:contain-child-depart="formData.containChildDepart"
                  :dept-code="formData.departmentCode"
                  :show-child-depart="true"
                  @change-dept-code="changeDeptCode"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand">
            <a-col :span="24">
              <a-form-item
                label="标的地址"
                :label-col="{ style: { width: pxToRem(65) } }"
              >
                <region-select
                  v-model:province="formData.province"
                  v-model:city="formData.city"
                  v-model:county="formData.county"
                  v-model:town="formData.town"
                  v-model:village="formData.village"
                  class="w-[70%]"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand">
            <a-col :span="24">
              <a-form-item
                label="标的"
                :label-col="{ style: { width: pxToRem(65) } }"
              >
                <RiskCodeSelect
                  v-model:value="formData.riskCode"
                  :department-code="formData.departmentCode"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="16">
            <a-col :span="10">
              <a-form-item
                label="产品"
                :label-col="{ style: { width: pxToRem(65) } }"
              >
                <ProductSelect
                  v-model:value="formData.productCode"
                  :department-code="formData.departmentCode"
                  :encode-key="formData.riskCode"
                  module="accept"
                />
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item label="投保申请单号">
                <a-input v-model:value="formData.insuredApplyNo" />
              </a-form-item>
            </a-col>
            <a-col :span="7">
              <a-form-item label="创建人UM">
                <a-input v-model:value="formData.createUM" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-show="expand" :gutter="16">
            <a-col :span="24">
              <a-form-item
                label="申请状态"
                :label-col="{ style: { width: pxToRem(65) } }"
              >
                <CheckBoxGroup
                  v-model:checked-list="formData.applyStatus"
                  :options="applyStatusOption"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="w-[50px]"><form-fold v-model="expand" /></div>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button @click="resetForm">重置</a-button>
        <a-button type="primary" ghost @click="submit">查询</a-button>
      </div>
    </div>
    <div class="bg-white p-16px rounded-md">
      <div class="mb-16px">
        <div class="text-[#404442] text-xl font-bold">查询结果</div>
      </div>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :scroll="{ x: 'max-content' }"
      >
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex === 'status'">
            <a-tag v-if="text === '1'" :bordered="false" color="warning">
              待修改
              <template #icon>
                <VueIcon :icon="IconErrorCircleFilledFont" />
              </template>
            </a-tag>
            <a-tag v-if="text === '2'" :bordered="false" color="default">
              已下发
              <template #icon>
                <VueIcon :icon="IconTimeCircleFilledFont" />
              </template>
            </a-tag>
            <a-tag v-if="text === '3'" :bordered="false" color="processing">
              审核通过
              <template #icon>
                <VueIcon :icon="IconCheckCircleFilledFont" />
              </template>
            </a-tag>
            <a-tag v-if="text === '4'" :bordered="false" color="error">
              已拒绝
              <template #icon>
                <VueIcon :icon="IconCloseCircleFilledFont" />
              </template>
            </a-tag>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <div class="flex items-center justify-center">
              <a-button type="link" size="small">审核</a-button>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IconErrorCircleFilledFont, IconCheckCircleFilledFont, IconTimeCircleFilledFont, IconCloseCircleFilledFont } from '@pafe/icons-icore-agr-an';
import Form from 'ant-design-vue/es/form/Form';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import RegionSelect from '@/components/selector/RegionSelect.vue';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import FormFold from '@/components/ui/FormFold.vue';
import ProductSelect from '@/components/selector/ProductSelect.vue';

import { pxToRem } from '@/utils/tools';
import { $post } from '@/composables/request';

const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));

const applyStatusOption = [
  { value: '1', label: '待审核' },
  { value: '2', label: '审核中' },
  { value: '3', label: '审核通过' },
  { value: '4', label: '已下发' },
  { value: '5', label: '已拒绝' },
];

const formData = reactive({
  departmentCode: defaultDeptCode.value,
  containChildDepart: true,
  province: '',
  city: '',
  county: '',
  town: '',
  village: '',
  riskCode: '',
  productCode: '',
  insuredApplyNo: '',
  createUM: '',
  applyStatus: [],
});

const formRules = reactive({
  departmentCode: [
    {
      required: true,
      message: '请选择机构',
    },
  ],
  checkStatus: [
    {
      required: true,
      message: '请选择验标状态',
    },
  ],
  hasInsuredList: [
    {
      required: true,
      message: '请选择是否有投保清单',
    },
  ],
  status: [
    {
      required: true,
      message: '请选择状态',
    },
  ],
});

const { validateInfos } = Form.useForm(
  formData,
  formRules,
);

const expand = ref(true);

const columns = [
  {
    title: '出单机构',
    dataIndex: 'department',
  },
  {
    title: '单号',
    dataIndex: 'insuranceNo',
  },
  {
    title: '标的',
    dataIndex: 'tagName',
  },
  {
    title: '区域',
    dataIndex: 'address',
  },
  {
    title: '发起人',
    dataIndex: 'sponsor',
  },
  {
    title: '承保数量',
    dataIndex: 'insureQuantity',
  },
  {
    title: '保额',
    dataIndex: 'insuredFee',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '操作',
    dataIndex: 'action',
  },
];

const dataSource = ref([]);

const changeDeptCode = (newVal: string) => {
  formData.departmentCode = newVal;
};

const resetForm = () => {};

const submit = () => {
  refresh();
};

const refresh = async () => {
  const response = await $post('/api/accept/getInsureApplyList');
  dataSource.value = response?.data || [];
};
</script>

<style lang="less">
.no-arrow-popover.process-popover {
  .ant-popover-content {
    left: -40px;
  }
}

.no-arrow-popover.modification-popover {
  .ant-popover-content {
    left: -20px;
  }
}
</style>
