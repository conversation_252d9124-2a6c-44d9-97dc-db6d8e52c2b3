<template>
  <a-form ref="formRef" :model="list">
    <a-table :columns="columns" :data-source="list" :pagination="false">
      <template #bodyCell="{ column, record, index }">
        <!-- 共保公司代码 -->
        <template v-if="column.key === 'reinsureCompanyCode'">
          {{ record.reinsureCompanyCode }}
        </template>
        <!-- 共保公司名称 -->
        <template v-if="column.key === 'reinsureCompanyName'">
          <a-input v-if="record.reinsureCompanyCode === '3005'" v-model:value="record.reinsureCompanyName" :style="{ width: pxToRem(296) }" placeholder="请输入" disabled />
          <a-form-item v-else :name="[index, 'reinsureCompanyCode']" :rules="[{ required: true, message: '请选择共保公司' }]">
            <a-select
              v-model:value="list[index].reinsureCompanyCode"
              show-search
              allow-clear
              :style="{ width: pxToRem(296) }"
              :filter-option="filterOption"
              @change="(value, option) => companyChange(record, option, index)"
            >
              <template v-for="option in companyList" :key="option.companyCode">
                <a-select-option :value="option.companyCode" :title="option.companyName" :disabled="list.filter(item => item.reinsureCompanyCode === option.companyCode)?.length > 0">
                  {{ option.companyName }}
                </a-select-option>
              </template>
            </a-select>
          </a-form-item>
        </template>
        <!-- 保险金额 -->
        <template v-if="column.key === 'insuredAmount'">
          {{ record.insuredAmount || 0 }}
        </template>
        <!-- 保费金额 -->
        <template v-if="column.key === 'premium'">
          {{ record.premium || 0 }}
        </template>
        <!-- 共保比例(%) -->
        <template v-if="column.key === 'reinsureScale'">
          <a-form-item :name="[index, 'reinsureScale']" :rules="[{ validator: reinsureScaleSumValidate }]">
            <a-input v-model:value="list[index].reinsureScale" :style="{ width: pxToRem(100) }" placeholder="请输入" @change="reinsureScaleChange" />
          </a-form-item>
        </template>
        <!-- 是否主承保 -->
        <template v-if="column.key === 'isAcceptInsuranceFlag'">
          <a-form-item :name="[index, 'acceptInsuranceFlag']" :rules="[{ validator: acceptInsuranceFlagValidate }]">
            <a-switch v-model:checked="list[index].acceptInsuranceFlag" checked-value="1" un-checked-value="0" :disabled="record.reinsureCompanyCode === '3005' || list?.[0]?.acceptInsuranceFlag === '1'" @click="switchFlag" />
          </a-form-item>
        </template>
        <!-- 操作列 -->
        <template v-if="column.key === 'action' && index > 0">
          <a-button type="link" size="small" @click="deleteRow(record.reinsureCompanyCode, index)">删除</a-button>
        </template>
      </template>
    </a-table>
  </a-form>
  <div class="mt-[12px] mb-[20px] text-[#576B95]">
    <div class="flex items-center space-x-[4px] cursor-pointer w-fit" @click="addRow()">
      <VueIcon :icon="IconTongyongXinzengFont" />
      <span>新增共保公司</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IconTongyongXinzengFont } from '@pafe/icons-icore-agr-an';
import type { DefaultOptionType } from 'ant-design-vue/es/select';
import type { Rule } from 'ant-design-vue/es/form/interface';
import type { Company, CoinsuranceDetail } from './coinsuranceInfo';
import { pxToRem, add } from '@/utils/tools';

// props
defineProps<{
  companyList: Company[];
}>();

// v-model
const data = defineModel<CoinsuranceDetail[]>({ default: [] });

// 系统外共保列表计算属性
const list = computed(() => {
  return data.value?.filter(item => item.coinsuranceType === '0');
});

// a-form ref
const formRef = ref();

// 已新增的共保公司，新增共保公司时会过滤这些公司，中国平安 3005 默认在companys中
const companys = ref(list.value && list.value.length && list.value[0].reinsureCompanyCode ? [list.value[0].reinsureCompanyCode] : []);

// 表头配置
const columns = [
  { title: '共保公司代码', key: 'reinsureCompanyCode' },
  { title: '共保公司名称', key: 'reinsureCompanyName' },
  { title: '保险金额', key: 'insuredAmount' },
  { title: '保费金额', key: 'premium' },
  { title: '共保比例(%)', key: 'reinsureScale' },
  { title: '是否主承保', key: 'isAcceptInsuranceFlag' },
  { title: '操作', key: 'action' },
];

const filterOption = (input: string, option: { title: string }) => {
  return option.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 共保公司下拉框change事件：选择共保公司
const companyChange = (record: CoinsuranceDetail, option: DefaultOptionType, index: number) => {
  record.reinsureCompanyName = option?.companyName;
  companys.value[index] = option.companyCode;
};

// switch组件点击事件：切换主承公司 更新校验结果
const switchFlag = () => {
  list.value.forEach((item, index) => {
    formRef?.value?.validate([[index, 'acceptInsuranceFlag']]);
  });
  // const mainIndex = data.value.findIndex((item: CoinsuranceDetail) => item.coinsuranceType === '0');
  // data.value.forEach((item: CoinsuranceDetail, itemIndex: number) => {
  //   if (item.coinsuranceType === '0' && itemIndex !== index + mainIndex) {
  //     item.acceptInsuranceFlag = '0';
  //   }
  // });
};

// 新增共保
const addRow = () => {
  // 系统外共保信息对象
  const detail = {
    reinsureCompanyCode: '',
    reinsureCompanyName: '',
    insuredAmount: '0',
    premium: '0',
    reinsureScale: '',
    acceptInsuranceFlag: '0',
    coinsuranceType: '0',
  };
  data.value.push(detail);
};

// 删除行
const deleteRow = (code: string, index: number) => {
  // 删除行
  if (data.value && data.value.length) {
    data.value.splice(index, 1);
  }
  // 在缓存的新增公司数组中删除指定行公司code
  companys.value = companys.value.filter((val: string) => val !== code);
};

// 共保比例和计算校验，加起来要等于100%
const reinsureScaleSumValidate = (rule: Rule, value: string) => {
  if (!value && value !== '0') {
    return Promise.reject('不能为空');
  }
  const regex = /^\d+(\.\d{1,4})?$/;

  if (!regex.test(value)) {
    return Promise.reject('请输入最多4位小数的数值');
  }

  let sum = '0';

  list.value.forEach((item: CoinsuranceDetail) => {
    if (item.reinsureScale) {
      sum = add(item.reinsureScale, sum);
    }
  });

  if (!sum) {
    return Promise.reject('不能为空');
  }
  if (Number(sum) !== 100) {
    return Promise.reject('共保比例的和不等100');
  }

  return Promise.resolve();
};

// 共保比例改变时校验
const reinsureScaleChange = () => {
  list.value.forEach((item, index) => {
    formRef?.value?.validate([[index, 'reinsureScale']]);
  });
};

// 表单校验
const validate = async () => {
  if (formRef?.value) {
    try {
      await formRef?.value.validateFields();
      return { valid: true, errors: [] };
    } catch (errors) {
      return { valid: false, errors };
    }
  }
};
defineExpose({ validate });

// 主承校验
const acceptInsuranceFlagValidate = () => {
  const acceptInsuranceArr = list.value.filter((item: CoinsuranceDetail) => item.acceptInsuranceFlag === '1');
  if (acceptInsuranceArr.length !== 1) {
    return Promise.reject('必须有且仅有一个主承保');
  }
  return Promise.resolve();
};
</script>
