<template>
  <a-modal v-model:open="visible" title="公示信息" :width="pxToRem(700)" :mask-closable="false" centered :style="{ 'max-height': pxToRem(1000) }" :confirm-loading="publicLoading" @ok="handleOk">
    <div v-if="listData?.length > 0" class="mb-[18px]">
      <div v-for="(item, i) in listData" :key="i" class="bg-gray-100 rounded p-14px box-border mb-8px">
        <div class="mb-[18px] shrink-0 box-border">
          <span>公示方式：</span>
          <a-checkbox v-model:checked="item.checked" :disabled="item.requiredFlag === 'Y'" class="text-[#262626]" />
          {{ item.publicityMethodDesc }}
        </div>
        <div class="mb-[18px] shrink-0 box-border flex items-center">
          <span v-if="item.publicityMethodCode !== 'PA_OFFLINE'" class="inline-block w-[90px]">公示起止期：</span>
          <div v-if="item.publicityMethodCode !== 'PA_OFFLINE'">
            <span class="text-[#191919]">{{ item.beginDate }}</span>
            <span class="px-[10px]">至</span>
            <span class="text-[#191919]">{{ item.endDate }}</span>
          </div>
        </div>
        <div v-if="item.publicityMethodCode !== 'PA_OFFLINE'" class="m-0 shrink-0 box-border">
          <span>公示天数：</span>
          <span class="text-[#191919]">{{ item.publicityDays || '-' }}天</span>
        </div>
      </div>
    </div>
    <a-empty v-else :image="simpleImage" />
    <div class="text-[rgba(0,0,0,0.55)] text-[14px]">注：线下公示需要开始和结束时拍摄照片并上传到影像资料中</div>
  </a-modal>
</template>

<script setup lang="ts">
import { Empty } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';
import type { PublicityData } from '../insuranceFormFill';
import { pxToRem } from '@/utils/tools';

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const visible = defineModel<boolean>('visible', { required: true, default: false });
const publicLoading = defineModel<boolean>('publicLoading', { required: true, default: false });
const listData = defineModel<PublicityData[]>('listData', { default: [] });
// 公示日期
const emit = defineEmits(['handlePublicOk']);
const handleOk = () => {
  // 已勾选的公示需要提交到后端
  const infoData = cloneDeep(listData.value).filter(item => !!item.checked);
  const index = infoData.findIndex(item => item.publicityMethodCode === 'PA_OFFLINE');
  if (index > -1) {
    // 如果我司线下未勾选，天数及日期传空字符窜给后端
    if (!infoData[index]?.checked) {
      emit('handlePublicOk', infoData);
    } else {
      infoData[index].requiredFlag = 'Y';
      emit('handlePublicOk', infoData);
    }
  } else {
    emit('handlePublicOk', infoData);
  }
};
</script>
