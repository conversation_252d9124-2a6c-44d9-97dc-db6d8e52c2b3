<template>
  <a-modal
    v-model:open="visible"
    title="请选择"
    :width="pxToRem(480)"
    :mask-closable="false"
    :confirm-loading="confirmLoading"
    centered
    @ok="handleOk"
  >
    <div>
      <a-radio-group v-model:value="extendInfo.informSignType" class="w-full">
        <div v-if="showWindowType === '2'" class="bg-gray-100 rounded p-14px box-border mb-12px">
          <a-radio class="w-[100%]" value="03">发送短信验证码</a-radio>
        </div>
        <div class="bg-gray-100 rounded p-14px box-border mb-12px">
          <a-radio class="w-[100%]" value="01">
            <p class="m-0 text-[rgba(0,0,0,0.8)]">电子签名</p>
            <p class="m-0 pt-[5px] text-[rgba(0,0,0,0.55)] text-[12px]">通过短信发送告知，请通知投保人完成投保确认动作</p>
          </a-radio>
        </div>
        <div class="bg-gray-100 rounded p-14px box-border flex align-center">
          <a-radio class="w-[100%]" value="02">
            <p class="m-0 text-[rgba(0,0,0,0.8)]">纸质盖章/签名</p>
            <p class="m-0 pt-[5px] text-[rgba(0,0,0,0.55)] text-[12px]">若选择纸质盖章/签名，请确保线下纸质材料完成盖章</p>
          </a-radio>
        </div>
      </a-radio-group>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import type { InsuranceNoticeType } from '../insuranceFormFill';
import { pxToRem } from '@/utils/tools';

// 显示
const visible = defineModel<boolean>('visible', { required: true, default: false });
// 单选
const extendInfo = defineModel<InsuranceNoticeType>('extendInfo', { required: true, default: {} });
// 确认loading
const confirmLoading = defineModel<boolean>('informLoading', { required: true, default: false });
const { showWindowType } = defineProps<{
  showWindowType: string;
}>();
const emit = defineEmits(['handleGetInformOk']);
// 提交
const handleOk = () => {
  if (!extendInfo.value?.informSignType) {
    message.warning('请选择');
  } else {
    emit('handleGetInformOk');
  }
};
</script>
