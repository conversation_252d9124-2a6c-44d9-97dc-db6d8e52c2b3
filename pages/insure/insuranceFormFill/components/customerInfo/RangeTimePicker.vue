<template>
  <div class="grid grid-cols-3 gap-x-16px text-[rgba(0,0,0,0.60)]">
    <a-form-item label="证件有效起期" :label-col="labelColStyle" :required="required">
      <a-date-picker v-model:value="startTime" :disabled-date="disabledStartDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
    </a-form-item>
    <a-form-item label="证件有效止期" :label-col="labelColStyle" :required="required">
      <a-date-picker v-model:value="endTime" :disabled-date="disabledEndDate" :disabled="disabled" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
    </a-form-item>
    <div class="flex items-center">
      <a-checkbox v-model:checked="checked" @change="handleChange">长期</a-checkbox>
    </div>
    <!-- 二次确认弹窗 -->
    <a-modal v-model:open="openVisible" :width="pxToRem(450)">
      <div class="mb-[10px]">
        <VueIcon class="text-[18px] text-[#FAAD14]" :icon="IconErrorCircleFilledFont" />
        <span class="font-bold text-[rgba(0,0,0,0.9)] text-[16px] ml-[5px]">提醒</span>
      </div>
      <div> 证件有效期勾选为长期有效，请根据客户证件核实确认！ </div>
      <template #footer>
        <a-button type="primary" @click="openVisible =false">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { CheckboxChangeEvent } from 'ant-design-vue/es/checkbox/interface';
import { IconErrorCircleFilledFont } from '@pafe/icons-icore-agr-an';
import { pxToRem } from '@/utils/tools';

defineProps<{
  required?: boolean; // 是否展示必选
}>();
const startTime = defineModel<string>('startTime', { default: '' });
const endTime = defineModel<string>('endTime', { default: '' });
const openVisible = ref<boolean>(false);
const checked = ref<boolean>(false);

const disabled = ref<boolean>(false);

// 证件有效期限起期时间范围
const disabledStartDate = (currentDate: Dayjs) => {
  return currentDate && (currentDate < dayjs('1949-10-01') || currentDate > dayjs());
};

// 证件有效期限止期时间范围
const disabledEndDate = (currentDate: Dayjs) => {
  return currentDate && (currentDate > dayjs('9999-12-31') || currentDate < dayjs().subtract(1, 'day'));
};

// 长期切换 选择长期需要把起期时间设置为1949-10-01并且不可选择
const handleChange = (e: CheckboxChangeEvent) => {
  if (e?.target?.checked) {
    disabled.value = true;
    endTime.value = '9999-12-31';
    openVisible.value = true;
  } else {
    disabled.value = false;
  }
};

const labelColStyle = {
  style: {
    width: pxToRem(100),
  },
};
watch(() => endTime.value, () => {
  // 结束时间为9999-12-31 设置为长期
  if (endTime.value === '9999-12-31') {
    checked.value = true;
    disabled.value = true;
  } else {
    checked.value = false;
    disabled.value = false;
  }
});
onMounted(() => {
  // 结束时间为9999-12-31 设置为长期
  if (endTime.value === '9999-12-31') {
    checked.value = true;
    disabled.value = true;
  }
});
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
  margin-bottom: 0px;
}
</style>
