export interface taskInfo {
  departmentNo: string;
  containChildDepart: boolean;
  productCode: string;
  riskType: string | undefined;
  taskCategoryCode: string[];
  bizNo: string;
  taskNameCode: string[];
  taskLevel: string[];
  progress: string[];
  taskStatus: string[];
}

export interface taskList {
  label: string;
  bizNo: string;
  taskCategory: string;
  taskName: string | undefined;
  taskStatus: string;
  taskStatusCode: '1' | '2' | '3' | '4';
  progress: string;
  progressCode: 'overdue' | 'deadLine' | 'normal';
  dueDateDay: string[];
}

export interface taskListRes {
  total: number;
  current: number;
  size: number;
  pages: number;
  records: taskList[];
}

export interface optType {
  label: string;
  value: string;
  children: optType[];
}
