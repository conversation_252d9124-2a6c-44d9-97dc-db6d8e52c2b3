<template>
  <a-modal width="480px" :visible="visible" title="改派" @cancel="visible = false">
    <div class="mt-[14px]">
      <a-form :colon="false" :label-col="{ style: { width: pxToRem(80) } }">
        <a-row justify="center">
          <a-col :span="19">
            <a-form-item label="原验标人">
              <a-input disabled />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row justify="center">
          <a-col :span="19">
            <a-form-item label="新验标人" required>
              <a-select show-search allow-clear placeholder="请输入姓名或者人员类型检索" :options="selectOptions">
                <template #suffixIcon>
                  <VueIcon :icon="IconSearchFont" />
                </template>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { IconSearchFont } from '@pafe/icons-icore-agr-an';
import { pxToRem } from '@/utils/tools';

const visible = defineModel<boolean>('visible', { default: false });
const selectOptions = ref([]);
</script>
