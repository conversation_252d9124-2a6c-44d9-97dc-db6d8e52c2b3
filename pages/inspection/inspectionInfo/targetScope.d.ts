import type { PhotoListType } from './components.d';

export interface QueryCheckFarmerShapeListReqParams {
  farmerId?: string;
  insrCheckTaskNo: string;
}
export interface Farmer {
  farmerName: string;
  areaInMu: string;
  collectionAreaMu: string;
  wgs84Shape: number[];
}
export interface LineGeometry {
  type: string;
  coordinates: number[][];
}
export interface LineFeature {
  type: string;
  geometry: LineGeometry;
}
export interface TextGeometry {
  type: string;
  coordinates: string[];
}
export interface TextFeature {
  type: string;
  geometry: TextGeometry;
}
export interface PolygonOptions {
  lineColor?: string;
  lineWidth?: number;
  lineDasharray?: number[];
}
export interface TextPointOptions {
  color?: string;
  justify?: 'center' | 'left' | 'right'; // 文本对齐
  anchor?: 'center' | 'left' | 'right' | 'top' | 'bottom' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'; // 文案那一方向靠近锚点
}

export interface AllCheckFile extends PhotoListType {
  latitude?: number;
  longitude?: number;
}

export interface KmeansFeature {
  geometry: {
    coordinates: number[];
  };
  properties: {
    cluster: number;
  };
}
export interface AllCheckFileListKmeans {
  feature: KmeansFeature;
  id?: string;
  fileList?: AllCheckFile[];
  marker?: unknown;
}
