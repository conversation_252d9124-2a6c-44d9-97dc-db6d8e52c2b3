export interface SearchFormState {
  departmentCode: string; // 机构编码
  containLowDepartment?: boolean; // 是否包含下级机构
  lastRiskType: string; // 标的类型
  insureDate: RangeValue; // 保单生成日期
  riskCheckStatus: string[]; // 验标状态
  documentNo: string; // 业务号
  documentType: string[]; // 业务类型
  insuredName: string; // 被保险人名称
  mpPlReportStatus: string | undefined; // 产品条款报备状态
}
export interface DataType {
  records: Record<string, string>[];
  total: number;
  current: number;
  size: number;
}
// 初始化类型
export interface Option {
  value: string;
  label: string;
}
