<template>
  <div class="m-14px space-y-16px">
    <div class="bg-white rounded p-16px">
      <div class="flex">
        <a-form ref="searchForm" :colon="false" class="flex-grow" :model="searchFormState" :label-col="{ style: { width: pxToRem(80) } }">
          <a-row>
            <a-col span="10">
              <a-form-item label="机构" name="departmentCode" required>
                <department-search v-model:contain-child-depart="searchFormState.containLowDepartment" :dept-code="searchFormState.departmentCode" :show-child-depart="true" @change-dept-code="(val: string) => changeDeptCode(val)" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="保单生成日期" name="insureDate" :label-col="{ style: { width: pxToRem(110) } }">
                <a-range-picker v-model:value="searchFormState.insureDate" :disabled="disabled" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row v-if="expand" :gutter="16">
            <a-col :span="24">
              <a-form-item label="标的" name="lastRiskType">
                <RiskCodeSelect v-model:value="searchFormState.lastRiskType" :department-code="searchFormState.departmentCode" :disabled="disabled" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="0">
            <a-col :span="14">
              <a-form-item label="单号" name="documentNo">
                <a-input-group>
                  <a-row :gutter="8">
                    <a-col :span="5">
                      <a-select v-model:value="searchFormState.documentType" :options="voucherTypeOpitons" :style="{ width: '100%' }" allow-clear placeholder="请选择" />
                    </a-col>
                    <a-col :span="19">
                      <a-input v-model:value.trim="searchFormState.documentNo" placeholder="请输入" @blur="handleBlurDisabled" />
                    </a-col>
                  </a-row>
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="被保险人" name="insuredName">
                <a-input v-model:value="searchFormState.insuredName" placeholder="请输入" allow-clear :disabled="disabled" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="验标状态" name="riskCheckStatus" required>
                <check-box-group v-model:checked-list="searchFormState.riskCheckStatus" :options="publicStatusOptions" :disabled="disabled" />
              </a-form-item>
            </a-col>
            <a-col span="12">
              <a-form-item label="产品报备状态" name="mpPlReportStatus" :label-col="{ style: { width: pxToRem(110) } }">
                <a-radio-group v-model:value="searchFormState.mpPlReportStatus" :options="mpPlReportStatusOptions" :disabled="disabled" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="w-[50px]">
          <form-fold v-model="expand" />
        </div>
      </div>
      <div class="flex justify-center items-center space-x-8px">
        <a-button type="default" @click="reset">重置</a-button>
        <a-button type="primary" @click="search">查询</a-button>
      </div>
    </div>
    <div class="bg-white rounded p-16px">
      <!-- 表格头部 -->
      <div class="mb-16px flex justify-between">
        <div class="text-[#404442] text-xl font-bold">查询结果</div>
        <div>
          <a-button v-if="showOneToOneBtn" class="ml-[12px]" type="primary" @click="handlePrintOne">一户一保打印</a-button>
          <a-button v-if="showOneToOneBtn" class="ml-[12px]" type="primary" @click="handlePrintFile">凭证文件下载</a-button>
        </div>
      </div>
      <a-table :columns="listColumns" :pagination="pagination" :data-source="dataSource" :loading="loading" :scroll="{ x: 'max-content' }" row-key="idPlySummaryInfo" :row-selection="rowSelection" :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'table-striped' : undefined)" class="table-box">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'policyNo'">
            <div class="flex items-center">
              <CopyLink :text="text" @click="openDetail(text, record.applyPolicyNo)" />
              <div v-if="record.dataSource === 'icore_agr_icp_self'" class="p-[4px] text-[#0958d9] bg-[#e6f4ff] border-[#91caff] text-[11px] rounded-[12px]" color="blue">自助</div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'insuranceBeginDate'">
            {{ (record.insuranceBeginDate ? record.insuranceBeginDate + ' 至 ' : '') + (record.insuranceEndDate ?? '') }}
          </template>
          <template v-if="['marketproductName', 'insuredName', 'departmentName', 'riskTypeName'].includes(column.dataIndex as string)">
            <a-tooltip placement="topLeft" :title="text" arrow-point-at-center>
              <div class="max-w-[200px] truncate">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <a-space :size="1">
              <a-button type="link" @click="openDetail(record.policyNo, record.applyPolicyNo)">最新保单</a-button>
              <a-button type="link" @click="handlePrint(record)">打印</a-button>
              <a-button type="link" @click="openHistory(record.policyNo)">历次批改</a-button>
              <a-button type="link" @click="handlerClaimsRecord(record.policyNo)">理赔记录</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
      <PrintModal v-model:visible="printVisible" type="02" :select-obj="selectObj as unknown as insuranceTrackingType" />
      <OneToOnePrintModal v-model:visible="onToOnePrintVisible" :select-obj-arr="selectObjArr" :sid="sid" />
      <FilePrintModal v-model:visible="filePrintVisible" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumnsType, TableProps } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { SearchFormState, DataType, Option } from './policyQuery.d';
import type { insuranceTrackingType } from '@/pages/insure/insuranceTracking/insuranceTracking.d';
import PrintModal from '@/pages/insure/insuranceTracking/components/PrintModal.vue';
import OneToOnePrintModal from '@/pages/insure/insuranceTracking/components/OneToOnePrintModal.vue';
import FilePrintModal from '@/pages/insure/insuranceTracking/components/FilePrintModal.vue';
import { useUserStore } from '@/stores/useUserStore';
import DepartmentSearch from '@/components/selector/DepartmentSearch.vue';
import { SUCCESS_CODE } from '@/utils/constants';
import { usePost, $getOnClient } from '@/composables/request';
import { usePagination } from '@/composables/usePagination';
import RiskCodeSelect from '@/components/selector/RiskCodeSelect.vue';
import CheckBoxGroup from '@/components/ui/CheckBoxGroup.vue';
import { pxToRem } from '@/utils/tools';
import CopyLink from '@/components/ui/CopyLink.vue';
import FormFold from '@/components/ui/FormFold.vue';

const listColumns: TableColumnsType = [
  { title: '出单机构', dataIndex: 'departmentName', fixed: 'left' },
  { title: '保单号', dataIndex: 'policyNo' },
  { title: '清单编号', dataIndex: 'farmerListNo' },
  { title: '产品名称', dataIndex: 'marketproductName' },
  { title: '被保险人', dataIndex: 'insuredName' },
  { title: '标的信息', dataIndex: 'riskTypeName' },
  { title: '验标状态', dataIndex: 'checkRiskStatus' },
  { title: '保险起止期', dataIndex: 'insuranceBeginDate' },
  { title: '操作', dataIndex: 'operation', fixed: 'right' },
];
// 验标状态选项
const publicStatusOptions = [
  { label: '未验标', value: '0' },
  { label: '已验标未审核', value: '1' },
  { label: '验标中', value: '2' },
  { label: '验标完成', value: '3' },
];
// 业务号类型选项
const voucherTypeOpitons = [
  { label: '投保单号', value: '1' },
  { label: '保单号', value: '2' },
];
const router = useRouter();
// 默认用户信息
const { userInfo } = useUserStore();
const defaultDeptCode = computed(() => (userInfo ? userInfo.deptList[0] : ''));
const searchForm = ref();
// 搜索项是否展开
const expand = ref<boolean>(false);
const searchFormState = reactive<SearchFormState>({
  departmentCode: defaultDeptCode.value, // 机构
  containLowDepartment: true, // 是否包含下级
  lastRiskType: '', // 标的类型
  insureDate: [dayjs().subtract(1, 'month'), dayjs()], // 保单生成日期
  riskCheckStatus: ['0', '1', '2', '3'], // 验标状态
  documentNo: '', // 业务号
  documentType: [], // 业务类型
  insuredName: '', // 被保险人名称
  mpPlReportStatus: undefined, // 产品条款报备状态 默认全选undefined
});
const { gateWay, service } = useRuntimeConfig().public || {};
const mpPlReportStatusOptions = [
  { label: '待报备', value: '0' },
  { label: '已报备', value: '1' },
  { label: '全部', value: undefined },
];

// 改变机构
const changeDeptCode = (val: string) => {
  searchFormState.departmentCode = val;
};
const dataSource = ref<Record<string, string>[]>([]);
const loading = ref(false);
// 查询
const search = async () => {
  try {
    await searchForm.value.validate();
    pagination.current = 1;
    pagination.pageSize = 10;
    refresh();
  } catch (e) {
    console.log(e);
  }
};
const getListReq = await usePost<DataType>(`${gateWay}${service.policy}/web/policy/manage/list`);
// 保单列表分页查询
const refresh = async () => {
  loading.value = true;
  try {
    const params = {
      manageListReqDTO: {
        ...searchFormState,
        containLowDepartment: searchFormState.containLowDepartment,
        inputStartDate: searchFormState.insureDate?.[0]?.format('YYYY-MM-DD') || '',
        inputEndDate: searchFormState.insureDate?.[1]?.format('YYYY-MM-DD') || '',
        documentType: searchFormState.documentType?.[0] || '',
        mpPlReportStatus: searchFormState.mpPlReportStatus === undefined ? undefined : Number(searchFormState.mpPlReportStatus), // 全部传undefined
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
      },
    };
    const res = await getListReq.fetchData(params);
    if (res && res.code === SUCCESS_CODE) {
      const { records = [], total = 0, current = 1, size = 10 } = res.data || [];
      dataSource.value = records || [];
      pagination.total = total;
      pagination.current = current;
      pagination.pageSize = size;
    } else {
      message.warning(res?.msg);
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};
const { pagination } = usePagination(refresh);
// 重置
const reset = () => {
  searchForm.value?.resetFields();
  searchFormState.documentType = [];
  searchFormState.riskCheckStatus = [];
  if (publicStatusOptions.length > 0) {
    publicStatusOptions.forEach((item: Option) => {
      searchFormState.riskCheckStatus.push(item.value);
    });
  }
  searchForm.value?.clearValidate();
  disabled.value = false;
  refresh();
};
// 打开保单详情页
const openDetail = (policyNo: string, applyPolicyNo: string) => {
  const path = {
    path: '/policyDetails',
    query: {
      policyNo: policyNo,
      applyPolicyNo: applyPolicyNo,
    },
  };
  router.push(path);
};
// 打开批改历史页
const openHistory = (policyNo: string) => {
  router.push({
    name: 'endorseHistory',
    query: {
      policyNo: policyNo,
    },
  });
};
// 点击打印获取需要传给后端的数据
const selectObj = ref<Record<string, string>>({});
// 打印信息弹窗
const printVisible = ref<boolean>(false);
// 一户一凭证弹窗
const onToOnePrintVisible = ref<boolean>(false);
// 凭证文件下载
const filePrintVisible = ref<boolean>(false);
// 点击打印按钮
const handlePrint = (record: Record<string, string>) => {
  printVisible.value = true;
  selectObj.value = record;
};

// 表格的select选择框
type Key = string | number;
const rowSelection: TableProps['rowSelection'] = {
  onChange: (selectedRowKeys: Key[], selectedRows: insuranceTrackingType[]) => onSelectChange(selectedRowKeys, selectedRows),
  getCheckboxProps: (record: insuranceTrackingType) => ({
    disabled: false,
    applyStatus: record.applyStatus,
  }),
};
// 表格选中ID
const selectedRow = ref<insuranceTrackingType[]>([]);
const selectedRowKey = ref<Key[]>([]);
// 选择数据
const onSelectChange = (selectedRowKeys: Key[], selectedRows: insuranceTrackingType[]) => {
  selectedRow.value = selectedRows;
  selectedRowKey.value = selectedRowKeys;
};
const selectObjArr = ref<insuranceTrackingType[]>([]);
const policyNos = ref<string[]>([]);
// 同批次打印任务id
const sid = ref<string>('');
// 合并导出接口
const mergeUrl = gateWay + service.document + '/print/mergePolicy';
const mergeReq = await usePost<string>(mergeUrl);
const getMergePolicy = async () => {
  const params = {
    policyNos: policyNos.value,
  };
  try {
    sid.value = '';
    const res = await mergeReq.fetchData(params);
    if (res?.code === SUCCESS_CODE) {
      sid.value = res.data || '';
      onToOnePrintVisible.value = true;
      selectObjArr.value = selectedRow.value;
    } else {
      message.error(res?.msg);
    }
  } catch (error) {
    console.log(error);
  }
};
// 点击一户一保打印
const handlePrintOne = () => {
  if (!selectedRowKey.value.length) {
    message.warning('请选择至少一条记录');
  } else {
    const isPolicyNo = ref<boolean>(true); // 是否生成了保单
    selectObjArr.value = [];
    policyNos.value = [];
    selectedRow.value.forEach((item) => {
      if (!item.policyNo) {
        isPolicyNo.value = false;
      } else {
        policyNos.value.push(item.policyNo);
      }
    });
    if (!isPolicyNo.value) {
      message.error('没有生成对应的保单号，不能打印');
    } else {
      getMergePolicy();
    }
  }
};
// 凭证文件下载
const handlePrintFile = () => {
  filePrintVisible.value = true;
};
// 一对一打印按钮权限控制，234机构的出单员才能显示
const showOneToOneBtn = computed(() => searchFormState.departmentCode.includes('234'));

// 单号有值时其余搜索条件禁用,交互逻辑同投保跟踪
const disabled = ref<boolean>(false);
const handleBlurDisabled = async () => {
  if (searchFormState.documentNo) {
    disabled.value = true;
    searchFormState.lastRiskType = '';
    searchFormState.insureDate = [];
    searchFormState.riskCheckStatus = ['0', '1', '2', '3'];
    searchFormState.insuredName = '';
    searchFormState.mpPlReportStatus = undefined;
    // 动态获取单号类型，和机构code
    const res = await $getOnClient<Record<string, string>>(gateWay + service.administrate + '/public/getBizTypeByBizNo', { bizNo: searchFormState.documentNo });
    if (res && res?.code === SUCCESS_CODE && res?.data) {
      const isBizType = voucherTypeOpitons.findIndex((item) => item.value === res.data?.bizType);
      if (isBizType !== -1) {
        searchFormState.documentType = [res.data?.bizType];
      }
      searchFormState.departmentCode = res.data?.insureDepartmentNo || '';
    }
  } else {
    disabled.value = false;
    searchFormState.documentType = [];
    searchFormState.departmentCode = defaultDeptCode.value;
    searchFormState.insureDate = [dayjs().subtract(1, 'month'), dayjs()];
  }
};
// 理赔记录
const handlerClaimsRecord = (policyNo: string) => {
  router.push({
    name: 'claimRecord',
    query: { policyNo },
  });
};
const route = useRoute();
onMounted(() => {
  searchFormState.mpPlReportStatus = route.query.mpPlReportStatus === undefined ? undefined : String(route.query.mpPlReportStatus);
  if (route.query.startTime && route.query.endTime) {
    searchFormState.insureDate = [dayjs(String(route.query.startTime)), dayjs(String(route.query.endTime))];
  }
  search();
});
watch(
  () => route.query.mpPlReportStatus,
  () => {
    searchFormState.mpPlReportStatus = route.query.mpPlReportStatus === undefined ? undefined : String(route.query.mpPlReportStatus);
    if (route.query.startTime && route.query.endTime) {
      searchFormState.insureDate = [dayjs(String(route.query.startTime)), dayjs(String(route.query.endTime))];
    }
    search();
  },
);
</script>
