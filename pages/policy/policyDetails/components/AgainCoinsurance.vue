<!-- 再次共保 -->
<template>
  <div class="flex justify-between">
    <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">再保信息</div>
  </div>
  <!-- 是否临分 -->
  <!-- 否 -->
  <div v-if="!contractModel?.reinsuranceInfo?.facultativeType || contractModel?.reinsuranceInfo?.facultativeType === '1'" class="mb-[18px]">
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">是否临分</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">否</div>
        </div>
      </div>
    </div>
  </div>
  <!-- 是（关联已有再保项目） -->
  <div v-if="contractModel?.reinsuranceInfo?.facultativeType === '4'" class="mb-[18px]">
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">是否临分</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">是（关联已有再保项目）</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">临分批复编号</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ contractModel?.reinsuranceInfo?.facultativeNo }}</div>
        </div>
      </div>
      <!-- <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">状态</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">缺字段</div>
        </div>
      </div> -->
    </div>
  </div>
  <!-- 是（非总对总FAC） -->
  <div v-if="contractModel?.reinsuranceInfo?.facultativeType === '7'" class="mb-[18px]">
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">是否临分</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">是（非总对总FAC）</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">临分批复编号</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ contractModel?.reinsuranceInfo?.facultativeNo }}</div>
        </div>
      </div>
      <!-- <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">状态</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">缺字段</div>
        </div>
      </div> -->
    </div>
    <!-- <div class="text-center mt-[10px]">
      <a-button type="primary" @click="getRimsSysInfo('1')">查看分出方案</a-button>
    </div> -->
  </div>
  <!-- 一般临分情况 -->
  <div v-if="contractModel?.reinsuranceInfo?.facultativeType === '2'" class="mb-[18px]">
    <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">是否临分</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]"> 是（一般临分）</div>
        </div>
      </div>
      <!-- <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">项目名称</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">缺字段</div>
        </div>
      </div> -->
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">临分批复编号</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">{{ contractModel?.reinsuranceInfo?.facultativeNo }}</div>
        </div>
      </div>
    </div>
    <!-- <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">状态</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">缺字段</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">签报号</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">缺字段</div>
        </div>
      </div>
      <div class="flex items-baseline">
        <div class="text-[rgba(0,0,0,0.60)] shrink-0">临分方式</div>
        <div class="pl-[10px]">
          <div class="text-[#333333]">缺字段</div>
        </div>
      </div>
    </div> -->
    <!-- <div class="rounded p-14px box-border">
      <a-table v-if="disDataSource?.length > 0" :columns="disColumns" :data-source="disDataSource" :pagination="false" class="mt-[16px]" />
      <a-table v-if="dataSource?.length > 0" :columns="compensateColumns" :data-source="dataSource" :pagination="false" class="mt-[16px]" />
    </div> -->
  </div>
  <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px" />
  <a-divider />
  <div class="form-title mt-[10px] text-[rgba(0,0,0,0.60)]">共保信息</div>
  <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
    <div class="flex">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">共保类型</div>
      <div class="pl-[10px]">
        <div class="text-[#333333]">{{ innerCoinsuranceMark }}</div>
      </div>
    </div>
    <div class="flex">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">共保协议代码</div>
      <div class="pl-[10px]">
        <div class="text-[#333333]">{{ coinsuranceInfo?.innerCoinsuranceAgreement || '-' }}</div>
      </div>
    </div>
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">总保额</div>
      <div class="pl-[10px]">
        <div class="text-[#333333] font-number">{{ coinsuranceInfo?.totalInsuredAmount || '-' }}</div>
      </div>
    </div>
  </div>
  <div class="grid grid-cols-3 gap-x-16px ml-8px mb-12px">
    <div class="flex items-baseline">
      <div class="text-[rgba(0,0,0,0.60)] shrink-0">总保费</div>
      <div class="pl-[10px]">
        <div class="text-[#333333] font-number">{{ coinsuranceInfo?.totalPremium || '-' }}</div>
      </div>
    </div>
  </div>
  <!-- 共保列表 -->
  <a-table v-if="contractModel?.baseInfo?.coinsuranceMark === '1'" :columns="coinsuranceColumns" :data-source="coinsuranceInfo?.coinsuranceDetailList" :pagination="false" class="my-[16px]" />
</template>

<script setup lang="ts">
// import type { ColumnsType } from 'ant-design-vue/es/table';
import type { contractModelType } from '../policyDetails';
// import { $getOnClient } from '@/composables/request';
// import { SUCCESS_CODE } from '@/utils/constants';

// 获取信息大对象
const { contractModel } = defineProps<{
  contractModel: contractModelType;
}>();
// 共保列表
const coinsuranceColumns = [
  {
    title: '共保公司代码',
    dataIndex: 'reinsureCompanyCode',
  },
  {
    title: '共保公司名称',
    dataIndex: 'reinsureCompanyName',
  },
  {
    title: '保险金额',
    dataIndex: 'insuredAmount',
  },
  {
    title: '保费金额',
    dataIndex: 'premium',
  },
  {
    title: '共保比例（%）',
    dataIndex: 'reinsureScale',
  },
];
// 比例临分列表
// const disColumns: ColumnsType<Record<string, string>> = [
//   {
//     title: '分出比例（%）',
//     dataIndex: 'separationRatio',
//   },
//   {
//     title: '手续费（%）',
//     dataIndex: 'poundage',
//   },
//   {
//     title: '免赔',
//     dataIndex: 'abatement',
//   },
//   {
//     title: '其他说明',
//     dataIndex: 'otherExplain',
//   },
// ];
// 超赔临分列表
// const compensateColumns: ColumnsType<Record<string, string>> = [
//   {
//     title: '起赔点',
//     dataIndex: 'franchise',
//   },
//   {
//     title: '分出比例（%）',
//     dataIndex: 'separationRatio',
//   },
//   {
//     title: '赔偿限额',
//     dataIndex: 'compensateLimit',
//   },
//   {
//     title: '分出净保费',
//     dataIndex: 'separationPremium',
//   },
//   {
//     title: '其他说明',
//     dataIndex: 'otherExplain',
//   },
//   {
//     title: '操作',
//     dataIndex: 'action',
//   },
// ];
// 比例临分
// const disDataSource = ref<ReinsuranceDetailListType[]>([]);
// 超赔临分
// const dataSource = ref<ReinsuranceDetailListType[]>([]);
// 共保信息数据处理
const coinsuranceInfo = computed(() => contractModel?.coinsuranceInfo || {});
// 共保类型处理
const innerCoinsuranceMark = computed(() => coinsuranceInfo.value?.innerCoinsuranceMark === '1' ? '系统内共保' : coinsuranceInfo.value?.innerCoinsuranceMark === '2' ? '系统内外共保' : coinsuranceInfo.value?.innerCoinsuranceMark === '0' ? '系统外共保' : '-');
// const { gateWay, service } = useRuntimeConfig().public || {};
// 查看分出方案
// const facultativeNo = ref<string>(''); // 分出方案需要传的批复编号
// const loading = ref<boolean>(false);
// const getRimsSysInfo = async (type?: string) => {
//   try {
//     loading.value = true;
//     const fetchUrl = `${gateWay}${service.ums}/reinsurance/getRimsSysInfo`;
//     const res = await $getOnClient(fetchUrl, { voucherNo: contractModel?.baseInfo?.applyPolicyNo });
//     const { httpAppUrl, sysId, timeStamp, code, virtualUsername, userId, deptCode } = res?.data as ResType || {};
//     if (res?.code === SUCCESS_CODE) {
//       if (type === '1') {
//         facultativeNo.value = contractModel?.reinsuranceInfo?.facultativeNo;
//       }
//       // 延续老项目的逻辑跳转
//       const href = `${httpAppUrl}/docc/confirmshareinfo/confirmShareInfoAdd.jsp?requestSysName=ICORE_AGR&outSysSessionId=REINSURANCE&confirmNoType=02&schemeNo=${contractModel?.baseInfo?.applyPolicyNo}` +
//         `&confirmNO=${facultativeNo.value}&quotationNo=${contractModel?.baseInfo?.applyPolicyNo}&callbackSysUrl=${location.origin}/reinsuranceFacIframeResult.html&userId=` +
//         `${userId}&timeStamp=${timeStamp}&sysUser=${virtualUsername}&sysId=${sysId}&ciphertext=${code}&deptCode=${deptCode}&agrAnFlag=1`;
//       window.open(href);
//     } else {
//       message.error(res?.msg as string);
//     }
//     loading.value = false;
//   } catch (error) {
//     console.log(error);
//     loading.value = false;
//   }
// };
</script>
