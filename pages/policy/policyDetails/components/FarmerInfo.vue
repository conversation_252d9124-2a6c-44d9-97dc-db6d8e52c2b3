<template>
  <!-- 清单信息 -->
  <div class="flex items-baseline mb-[10px]">
    <div class="font-[600] text-[16px] text-[#333] shrink-0">清单累计农户数{{ contractModel?.baseInfo?.farmersCount || '0' }}户，客户验真认证已通过。</div>
  </div>
  <a-button type="primary" :loading="loading" @click="singleFarmerListExport">导出清单数据</a-button>
</template>

<script setup lang="ts">
import type { contractModelType } from '../policyDetails';
import { $getOnClient } from '@/composables/request';
import { SUCCESS_CODE } from '@/utils/constants';
import { message } from '@/components/ui/Message';
import { downloadBlob } from '@/utils/tools';

// 获取信息大对象
const { contractModel } = defineProps<{
  contractModel: contractModelType;
}>();
const { gateWay, service } = useRuntimeConfig().public || {};
const loading = ref<boolean>(false);
// 导出清单数据
const singleFarmerListExport = async () => {
  try {
    loading.value = true;
    const fetchUrl = `${gateWay}${service.farmer}/file/policyFarmerListExport`;
    await $getOnClient(
      fetchUrl,
      { policyNo: contractModel?.baseInfo?.policyNo },
      {
        onResponse({ response }) {
          if (response._data instanceof Blob) {
            const contentDisposition = response.headers.get('Content-Disposition');
            const fileData = response._data;
            const fileType = response.headers.get('Content-Type') || 'application/octet-stream';
            if (contentDisposition) {
              const match = contentDisposition.match(/filename=(.+)/);
              const fileName = match ? decodeURI(match[1]) : '';
              downloadBlob(fileData, fileName, fileType);
            }
            message.success('导出成功');
          } else {
            const { code, data, msg = '' } = response._data;
            if (code === SUCCESS_CODE) {
              message.success(data);
            } else if (msg) {
              message.error(msg);
            }
          }
        },
      },
    );
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
};
</script>
