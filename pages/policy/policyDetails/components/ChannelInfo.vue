<!-- 渠道信息 -->
<template>
  <div id="policy-channel-info">
    <a-descriptions :colon="false" :column="contractModel?.saleInfo?.developFlg === 'N' ? 3 : 4">
      <a-descriptions-item class="font-number" label="出单机构">{{ contractModel?.saleInfo?.departmentCodeAndName || '-' }}</a-descriptions-item>
      <a-descriptions-item class="font-number" label="是否共展">{{ contractModel?.saleInfo?.developFlgChName || '-' }}</a-descriptions-item>
    </a-descriptions>
    <a-descriptions v-for="(item, index) in contractModel?.saleInfo?.employeeInfoList" :key="index" :colon="false" :column="contractModel?.saleInfo?.developFlg === 'N' ? 3 : 4">
      <a-descriptions-item class="font-number" label="业务员信息">{{ item.employeeName || '-' }}</a-descriptions-item>
      <a-descriptions-item class="font-number" label="业务员执业证号">{{ item.employeeProfCertifNo || '-' }}</a-descriptions-item>
      <a-descriptions-item class="font-number" label="业务员联系方式">{{ item.employeeMobileTelephone || '-' }}</a-descriptions-item>
      <a-descriptions-item v-if="contractModel?.saleInfo?.developFlg === 'Y'" class="font-number" label="占比">
        {{ item.commisionScale || '-' }} %
        <span v-if="item.mainEmployeeFlag === '1'">（主业务员）</span>
      </a-descriptions-item>
    </a-descriptions>
    <a-descriptions :colon="false" :column="contractModel?.saleInfo?.developFlg === 'N' ? 3 : 4">
      <a-descriptions-item class="font-number" label="渠道来源">{{ contractModel?.saleInfo?.channelSourceName || '-' }}</a-descriptions-item>
      <a-descriptions-item class="font-number" label="渠道来源细分">{{ contractModel?.saleInfo?.channelSourceDetailName || '-' }}</a-descriptions-item>
      <!-- 代理人 -->
      <a-descriptions-item v-if="contractModel?.saleInfo?.businessSourceCode === '2'" class="font-number" label="代理人">{{ contractModel?.saleInfo?.agentInfoList?.[0]?.agentName || '-' }}</a-descriptions-item>
      <a-descriptions-item v-if="contractModel?.saleInfo?.businessSourceCode === '2'" class="font-number" label="代理协议">{{ contractModel?.saleInfo?.agentInfoList?.[0]?.agentAgreementNoChName || '-' }}</a-descriptions-item>
      <a-descriptions-item v-if="contractModel?.saleInfo?.businessSourceCode === '2'" class="font-number" label="中介执业证号">{{ contractModel?.saleInfo?.agentInfoList?.[0]?.agencySaleProfCertifNo || '-' }}</a-descriptions-item>
      <a-descriptions-item v-if="contractModel?.saleInfo?.businessSourceCode === '2'" class="font-number" label="中介人员名称">{{ contractModel?.saleInfo?.agentInfoList?.[0]?.agencySaleName || '-' }}</a-descriptions-item>
      <!-- 经纪人 -->
      <a-descriptions-item v-if="contractModel?.saleInfo?.businessSourceCode === '3'" class="font-number" label="经纪人">{{ contractModel?.saleInfo?.brokerInfoList?.[0]?.brokerName || '-' }}</a-descriptions-item>
      <a-descriptions-item v-if="contractModel?.saleInfo?.businessSourceCode === '3'" class="font-number" label="中介执业证号">{{ contractModel?.saleInfo?.brokerInfoList?.[0]?.agencySaleProfCertifNo || '-' }}</a-descriptions-item>
      <a-descriptions-item v-if="contractModel?.saleInfo?.businessSourceCode === '3'" class="font-number" label="中介人员名称">{{ contractModel?.saleInfo?.brokerInfoList?.[0]?.agencySaleName || '-' }}</a-descriptions-item>
      <!-- 主介绍人 -->
      <a-descriptions-item v-if="showIntroducer" class="font-number" label="主介绍人代码">{{ contractModel?.saleInfo?.primaryIntroducerInfo?.primaryIntroducerCode || '-' }}</a-descriptions-item>
      <a-descriptions-item v-if="showIntroducer" class="font-number" label="主介绍人名称">{{ contractModel?.saleInfo?.primaryIntroducerInfo?.primaryIntroducerName || '-' }}</a-descriptions-item>
    </a-descriptions>
    <a-descriptions :colon="false">
      <a-descriptions-item class="font-number" label="农保ID">
        <span v-for="(item, index) in contractModel?.baseInfo?.assisterInfoList" :key="item.assisterId" class="font-number">
          {{ item.assisterId }}
          <span v-if="index !== contractModel.baseInfo.assisterInfoList.length - 1">、</span>
        </span>
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<script setup lang="ts">
import type { contractModelType } from '../policyDetails';

// 获取信息大对象
const { contractModel } = defineProps<{
  contractModel: contractModelType;
}>();
// 主介绍人
const showIntroducer = ref(false);
watchEffect(() => {
  const channelSourceCode = contractModel?.saleInfo?.channelSourceCode;
  const channelSourceDetailCode = contractModel?.saleInfo?.channelSourceDetailCode?.split(',')?.[0] ?? '';
  if ((channelSourceCode === '7' && ['D', 'L'].includes(channelSourceDetailCode)) || (channelSourceCode === 'C' && channelSourceDetailCode === 'G')) {
    showIntroducer.value = true;
  } else {
    showIntroducer.value = false;
  }
});
</script>

<style lang="less" scoped>
#policy-channel-info {
  :deep(.ant-descriptions-item) {
    padding-bottom: 8px;
    .ant-descriptions-item-label {
      color: rgba(0, 0, 0, 0.6);
    }
    .ant-descriptions-item-content {
      color: #333;
    }
  }
}
</style>
