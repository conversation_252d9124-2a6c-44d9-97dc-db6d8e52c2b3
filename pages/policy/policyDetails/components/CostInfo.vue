<template>
  <div>
    <a-form ref="formRef" :colon="false" :label-col="{ style: { width: pxToRem(110) } }">
      <div class="grid grid-cols-4 gap-x-16px">
        <a-form-item label="协办费" name="assisterCharge">
          <div>{{ contractModel?.costInfo?.assisterCharge }} %</div>
        </a-form-item>
        <a-form-item label="手续费/经纪费" name="commissionBrokerChargeProportion">
          <div>{{ contractModel?.costInfo?.commissionBrokerChargeProportion }} %</div>
        </a-form-item>
        <a-form-item label="工作经费" name="managementFees">
          <div>{{ contractModel?.costInfo?.managementFees }} %</div>
        </a-form-item>
        <a-form-item label="农险补贴" name="performanceValue1Default">
          <div>{{ contractModel?.costInfo?.performanceValue1Default }} %</div>
        </a-form-item>
      </div>
      <div class="grid grid-cols-4 gap-x-16px">
        <a-form-item label="防灾防损费" name="calamitySecurityRate">
          <div>{{ contractModel?.costInfo?.calamitySecurityRate }}%</div>
        </a-form-item>
        <a-form-item label="共保出单费" name="coinsuranceInsureFeeRatio">
          <div>{{ contractModel?.costInfo?.coinsuranceInsureFeeRatio }} %</div>
        </a-form-item>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import type { contractModelType } from '../policyDetails';
import { pxToRem } from '@/utils/tools';

// 获取信息大对象
const { contractModel } = defineProps<{
  contractModel: contractModelType;
}>();
const formRef = ref();
</script>
