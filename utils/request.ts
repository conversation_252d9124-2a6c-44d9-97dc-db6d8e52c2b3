import { getRequestURL, type H3Event } from 'h3';
import RequestController from './abortController';
import { message } from '@/components/ui/Message';

export interface RequestConfig {
  key?: string;
  method?: string;
  headers?: { [key: string]: unknown };
  baseURL?: string;
  server?: boolean;
  lazy?: boolean;
  immediate?: boolean;
  deep?: boolean;
  watch?: boolean;
  query?: unknown;
  canCancel?: boolean;
}

export interface RequestOptions<D> extends RequestConfig {
  query?: D | Ref<D>;
  params?: D;
  body?: D | Ref<D>;
}

interface RequestOptionsAutnCancel<D> extends RequestOptions<D> {
  signal?: unknown;
}

export interface ApiResult<T> {
  code?: string;
  data?: T;
  msg?: string;
}

export const isAPI = (url: string) => {
  return /\/api/.test(url);
};

export const getRedirectUrl = (event: H3Event) => {
  const { isServer, workEnv, loginRedirect } = runtimeConfig();
  if (isServer) {
    if (event) {
      const urlObj = getRequestURL(event, { xForwardedHost: true, xForwardedProto: true });
      let protocal = urlObj.protocol;
      const host = urlObj.host;
      if (workEnv !== 'development') {
        // 测试环境和生产环境统一换成https
        protocal = protocal.replace(/^http(s)*/, 'https');
      }

      // return encodeURIComponent(url || '')
      // nodejs服务到浏览器之间有多个链路，不知是哪个链路会自动帮忙encode一次链接参数，导致浏览器拿到的参数会被encode2次，紧急修复一下。
      // 也有可能是navigateTo方法自动encode
      // return `${protocal}//${host}${loginRedirect}?X-Portal-Token=${xPortalToken}`; // 后端塞灰度标识入口
      return `${protocal}//${host}${loginRedirect}`; // 塞灰度标识在登录页实现了
    } else {
      return '';
    }
  } else {
    // return encodeURIComponent(`${window?.location?.protocol}//${window?.location?.host}${loginRedirect}?X-Portal-Token=${xPortalToken}`); // 后端塞灰度标识入口
    return encodeURIComponent(`${window?.location?.protocol}//${window?.location?.host}${loginRedirect}`); // 塞灰度标识在登录页实现了
  }
};

export const runtimeConfig = () => {
  const runtimeConfig = useRuntimeConfig() || {};
  const { xPortalToken, loginUrl, workEnv, loginRedirect } = runtimeConfig.public || {};
  const { isServer } = runtimeConfig;
  return { xPortalToken, isServer, loginUrl, workEnv, loginRedirect };
};

export const setXPortalToken = <D>(options: RequestOptions<D>, url: string) => {
  const { xPortalToken } = runtimeConfig();
  if (!isAPI(url)) {
    if (options.headers) {
      options.headers['X-Portal-Token'] = xPortalToken;
    } else {
      options.headers = { 'X-Portal-Token': xPortalToken };
    }
  }
};

export const setReqBody = <D>(options: RequestOptions<D>, data: D | Ref<D>) => {
  if (options.method === 'get') {
    options.query = data;
  } else {
    options.body = data;
  }
};

export const resetContentType = <D>(options: RequestOptions<D>) => {
  if (options && options.headers && (options.headers['Content-Type'] === '' || options.headers['Content-Type'] === null)) {
    // 说明接口要用默认的Content-Type
    delete options.headers['Content-Type'];
  }
};

// 通用错误处理
export const handleAuthError = async () => {
  const { isServer, xPortalToken, loginUrl } = runtimeConfig();
  const nuxtApp = useNuxtApp();
  const login = `${loginUrl}/#/login?portalToken=${xPortalToken}&redirectUrl=${getRedirectUrl(nuxtApp.ssrContext?.event)}`;
  if (isServer) {
    console.log(nuxtApp.ssrContext?.event, '即将要跳转到登录页', login);
  }
  await navigateTo(login, { external: true });
};

const controllers = new RequestController();

const createRequestId = <D>(url: string, options: RequestOptions<D>) => {
  return `${options.method}_${url}`;
};

const request = async <T = unknown, D = unknown>(url: string, data: D, config: RequestOptions<D>): Promise<ApiResult<T>> => {
  const query = data ? (isReactive(data) ? toRaw(data) : unref(data)) : undefined;
  const { isServer } = runtimeConfig();
  if (isServer) {
    // 服务端执行的，立马返回一个空对象
    return {};
  }
  let responseStatus = '';
  const options: RequestOptionsAutnCancel<D> = {
    deep: false,
    canCancel: false,
    headers: {
      'Content-Type': 'application/json',
    },
  };
  Object.assign(options, config);
  setXPortalToken(options, url);
  setReqBody(options, query);
  resetContentType(options);

  if (options.canCancel) {
    // 需要开启取消上一次请求
    const requestId = createRequestId(url, options);
    const controller = controllers.create(requestId);
    options.signal = controller.signal;
  }
  try {
    const res: ApiResult<T> = await $fetch(url, {
      ...options,
      onResponseError({ response }) {
        responseStatus = response.status;
        if (options.onResponseError) {
          options.onResponseError();
        }
      },
    });
    // 访问异常，需退出重新登录
    if (['988700', 'S0010'].includes(res?.code || '')) {
      await handleAuthError();
      return { code: '401', msg: '登录超时' } as ApiResult<T>;
    }
    return res;
  } catch (err) {
    const code = String(responseStatus);
    if (code === '401') {
      await handleAuthError();
      return { code: '401', msg: '登录超时' } as ApiResult<T>;
    } else if (code === '403') {
      await navigateTo({ path: '/noAuth' });
      throw new Error('接口无权限');
    } else {
      if (!err.message.includes('The user aborted a request')) {
        message.error(err.message || '请求有误，请稍后重试');
      }
      throw err;
    }
  }
};
/**
 * get请求，客户端发起请求，服务端不发起请求并返回{}
 * 应用于无需进行服务端渲染场景
 */
export async function $get<T = unknown, D = unknown>(url: string, data?: D, config?: RequestOptions<D>): Promise<ApiResult<T>> {
  return request(url, data || undefined, { ...config, ...{ method: 'get' } });
}
/**
 * post请求，客户端发起请求，服务端不发起请求并返回{}
 * 应用于无需进行服务端渲染场景
 */
export async function $post<T = unknown, D = unknown>(url: string, data?: D, config?: RequestOptions<D>): Promise<ApiResult<T>> {
  return request(url, data || undefined, { ...config, ...{ method: 'post' } });
}
