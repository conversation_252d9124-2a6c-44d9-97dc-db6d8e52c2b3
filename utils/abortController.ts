// 请求控制器类
class RequestController {
  private controllers: Map<string, AbortController>;

  constructor() {
    this.controllers = new Map();
  }

  create(key: string): AbortController {
    console.log(key);
    this.abort(key); // 如果存在旧的controller，先取消
    const controller = new AbortController();
    this.controllers.set(key, controller);
    return controller;
  }

  abort(key: string) {
    const controller = this.controllers.get(key);
    if (controller) {
      controller.abort();
      this.controllers.delete(key);
    }
  }

  abortAll() {
    this.controllers.forEach(controller => controller.abort());
    this.controllers.clear();
  }
}

export default RequestController;
