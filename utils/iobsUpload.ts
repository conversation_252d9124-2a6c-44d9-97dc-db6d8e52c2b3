// import { getInIobsInfo, initCombineUpload, initShardUpload, initUploadPart } from '@/api/iobsUpload';
import { SUCCESS_CODE } from './constants';
import type { ApiResult } from '@/utils/service/types';
import { $getOnClient, $postOnClient} from '@/composables/request'

const maxTimes = 5;
const transData = (orign: string) => {
  let tmp;
  try {
    tmp = JSON.parse(orign);
  } catch (error) {
    console.error('数据解析出错,请检查返回数据');
  }
  return tmp;
};

const onInnerFail = (e: string) => {
  console.log(e);
};

const onInnerSuccess = (params: {bucket: string; hask: string; key: string;}) => {
  console.log(params);
};
/**
 * 文件分片上传
 * @param file 文件
 * @param onFail？ 失败的回调
 * @param onSuccess？ // 成功的回调
 * @returns onFail or onSuccess 最终结果
 */
export const iobsUploadPro = async (file: File & {uid: string;}, onFail?: Function, onSuccess?: Function): Promise<ApiResult<any>> => {
  try {
    const res = await $getOnClient('/api/iobs/getInIobsInfo');
    const { iobsBucketName, host, fileKey, token } = res?.data || {};
    const fail = onFail ?? onInnerFail;
    const success = onSuccess ?? onInnerSuccess;
    const resData = await iobsUpload(file, iobsBucketName, host, fileKey, token, fail, success, file.uid);
    if (resData) {
      return { msg: '文件上传成功', data: resData, code: SUCCESS_CODE };
    }
    return { msg: '文件上传失败', data: null, code: '9999' };
  } catch (e) {
    console.log('上传失败');
    return { msg: '文件上传失败', data: null, code: '9999' };
  }
};

/**
 * 初始化上传分片文件基础信息
 */
const iobsUpload = async (file: File, bucket: string, host: string, key: string, token: string, onError: Function, onSuccess: Function, uid: string) => {
  const { size, name } = file;
  const fileName = encodeURIComponent(name);

  return new Promise((resolve, reject) => {

    $postOnClient(`${host}/initUploadPart/${bucket}/${key}?token=${token}&fileSize=${size}&fileName=${fileName}`).then((res) => {
      if (!res) {
        console.log('iobs获取ID失败');
        return;
      }
      const uploadId = res;
      const shardSize = 5 * 1024 * 1024;
      //* 分片数量
      const num = Math.ceil(size / shardSize);
      //* 进度条进度
      const p = 99;
      const arr = new Array(num).fill(null);
      for (let i in arr) {
        if (Object.prototype.hasOwnProperty.call(arr, i)) {
          const curShardSize = size - Number(i) * shardSize < shardSize ? size - Number(i) * shardSize : shardSize;
          const shard = file.slice(Number(i) * shardSize, Number(i) * shardSize + curShardSize);
          arr[i] = {
            stream: shard,
            p,
            partNumber: parseInt(i) + 1,
            times: 1,
          };
        }
      }
      return chunkUpload(arr, bucket, host, key, token, uid, uploadId, num);
    }).then(({ partMap, uploadId }) => combineUpload(bucket, host, key, token, uploadId, fileName, partMap, onError, onSuccess)).then((res) => {
      resolve(res);
    })
      .catch((e) => reject(e));
  });
};


/**
 * 文件分片上传
 */
const shardUpload = async (params:
{ stream: number; bucket: string; host: string; key: string; token: string; uploadId: string; partMap: any;
  partNumber: number; times: number; p: number; uid: string; }) => {
  const { stream, host, bucket, key, token, uploadId, partMap, partNumber, times, p, uid } = params;
  const URL = `${host}/uploadPart/${bucket}/${key}?token=${token}&uploadId=${uploadId}&partNumber=${partNumber}`;
  return new Promise<void>((resolve, reject) => {
    $postOnClient(URL, stream, {
      headers: { 'Content-Type': 'application/octet-stream' }
    }).then((res) => {
      const data = res;
      const resData = transData(data);
      if (resData?.hash) {
        partMap[partNumber] = resData.hash;
        return resolve(resData.hash);
      } else if (times < maxTimes) {
        shardUpload({ stream, bucket, host, key, token, uploadId, partMap, partNumber, times: times + 1, p, uid })
          .then(() => resolve())
          .catch(() => reject('超过最大重试次数，请重新上传'));
      }
    })
      .catch(() => {
        if (times < maxTimes) {
          shardUpload({ stream, bucket, host, key, token, uploadId, partMap, partNumber, times: times + 1, p, uid })
            .then(() => resolve())
            .catch(() => reject('超过最大重试次数，请重新上传'));
        } else reject('超过最大重试次数，请重新上传');
      });
  });
};


/**
 * 文件分片
 */
const chunkUpload = (arr: any[], bucket: string, host: string, key: string,
  token: string, uid: string, uploadId: string, shardNum: number) => new Promise<any>((resolve, reject) => {
  //* hashMap
  let partMap = {};
  //* 最大请求数量
  const requestLimit = 5;
  //* 当前请求数量
  let curRequest = 0;
  const upload = (arr: any[]) => {
    if (Object.keys(partMap).length === shardNum) resolve({ partMap, uploadId });
    while (curRequest < requestLimit && arr.length > 0) {
      curRequest++;
      const params = arr.shift();
      shardUpload({ ...params, bucket, host, key, token, uid, uploadId, partMap })
        .then(() => {
          curRequest--;
          upload(arr);
        })
        .catch((e: any) => reject(e));
    }
  };
  upload(arr);
});

/**
 * 分片文件合并
 */
const combineUpload = (bucket: string, host: string, key: string,
  token: string, uploadId: string, fileName: string, partMap: {}, fail?: Function, success?: Function) => {
  const URL = `${host}/completeUpload/${bucket}/${key}?token=${token}&uploadId=${uploadId}&fileName=${fileName}`;
  const form = new FormData();
  form.append('partMap', JSON.stringify(partMap));
  return new Promise<void>((resolve, reject) => {
    $postOnClient(URL, form,{
      headers: {
        'Content-Type': '',
        'Content-Disposition': form
      },
    }).then((res) => {
      const data = res;
      const resData = transData(data);
      if (resData?.hash) {
        const num = resData.hash.split('-')[1];
        if (Object.keys(partMap).length !== parseInt(num)) {
          fail?.('分片数量错误，请重新上传');
          console.log('分片数量错误，请重新上传');
          return;
        }
        success?.(resData);
        resolve(resData);
      }
    })

      .catch(() => { reject('合片失败，请重新上传'); });
  });
};
