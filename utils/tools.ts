import { isNumber } from 'lodash-es';
import { message } from '@/components/ui/Message';

export const pxToRem = (value: number) => {
  if (!isNumber(value) || value <= 0) return;
  // const _fontSize = document.documentElement.clientWidth / 10;
  const result = value / 144 + 'rem';
  return result;
};

// 下载文件
export const downloadBlob = (buffer: Blob, fileName: string, type: string): void => {
  const blob = new Blob([buffer], { type });
  const anchor = document.createElement('a');
  anchor.download = fileName;
  anchor.style.display = 'none';
  anchor.href = URL.createObjectURL(blob);
  document.body.appendChild(anchor);
  anchor.click();
  document.body.removeChild(anchor);
};

// 根据url下载文件
export const downloadFile = (url: string) => {
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', '');
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 复制
export const copyText = (text: string) => {
  const inputs = document.createElement('input');
  inputs.value = text;
  document.body.appendChild(inputs);
  inputs.select();
  const actions = document.execCommand('Copy');
  if (actions) {
    message.success('复制成功');
    document.body.removeChild(inputs);
  } else {
    message.error('复制失败');
  }
};

/**
 * 高精度加减乘除方法
 * 以下函数通过模拟手算的方式实现高精度的加、减、乘、除运算。在进行加法和减法时，
 * 先将两个数补成相同长度，然后从低位到高位逐位进行运算。乘法是通过多次加法实现的，
 * 每次将一个数与另一个数的一位相乘，然后将结果相加。除法是通过逐位试商的方式实现的
 */
// 高精度加法函数
export const add = (num1: string, num2: string): string => {
  // 分离整数部分和小数部分
  const [int1, dec1 = ''] = num1.split('.');
  const [int2, dec2 = ''] = num2.split('.');

  // 获取小数部分的长度，并补齐
  const maxDecLen = Math.max(dec1.length, dec2.length);
  const dec1Padded = dec1.padEnd(maxDecLen, '0');
  const dec2Padded = dec2.padEnd(maxDecLen, '0');

  // 计算小数部分的和
  const lenDec1 = dec1Padded.length;
  const lenDec2 = dec2Padded.length;
  let carry = 0;
  let decResult = '';

  for (let i = Math.max(lenDec1, lenDec2) - 1; i >= 0; i--) {
    const sum = parseInt(dec1Padded[i] || '0') + parseInt(dec2Padded[i] || '0') + carry;
    decResult = (sum % 10) + decResult;
    carry = Math.floor(sum / 10);
  }

  // 获取整数部分的长度，并补齐
  const maxIntLen = Math.max(int1.length, int2.length);
  const int1Padded = int1.padStart(maxIntLen, '0');
  const int2Padded = int2.padStart(maxIntLen, '0');

  // 计算整数部分的和
  const lenInt1 = int1Padded.length;
  const lenInt2 = int2Padded.length;
  let intResult = '';

  for (let i = Math.max(lenInt1, lenInt2) - 1; i >= 0; i--) {
    const sum = parseInt(int1Padded[i]) + parseInt(int2Padded[i]) + carry;
    intResult = (sum % 10) + intResult;
    carry = Math.floor(sum / 10);
  }

  // 如果最后还有进位，将进位加到结果的前面
  if (carry > 0) {
    intResult = carry + intResult;
  }

  // 处理小数部分的进位
  if (decResult.length > maxDecLen) {
    intResult += decResult.slice(0, -maxDecLen);
    decResult = decResult.slice(-maxDecLen);
  }

  return intResult + (decResult.length > 0 ? '.' + decResult : '');
};

// 高精度减法函数
export const subtract = (num1: string, num2: string): string => {
  // 获取两个数的长度
  const len1 = num1.length;
  const len2 = num2.length;
  // 如果 num1 小于 num2，返回负数的结果
  if (len1 < len2 || (len1 === len2 && num1 < num2)) {
    return '-' + subtract(num2, num1);
  }
  // 将 num2 补成和 num1 一样的长度
  num2 = num2.padStart(len1, '0');
  // 借位标志
  let borrow = 0;
  // 结果字符串
  let result = '';
  // 从低位到高位逐位相减
  for (let i = len1 - 1; i >= 0; i--) {
    // 当前位的差等于 num1 对应位的值减去 num2 对应位的值再减去借位
    let diff = parseInt(num1[i]) - parseInt(num2[i]) - borrow;
    // 如果差小于 0，需要向高位借位
    if (diff < 0) {
      diff += 10;
      borrow = 1;
    } else {
      borrow = 0;
    }
    // 将当前位的差加入结果字符串
    result = diff + result;
  }
  // 去除结果前面的多余的'0'
  while (result.length > 1 && result[0] === '0') {
    result = result.slice(1);
  }
  return result;
};

// 高精度乘法函数（支持两位小数）
export const multiply = (num1: string, num2: string) => {
  // 获取小数点的位置
  const decimal1 = num1.indexOf('.');
  const decimal2 = num2.indexOf('.');

  // 计算小数位数
  const decimalPlaces = (decimal1 !== -1 ? num1.length - decimal1 - 1 : 0) + (decimal2 !== -1 ? num2.length - decimal2 - 1 : 0);

  // 移除小数点
  num1 = num1.replace('.', '');
  num2 = num2.replace('.', '');

  // 获取两个数的长度
  const len1 = num1.length;
  const len2 = num2.length;

  // 结果初始化为一个全是0的数组
  const result = new Array(len1 + len2).fill(0);

  // 进行乘法运算
  for (let i = len1 - 1; i >= 0; i--) {
    for (let j = len2 - 1; j >= 0; j--) {
      const product = (num1[i] - '0') * (num2[j] - '0') + result[i + j + 1];
      result[i + j + 1] = product % 10; // 当前位
      result[i + j] += Math.floor(product / 10); // 进位
    }
  }

  // 转换数组为结果字符串，并去掉前面的0
  let resultStr = result.join('').replace(/^0+/, '');

  // 根据计算的总小数位数，插入小数点
  if (decimalPlaces > 0) {
    const insertPosition = resultStr.length - decimalPlaces;
    if (insertPosition > 0) {
      resultStr = resultStr.slice(0, insertPosition) + '.' + resultStr.slice(insertPosition);
    } else {
      resultStr = '0.' + resultStr.padStart(decimalPlaces, '0');
    }
  } else if (resultStr === '') {
    resultStr = '0'; // 如果结果为空，则返回0
  }

  return resultStr;
};

// 高精度除法函数
export const divide = (num1: string, num2: string): string => {
  // 如果除数为 0，抛出错误
  if (num2 === '0') {
    throw new Error('Division by zero');
  }
  // 结果字符串
  let result = '';
  // 余数初始化为 0
  let remainder = 0;
  // 遍历 num1 的每一位进行除法运算
  for (let i = 0; i < num1.length; i++) {
    // 将余数乘以 10 再加上当前位的值
    remainder = remainder * 10 + parseInt(num1[i]);
    // 试商，得到当前位的商
    const quotient = Math.floor(remainder / parseInt(num2));
    // 将商加入结果字符串
    result += quotient;
    // 更新余数
    remainder %= parseInt(num2);
  }
  // 去除结果前面的多余的'0'
  while (result.length > 1 && result[0] === '0') {
    result = result.slice(1);
  }
  return result;
};

// 生成UUId
export const generateUUID = () => {
  let d = new Date().getTime(); // Timestamp
  let d2 = (performance && performance.now && performance.now() * 1000) || 0; // Time in microseconds since page-load or 0 if unsupported
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = Math.random() * 16; // random number between 0 and 16
    if (d > 0) {
      // Use timestamp until depleted
      r = (d + r) % 16 | 0;
      d = Math.floor(d / 16);
    } else {
      // Use microseconds since page-load if supported
      r = (d2 + r) % 16 | 0;
      d2 = Math.floor(d2 / 16);
    }
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
};

// 文件是否为图片（系统允许上传的附件）
export const isImage = (format: string) => /(jpg|jpeg|jpe|gif|png)$/.test((format || '').toLowerCase());

export const isUnPreviewImage = (format: string) => /(bmp|dib|jfif|tiff|tif)$/.test((format || '').toLowerCase());

// 文件是否为word文档（系统允许上传的附件）
export const isWord = (format: string) => /(doc|docx)$/.test((format || '').toLowerCase());

// 文件是否为word文档（系统允许上传的附件）
export const isExcel = (format: string) => /(xls|xlsx)$/.test((format || '').toLowerCase());

// 文件是否为pdf文档（系统允许上传的附件）
export const isPdf = (format: string) => /pdf$/.test((format || '').toLowerCase());

// 文件是否为mail文档（系统允许上传的附件）
export const isMail = (format: string) => /(msg|eml)$/.test((format || '').toLowerCase());

// 文件是否为html文档（系统允许上传的附件）
export const isHtml = (format: string) => /(htm|html)$/.test((format || '').toLowerCase());

// 文件是否为ppt文档（系统允许上传的附件）
export const isPpt = (format: string) => /(ppt|pptx)$/.test((format || '').toLowerCase());

// 文件是否为zip文档（系统允许上传的附件）
export const isZip = (format: string) => /(rar|zip)$/.test((format || '').toLowerCase());

// 文件是否为txt文档（系统允许上传的附件）
export const isTxt = (format: string) => /txt$/.test((format || '').toLowerCase());

// 文件是否为mp4文档（系统允许上传的附件）
export const isMp4 = (format: string) => (format || '').toLowerCase() === 'mp4';

export const compareArrayWithSet = (array1: unknown[], array2: unknown[]) => {
  if (array1.length !== array2.length) {
    return false;
  }
  const set1 = new Set(array1);
  for (let i = 0; i < array2.length; i++) {
    if (!set1.has(array2[i])) {
      return false;
    }
  }
  return true;
};

export const getBrowserInfo = (ua?: string) => {
  const userAgent = ua ? ua : (navigator && navigator.userAgent) || '';
  const info = {
    browser: '',
    version: 0,
  };
  if (/Chrome\/(\S+)/.test(userAgent)) {
    info.browser = 'chrome';
    const version = (userAgent.match(/Chrome\/(\S+)/) || [])[1] || '0';
    info.version = Number(version.split('.')[0]);
  } else if (/Firefox\/(\S+)/.test(userAgent)) {
    info.browser = 'firefox';
    const version = (userAgent.match(/Firefox\/(\S+)/) || [])[1] || '0';
    info.version = Number(version.split('.')[0]);
  } else if (/Safari\/(\S+)/.test(userAgent)) {
    info.browser = 'safari';
    const version = (userAgent.match(/Version\/(\S+)/) || [])[1] || '0';
    info.version = Number(version.split('.')[0]);
  } else if (/Edg\/(\S+)/.test(userAgent)) {
    info.browser = 'Edge';
    const version = (userAgent.match(/Edg\/(\S+)/) || [])[1] || '0';
    info.version = Number(version.split('.')[0]);
  } else {
    info.browser = 'other';
    info.version = 0;
  }

  return info;
};

/**
 * 计算字符串长度，双字节字符计为1.5，单字节字符计为1
 * @param str 要计算长度的字符串或数字
 * @returns 字符串的计算长度
 */
export const getStringLength = (str: string | number): number => {
  if (str === undefined || str === null) return 0;
  // 将数字转换为字符串
  const strValue = str.toString();
  let length = 0;
  for (let i = 0; i < strValue.length; i++) {
    // 使用charCodeAt判断字符是否为双字节
    // 大于255的为双字节字符
    if (strValue.charCodeAt(i) > 255) {
      length += 1.5;
    } else {
      length += 1;
    }
  }
  return length;
};

/**
 * 获取DOM元素的宽度
 * @param element DOM元素或者null
 * @returns 元素的宽度(px)，如果元素不存在则返回0
 */
export const getElementWidth = (element: HTMLElement | null): number => {
  if (!element) return 0;

  // 获取元素的完整尺寸信息
  const rect = element.getBoundingClientRect();

  // 返回宽度(不包含padding和border)
  return Math.round(rect.width);
};

/**
 * Extracts the filename without the extension from a given file path or name.
 * @param fileName The full file name or path.
 * @returns The file name without the extension.
 */
export const extractFileNameWithoutExtension = (fileName: string): string => {
  // Use a regular expression to remove the extension
  return fileName.replace(/\.[^/.]+$/, '');
};
