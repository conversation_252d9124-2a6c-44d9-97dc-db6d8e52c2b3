import type { Rule } from 'ant-design-vue/es/form';

// 字母数字-_/
export const isWordDigitValidator = (_rule: Rule, value: string) => {
  const reg = /^[a-zA-Z0-9-_/]*$/;
  if (reg.test(value)) {
    return Promise.resolve();
  }
  return Promise.reject();
};

// 数字-_/
export const isDigitSignValidator = (_rule: Rule, value: string) => {
  const reg = /^[0-9-_/]*$/;
  if (reg.test(value)) {
    return Promise.resolve();
  }
  return Promise.reject();
};

// 检验组织机构代码值:9位，前8位是数字或字母，第九位C9是数字或X，C9=11-MOD(∑Ci(i=1→8)×Wi,11)
export const isOrgCode = (_rule: Rule, value: string) => {
  const reg = /^$|^[0-9|A-Z]{8}[0-9|X]$/;
  if (!reg.test(value)) {
    return Promise.reject('组织机构证件码不规范，请检查后重新录入！');
  }
  // 计算加权和
  const str = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let sum = 0;
  const params = [3, 7, 9, 10, 5, 8, 4, 2];
  for (let i = 0; i < value.length - 1; i++) {
    const temp = value.charAt(i);
    const weight = params[i];
    if (str.includes(temp)) {
      sum += (str.indexOf(temp) + 10) * weight;
    } else {
      sum += parseInt(temp, 10) * weight;
    }
  }
  // 计算校验码
  let isTrue = false;
  if (!value || value.length === 0) {
    isTrue = false;
  } else {
    const mod = 11 - (sum % 11);
    const expectedChar = mod === 10 ? 'X' : mod === 11 ? '0' : String(mod);
    isTrue = value.charAt(8) !== expectedChar;
  }
  if (isTrue) {
    return Promise.reject('组织机构证件码不规范，请检查后重新录入！');
  }
  return Promise.resolve();
};

// 统一社会信用号码
export const isSocialCreditNumber = (_rule: Rule, value: string) => {
  const reg = /^[0-9|A-Z]+$/; // 整体编码格式校验
  if (value.length !== 18 || !reg.test(value)) {
    return Promise.reject('统一社会信用代码由18位大写字母 、数字组成');
  }
  const reg2 = /^[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/g;
  if (!reg2.test(value)) {
    return Promise.reject('证件号码不规范，请检查后重新录入！');
  }
  return Promise.resolve();
};

// 税务登记证
export const isTaxationNo = (_rule: Rule, value: string) => {
  const myRegExp = /^[0-9]{14}[0-9|A-Z]{1}$/;
  const myRegExp2 = /^[0-9]{18}[0-9|A-Z]{2}$/;
  const myRegExp3 = /^[0-9]{17}[X][0-9|A-Z]{2}$/;
  const myRegExpIDCardNo = /^\d{6}(((19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])\d{3}([0-9]|x|X))|(\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])\d{3}))$/;
  const valid = myRegExp.test(value) || myRegExp2.test(value);
  const valid2 = myRegExp3.test(value) || myRegExpIDCardNo.test(value);
  const valid3 = value.length === 17 && myRegExpIDCardNo.test(value.substring(0, 15));
  if (!(valid || valid2 || valid3)) {
    return Promise.reject('税务登记证号码不规范，请检查后重新录入');
  }
  return Promise.resolve();
};

// 工商登记号
export const isBusinessNo = (_rule: Rule, value: string) => {
  // 13或15位数字
  const myRegExp = /^\d{13}(\d{2}){0,1}$/;
  // 6位连续重复的数字
  const myRegExp1 = /^([\d])\1{5}$/;
  const ssCode = value.substring(0, 6);
  const valid = myRegExp.test(value) && !myRegExp1.test(ssCode) && '**********,**********'.indexOf(ssCode) < 0;
  if (!valid) {
    return Promise.reject('工商登记号不规范，请检查后重新录入！');
  }
  return Promise.resolve();
};

// 营业执照注册号
export const isLicenseNo = (_rule: Rule, value: string) => {
  const myRegExp = /^[0-9]{15}$/;
  const valid = myRegExp.test(value);
  if (!valid) {
    return Promise.reject('请输入有效的营业执照注册号，号码只能为15位数！');
  }
  return Promise.resolve();
};
// 护照校验
export const isPassport = (_rule: Rule, value: string) => {
  // G+8位数字 或者 E+英文（I,O除外）+7位数字
  const myRegExp = /^(G\d{8}|E[A-HJ-NP-Z]\d{7})$/;
  const valid = myRegExp.test(value);
  if (!valid) {
    return Promise.reject('请输入正确的护照证件号码!');
  }
  return Promise.resolve();
};

// 军人证证件
export const isSoldierCard = (_rule: Rule, value: string) => {
  const myRegExp = /^\d{7}$/;
  const valid = myRegExp.test(value);
  if (!valid) {
    return Promise.reject('请输入正确的军人证证件号码!');
  }
  return Promise.resolve();
};

// 港澳通行证
export const isHM = (_rule: Rule, value: string) => {
  if (value.length <= 5) {
    return Promise.reject('港澳通行证证件号必须大于5位!');
  }
  return Promise.resolve();
};

// 驾驶证
export const isDriveCard = (_rule: Rule, value: string) => {
  const myRegExp = /^\d{6}(((19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])\d{3}([0-9]|x|X))|(\d{2}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])\d{3}))$/;
  const valid = myRegExp.test(value);
  if (!valid) {
    return Promise.reject('请输入正确的驾驶证证件号码!');
  }
  return Promise.resolve();
};

// 港澳回乡证或台胞证
export const checkHMT = (_rule: Rule, value: string) => {
  const rtnMsg = '港澳回乡证或台胞证证件号必须大于5位!';
  if (value.length <= 5) {
    return Promise.reject(rtnMsg);
  }
  return Promise.resolve();
};
// 港澳台居住证校验
export const checkHMResident = (_rule: Rule, value: string) => {
  const rtnMsg = '港澳台居民居住证不能有空格且长度须为18位!';
  if (value.length !== 18 || /\s/g.test(value)) {
    return Promise.reject(rtnMsg);
  }
  return Promise.resolve();
};

// 港澳通行证证件号校验 - 港澳通行证与台胞证分开校验
export const checkHMRP = (_rule: Rule, value: string) => {
  value = value.replace(/\s/g, '');
  const reg = /^[HM]{1}([0-9]{10}|[0-9]{8})$/;
  if (!reg.test(value)) {
    return Promise.reject('港澳居民来往内地通行证需以字母H或M开头+8位或10位数字，字符长度9、11');
  }
  return Promise.resolve();
};

// 台胞证校验 - 港澳通行证与台胞证分开校验
export const checkTWP = (_rule: Rule, value: string) => {
  value = value.replace(/\s/g, '');
  const reg = /^\d{8}$/;
  if (!reg.test(value)) {
    return Promise.reject('台湾居民来往大陆通行证必须是8位数字');
  }
  return Promise.resolve();
};

// 邮政编码
export const isPostCode = (_rule: Rule, value: string) => {
  if (!value) {
    return Promise.resolve();
  }
  const reg = /^[0-9]{6}$/;
  if (!reg.test(value)) {
    return Promise.reject('邮政编码为6位数字');
  }
  return Promise.resolve();
};

// 手机号
export const isMobilePhone = (_rule: Rule, value: string) => {
  if (!value) {
    return Promise.resolve();
  }
  const regExg = /^1[3456789]\d{3}\*{4}$/;
  if (regExg.test(value)) {
    return Promise.resolve();
  }
  const reg = /^1[3456789]\d{9}$/;
  if (!reg.test(value)) {
    return Promise.reject('手机号必须为11位数字且必须以13、14、15、16、17、18或19开头');
  }
  return Promise.resolve();
};

// 座机号或手机号
export const phoneRule = async (_rule: Rule, value: string) => {
  if (!value) {
    return Promise.resolve();
  }

  const mobileReg = /^1[3-9]\d{9}$/; // 匹配手机号(与后端校验逻辑一致)
  const deskReg = /^(0\d{2})-(\d{8})$|^(0\d{2})-(\d{7})$|^(0\d{3})-(\d{7})$|^(0\d{3})-(\d{8})$|^(400|800)\d{7}$/; // 匹配座机号(与后端校验逻辑一致)
  if (value.startsWith('1') && !mobileReg.test(value)) {
    return Promise.reject(new Error('手机号码格式错误：必须为11位数字且必须以13、14、15、16、17、18或19开头;'));
  } else if (!value.startsWith('1') && !deskReg.test(value)) {
    return Promise.reject(new Error('座机号格式错误：区号必须为3或4位且以0开头，号码必须为7或8位，注意请以(-)为分割，或者400/800必须为10位数;'));
  }
  return Promise.resolve();
};

// 邮箱
export const isEmail = (_rule: Rule, value: string) => {
  if (!value) {
    return Promise.resolve();
  }
  const regExp = /^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/;
  if (!regExp.test(value)) {
    return Promise.reject('电子邮箱格式不正确');
  }
  return Promise.resolve();
};

// 客户名称校验
export const checkClientName = (_rule: Rule, value: string) => {
  if (!value) {
    return Promise.resolve();
  }

  // 匹配中文(包含生僻字)、英文字母
  // eslint-disable-next-line no-control-regex
  const chineseRegex = /[^\u0000-\u00ff]/gi;

  // 计算字符长度(中文算2个字符)
  const len = value.replace(chineseRegex, '--').length;

  // 校验规则:
  // 1. 长度在4-240之间
  // 2. 不能为纯数字
  // 3. 不能以.或数字结尾
  if (len > 240 || len < 4 || /^[0-9]*$/.test(value) || /[.0-9]$/.test(value)) {
    return Promise.reject('名称为最少2位汉字，或4位英文字符，且不超过120位字符；不能为纯数字，不能以"."或以数字结尾');
  }

  return Promise.resolve();
};
// 地址校验
export const checkAddress = (_rule: Rule, value: string) => {
  if (value) {
    if (value.replace(/[^\u4e00-\u9fa5A-Za-z0-9]/g, '--').length > 400 || value.replace(/[^\u4e00-\u9fa5A-Za-z0-9]/g, '--').length < 12) {
      return Promise.reject('地址为最少6位汉字，或12位英文字符，且不超过200位字符；');
    }
  }
  return Promise.resolve();
};
// 身份证
export const checkIDCardNo = (_rule: Rule, value: string) => {
  const rtnMsg = '请录入正确的身份证号码!';
  if (!value) {
    return Promise.reject(rtnMsg);
  }

  // 身份证正则表达式
  const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  if (!idCardReg.test(value)) {
    return Promise.reject(rtnMsg);
  }

  // 15位身份证转换为18位
  if (value.length === 15) {
    let idCard18 = value.slice(0, 6) + '19' + value.slice(6);
    idCard18 += getCheckCode(idCard18);
    value = idCard18;
  }

  // 校验18位身份证的校验码
  if (value.length === 18 && !validateCheckCode(value)) {
    return Promise.reject(rtnMsg);
  }

  return Promise.resolve();
};

// 获取18位身份证的校验码
const getCheckCode = (idCard17: string) => {
  const weight = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  const checkCode = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    sum += parseInt(idCard17[i]) * weight[i];
  }
  return checkCode[sum % 11];
};

// 校验18位身份证的校验码
const validateCheckCode = (idCard18: string) => {
  const checkCode = getCheckCode(idCard18.slice(0, 17));
  return idCard18[17].toUpperCase() === checkCode;
};

// 外国人身份证
export const checkForeignerCard = (_rule: Rule, value: string) => {
  if (value?.length !== 18) {
    return Promise.reject('外国人身份证为18位！');
  }
  return Promise.resolve();
};
