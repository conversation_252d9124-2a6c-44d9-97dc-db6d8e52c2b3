interface CompressConfig {
  // 压缩质量配置
  quality: {
    // 文件大小阈值(单位:MB)
    size: number;
    // 压缩比例(0-1)
    ratio: number;
  }[];
  // 最大宽度(px)
  maxWidth?: number;
  // 最大高度(px)
  maxHeight?: number;
  // 输出图片格式
  mimeType?: string;
}

/**
 * 图片压缩
 * @param file 图片文件
 * @param config 压缩配置
 * @returns Promise<File>
 */
export const compressImage = async (file: File, config?: Partial<CompressConfig>): Promise<File> => {
  // 默认配置
  const defaultConfig: CompressConfig = {
    quality: [
      { size: 1, ratio: 0.92 }, // 1MB以下不压缩
      { size: 2, ratio: 0.8 }, // 1-2MB压缩30%
      { size: Infinity, ratio: 0.5 }, // 2MB以上压缩50%
    ],
    // maxWidth: 1920,
    // maxHeight: 1080,
    mimeType: file.type,
  };

  const finalConfig = { ...defaultConfig, ...config };

  // 获取压缩比例
  const getQuality = (fileSize: number): number => {
    const size = fileSize / 1024 / 1024; // 转为MB
    const quality = finalConfig.quality.find((q) => size <= q.size);
    return quality ? quality.ratio : finalConfig.quality[finalConfig.quality.length - 1].ratio;
  };

  // png图片不能使用这种压缩，会越压越大
  if (file && file.type === 'image/png') {
    return Promise.resolve(file);
  }

  if (file && file.size && file.size / 1024 / 1024 <= 1) {
    return Promise.resolve(file);
  }

  return new Promise((resolve, reject) => {
    // 创建文件读取器
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (e) => {
      // 创建图片对象
      const img = new Image();
      img.src = e.target?.result as string;

      img.onload = () => {
        // 创建canvas
        const canvas = document.createElement('canvas');
        const width = img.width;
        const height = img.height;

        // 等比例缩放
        // if (finalConfig.maxWidth && width > finalConfig.maxWidth) {
        //   height = (finalConfig.maxWidth / width) * height;
        //   width = finalConfig.maxWidth;
        // }

        // if (finalConfig.maxHeight && height > finalConfig.maxHeight) {
        //   width = (finalConfig.maxHeight / height) * width;
        //   height = finalConfig.maxHeight;
        // }

        canvas.width = width;
        canvas.height = height;

        // 绘制图片
        const ctx = canvas.getContext('2d');
        ctx?.drawImage(img, 0, 0, width, height);

        // 获取压缩比例
        const quality = getQuality(file.size);
        // 转换为blob
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Canvas to Blob failed'));
              return;
            }
            // 转换为文件
            const newFile = new File([blob], file.name, {
              type: finalConfig.mimeType,
              lastModified: Date.now(),
            });
            resolve(newFile);
          },
          finalConfig.mimeType,
          quality,
        );
      };

      img.onerror = () => {
        reject(new Error('Image load failed'));
      };
    };

    reader.onerror = () => {
      reject(new Error('File read failed'));
    };
  });
};
