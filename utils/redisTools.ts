import type { H3Event } from 'h3';

type TimeUnit = 'MILLISECONDS' | 'SECONDS' | 'MINUTES' | 'HOURS' | 'DAYS'; // 毫秒/秒/分钟/小时/天
export function setRedis(event: H3Event, data: { redisKey: string; data: unknown; timeout: number; timeUnit: TimeUnit }) {
  return new Promise((resolve) => {
    const { host, gateWay, service } = useRuntimeConfig().public || {};
    const baseUrl = host + gateWay + service.administrate;
    // const testBaseUrl = 'http://**************:9007/';
    $fetch(baseUrl + '/menu/saveMenuCacheRedis', {
      method: 'POST',
      headers: event.context.headers,
      body: data,
    }).then((res) => {
      console.log(event, '更新缓存接口返回：', res);
      if (res && res.code === '000000') {
        resolve({});
      } else {
        resolve(null);
      }
    }).catch((err) => {
      console.error(event, err);
      resolve(null);
    });
  });
}

export function getRedis(event: H3Event, key: string) {
  return new Promise((resolve) => {
    const { host, gateWay, service } = useRuntimeConfig().public || {};
    const baseUrl = host + gateWay + service.administrate;
    // const testBaseUrl = 'http://**************:9007/';
    $fetch(baseUrl + '/menu/getCacheMenuRedis', {
      method: 'GET',
      headers: event.context.headers,
      query: {
        redisKey: key,
      },
    }).then((res) => {
      console.log(event, '获取缓存接口返回：', res);
      if (res && res.code === '000000') {
        resolve(res?.data?.data || null);
      } else {
        resolve(null);
      }
    }).catch((err) => {
      console.error(event, err);
      resolve(null);
    });
  });
}

export function deleteRedis(event: H3Event, key: string) {
  return new Promise((resolve, reject) => {
    const { host, gateWay, service } = useRuntimeConfig().public || {};
    const baseUrl = host + gateWay + service.administrate;
    // const testBaseUrl = 'http://**************:9007/';
    $fetch(baseUrl + '/menu/deleteCacheMenuRedis', {
      method: 'GET',
      headers: event.context.headers,
      query: {
        redisKey: key,
      },
    }).then((res) => {
      console.log(event, '删除缓存接口返回：', res);
      resolve(res);
    }).catch((err) => {
      console.error(event, err);
      reject(err);
    });
  });
}
