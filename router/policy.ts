import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/policyQuery',
    component: () => import('@/pages/policy/policyQuery/policyQuery.vue'),
    name: 'policyQuery',
    meta: {
      title: '保单查询',
    },
  },
  {
    path: '/policyDetails',
    component: () => import('@/pages/policy/policyDetails/policyDetails.vue'),
    name: 'policyDetails',
    meta: {
      title: '保单详情',
    },
  },
];

export default routes;
