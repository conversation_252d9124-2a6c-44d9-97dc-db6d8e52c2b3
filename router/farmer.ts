import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/editFarmerList',
    component: () => import('@/pages/farmer/editFarmerList/editFarmerList.vue'),
    name: 'editFarmerList',
    meta: {
      title: '分户清单编辑',
    },
  },
  {
    path: '/dangerFarmerList',
    component: () => import('@/pages/farmer/dangerFarmerList/dangerFarmerList.vue'),
    name: 'dangerFarmerList',
    meta: {
      title: '风险分户清单',
    },
  },
  {
    path: '/errorFarmerList',
    component: () => import('@/pages/farmer/errorFarmerList/errorFarmerList.vue'),
    name: 'errorFarmerList',
    meta: {
      title: '错误分户清单',
    },
  },
  {
    path: '/farmerInfo',
    component: () => import('@/pages/farmer/farmerInfo/farmerInfo.vue'),
    name: 'farmerInfo',
    meta: {
      title: '农户信息页',
    },
  },
  {
    path: '/farmerList',
    component: () => import('@/pages/farmer/farmerList/farmerList.vue'),
    name: 'farmerList',
    meta: {
      title: '投保清单查询',
    },
  },
  {
    path: '/publicityFarmerList',
    component: () => import('@/pages/farmer/publicityList/publicityList.vue'),
    name: 'publicityFarmerList',
    meta: {
      title: '公示清单查询',
    },
  },
  {
    path: '/objectionsHandling',
    component: () => import('@/pages/farmer/objectionsHandling/objectionsHandling.vue'),
    name: 'objectionsHandling',
    meta: {
      title: '异议处理',
    },
  },
  {
    path: '/relatedFarmerList',
    component: () => import('@/pages/farmer/relatedList/relatedList.vue'),
    name: 'relatedFarmerList',
    meta: {
      title: '关联清单',
    },
  },
  {
    path: '/landQuery',
    component: () => import('@/pages/land/landManage/landQuery.vue'),
    name: 'landQuery',
    meta: {
      title: '地块查询',
    },
  },
  {
    path: '/landToList',
    component: () => import('@/pages/land/landManage/landToList.vue'),
    name: 'landToList',
    meta: {
      title: '按图制作',
    },
  },
  {
    path: '/landEdit',
    component: () => import('@/pages/land/landManage/landEdit.vue'),
    name: 'landEdit',
    meta: {
      title: '地块编辑',
    },
  },
  {
    path: '/landImport',
    component: () => import('@/pages/land/landManage/landImport.vue'),
    name: 'landImport',
    meta: {
      title: '地块导入',
    },
  },
  {
    path: '/landManage',
    component: () => import('@/pages/land/landManage/landManage.vue'),
    name: 'landManage',
    meta: {
      title: '地块管理',
    },
  },
];

export default routes;
