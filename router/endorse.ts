import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/endorseApply',
    component: () => import('@/pages/endorse/endorseApply/endorseApply.vue'),
    name: 'endorseApply',
    meta: {
      title: '批改申请',
    },
  },
  {
    path: '/endorseTracking',
    component: () => import('@/pages/endorse/endorseTracking/endorseTracking.vue'),
    name: 'endorseTracking',
    meta: {
      title: '批改跟踪',
    },
  },
  {
    path: '/endorseModify',
    component: () => import('@/pages/endorse/endorseModify/endorseModify.vue'),
    name: 'endorseModify',
    meta: {
      title: '整单批改',
    },
  },
  {
    path: '/endorseHistory',
    component: () => import('@/pages/endorse/endorseHistory/endorseHistory.vue'),
    name: 'endorseHistory',
    meta: {
      title: '历史批改记录',
    },
  },
  {
    path: '/surrenderModify',
    component: () => import('@/pages/endorse/endorseModify/surrenderModify.vue'),
    name: 'surrenderModify',
    meta: {
      title: '退保',
    },
  },
  {
    path: '/cancelModify',
    component: () => import('@/pages/endorse/endorseModify/cancelModify.vue'),
    name: 'cancelModify',
    meta: {
      title: '注销',
    },
  },
  {
    path: '/listModify',
    component: () => import('@/pages/endorse/endorseModify/listModify.vue'),
    name: 'listModify',
    meta: {
      title: '批改清单编辑',
    },
  },
  {
    path: '/endorseUpload',
    component: () => import('@/pages/endorse/endorseUpload/endorseUpload.vue'),
    name: 'endorseUpload',
    meta: {
      title: '附件管理',
    },
  },
  {
    path: '/claimRecord',
    component: () => import('@/pages/endorse/claimRecord/claimRecord.vue'),
    name: 'claimRecord',
    meta: {
      title: '理赔记录',
    },
  },
  {
    path: '/approvalTraceCheck',
    component: () => import('@/pages/endorse/approvalTraceCheck/approvalTraceCheck.vue'),
    name: 'approvalTraceCheck',
    meta: {
      title: '批改明细',
    },
  },
  {
    path: '/endorseFarmerInfo',
    component: () => import('@/pages/endorse/farmerInfo/farmerInfo.vue'),
    name: 'endorseFarmerInfo',
    meta: {
      title: '农户信息页',
    },
  },
  {
    path: '/endorseErrorFarmerList',
    component: () => import('@/pages/endorse/errorFarmerList/errorFarmerList.vue'),
    name: 'endorseErrorFarmerList',
    meta: {
      title: '错误分户清单',
    },
  },
  {
    path: '/endorseDangerFarmerList',
    component: () => import('@/pages/endorse/dangerFarmerList/dangerFarmerList.vue'),
    name: 'endorseDangerFarmerList',
    meta: {
      title: '风险分户清单',
    },
  },
];

export default routes;
