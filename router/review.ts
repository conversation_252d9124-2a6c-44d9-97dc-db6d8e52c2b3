import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/policyReview',
    component: () => import('@/pages/review/policyReview/policyReview.vue'),
    name: 'policyReview',
    meta: {
      title: '保单审核',
    },
  },
  {
    path: '/insureProcess',
    component: () => import('@/pages/review/insureProcess/insureProcess.vue'),
    name: 'insureProcess',
    meta: {
      title: '核保处理',
    },
  },
  {
    path: '/attachment',
    component: () => import('@/pages/review/attachment/attachment.vue'),
    name: 'attachment',
    meta: {
      title: '投保申请审核',
    },
  },
  {
    path: '/endorseVerify',
    component: () => import('@/pages/review/endorseVerify/endorseVerify.vue'),
    name: 'endorseVerify',
    meta: {
      title: '批单审核',
    },
  },
  {
    path: '/endorseVerifyDetail',
    component: () => import('@/pages/review/endorseVerifyDetail/endorseVerifyDetail.vue'),
    name: 'endorseVerifyDetail',
    meta: {
      title: '批单审核详情',
    },
  },
];

export default routes;
