import type { RouteRecordRaw } from 'vue-router';

/**
 * 信息修改
 */
const routes: RouteRecordRaw[] = [
  { // 信息修改
    path: '/infoModify',
    component: () => import('@/pages/infoModify/infoModify/infoModify.vue'),
    name: 'infoModify',
    meta: {
      title: '信息修改',
    },
  },
  { // 资料修改跟踪
    path: '/infoModificationTrack',
    component: () => import('@/pages/infoModify/infoModificationTrack/infoModificationTrack.vue'),
    name: 'infoModificationTrack',
    meta: {
      title: '资料修改跟踪',
    },
  },
  { // 资料修改审核
    path: '/infoModificationAudit',
    component: () => import('@/pages/infoModify/infoModificationAudit/infoModificationAudit.vue'),
    name: 'infoModificationAudit',
    meta: {
      title: '资料修改审核',
    },
  },
  { // 审核详情
    path: '/auditDetail',
    component: () => import('@/pages/infoModify/auditDetail/auditDetail.vue'),
    name: 'auditDetail',
    meta: {
      title: '审核详情',
    },
  },
  { // 资料修改
    path: '/fileModify',
    component: () => import('@/pages/infoModify/fileModify/fileModify.vue'),
    name: 'fileModify',
    meta: {
      title: '资料修改',
    },
  },
  { // 审核处理
    path: '/auditProcessing',
    component: () => import('@/pages/infoModify/auditProcessing/auditProcessing.vue'),
    name: 'auditProcessing',
    meta: {
      title: '审核处理',
    },
  },
];
export default routes;
