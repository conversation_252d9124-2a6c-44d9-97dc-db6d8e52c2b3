import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/queryCheckTask',
    component: () => import('@/pages/inspection/inspectionTask/inspectionTask.vue'),
    name: 'queryCheckTask',
    meta: {
      title: '验标任务查询',
    },
  },
  {
    path: '/inspectionInfo',
    component: () => import('@/pages/inspection/inspectionInfo/inspectionInfo.vue'),
    name: 'inspectionInfo',
    meta: {
      title: '验标信息',
    },
  },
  {
    path: '/targetScope',
    component: () => import('@/pages/inspection/inspectionInfo/targetScope.vue'),
    name: 'targetScope',
    meta: {
      title: '标的范围',
    },
  },
  {
    path: '/checkAudit',
    component: () => import('@/pages/inspection/checkAudit/index.vue'),
    name: 'checkAudit',
    meta: {
      title: '验标审核',
    },
  },
];

export default routes;
