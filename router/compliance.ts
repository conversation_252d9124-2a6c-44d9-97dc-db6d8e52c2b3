import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/uploadInfoManage',
    component: () => import('@/pages/compliance/uploadInfoManage/uploadInfoManage.vue'),
    name: 'uploadInfoManage',
    meta: {
      title: '上报情况管理',
    },
  },
  {
    path: '/platformBaseInfo',
    component: () => import('@/pages/compliance/platformBaseInfo/platformBaseInfo.vue'),
    name: 'platformBaseInfo',
    meta: {
      title: '平台信息配置',
    },
  },
  {
    path: '/processSchemeConfig',
    component: () => import('@/pages/compliance/processSchemeConfig/processSchemeConfig.vue'),
    name: 'processSchemeConfig',
    meta: {
      title: '处理方案配置',
    },
  },
  {
    path: '/dataMappingBasic',
    component: () => import('@/pages/compliance/dataMappingBasic/dataMappingBasic.vue'),
    name: 'dataMappingBasic',
    meta: {
      title: '基础数据影射',
    },
  },
  {
    path: '/dataMappingDetail',
    component: () => import('@/pages/compliance/dataMappingDetail/dataMappingDetail.vue'),
    name: 'dataMappingDetail',
    meta: {
      title: '明细映射配置',
    },
  },
  {
    path: '/platformDataManage',
    component: () => import('@/pages/compliance/platformDataManage/platformDataManage.vue'),
    name: 'platformDataManage',
    meta: {
      title: '平台数据维护',
    },
  },
  {
    path: '/platformInterface',
    component: () => import('@/pages/compliance/platformInterface/platformInterface.vue'),
    name: 'platformInterface',
    meta: {
      title: '接口信息配置',
    },
  },
  {
    path: '/paramsSet',
    component: () => import('@/pages/compliance/paramsSet/index.vue'),
    name: 'paramsSet',
    meta: {
      title: '参数配置',
    },
  },
];

export default routes;
