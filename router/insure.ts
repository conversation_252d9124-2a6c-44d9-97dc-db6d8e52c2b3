import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/insuranceApply',
    component: () => import('@/pages/insure/insuranceApply/insuranceApply.vue'),
    name: 'insuranceApply',
    meta: {
      title: '投保申请',
    },
  },
  {
    path: '/insuranceApplyDetail', // 投保申请需要区分查看和编辑权限，分开页面方便权限控制，后续查看页面替换新的查询接口
    component: () => import('@/pages/insure/insuranceApply/insuranceApply.vue'),
    name: 'insuranceApplyDetail',
    meta: {
      title: '投保申请详情',
    },
  },
  {
    path: '/insuranceFormFill',
    component: () => import('@/pages/insure/insuranceFormFill/insuranceFormFill.vue'),
    name: 'insuranceFormFill',
    meta: {
      title: '投保单录入',
    },
  },
  {
    path: '/insuranceTracking',
    component: () => import('@/pages/insure/insuranceTracking/insuranceTracking.vue'),
    name: 'insuranceTracking',
    meta: {
      title: '投保跟踪',
    },
  },
  {
    path: '/insuranceApplyAudit',
    component: () => import('@/pages/insure/insuranceApplyAudit/insuranceApplyAudit.vue'),
    name: 'insuranceApplyAudit',
    meta: {
      title: '投保申请审核',
    },
  },
  {
    path: '/imageUpload',
    component: () => import('@/pages/insure/imageUpload/imageUpload.vue'),
    name: 'imageUpload',
    meta: {
      title: '附件上传',
    },
  },
  {
    path: '/applyPolicyDetails',
    component: () => import('@/pages/insure/applyPolicyDetails/applyPolicyDetails.vue'),
    name: 'applyPolicyDetails',
    meta: {
      title: '投保单详情',
    },
  },
  {
    path: '/selfInsurance',
    component: () => import('@/pages/insure/selfInsurance/selfInsurance.vue'),
    name: 'selfInsurance',
    meta: {
      title: '自助投保配置',
    },
  },
  {
    path: '/selfInsuranceQuery',
    component: () => import('@/pages/insure/selfInsuranceQuery/selfInsuranceQuery.vue'),
    name: 'selfInsuranceQuery',
    meta: {
      title: '自助投保方案',
    },
  },

];

export default routes;
