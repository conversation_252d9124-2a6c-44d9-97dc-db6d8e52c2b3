import { defineStore } from 'pinia';

export interface UserInfo {
  umCode: string;
  umName: string;
  deptList: Array<string>;
  roleCodeList: Array<string>;
}

// 蘑菇云配置菜单结构
export interface MenuItem {
  id: number;
  authCode: string;
  menuName: string;
  menuType: string; // 01目录， 02菜单(可访问路由)
  menuUrl: string; // 路由path
  menuDesc: string;// 用来判断是否是详情页（hidden）
  subMenus?: MenuItem[];
}

export interface AuthPathBtn {
  [name: string]: string[];
}

export const useUserStore = defineStore('user', () => {
  // 是否执行初始化
  const hasInit = ref(false);
  const userInfo = reactive<UserInfo>(
    {
      umName: '',
      umCode: '',
      deptList: [],
      roleCodeList: [],
    },
  );

  const menuList = ref<MenuItem[]>([]); // 左侧菜单

  const authPathList = reactive<string[]>([]); // 左侧菜单打平，用于权限校验

  const authPathBtnObj = ref<AuthPathBtn>({});

  const changeInit = () => {
    // 保证只执行一次初始化
    if (!hasInit.value) {
      hasInit.value = true;
    }
  };

  const setUserInfo = ({ umName, umCode, deptList, roleCodeList }: UserInfo) => {
    userInfo.umName = umName;
    userInfo.umCode = umCode;
    userInfo.deptList = deptList;
    userInfo.roleCodeList = roleCodeList;
  };

  const setMenuList = (list: MenuItem[]) => {
    menuList.value = list;
  };

  const traverseMenuTree = (nodes?: MenuItem[]) => {
    if (!nodes) return; // 遍历出口;
    for (const node of nodes) {
      if (node.menuType === '02') {
        authPathList.push(node.menuUrl);
      }
      traverseMenuTree(node.subMenus);
    }
  };

  const traverBtnTree = (nodes?: MenuItem[]) => { // 整理按钮权限
    if (!nodes) return; // 遍历出口;
    for (const node of nodes) {
      if (node.menuType === '02' && node.subMenus && node.subMenus.length) {
        authPathBtnObj.value[node.menuUrl] = node.subMenus.map(val => val.authCode);
      }
      traverBtnTree(node.subMenus);
    }
  };

  const clearAuthPathList = () => {
    while (authPathList.length) {
      authPathList.pop();
    }
  };

  const clearAuthPathBtnList = () => {
    authPathBtnObj.value = {};
  };

  const setAuthPathList = (list: MenuItem[]) => {
    // 清空数组
    clearAuthPathList();
    clearAuthPathBtnList();
    authPathList.push('/home'); // 用户有权访问的菜单
    authPathList.push('/noAuth');
    traverseMenuTree(list);
    traverBtnTree(list);
  };

  return {
    userInfo,
    menuList,
    hasInit,
    authPathList,
    authPathBtnObj,
    changeInit,
    setUserInfo,
    setMenuList,
    setAuthPathList,
  };
});
