export function useRedis<T = any>() {
  const storage = useStorage('redis');

  // 获取缓存
  const getItem = async (key: string): Promise<T | null> => {
    try {
      const cacheData = await storage.getItem<T>(key);
      return cacheData;
    } catch(err) {
      return null;
    }
  }

  // 设置缓存，ttl设置缓存最大时间（秒）
  const setItem = async (key: string, value: T, ttl: number) => {
    try {
      storage.setItem<T>(key, value, {ttl})
    } catch(err) {
      console.log('设置缓存失败', key)
    }
  }

   // 同步设置缓存
   const setItemAsync = async (key: string, value: T, ttl: number) => {
    try {
      await storage.setItem<T>(key, value, {ttl})
      return {code: '000000', msg: 'success'}
    } catch(err) {
      console.log('设置缓存失败', key)
      return {code: '100000', msg: err.message || 'set item fail'}
    }
  }

  const clear = async (base) => {
    // console.log('storage', storage.getMounts())
    // const instance = storage.getInstance();
    // const masters = instance.nodes('master');
    // const result = [];
    // for (const master of masters) {
    //   const keys = await master.keys();
    //   result.push(keys);
    // }
    const keys = await storage.getKeys(base)

    console.log('----keys-----', keys)
    try {
      if (keys.length) {
        for (const item of keys) {
          await storage.removeItem(item)
        }
      } else {
        return {code: '100000', msg: '搜索不到这个key，请多试几次'}
      }

      return {code: '000000', msg: 'success'}
    } catch(err) {
      return {code: '100000', msg: err.message || 'clear item fail'}
    }
  }

  return {
    getItem,
    setItem,
    setItemAsync,
    clear,
  }
}