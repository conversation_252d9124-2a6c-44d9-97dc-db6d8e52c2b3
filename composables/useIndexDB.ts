import { openDB } from 'idb';
import { $postOnClient } from './request';

interface RequestParams {
  url: string;
  params: Array<string>;
}

export function useIndexDB() {
  const open = async () => {
    return await openDB('icore-agr-an-web', 1, {
      upgrade(db) {
        db.createObjectStore('optionData');
      },
    });
  };
  /*
  storeName: indexDb store的名称 目前只有optionData
  requestParams: 发起请求的参数包括url、params
  发起请求之前先去indexdb查找是否有对应的数据 如果有数据则取indexdb的数据 如果没数据则调用接口拿数据并写入indexdb
  */
  const getCacheData = async (storeName: string, requestParams: RequestParams) => {
    const result: unknown[] = [];
    const paramsArr: string[] = [];
    // indexdb实例
    const db = await openDB('icore-agr-an-web', 1, {
      upgrade(db) {
        db.createObjectStore('optionData');
      } });
    await Promise.all(requestParams.params.map(async (item) => {
      const fetchedItem = await db.get(storeName, item); // 获取indexDB的值

      if (!fetchedItem) {
        paramsArr.push(item); // 如果未获取到，则更新params数组
      } else {
        result.push(fetchedItem); // 如果获取到，则更新结果数组
      }
    }));
    // 如果请求paramsArr有值，则发起请求拉取最新数据
    if (paramsArr?.length > 0) {
      const res = await $postOnClient(requestParams.url, paramsArr);
      if (res && res?.code === '000000') {
        res.data.forEach((item: { value: string }) => {
          db.put('optionData', item, item.value);
        });
        return res.data;
      }
    } else {
      // paramsArr没值，代表从indexDB取值
      return result;
    }
  };

  return {
    open,
    getCacheData,
  };
}
