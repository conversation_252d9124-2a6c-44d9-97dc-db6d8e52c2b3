// useRowSelect函数用于处理表格行的选择逻辑
export function useRowSelect(pagination: any) {
  // 为选中的行的键创建一个响应式引用
  const selectedRowKeys = ref<any[]>([]);
  // 表格翻页时记录之前的数据
  const selectedCache = new Map(); 
  // 当选择发生改变时的回调函数，用于更新选中的行的键集合
  const onSelectChange = (keys: any[]) => {
    selectedCache.set(pagination.current, keys)
    const allKeys: any[] = [];
    selectedCache.forEach((value) => {
      allKeys.push(value);
    });
    selectedRowKeys.value = allKeys.flat();
  };

  // 清空选中的行
  const clearSelect = () => {
    selectedCache.clear();
    selectedRowKeys.value = [];
  };

  // 返回选中行的键集合以及操作方法
  return {
    selectedRowKeys,
    onSelectChange,
    clearSelect
  }
}