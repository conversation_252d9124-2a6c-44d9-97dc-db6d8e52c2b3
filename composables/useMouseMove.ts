export interface UseMouseMoveProps {
  moveId: string;
  targetId?: string;
  needListenwheel?: boolean; // 是否需要监听滚轮放大放小
}
// 支持某元素拖拽功能，该元素在放大、缩小、翻转情况下同样适用。
// 注意设置该元素transform属性时，一定要将translate放到rotate前面
export function useMouseMove({ moveId, targetId, needListenwheel = false }: UseMouseMoveProps) {
  const currentX = ref<number>();
  const currentY = ref<number>();
  const initialX = ref<number>();
  const initialY = ref<number>();
  const xOffset = ref<number>(0);
  const yOffset = ref<number>(0);
  const initScaleX = ref<number>(1); // 放大、放小 initScaleX和initScaleY的值是一样的，仅支持XY同时放大放小
  const initScaleY = ref<number>(1); // 放大、放小
  const initRotation = ref<number>(0);
  const draggableDom = ref<HTMLElement | null>(null); // 需要移动的dom节点
  const moveDom = ref<HTMLElement | null>(null); // 触发移动操作的dom节点
  const dragStart = ref(false);
  const MIN_DRAG_DISTANCE = 5; // 最小拖动距离（像素）
  const isDragging = ref(false); // 是否在滑动中

  const getTranslateValues = (transform) => {
    // 匹配matrix格式
    const matrixMatch = transform.match(/matrix\(([^)]+)\)/);
    if (matrixMatch) {
      const values = matrixMatch[1].split(',').map(Number);
      // matrix的最后两个值是translateX和translateY

      const a = values[0];
      const b = values[1];
      const c = values[2];
      const d = values[3];
      const e = values[4];
      const f = values[5];

      // 计算旋转角度
      const angle = Math.atan2(b, a) * (180 / Math.PI);
      const rotation = angle.toFixed(2);

      // 计算缩放因子
      const scaleX = Math.sqrt(a * a + b * b).toFixed(2);
      const scaleY = Math.sqrt(c * c + d * d).toFixed(2);

      // 计算平移
      const translateX = (e / scaleX).toFixed(2);
      const translateY = (f / scaleY).toFixed(2);

      return { translateX, translateY, scaleX, scaleY, rotation };
    } else {
      // 如果不是矩阵解析，可能会获取错
      return { translateX: 0, translateY: 0, scaleX: 1, scaleY: 1, rotation: 0 };
    }
  };

  // 更新图标位置
  const setTranslate = (xPos, yPos) => {
    // 计算拖动距离
    const distance = Math.sqrt(xPos ** 2 + yPos ** 2);
    if (distance > MIN_DRAG_DISTANCE) {
      isDragging.value = true;
    }
    draggableDom.value.style.transform = `scale(${initScaleX.value}, ${initScaleY.value}) translate(${xPos}px, ${yPos}px) rotate(${initRotation.value}deg)`;
  };

  const elementMouseMove = (e) => {
    if (dragStart.value) {
      currentX.value = e.clientX;
      currentY.value = e.clientY;
      xOffset.value = currentX.value - initialX.value;
      yOffset.value = currentY.value - initialY.value;
      setTranslate(xOffset.value, yOffset.value);
    }
  };

  const elementMouseDown = (e) => {
    e.preventDefault();
    e.stopPropagation();
    const style = window.getComputedStyle(draggableDom.value);
    const transform = style.getPropertyValue('transform');
    const { translateX, translateY, scaleX, scaleY, rotation } = getTranslateValues(transform);
    if (translateX) {
      xOffset.value = translateX;
    }
    if (translateY) {
      yOffset.value = translateY;
    }
    initScaleX.value = Number(scaleX);
    initScaleY.value = Number(scaleY);
    initRotation.value = parseInt(rotation);

    dragStart.value = true;
    initialX.value = e.clientX - xOffset.value;
    initialY.value = e.clientY - yOffset.value;
    // 鼠标移动事件
    document.addEventListener('mousemove', elementMouseMove);
    // 鼠标松开事件
    document.addEventListener('mouseup', elementMouseUp);
  };

  const elementWheel = (e) => {
    e.preventDefault();
    const delta = e.deltaY;
    initScaleX.value = delta < 0 ? Number((initScaleX.value + 0.1).toFixed(1)) : Number((initScaleX.value - 0.1).toFixed(1)); // 向上滚放大，向下滚缩小
    initScaleY.value = delta < 0 ? Number((initScaleY.value + 0.1).toFixed(1)) : Number((initScaleY.value - 0.1).toFixed(1)); // 向上滚放大，向下滚缩小

    console.log(initScaleX.value, initScaleY.value);
  };

  const elementMouseUp = () => {
    document.removeEventListener('mousemove', elementMouseMove);
    document.removeEventListener('mouseup', elementMouseUp);
    dragStart.value = false;
    setTimeout(() => {
      isDragging.value = false;
    }, 10);
  };

  const addDomEvent = () => {
    moveDom.value = document.getElementById(moveId);
    draggableDom.value = document.getElementById(targetId || moveId);
    // 鼠标按下事件
    if (moveDom.value) {
      moveDom.value.removeEventListener('mousedown', elementMouseDown);
      moveDom.value.addEventListener('mousedown', elementMouseDown);

      if (needListenwheel) {
        // 添加滚轮事件
        moveDom.value.removeEventListener('wheel', elementWheel);
        moveDom.value.addEventListener('wheel', elementWheel);
      }
    }
  };

  onMounted(() => {
    addDomEvent();
  });

  const removeDomEvent = () => {
    if (moveDom.value) {
      moveDom.value.removeEventListener('mousedown', elementMouseDown);
      moveDom.value.removeEventListener('wheel', elementWheel);
    }
  };

  onUnmounted(() => {
    removeDomEvent();
  });

  const stop = () => {
    if (moveDom.value) {
      elementMouseUp();
    }
  };

  // 某些元素并非是在onMounted就可以添加监听事件的
  const addListener = async () => {
    await nextTick();
    addDomEvent();
  };

  return {
    isDragging,
    stop,
    addListener,
    translateX: xOffset,
    translateY: yOffset,
    scale: initScaleX,
    rotate: initRotation,
  };
}
