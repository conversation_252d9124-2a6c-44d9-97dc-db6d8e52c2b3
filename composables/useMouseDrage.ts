export interface UseMouseMoveProps {
  moveId: string;
  targetId?: string;
  direction: 'top' | 'left' | 'bottom' | 'right';
}
// 支持某元素往上下左右四个方向拉伸四边
export const useMouseDrage = ({ moveId, targetId, direction }: UseMouseMoveProps) => {
  const width = ref(0);
  const height = ref(0);
  const transformX = ref(0);
  const transformY = ref(0);
  const resizing = ref(false);
  const resizeType = ref('');
  const initialX = ref(0);
  const initialY = ref(0);
  const initialWidth = ref(0);
  const initialHeight = ref(0);
  const initialTransformX = ref(0);
  const initialTransformY = ref(0);
  const draggableDom = ref<HTMLElement | null>(null); // 需要移动的dom节点
  const moveDom = ref<HTMLElement | null>(null); // 触发移动操作的dom节点
  const getTranslateValues = (transform) => {
    // 匹配matrix格式
    const matrixMatch = transform.match(/matrix\(([^)]+)\)/);
    if (matrixMatch) {
      const values = matrixMatch[1].split(',').map(Number);
      // matrix的最后两个值是translateX和translateY
      const a = values[0];
      const b = values[1];
      const c = values[2];
      const d = values[3];
      const e = values[4];
      const f = values[5];
      // 计算旋转角度
      const angle = Math.atan2(b, a) * (180 / Math.PI);
      const rotation = angle.toFixed(2);
      // 计算缩放因子
      const scaleX = Math.sqrt(a * a + b * b).toFixed(2);
      const scaleY = Math.sqrt(c * c + d * d).toFixed(2);
      // 计算平移
      const translateX = (e / scaleX).toFixed(2);
      const translateY = (f / scaleY).toFixed(2);
      return { translateX, translateY, scaleX, scaleY, rotation };
    } else {
      // 如果不是矩阵解析，可能会获取错
      return { translateX: 0, translateY: 0, scaleX: 0, scaleY: 0, rotation: 0 };
    }
  };

  // 更新图标位置
  const updateTarget = () => {
    draggableDom.value.style.transform = `translate(${transformX.value}px, ${transformY.value}px)`;
    draggableDom.value.style.width = `${width.value}px`;
    draggableDom.value.style.height = `${height.value}px`;
  };

  const startResize = (event) => {
    resizing.value = true;
    resizeType.value = direction;
    const style = window.getComputedStyle(draggableDom.value);
    const transform = style.getPropertyValue('transform');
    const { translateX, translateY } = getTranslateValues(transform);
    width.value = parseFloat(style.width); // 获取宽度数值
    height.value = parseFloat(style.height); // 获取高度数值
    transformX.value = translateX;
    transformY.value = translateY;

    initialX.value = event.clientX;
    initialY.value = event.clientY;
    initialWidth.value = width.value;
    initialHeight.value = height.value;
    initialTransformX.value = transformX.value;
    initialTransformY.value = transformY.value;
    document.addEventListener('mousemove', resize);
  };
  const resize = (event) => {
    if (!resizing.value) return;
    switch (resizeType.value) {
      case 'top':
        height.value = initialHeight.value - (event.clientY - initialY.value);
        transformY.value = initialTransformY.value - (initialY.value - event.clientY);
        break;
      case 'right':
        width.value = initialWidth.value + (event.clientX - initialX.value);
        break;
      case 'bottom':
        height.value = initialHeight.value + (event.clientY - initialY.value);
        break;
      case 'left':
        width.value = initialWidth.value - (event.clientX - initialX.value);
        transformX.value = initialTransformX.value - (initialX.value - event.clientX);
        break;
    }
    updateTarget();
  };

  const stopResize = () => {
    resizing.value = false;
    document.removeEventListener('mousemove', resize);
    // document.removeEventListener('mouseup', stopResize);
  };

  onMounted(() => {
    moveDom.value = document.getElementById(moveId);
    draggableDom.value = document.getElementById(targetId || moveId);
    // 鼠标按下事件
    if (moveDom.value) {
      moveDom.value.addEventListener('mousedown', startResize);
    }
    document.addEventListener('mouseup', stopResize);
  });
  onUnmounted(() => {
    if (moveDom.value) {
      moveDom.value.removeEventListener('mousedown', startResize);
      document.removeEventListener('mouseup', stopResize);
    }
  });

  const stop = () => {
    if (moveDom.value) {
      stopResize();
    }
  };

  return {
    stop,
  };
};
