import type { TablePaginationConfig } from 'ant-design-vue';

// 导出一个自定义 Hook 用于处理分页逻辑
export function usePagination(
  refresh: () => void, // 刷新函数，当分页器变化时调用
  pageSizeOptions?: string[],
) {
  // 分页器变化函数，更新当前页和页面大小并调用刷新函数
  const onPaginationChange = (page: number, pageSize: number) => {
    pagination.current = page;
    pagination.pageSize = pageSize;
    refresh();
  };

  // 响应式分页器对象
  const pagination = reactive<TablePaginationConfig>(
    {
      size: 'small', // 分页器尺寸
      showLessItems: true, // 是否显示较少页面内容
      showSizeChanger: true, // 是否显示页面大小切换器
      showQuickJumper: true, // 是否显示快速跳转
      onChange: onPaginationChange, // 当页码改变时的回调
      showTotal: (total: number) => `共${total}条`, // 总条目显示文本
      pageSizeOptions: ['10', '20', '50', '100'].concat(pageSizeOptions || []),
      current: 1, // 当前页码
      pageSize: 10, // 每页条数
      total: 0, // 总条目数
    },
  );

  // 返回包含分页器对象的对象
  return {
    pagination,
  };
}
