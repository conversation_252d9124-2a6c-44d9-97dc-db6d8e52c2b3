<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title></title>
</head>

<body>
  <script>
    /** 核保处理页面/insureProcess，再/共保信息模块--是（非总对总FAC）-点击发起再保申请，
     * 跳转到再保系统，再保系统发起签报成功后，调用此方法，
        关闭再保系统，拿到对应参数
    */
      var str = location.search.substring(1, location.search.length);
      var param = decodeURI(str);
      console.log('FAC result:', param);
      var paramList = param.split('&');
      var params = {};
      for (var i = 0; i < paramList.length; i++) {
          var element = paramList[i].split('=');
          params[element[0]] = element[1]
      }
      window.opener.facCallbackParamStr(params);
      window.close();
  </script>

</body>

</html>