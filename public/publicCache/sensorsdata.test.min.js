!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():t()}(this,function(){function e(e,t,r){if(null==e)return!1;if(Qt&&e.forEach===Qt)e.forEach(t,r);else if(nr(e)&&e.length===+e.length){for(var i=0,n=e.length;i<n;i++)if(i in e&&t.call(r,e[i],i,e)===ir)return!1}else for(var a in e)if(rr.call(e,a)&&t.call(r,e[a],a,e)===ir)return!1}function t(t,r){var i=[];return null==t?i:Array.prototype.map&&t.map===Array.prototype.map?t.map(r):(e(t,function(e,t,n){i.push(r(e,t,n))}),i)}function r(t){return e(Yt.call(arguments,1),function(e){for(var r in e)rr.call(e,r)&&void 0!==e[r]&&(t[r]=e[r])}),t}function i(t){return e(Yt.call(arguments,1),function(e){for(var i in e)void 0!==e[i]&&(_(e[i])&&_(t[i])?r(t[i],e[i]):t[i]=e[i])}),t}function n(t){return e(Yt.call(arguments,1),function(e){for(var r in e)void 0!==e[r]&&void 0===t[r]&&(t[r]=e[r])}),t}function a(e){if(!e)return!1;var t=tr.call(e);return"[object Function]"==t||"[object AsyncFunction]"==t}function s(e){return!(!e||!rr.call(e,"callee"))}function l(e){return e?e.toArray?e.toArray():nr(e)?Yt.call(e):s(e)?Yt.call(e):c(e):[]}function c(t){var r=[];return null==t?r:(e(t,function(e){r[r.length]=e}),r)}function u(e,t){var r=e.indexOf;if(r)return r.call(e,t);for(var i=0;i<e.length;i++)if(t===e[i])return i;return-1}function d(e,t,r){var i=Object.prototype.hasOwnProperty;if(e.filter)return e.filter(t);for(var n=[],a=0;a<e.length;a++)if(i.call(e,a)){var s=e[a];t.call(r,s,a,e)&&n.push(s)}return n}function p(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}function _(e){return null!=e&&"[object Object]"==tr.call(e)}function f(e){if(_(e)){for(var t in e)if(rr.call(e,t))return!1;return!0}return!1}function g(e){return void 0===e}function h(e){return"[object String]"==tr.call(e)}function v(e){return"[object Date]"==tr.call(e)}function y(e){return"[object Boolean]"==tr.call(e)}function w(e){return"[object Number]"==tr.call(e)&&/[\d\.]+/.test(String(e))}function S(e){return!(!e||1!==e.nodeType)}function b(e){try{JSON.parse(e)}catch(t){return!1}return!0}function k(e){var t=null;try{t=JSON.parse(e)}catch(r){return!1}return t}function P(e,t,r){var i,n,a,s=null,o=0;r||(r={});var l=function(){o=r.leading===!1?0:sr(),s=null,a=e.apply(i,n),s||(i=n=null)};return function(){var c=sr();o||r.leading!==!1||(o=c);var u=t-(c-o);return i=this,n=arguments,u<=0||u>t?(s&&(clearTimeout(s),s=null),o=c,a=e.apply(i,n),s||(i=n=null)):s||r.trailing===!1||(s=setTimeout(l,u)),a}}function D(e){if("string"!=typeof e)return 0;var t=0,r=null;if(0==e.length)return t;for(var i=0;i<e.length;i++)r=e.charCodeAt(i),t=(t<<5)-t+r,t&=t;return t}function C(e){var t=9007199254740992,r=-9007199254740992,i=31,n=0;if(e.length>0)for(var a=e.split(""),s=0;s<a.length;s++){var o=a[s].charCodeAt(),l=i*n+o;if(l>t)for(n=r+n;l=i*n+o,l<r;)n=n/2+o;if(l<r)for(n=t+n;l=i*n+o,l>t;)n=n/2+o;n=i*n+o}return n}function N(){if("function"==typeof Uint32Array){var e="";if("undefined"!=typeof crypto?e=crypto:"undefined"!=typeof msCrypto&&(e=msCrypto),_(e)&&e.getRandomValues){var t=new Uint32Array(1),r=e.getRandomValues(t)[0],i=Math.pow(2,32);return r/i}}return ar(1e19)/1e19}function $(e){try{return JSON.stringify(e,null,"  ")}catch(t){return JSON.stringify(e)}}function j(e){for(var t,r=[],i={},n=0;n<e.length;n++)t=e[n],t in i||(i[t]=!0,r.push(t));return r}function A(e){var t="";try{t=btoa(encodeURIComponent(e).replace(/%([0-9A-F]{2})/g,function(e,t){return String.fromCharCode("0x"+t)}))}catch(r){t=e}return t}function O(e){var r=[];try{r=t(atob(e).split(""),function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})}catch(i){r=[]}try{return decodeURIComponent(r.join(""))}catch(i){return r.join("")}}function I(e,t){e=String(e),t="number"==typeof t?t:13;for(var r=126,i=e.split(""),n=0,a=i.length;n<a;n++){var s=i[n].charCodeAt(0);s<r&&(i[n]=String.fromCharCode((i[n].charCodeAt(0)+t)%r))}return i.join("")}function T(e){var t=13,r=126;return e=String(e),I(e,r-t)}function x(e){var t="t6KJCZa5pDdQ9khoEM3Tj70fbP2eLSyc4BrsYugARqFIw1mzlGNVXOHiWvxUn8",r=t.length-1,i={},n=0;for(n=0;n<t.length;n++)i[t.charAt(n)]=t.charAt(r-n);var a="";for(n=0;n<e.length;n++)a+=e.charAt(n)in i?i[e.charAt(n)]:e.charAt(n);return a}function L(e,t){if("string"!=typeof e)return t("\u8f6c\u6362unicode\u9519\u8bef",e),e;for(var r="",i=0;i<e.length;i++)r+="\\"+e.charCodeAt(i).toString(16);return r}function E(){var e=!0,t="__sensorsdatasupport__",r="testIsSupportStorage";try{sessionStorage&&sessionStorage.setItem?(sessionStorage.setItem(t,r),sessionStorage.removeItem(t,r),e=!0):e=!1}catch(i){e=!1}return e}function H(){if((E()&&"true"===sessionStorage.getItem("sensorsdata_jssdk_debug")||or.show_log)&&(!_(arguments[0])||or.show_log!==!0&&"string"!==or.show_log&&or.show_log!==!1||(arguments[0]=$(arguments[0])),"object"==typeof console&&console.log))try{return console.log.apply(console,arguments)}catch(e){console.log(arguments[0])}}function J(e,t){if("string"==typeof t)return B(e,t);if(nr(t)){for(var r=!1,i=0;i<t.length;i++){var n=B(e,t[i]);if(n){r=!0;break}}return r}}function B(e,t){return e.hasAttribute?e.hasAttribute(t):e.attributes?!(!e.attributes[t]||!e.attributes[t].specified):void 0}function U(e,t){var r="",i="";return e.textContent?r=p(e.textContent):e.innerText&&(r=p(e.innerText)),r&&(r=r.replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)),i=r||"","input"!==t&&"INPUT"!==t||("button"===e.type||"submit"===e.type?i=e.value||"":or.heatmap&&"function"==typeof or.heatmap.collect_input&&or.heatmap.collect_input(e)&&(i=e.value||"")),i}function R(e){e=r({success:function(){},error:function(){},appendCall:function(e){document.getElementsByTagName("head")[0].appendChild(e)}},e);var t=null;"css"===e.type&&(t=document.createElement("link"),t.rel="stylesheet",t.href=e.url),"js"===e.type&&(t=document.createElement("script"),t.async="async",t.setAttribute("charset","UTF-8"),t.src=e.url,t.type="text/javascript"),t.onload=t.onreadystatechange=function(){this.readyState&&"loaded"!==this.readyState&&"complete"!==this.readyState||(e.success(),t.onload=t.onreadystatechange=null)},t.onerror=function(){e.error(),t.onerror=null},e.appendCall(t)}function M(e){return new M.init(e)}function K(e){var t=document.createElement("style");t.type="text/css";try{t.appendChild(document.createTextNode(e))}catch(r){t.styleSheet.cssText=e}var i=document.getElementsByTagName("head")[0],n=document.getElementsByTagName("script")[0];i?i.children.length?i.insertBefore(t,i.children[0]):i.appendChild(t):n.parentNode.insertBefore(t,n)}function W(e){function t(e,t){e=p(e);var r;if("body"===e)return document.getElementsByTagName("body")[0];if(0===e.indexOf("#"))e=e.slice(1),r=document.getElementById(e);else if(e.indexOf(":nth-of-type")>-1){var i=e.split(":nth-of-type");if(!i[0]||!i[1])return null;var n=i[0],a=i[1].match(/\(([0-9]+)\)/);if(!a||!a[1])return null;var s=Number(a[1]);if(!(S(t)&&t.children&&t.children.length>0))return null;for(var o=t.children,l=0;l<o.length;l++)if(S(o[l])){var c=o[l].tagName.toLowerCase();if(c===n&&(s--,0===s)){r=o[l];break}}if(s>0)return null}return r?r:null}function r(e){var n,a=i.shift();if(!a)return e;try{n=t(a,e)}catch(s){H(s)}return n&&S(n)?r(n):null}if(!h(e))return null;var i=e.split(">"),n=null;return n=r(),n&&S(n)?n:null}function V(e){var t=e;try{t=decodeURIComponent(e)}catch(r){t=e}return t}function q(e){var t=e;try{t=decodeURI(e)}catch(r){t=e}return t}function F(e,t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]"),e=V(e);var r="[\\?&]"+t+"=([^&#]*)",i=new RegExp(r),n=i.exec(e);return null===n||n&&"string"!=typeof n[1]&&n[1].length?"":V(n[1])}function z(e){var t=function(e){this._fields={Username:4,Password:5,Port:7,Protocol:2,Host:6,Path:8,URL:0,QueryString:9,Fragment:10},this._values={},this._regex=null,this._regex=/^((\w+):\/\/)?((\w+):?(\w+)?@)?([^\/\?:]+):?(\d+)?(\/?[^\?#]+)?\??([^#]+)?#?(\w*)/,"undefined"!=typeof e&&this._parse(e)};return t.prototype.setUrl=function(e){this._parse(e)},t.prototype._initValues=function(){for(var e in this._fields)this._values[e]=""},t.prototype.addQueryString=function(e){if("object"!=typeof e)return!1;var t=this._values.QueryString||"";for(var r in e)t=new RegExp(r+"[^&]+").test(t)?t.replace(new RegExp(r+"[^&]+"),r+"="+e[r]):"&"===t.slice(-1)?t+r+"="+e[r]:""===t?r+"="+e[r]:t+"&"+r+"="+e[r];this._values.QueryString=t},t.prototype.getUrl=function(){var e="";return e+=this._values.Origin,e+=this._values.Port?":"+this._values.Port:"",e+=this._values.Path,e+=this._values.QueryString?"?"+this._values.QueryString:"",e+=this._values.Fragment?"#"+this._values.Fragment:""},t.prototype.getUrl=function(){var e="";return e+=this._values.Origin,e+=this._values.Port?":"+this._values.Port:"",e+=this._values.Path,e+=this._values.QueryString?"?"+this._values.QueryString:""},t.prototype._parse=function(e){this._initValues();var t=this._regex.exec(e);t||H("DPURLParser::_parse -> Invalid URL");for(var r in this._fields)"undefined"!=typeof t[this._fields[r]]&&(this._values[r]=t[this._fields[r]]);this._values.Hostname=this._values.Host.replace(/:\d+$/,""),this._values.Origin=this._values.Protocol+"://"+this._values.Hostname},new t(e)}function X(e){e=e||"";for(var t=function(e){return V(e)},r={},i=e.substring(1),n=i.split("&"),a=0;a<n.length;a++){var s=n[a].indexOf("=");if(s!==-1){var o=n[a].substring(0,s),l=n[a].substring(s+1);o=t(o),l=t(l),r[o]=l}}return r}function Z(e){var t={},r=function(){var e;try{return e=new URL("http://modernizr.com/"),"http://modernizr.com/"===e.href}catch(t){return!1}};if("function"==typeof window.URL&&r())t=new URL(e),t.searchParams||(t.searchParams=function(){var e=X(t.search);return{get:function(t){return e[t]}}}());else{h(e)||(e=String(e)),e=p(e);var i=/^https?:\/\/.+/;if(i.test(e)===!1)return void H("Invalid URL");var n=z(e);t.hash="",t.host=n._values.Host?n._values.Host+(n._values.Port?":"+n._values.Port:""):"",t.href=n._values.URL,t.password=n._values.Password,t.pathname=n._values.Path,t.port=n._values.Port,t.search=n._values.QueryString?"?"+n._values.QueryString:"",t.username=n._values.Username,t.hostname=n._values.Hostname,t.protocol=n._values.Protocol?n._values.Protocol+":":"",t.origin=n._values.Origin?n._values.Origin+(n._values.Port?":"+n._values.Port:""):"",t.searchParams=function(){var e=X("?"+n._values.QueryString);return{get:function(t){return e[t]}}}()}return t}function Q(e,t){t&&"string"==typeof t||(t="hostname\u89e3\u6790\u5f02\u5e38");var r=null;try{r=Z(e).hostname}catch(i){H("getHostname\u4f20\u5165\u7684url\u53c2\u6570\u4e0d\u5408\u6cd5\uff01")}return r||t}function Y(e){var t={},r=e.split("?"),i=r[1]||"";return i&&(t=X("?"+i)),t}function G(e){return h(e)?(e=p(e),q(e)):q(location.href)}function ee(t){return e(t,function(e,r){v(e)?t[r]=te(e):_(e)&&(t[r]=ee(e))}),t}function te(e){function t(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+t(e.getMonth()+1)+"-"+t(e.getDate())+" "+t(e.getHours())+":"+t(e.getMinutes())+":"+t(e.getSeconds())+"."+t(e.getMilliseconds())}function re(t){_(t)&&e(t,function(e,r){_(e)?re(t[r]):v(e)&&(t[r]=te(e))})}function ie(t){var r=t.properties,i=JSON.parse(JSON.stringify(t));_(r)&&e(r,function(e,t){if(a(e))try{r[t]=e(i),a(r[t])&&(H("\u60a8\u7684\u5c5e\u6027- "+t+" \u683c\u5f0f\u4e0d\u6ee1\u8db3\u8981\u6c42\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664"),delete r[t])}catch(n){delete r[t],H("\u60a8\u7684\u5c5e\u6027- "+t+" \u629b\u51fa\u4e86\u5f02\u5e38\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664")}})}function ne(e){if("object"==typeof e&&e.$option){var t=e.$option;return delete e.$option,t}return{}}function ae(t){var r={};return e(t,function(e,t){null!=e&&(r[t]=e)}),r}function se(e){var t=or.current_domain;switch(typeof t){case"function":var r=t();return""===r||""===p(r)?"url\u89e3\u6790\u5931\u8d25":r.indexOf(".")!==-1?r:"url\u89e3\u6790\u5931\u8d25";case"string":return""===t||""===p(t)?"url\u89e3\u6790\u5931\u8d25":t.indexOf(".")!==-1?t:"url\u89e3\u6790\u5931\u8d25";default:var i=de();return""===e?"url\u89e3\u6790\u5931\u8d25":""===i?"url\u89e3\u6790\u5931\u8d25":i}}function oe(e){if(!e.target)return!1;var t=e.target,r=t.tagName.toLowerCase(),i={};return i.$element_type=r,i.$element_name=t.getAttribute("name"),i.$element_id=t.getAttribute("id"),i.$element_class_name="string"==typeof t.className?t.className:null,i.$element_target_url=t.getAttribute("href"),i.$element_content=U(t,r),i=ae(i),i.$url=G(),i.$url_path=location.pathname,i.$title=document.title,i}function le(){var e=document.referrer,t="baidu.com";if(!e)return!1;try{var r=Z(e).hostname;return r&&r.substring(r.length-t.length)===t}catch(i){return!1}}function ce(){var e=Y(document.referrer);return f(e)||!e.eqid?fr().replace(/-/g,""):e.eqid}function ue(){var e=Y(document.referrer);if(f(e)||!e.eqid){var t=Y(location.href);return e.ck||t.utm_source?"baidu_sem_keyword_id":"baidu_other_keyword_id"}return"baidu_seo_keyword_id"}function de(e){function t(e){return!!e&&e}e=e||location.hostname;var r=t(e);if(!r)return"";var i=r.split(".");if(nr(i)&&i.length>=2&&!/^(\d+\.)+\d+$/.test(r))for(var n="."+i.splice(i.length-1,1);i.length>0;)if(n="."+i.splice(i.length-1,1)+n,document.cookie="sensorsdata_domain_test=true; path=/; SameSite=Lax; domain="+n,document.cookie.indexOf("sensorsdata_domain_test=true")!==-1){var a=new Date;return a.setTime(a.getTime()-1e3),document.cookie="sensorsdata_domain_test=true; expires="+a.toGMTString()+"; path=/; SameSite=Lax; domain="+n,n}return""}function pe(e){return e=e||document.referrer,""===e||de(Q(e))!==de()}function _e(e,t){return e=e||document.referrer,"string"!=typeof e?"\u53d6\u503c\u5f02\u5e38_referrer\u5f02\u5e38_"+String(e):(e=p(e),e=q(e),0!==e.indexOf("https://www.baidu.com/")||t||(e=e.split("?")[0]),e=e.slice(0,or.max_referrer_string_length),"string"==typeof e?e:"")}function fe(e){var t=Q(e);if(!t||"hostname\u89e3\u6790\u5f02\u5e38"===t)return"";var r={baidu:[/^.*\.baidu\.com$/],bing:[/^.*\.bing\.com$/],google:[/^www\.google\.com$/,/^www\.google\.com\.[a-z]{2}$/,/^www\.google\.[a-z]{2}$/],sm:[/^m\.sm\.cn$/],so:[/^.+\.so\.com$/],sogou:[/^.*\.sogou\.com$/],yahoo:[/^.*\.yahoo\.com$/]};for(var i in r)for(var n=r[i],a=0,s=n.length;a<s;a++)if(n[a].test(t))return i;return"\u672a\u77e5\u641c\u7d22\u5f15\u64ce"}function ge(e,t){e=e||document.referrer;var r=or.source_type.keyword;if(document&&"string"==typeof e){if(0===e.indexOf("http")){var i=fe(e),n=Y(e);if(f(n))return or.preset_properties.search_keyword_baidu&&le()?void 0:"\u672a\u53d6\u5230\u503c";var a=null;for(var s in r)if(i===s&&"object"==typeof n)if(a=r[s],nr(a))for(s=0;s<a.length;s++){var o=n[a[s]];if(o)return t?{active:o}:o}else if(n[a])return t?{active:n[a]}:n[a];return or.preset_properties.search_keyword_baidu&&le()?void 0:"\u672a\u53d6\u5230\u503c"}return""===e?"\u672a\u53d6\u5230\u503c_\u76f4\u63a5\u6253\u5f00":"\u672a\u53d6\u5230\u503c_\u975ehttp\u7684url"}return"\u53d6\u503c\u5f02\u5e38_referrer\u5f02\u5e38_"+String(e)}function he(e){var t=F(e,"gdt_vid"),r=F(e,"hash_key"),i=F(e,"callbacks"),n={click_id:"",hash_key:"",callbacks:""};return h(t)&&t.length&&(n.click_id=16==t.length||18==t.length?t:"\u53c2\u6570\u89e3\u6790\u4e0d\u5408\u6cd5",h(r)&&r.length&&(n.hash_key=r),h(i)&&i.length&&(n.callbacks=i)),n}function me(){function e(e,t){for(var r=0;r<e.length;r++)if(t.split("?")[0].indexOf(e[r])!==-1)return!0}var t="("+or.source_type.utm.join("|")+")\\=[^&]+",r=or.source_type.search,i=or.source_type.social,n=document.referrer||"",a=hr.pageProp.url;if(a){var s=a.match(new RegExp(t));return s&&s[0]?"\u4ed8\u8d39\u5e7f\u544a\u6d41\u91cf":e(r,n)?"\u81ea\u7136\u641c\u7d22\u6d41\u91cf":e(i,n)?"\u793e\u4ea4\u7f51\u7ad9\u6d41\u91cf":""===n?"\u76f4\u63a5\u6d41\u91cf":"\u5f15\u8350\u6d41\u91cf"}return"\u83b7\u53d6url\u5f02\u5e38"}function ve(){var e={items:[],enqueue:function(e){this.items.push(e),this.start()},dequeue:function(){return this.items.shift()},getCurrentItem:function(){return this.items[0]},isRun:!1,start:function(){this.items.length>0&&!this.isRun&&(this.isRun=!0,this.getCurrentItem().start())},close:function(){this.dequeue(),this.isRun=!1,this.start()}};return e}function ye(){return"undefined"!=typeof window.matchMedia||"undefined"!=typeof window.msMatchMedia}function we(){var e=screen.msOrientation||screen.mozOrientation||(screen.orientation||{}).type,t="\u672a\u53d6\u5230\u503c";if(e)t=e.indexOf("landscape")>-1?"landscape":"portrait";else if(ye()){var r=window.matchMedia||window.msMatchMedia;r("(orientation: landscape)").matches?t="landscape":r("(orientation: portrait)").matches&&(t="portrait")}return t}function Se(){return"undefined"!=typeof window.XMLHttpRequest&&("withCredentials"in new XMLHttpRequest||"undefined"!=typeof XDomainRequest)}function be(){return!!navigator.userAgent.match(/iPhone|iPad|iPod/i)}function ke(){try{var e=navigator.appVersion.match(/OS (\d+)[._](\d+)[._]?(\d+)?/);return e&&e[1]?Number.parseInt(e[1],10):""}catch(t){return""}}function Pe(){var e,t={},r=navigator.userAgent.toLowerCase();return(e=r.match(/opera.([\d.]+)/))?t.opera=Number(e[1].split(".")[0]):(e=r.match(/msie ([\d.]+)/))?t.ie=Number(e[1].split(".")[0]):(e=r.match(/edge.([\d.]+)/))?t.edge=Number(e[1].split(".")[0]):(e=r.match(/firefox\/([\d.]+)/))?t.firefox=Number(e[1].split(".")[0]):(e=r.match(/chrome\/([\d.]+)/))?t.chrome=Number(e[1].split(".")[0]):(e=r.match(/version\/([\d.]+).*safari/))?t.safari=Number(e[1].match(/^\d*.\d*/)):(e=r.match(/trident\/([\d.]+)/))&&(t.ie=11),t}function De(){var e=!1;if("object"!=typeof navigator||"function"!=typeof navigator.sendBeacon)return e;var t=Pe(),r=navigator.userAgent.toLowerCase();if(/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)){var i=/os [\d._]*/gi,n=r.match(i),a=(n+"").replace(/[^0-9|_.]/gi,"").replace(/_/gi,"."),s=a.split(".");"undefined"==typeof t.safari&&(t.safari=s[0]),s[0]&&s[0]<13?(t.chrome>41||t.firefox>30||t.opera>25||t.safari>12)&&(e=!0):(t.chrome>41||t.firefox>30||t.opera>25||t.safari>11.3)&&(e=!0)}else(t.chrome>38||t.edge>13||t.firefox>30||t.opera>25||t.safari>11)&&(e=!0);return e}function Ce(){function e(t){return t&&(t.preventDefault=e.preventDefault,t.stopPropagation=e.stopPropagation,t._getPath=e._getPath),t}function t(t,r,i,n){var a=function(a){if(a=a||e(window.event)){a.target=a.srcElement;var s,o,l=!0;return"function"==typeof i&&(s=i(a)),o=r.call(t,a),"beforeunload"!==n?(!1!==s&&!1!==o||(l=!1),l):void 0}};return a}e._getPath=function(){var e=this;return this.path||this.composedPath&&this.composedPath()||M(e.target).getParents()},e.preventDefault=function(){this.returnValue=!1},e.stopPropagation=function(){this.cancelBubble=!0};var r=function(r,i,n){var a=!(!_(or.heatmap)||!or.heatmap.useCapture);if(_(or.heatmap)&&"undefined"==typeof or.heatmap.useCapture&&"click"===i&&(a=!0),r&&r.addEventListener)r.addEventListener(i,function(t){t._getPath=e._getPath,n.call(this,t)},a);else{var s="on"+i,o=r[s];r[s]=t(r,n,o,i)}};r.apply(null,arguments)}function Ne(e){var t="pushState"in window.history?"popstate":"hashchange";Ce(window,t,e)}function $e(e){var t={visibleHandler:a(e.visible)?e.visible:function(){},hiddenHandler:a(e.hidden)?e.hidden:function(){},visibilityChange:null,hidden:null,isSupport:function(){return"undefined"!=typeof document[this.hidden]},init:function(){"undefined"!=typeof document.hidden?(this.hidden="hidden",this.visibilityChange="visibilitychange"):"undefined"!=typeof document.mozHidden?(this.hidden="mozHidden",this.visibilityChange="mozvisibilitychange"):"undefined"!=typeof document.msHidden?(this.hidden="msHidden",this.visibilityChange="msvisibilitychange"):"undefined"!=typeof document.webkitHidden&&(this.hidden="webkitHidden",this.visibilityChange="webkitvisibilitychange"),this.listen()},listen:function(){if(this.isSupport()){var e=this;Ce(document,this.visibilityChange,function(){document[e.hidden]?e.hiddenHandler():e.visibleHandler()},1)}else Ce(window,"focus",this.visibleHandler),Ce(window,"blur",this.hiddenHandler)}};t.init()}function je(e,t){t=t||window;var r=!1,i=!0,n=t.document,a=n.documentElement,s=n.addEventListener,o=s?"addEventListener":"attachEvent",l=s?"removeEventListener":"detachEvent",c=s?"":"on",u=function(i){"readystatechange"==i.type&&"complete"!=n.readyState||(("load"==i.type?t:n)[l](c+i.type,u,!1),!r&&(r=!0)&&e.call(t,i.type||i))},d=function(){try{a.doScroll("left")}catch(e){return void setTimeout(d,50)}u("poll")};if("complete"==n.readyState)e.call(t,"lazy");else{if(!s&&a.doScroll){try{i=!t.frameElement}catch(p){H(p)}i&&d()}n[o](c+"DOMContentLoaded",u,!1),n[o](c+"readystatechange",u,!1),t[o](c+"load",u,!1)}}function Ae(e){if(e)return"undefined"!=typeof window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest?new XMLHttpRequest:"undefined"!=typeof XDomainRequest?new XDomainRequest:null;if("undefined"!=typeof window.XMLHttpRequest)return new XMLHttpRequest;if(window.ActiveXObject)try{return new ActiveXObject("Msxml2.XMLHTTP")}catch(t){try{return new ActiveXObject("Microsoft.XMLHTTP")}catch(t){H(t)}}}function Oe(t){function i(e){if(!e)return"";try{return JSON.parse(e)}catch(t){return{}}}function n(){try{_(a)&&a.abort&&a.abort()}catch(e){H(e)}s&&(clearTimeout(s),s=null,t.error&&t.error(),a.onreadystatechange=null,a.onload=null,a.onerror=null)}t.timeout=t.timeout||2e4,t.credentials="undefined"==typeof t.credentials||t.credentials;var a=Ae(t.cors);if(!a)return!1;t.type||(t.type=t.data?"POST":"GET"),t=r({success:function(){},error:function(){}},t),dr.protocol.ajax(t.url);var s,o=t.success,l=t.error;t.success=function(e){o(e),s&&(clearTimeout(s),s=null)},t.error=function(e){l(e),s&&(clearTimeout(s),s=null)},s=setTimeout(function(){n()},t.timeout),"undefined"!=typeof XDomainRequest&&a instanceof XDomainRequest&&(a.onload=function(){t.success&&t.success(i(a.responseText)),a.onreadystatechange=null,a.onload=null,a.onerror=null},a.onerror=function(){t.error&&t.error(i(a.responseText),a.status),a.onreadystatechange=null,a.onerror=null,a.onload=null}),a.onreadystatechange=function(){try{4==a.readyState&&(a.status>=200&&a.status<300||304==a.status?t.success(i(a.responseText)):t.error(i(a.responseText),a.status),a.onreadystatechange=null,a.onload=null)}catch(e){a.onreadystatechange=null,a.onload=null}},a.open(t.type,t.url,!0);try{t.credentials&&(a.withCredentials=!0),_(t.header)&&e(t.header,function(e,t){a.setRequestHeader&&a.setRequestHeader(t,e)}),t.data&&(t.cors||a.setRequestHeader&&a.setRequestHeader("X-Requested-With","XMLHttpRequest"),"application/json"===t.contentType?a.setRequestHeader&&a.setRequestHeader("Content-type","application/json; charset=UTF-8"):a.setRequestHeader&&a.setRequestHeader("Content-type","application/x-www-form-urlencoded"))}catch(c){H(c)}a.send(t.data||null)}function Ie(t){if(!_(t)||!h(t.callbackName))return H("JSONP \u8bf7\u6c42\u7f3a\u5c11 callbackName"),!1;t.success=a(t.success)?t.success:function(){},t.error=a(t.error)?t.error:function(){},t.data=t.data||"";var r=document.createElement("script"),i=document.getElementsByTagName("head")[0],n=null,s=!1;if(i.appendChild(r),w(t.timeout)&&(n=setTimeout(function(){return!s&&(t.error("timeout"),window[t.callbackName]=function(){H("call jsonp error")},n=null,i.removeChild(r),void(s=!0))},t.timeout)),window[t.callbackName]=function(){clearTimeout(n),n=null,t.success.apply(null,arguments),window[t.callbackName]=function(){H("call jsonp error")},i.removeChild(r)},t.url.indexOf("?")>-1?t.url+="&callbackName="+t.callbackName:t.url+="?callbackName="+t.callbackName,_(t.data)){var o=[];e(t.data,function(e,t){o.push(t+"="+e)}),t.data=o.join("&"),t.url+="&"+t.data}r.onerror=function(e){return!s&&(window[t.callbackName]=function(){H("call jsonp error")},clearTimeout(n),n=null,i.removeChild(r),t.error(e),void(s=!0))},r.src=t.url}function Te(e){return"function"==typeof e||!(!e||"object"!=typeof e)&&Te(e.listener)}function xe(){this._events={}}function Le(e){return 0===e.indexOf(kr)?(e=e.substring(kr.length),e=T(e)):0===e.indexOf(Pr)&&(e=e.substring(Pr.length),e=x(e)),e}function Ee(e){return!h(e)||0!==e.indexOf(kr)&&0!==e.indexOf(Pr)||(e=Le(e)),e}function He(e){return Pr+x(e)}function Je(e,t){for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&!Ar.check(r,e[r],t))return!1;return!0}function Be(e){var t=!e.type||"profile"!==e.type.slice(0,7),r="\u53d6\u503c\u5f02\u5e38";_(e.properties)&&(e.properties.$first_referrer&&(e.properties.$first_referrer_host=Q(e.properties.$first_referrer,r)),t&&("$referrer"in e.properties&&(e.properties.$referrer_host=""===e.properties.$referrer?"":Q(e.properties.$referrer,r)),Xt.para.preset_properties.latest_referrer&&Xt.para.preset_properties.latest_referrer_host&&(e.properties.$latest_referrer_host=""===e.properties.$latest_referrer?"":Q(e.properties.$latest_referrer,r))))}function Ue(e){var t=!e.type||"profile"!==e.type.slice(0,7),r=Xt.para.preset_properties&&t;r&&Xt.para.preset_properties.url&&"undefined"==typeof e.properties.$url&&(e.properties.$url=G()),r&&Xt.para.preset_properties.title&&"undefined"==typeof e.properties.$title&&(e.properties.$title=document.title)}function Re(t){r(or,t||Xt.para||{}),Xt.para=or;var i={};if(_(Xt.para.is_track_latest))for(var n in Xt.para.is_track_latest)i["latest_"+n]=Xt.para.is_track_latest[n];Xt.para.preset_properties=r({},Xt.para_default.preset_properties,i,Xt.para.preset_properties||{});var a;for(a in Xt.para_default)void 0===Xt.para[a]&&(Xt.para[a]=Xt.para_default[a]);"string"==typeof Xt.para.server_url&&(Xt.para.server_url=p(Xt.para.server_url),Xt.para.server_url&&("://"===Xt.para.server_url.slice(0,3)?Xt.para.server_url=location.protocol.slice(0,-1)+Xt.para.server_url:"//"===Xt.para.server_url.slice(0,2)?Xt.para.server_url=location.protocol+Xt.para.server_url:"http"!==Xt.para.server_url.slice(0,4)&&(Xt.para.server_url=""))),"string"!=typeof Xt.para.web_url||"://"!==Xt.para.web_url.slice(0,3)&&"//"!==Xt.para.web_url.slice(0,2)||("://"===Xt.para.web_url.slice(0,3)?Xt.para.web_url=location.protocol.slice(0,-1)+Xt.para.web_url:Xt.para.web_url=location.protocol+Xt.para.web_url),"image"!==Xt.para.send_type&&"ajax"!==Xt.para.send_type&&"beacon"!==Xt.para.send_type&&(Xt.para.send_type="image"),Ir.check({loginIdKey:Xt.para.login_id_key})||(Xt.para.login_id_key="$identity_login_id"),Xt.debug.protocol.serverUrl(),Xt.bridge.initPara(),Xt.bridge.initState();var s={datasend_timeout:6e3,send_interval:6e3};vr.isSupport()&&Se()&&"object"==typeof localStorage?Xt.para.batch_send===!0?Xt.para.batch_send=r({},s):"object"==typeof Xt.para.batch_send&&(Xt.para.batch_send=r({},s,Xt.para.batch_send)):Xt.para.batch_send=!1;var o=["utm_source","utm_medium","utm_campaign","utm_content","utm_term"],l=["www.baidu.","m.baidu.","m.sm.cn","so.com","sogou.com","youdao.com","google.","yahoo.com/","bing.com/","ask.com/"],c=["weibo.com","renren.com","kaixin001.com","douban.com","qzone.qq.com","zhihu.com","tieba.baidu.com","weixin.qq.com"],f={baidu:["wd","word","kw","keyword"],google:"q",bing:"q",yahoo:"p",sogou:["query","keyword"],so:"q",sm:"q"};"object"==typeof Xt.para.source_type&&(Xt.para.source_type.utm=nr(Xt.para.source_type.utm)?Xt.para.source_type.utm.concat(o):o,Xt.para.source_type.search=nr(Xt.para.source_type.search)?Xt.para.source_type.search.concat(l):l,Xt.para.source_type.social=nr(Xt.para.source_type.social)?Xt.para.source_type.social.concat(c):c,Xt.para.source_type.keyword=_(Xt.para.source_type.keyword)?r(f,Xt.para.source_type.keyword):f);var g={div:!1},h=["mark","/mark","strong","b","em","i","u","abbr","ins","del","s","sup"];if(Xt.para.heatmap&&!_(Xt.para.heatmap)&&(Xt.para.heatmap={}),_(Xt.para.heatmap)){Xt.para.heatmap.clickmap=Xt.para.heatmap.clickmap||"default",Xt.para.heatmap.scroll_notice_map=Xt.para.heatmap.scroll_notice_map||"default",Xt.para.heatmap.scroll_delay_time=Xt.para.heatmap.scroll_delay_time||4e3,Xt.para.heatmap.scroll_event_duration=Xt.para.heatmap.scroll_event_duration||18e3,Xt.para.heatmap.renderRefreshTime=Xt.para.heatmap.renderRefreshTime||1e3,Xt.para.heatmap.loadTimeout=Xt.para.heatmap.loadTimeout||1e3,Xt.para.heatmap.get_vtrack_config!==!0&&(Xt.para.heatmap.get_vtrack_config=!1);var m=nr(Xt.para.heatmap.track_attr)?d(Xt.para.heatmap.track_attr,function(e){return e&&"string"==typeof e}):[];if(m.push("data-sensors-click"),Xt.para.heatmap.track_attr=m,_(Xt.para.heatmap.collect_tags))if(Xt.para.heatmap.collect_tags.div===!0)Xt.para.heatmap.collect_tags.div={ignore_tags:h,max_level:1};else if(_(Xt.para.heatmap.collect_tags.div)){if(Xt.para.heatmap.collect_tags.div.ignore_tags?nr(Xt.para.heatmap.collect_tags.div.ignore_tags)||(Xt.log("ignore_tags \u53c2\u6570\u5fc5\u987b\u662f\u6570\u7ec4\u683c\u5f0f"),Xt.para.heatmap.collect_tags.div.ignore_tags=h):Xt.para.heatmap.collect_tags.div.ignore_tags=h,Xt.para.heatmap.collect_tags.div.max_level){var v=[1,2,3];u(v,Xt.para.heatmap.collect_tags.div.max_level)===-1&&(Xt.para.heatmap.collect_tags.div.max_level=1)}}else Xt.para.heatmap.collect_tags.div=!1;else Xt.para.heatmap.collect_tags=g}if(nr(Xt.para.server_url)&&Xt.para.server_url.length)for(a=0;a<Xt.para.server_url.length;a++)/sa\.gif[^\/]*$/.test(Xt.para.server_url[a])||(Xt.para.server_url[a]=Xt.para.server_url[a].replace(/\/sa$/,"/sa.gif").replace(/(\/sa)(\?[^\/]+)$/,"/sa.gif$2"));else/sa\.gif[^\/]*$/.test(Xt.para.server_url)||"string"!=typeof Xt.para.server_url||(Xt.para.server_url=Xt.para.server_url.replace(/\/sa$/,"/sa.gif").replace(/(\/sa)(\?[^\/]+)$/,"/sa.gif$2"));"string"==typeof Xt.para.server_url&&(Xt.para.debug_mode_url=Xt.para.debug_mode_url||Xt.para.server_url.replace("sa.gif","debug")),Xt.para.noCache===!0?Xt.para.noCache="?"+(new Date).getTime():Xt.para.noCache="",Xt.para.callback_timeout>Xt.para.datasend_timeout&&(Xt.para.datasend_timeout=Xt.para.callback_timeout),Xt.para.heatmap&&Xt.para.heatmap.collect_tags&&_(Xt.para.heatmap.collect_tags)&&e(Xt.para.heatmap.collect_tags,function(e,t){"div"!==t&&e&&Xt.heatmap.otherTags.push(t)})}function Me(){Xt._t=Xt._t||1*new Date,Xt.lib_version=_r,Xt.is_first_visitor=!1,Xt.source_channel_standard=pr}function Ke(){if(wr.isSupport())try{sessionStorage.setItem("sensorsdata_jssdk_debug","true")}catch(e){Xt.log("enableLocalLog error: "+e.message)}}function We(){wr.isSupport()&&sessionStorage.removeItem("sensorsdata_jssdk_debug")}function Ve(){var e=Array.prototype.slice.call(arguments),t=e[0],r=e.slice(1);return"string"==typeof t&&xr[t]?xr[t].apply(xr,r):void("function"==typeof t?t.apply(Xt,r):Xt.log("quick\u65b9\u6cd5\u4e2d\u6ca1\u6709\u8fd9\u4e2a\u529f\u80fd"+e[0]))}function qe(e,t){return h(e)?_(window.SensorsDataWebJSSDKPlugin)&&_(window.SensorsDataWebJSSDKPlugin[e])&&a(window.SensorsDataWebJSSDKPlugin[e].init)?(window.SensorsDataWebJSSDKPlugin[e].init(Xt,t),window.SensorsDataWebJSSDKPlugin[e]):_(Xt.modules)&&_(Xt.modules[e])&&a(Xt.modules[e].init)?(Xt.modules[e].init(Xt,t),Xt.modules[e]):void Xt.log(e+"\u6ca1\u6709\u83b7\u53d6\u5230,\u8bf7\u67e5\u9605\u6587\u6863\uff0c\u8c03\u6574"+e+"\u7684\u5f15\u5165\u987a\u5e8f\uff01"):(Xt.log("use\u63d2\u4ef6\u540d\u79f0\u5fc5\u987b\u662f\u5b57\u7b26\u4e32\uff01"),!1)}function Fe(e,t,r){Ir.check({event:e,properties:t})&&Ir.send({type:"track",event:e,properties:t},r)}function ze(e,t){return!!Ir.check({bindKey:e,bindValue:t})&&(Xt.store._state.identities[e]=t,Xt.store.save(),void Ir.send({type:"track_id_bind",event:"$BindID",properties:{}}))}function Xe(e,t){if(!Ir.check({bindKey:e,bindValue:t}))return!1;_(Xt.store._state.identities)&&Xt.store._state.identities.hasOwnProperty(e)&&Xt.store._state.identities[e]===t&&(delete Xt.store._state.identities[e],Xt.store.save());var r={};r[e]=t,Ir.send({identities:r,type:"track_id_unbind",event:"$UnbindID",properties:{}})}function Ze(e,t,r){function i(e,t,r){function i(e){function i(){a||(a=!0,location.href=n.href)}e.stopPropagation(),e.preventDefault();var a=!1;setTimeout(i,1e3),Xt.track(t,r,i)}e=e||{};var n=null;return e.ele&&(n=e.ele),e.event&&(n=e.target?e.target:e.event.target),r=r||{},!(!n||"object"!=typeof n)&&(!n.href||/^javascript/.test(n.href)||n.target||n.download||n.onclick?(Xt.track(t,r),!1):(e.event&&i(e.event),void(e.ele&&Ce(e.ele,"click",function(e){i(e)}))))}"object"==typeof e&&e.tagName?i({ele:e},t,r):"object"==typeof e&&e.target&&e.event&&i(e,t,r)}function Qe(e,t,r){return r=r||{},!(!e||"object"!=typeof e)&&(!(!e.href||/^javascript/.test(e.href)||e.target)&&void Ce(e,"click",function(i){function n(){a||(a=!0,location.href=e.href)}i.preventDefault();var a=!1;setTimeout(n,1e3),Xt.track(t,r,n)}))}function Ye(e,t,r){Ir.check({item_type:e,item_id:t,properties:r})&&Ir.sendItem({type:"item_set",item_type:e,item_id:t,properties:r||{}})}function Ge(e,t){Ir.check({item_type:e,item_id:t})&&Ir.sendItem({type:"item_delete",item_type:e,item_id:t})}function et(e,t){Ir.check({propertiesMust:e})&&Ir.send({type:"profile_set",properties:e},t)}function tt(e,t){Ir.check({propertiesMust:e})&&Ir.send({type:"profile_set_once",properties:e},t)}function rt(t,r){Ir.check({propertiesMust:t})&&(e(t,function(e,r){h(e)?t[r]=[e]:nr(e)?t[r]=e:(delete t[r],Xt.log("appendProfile\u5c5e\u6027\u7684\u503c\u5fc5\u987b\u662f\u5b57\u7b26\u4e32\u6216\u8005\u6570\u7ec4"))}),f(t)||Ir.send({type:"profile_append",properties:t},r))}function it(e,t){function r(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&!/-*\d+/.test(String(e[t])))return!1;return!0}var i=e;h(e)&&(e={},e[i]=1),Ir.check({propertiesMust:e})&&(r(e)?Ir.send({type:"profile_increment",properties:e},t):Xt.log("profile_increment\u7684\u503c\u53ea\u80fd\u662f\u6570\u5b57"));
}function nt(e){Ir.send({type:"profile_delete"},e),Nr.set("distinct_id",fr()),Nr.set("first_id","")}function at(t,r){var i=t,n={};h(t)&&(t=[],t.push(i)),nr(t)?(e(t,function(e){h(e)?n[e]=!0:Xt.log("profile_unset\u7ed9\u7684\u6570\u7ec4\u91cc\u9762\u7684\u503c\u5fc5\u987b\u65f6string,\u5df2\u7ecf\u8fc7\u6ee4\u6389",e)}),Ir.send({type:"profile_unset",properties:n},r)):Xt.log("profile_unset\u7684\u53c2\u6570\u662f\u6570\u7ec4")}function st(e,t){"number"==typeof e&&(e=String(e));var r=Nr.getFirstId();if("undefined"==typeof e){var i=fr();r?Nr.set("first_id",i):Nr.set("distinct_id",i),Xt.store.identities.set("identify",i)}else Ir.check({distinct_id:e})&&(t===!0?r?Nr.set("first_id",e):Nr.set("distinct_id",e):r?Nr.change("first_id",e):Nr.change("distinct_id",e),Xt.store.identities.set("identify",e))}function ot(e,t,r,i){var n=Nr.getFirstId()||Nr.getDistinctId();Nr.set("distinct_id",e),Ir.send({original_id:n,distinct_id:e,type:"track_signup",event:t,properties:r},i)}function lt(e,t,r,i){"number"==typeof e&&(e=String(e)),Ir.check({distinct_id:e,event:t,properties:r})&&ot(e,t,r,i)}function ct(e){Ir.check({properties:e})?r(hr.currentProps,e):Xt.log("register\u8f93\u5165\u7684\u53c2\u6570\u6709\u8bef")}function ut(e){Nr.clearAllProps(e)}function dt(e){var t;if(nr(e)&&e.length>0)for(t=0;t<e.length;t++)h(e[t])&&e[t]in hr.currentProps&&delete hr.currentProps[e[t]];else if(e===!0)for(t in hr.currentProps)delete hr.currentProps[t]}function pt(e){Ir.check({properties:e})?Nr.setProps(e):Xt.log("register\u8f93\u5165\u7684\u53c2\u6570\u6709\u8bef")}function _t(e){Ir.check({properties:e})?Nr.setPropsOnce(e):Xt.log("registerOnce\u8f93\u5165\u7684\u53c2\u6570\u6709\u8bef")}function ft(e){Ir.check({properties:e})?Nr.setSessionProps(e):Xt.log("registerSession\u8f93\u5165\u7684\u53c2\u6570\u6709\u8bef")}function gt(e){Ir.check({properties:e})?Nr.setSessionPropsOnce(e):Xt.log("registerSessionOnce\u8f93\u5165\u7684\u53c2\u6570\u6709\u8bef")}function ht(e,t){if("number"==typeof e&&(e=String(e)),Ir.check({distinct_id:e})&&e!==Xt.store.getDistinctId()){if(_(Xt.store._state.identities)&&Xt.store._state.identities.hasOwnProperty(Xt.para.login_id_key)&&e===Xt.store._state.first_id)return a(t)&&t(),!1;var r=Xt.store._state.history_login_id.name!==Xt.para.login_id_key||e!==Xt.store._state.history_login_id.value;if(r){Xt.store._state.identities[Xt.para.login_id_key]=e;var i=Nr.getFirstId(),n=Nr.getDistinctId();i||Nr.set("first_id",n),ot(e,"$SignUp",{},t),Xt.store.identities.set("login",e),Xt.store.set("history_login_id",{name:Xt.para.login_id_key,value:e})}}else a(t)&&t()}function mt(e){var t=Nr.getFirstId();if(t)if(Nr.set("first_id",""),e===!0){var r=fr();Nr.set("distinct_id",r)}else Nr.set("distinct_id",t);Xt.store.identities.set("logout"),Xt.store.set("history_login_id",{name:"",value:""})}function vt(){function t(){var t=hr.campaignParams(),r={};return e(t,function(e,t,i){(" "+Xt.source_channel_standard+" ").indexOf(" "+t+" ")!==-1?r["$"+t]=i[t]:r[t]=i[t]}),r}var i={$is_first_day:mr.getNewUser(),$is_first_time:Cr.is_page_first_visited,$referrer:hr.pageProp.referrer||"",$referrer_host:hr.pageProp.referrer?Q(hr.pageProp.referrer):"",$url:G(),$url_path:location.pathname,$title:document.title||"",_distinct_id:Nr.getDistinctId()},n=r({},hr.properties(),Xt.store.getProps(),t(),i);return Xt.para.preset_properties.latest_referrer&&Xt.para.preset_properties.latest_referrer_host&&(n.$latest_referrer_host=""===n.$latest_referrer?"":Q(n.$latest_referrer)),n}function yt(){var t="",r=" { cursor: pointer; -webkit-tap-highlight-color: rgba(0,0,0,0); }";Xt.heatmap&&nr(Xt.heatmap.otherTags)&&e(Xt.heatmap.otherTags,function(e){t+=e+r}),Xt._.isIOS()&&Xt._.getIOSVersion()&&Xt._.getIOSVersion()<13&&(Xt.para.heatmap&&Xt.para.heatmap.collect_tags&&Xt.para.heatmap.collect_tags.div&&Xt._.setCssStyle("div, [data-sensors-click]"+r),Xt.para.heatmap&&Xt.para.heatmap.track_attr&&Xt._.setCssStyle("["+Xt.para.heatmap.track_attr.join("], [")+"]"+r),""!==t&&Xt._.setCssStyle(t))}function wt(e,t){var r=Jr.encodeTrackData(t);return e.indexOf("?")!==-1?e+"&"+r:e+"?"+r}function St(e){return Jr.encodeTrackData(e)}function bt(e){var t=["image","ajax","beacon"],r=t[0];return r=e.config&&u(t,e.config.send_type)>-1?e.config.send_type:Xt.para.send_type,"beacon"===r&&De()===!1&&(r="image"),"ajax"===r&&Se()===!1&&(r="image"),r}function kt(e){var t=bt(e);switch(t){case"image":return new Br(e);case"ajax":return new Ur(e);case"beacon":return new Rr(e);default:return new Br(e)}}function Pt(e){var t=kt(e),r=t.start;return t.start=function(){var e=this;r.apply(this,arguments),setTimeout(function(){e.isEnd(!0)},Xt.para.callback_timeout)},t.end=function(){this.callback&&this.callback();var e=this;setTimeout(function(){e.lastClear&&e.lastClear()},Xt.para.datasend_timeout-Xt.para.callback_timeout)},t.isEnd=function(){this.received||(this.received=!0,this.end())},t}function Dt(){this.sendingData=0,this.sendingItemKeys=[]}function Ct(e){var t=location.href,r=window.history.pushState,i=window.history.replaceState;a(window.history.pushState)&&(window.history.pushState=function(){r.apply(window.history,arguments),e(t),t=location.href}),a(window.history.replaceState)&&(window.history.replaceState=function(){i.apply(window.history,arguments),e(t),t=location.href});var n;n=window.document.documentMode?"hashchange":r?"popstate":"hashchange",Ce(window,n,function(){e(t),t=location.href})}function Nt(t){var r=null,i=new RegExp(t+"=([^&#]+)");try{var n=JSON.parse(window.name);e(n,function(e,i){t===i&&(r=e)})}catch(a){r=null}if(null===r){var s=location.href.match(i);s&&s[0]&&s[1]&&(r=V(s[1]))}return r}function $t(e){function t(){var e=[];r.touch_app_bridge||e.push(Xt.debug.defineMode("1")),_(Xt.para.app_js_bridge)||(e.push(Xt.debug.defineMode("2")),r.verify_success=!1),_(Xt.para.heatmap)&&"default"==Xt.para.heatmap.clickmap||e.push(Xt.debug.defineMode("3")),"fail"===r.verify_success&&e.push(Xt.debug.defineMode("4"));var t={callType:"app_alert",data:e};SensorsData_App_Visual_Bridge&&SensorsData_App_Visual_Bridge.sensorsdata_visualized_alert_info?SensorsData_App_Visual_Bridge.sensorsdata_visualized_alert_info(JSON.stringify(t)):window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(JSON.stringify(t))}var r=Xt.bridge.bridge_info;if(_(window.SensorsData_App_Visual_Bridge)&&window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode&&(window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode===!0||window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode()))if(_(Xt.para.heatmap)&&"default"==Xt.para.heatmap.clickmap)if(_(Xt.para.app_js_bridge)&&"success"===r.verify_success)if(e)sa_jssdk_app_define_mode(Xt,e);else{var i=location.protocol,n=["http:","https:"];i=u(n,i)>-1?i:"https:",R({success:function(){setTimeout(function(){"undefined"!=typeof sa_jssdk_app_define_mode&&sa_jssdk_app_define_mode(Xt,e)},0)},error:function(){},type:"js",url:i+"//static.sensorsdata.cn/sdk/"+Xt.lib_version+"/vapph5define.min.js"})}else t();else t()}function jt(){Xt.para.is_track_single_page&&Yr.on("switch",function(e){var t=function(t){t=t||{},e!==location.href&&(hr.pageProp.referrer=G(e),Xt.quick("autoTrack",r({$url:G(),$referrer:G(e)},t)))};if("boolean"==typeof Xt.para.is_track_single_page)t();else if("function"==typeof Xt.para.is_track_single_page){var i=Xt.para.is_track_single_page();_(i)?t(i):i===!0&&t()}})}function At(){Xt._q&&nr(Xt._q)&&Xt._q.length>0&&e(Xt._q,function(e){Xt[e[0]].apply(Xt,Array.prototype.slice.call(e[1]))}),_(Xt.para.heatmap)&&(Tr.initHeatmap(),Tr.initScrollmap())}function Ot(){Xt.readyState.setState(3),new Xt.JSBridge({type:"visualized",app_call_js:function(){$t("undefined"!=typeof sa_jssdk_app_define_mode?!0:!1)}}),$t(!1),Xt.bridge.app_js_bridge_v1(),hr.initPage(),jt(),Xt.para.batch_send&&(Ce(window,"onpagehide"in window?"pagehide":"unload",function(){Xt.batchSend.clearPendingStatus()}),Xt.batchSend.batchInterval()),Xt.store.init(),Xt.vtrackBase.init(),Xt.readyState.setState(4),At()}function It(){ei.isSeachHasKeyword()?ei.hasKeywordHandle():window.parent!==self&&ti.isSearchHasKeyword()?ti.verifyVtrackMode():ei.isStorageHasKeyword()?ei.storageHasKeywordHandle():window.parent!==self&&ti.isStorageHasKeyword()?ti.verifyVtrackMode():(Ot(),ti.notifyUser())}function Tt(){e(ri,function(e){var t=Xt[e];Xt[e]=function(){if(Xt.readyState.state<3)return nr(Xt._q)||(Xt._q=[]),Xt._q.push([e,arguments]),!1;{if(Xt.readyState.getState())return t.apply(Xt,arguments);try{console.error("\u8bf7\u5148\u521d\u59cb\u5316\u795e\u7b56JS SDK")}catch(r){Xt.log(r)}}}})}function xt(e){this.cancel=function(){e=!0},this.getCanceled=function(){return e||!1}}function Lt(e,t,r){var i=null;try{i=JSON.parse(JSON.stringify(e))}catch(n){H(n)}this.getOriginalData=function(){return i},this.getPosition=function(){return t},this.cancelationToken=new xt,this.sensors=r}function Et(e){if(!_(e))throw"error: Stage constructor requires arguments.";this.processDef=e,this.registeredInterceptors={}}function Ht(e){e&&e.dataStage&&ai.registerStageImplementation(e.dataStage)}function Jt(e){e.kit=Jr,e.saEvent=Ir,this.dataStage=Or}function Bt(t){return _(t)?(e(t,function(r,i){if(nr(r)){var n=[];e(r,function(e){h(e)?n.push(e):H("\u60a8\u7684\u6570\u636e-",i,r,"\u7684\u6570\u7ec4\u91cc\u7684\u503c\u5fc5\u987b\u662f\u5b57\u7b26\u4e32,\u5df2\u7ecf\u5c06\u5176\u5220\u9664")}),t[i]=n}h(r)||w(r)||v(r)||y(r)||nr(r)||a(r)||"$option"===i||(H("\u60a8\u7684\u6570\u636e-",i,r,"-\u683c\u5f0f\u4e0d\u6ee1\u8db3\u8981\u6c42\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664"),delete t[i])}),t):t}function Ut(e,t){return w(t)&&e.length>t?(H("\u5b57\u7b26\u4e32\u957f\u5ea6\u8d85\u8fc7\u9650\u5236\uff0c\u5df2\u7ecf\u505a\u622a\u53d6--"+e),e.slice(0,t)):e}function Rt(t){var r=["distinct_id","user_id","id","date","datetime","event","events","first_id","original_id","device_id","properties","second_id","time","users"];_(t)&&e(r,function(e,r){e in t&&(r<3?(delete t[e],H("\u60a8\u7684\u5c5e\u6027- "+e+"\u662f\u4fdd\u7559\u5b57\u6bb5\uff0c\u6211\u4eec\u5df2\u7ecf\u5c06\u5176\u5220\u9664")):H("\u60a8\u7684\u5c5e\u6027- "+e+"\u662f\u4fdd\u7559\u5b57\u6bb5\uff0c\u8bf7\u907f\u514d\u5176\u4f5c\u4e3a\u5c5e\u6027\u540d"))})}function Mt(t){var r=["$element_selector","$element_path"],i=["sensorsdata_app_visual_properties"];_(t)&&e(t,function(e,n){if(_(e))Mt(t[n]);else if(h(e)){if(u(i,n)>-1)return;t[n]=Ut(e,u(r,n)>-1?1024:or.max_string_length)}})}function Kt(e){"undefined"!=typeof e.properties.$project&&(e.project=e.properties.$project,delete e.properties.$project),"undefined"!=typeof e.properties.$token&&(e.token=e.properties.$token,delete e.properties.$token)}function Wt(e){if("item_type"in e){var t=e.item_type,r=function(t){return t||delete e.item_type,!0};Je({item_type:t},r)}if("item_id"in e){var i=e.item_id,n=function(t,r,i){return t||"string"!==i||delete e.item_id,!0};Je({item_id:i},n)}}function Vt(t){e(t,function(e,r){var i=function(e,i,n){return e||"keyLength"===n||delete t[r],!0};Je({propertyKey:r},i)})}function qt(e){var t=e.properties;_(t)?(Bt(t),Rt(t),Kt(e),Vt(t),Mt(t)):"properties"in e&&(e.properties={}),re(e),Wt(e)}function Ft(){this.dataStage=si}function zt(e){e&&(Xt._=Dr,Xt.ee=Gr,Xt.sendState=Mr,Xt.events=new br,Xt.batchSend=Kr,Xt.bridge=Wr,Xt.JSBridge=Vr,Xt.store=Nr,Xt.vtrackBase=qr,Xt.unlimitedDiv=Fr,Xt.customProp=zr,Xt.vtrackcollect=Xr,Xt.vapph5collect=Qr,Xt.heatmap=Tr,Xt.detectMode=It,Ht(new Jt(Xt)),Ht(new Ft(Xt)));var t=e?Hr:ii;for(var r in t)Xt[r]=t[r]}var Xt={};(function(){function e(i,n){function a(e,t){try{e()}catch(r){t&&t()}}function s(e){if(null!=s[e])return s[e];var t;if("bug-string-char-index"==e)t="a"!="a"[0];else if("json"==e)t=s("json-stringify")&&s("date-serialization")&&s("json-parse");else if("date-serialization"==e){if(t=s("json-stringify")&&w){var r=n.stringify;a(function(){t='"-271821-04-20T00:00:00.000Z"'==r(new d((-864e13)))&&'"+275760-09-13T00:00:00.000Z"'==r(new d(864e13))&&'"-000001-01-01T00:00:00.000Z"'==r(new d((-621987552e5)))&&'"1969-12-31T23:59:59.999Z"'==r(new d((-1)))})}}else{var i,o='{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}';if("json-stringify"==e){var r=n.stringify,u="function"==typeof r;u&&((i=function(){return 1}).toJSON=i,a(function(){u="0"===r(0)&&"0"===r(new l)&&'""'==r(new c)&&r(v)===h&&r(h)===h&&r()===h&&"1"===r(i)&&"[1]"==r([i])&&"[null]"==r([h])&&"null"==r(null)&&"[null,null,null]"==r([h,v,null])&&r({a:[i,!0,!1,null,"\0\b\n\f\r\t"]})==o&&"1"===r(null,i)&&"[\n 1,\n 2\n]"==r([1,2],null,1)},function(){u=!1})),t=u}if("json-parse"==e){var p,_=n.parse;"function"==typeof _&&a(function(){0!==_("0")||_(!1)||(i=_(o),p=5==i.a.length&&1===i.a[0],p&&(a(function(){p=!_('"\t"')}),p&&a(function(){p=1!==_("01")}),p&&a(function(){p=1!==_("1.")})))},function(){p=!1}),t=p}}return s[e]=!!t}function o(e){return I(this)}i||(i=r.Object()),n||(n=r.Object());var l=i.Number||r.Number,c=i.String||r.String,u=i.Object||r.Object,d=i.Date||r.Date,p=i.SyntaxError||r.SyntaxError,_=i.TypeError||r.TypeError,f=i.Math||r.Math,g=i.JSON||r.JSON;if("object"==typeof g&&g)return n.stringify=g.stringify,n.parse=g.parse,n.runInContext=e,n;var h,m=u.prototype,v=m.toString,y=m.hasOwnProperty,w=new d((-0xc782b5b800cec));if(a(function(){w=w.getUTCFullYear()==-109252&&0===w.getUTCMonth()&&1===w.getUTCDate()&&10==w.getUTCHours()&&37==w.getUTCMinutes()&&6==w.getUTCSeconds()&&708==w.getUTCMilliseconds()}),s["bug-string-char-index"]=s["date-serialization"]=s.json=s["json-stringify"]=s["json-parse"]=null,!s("json")){var S="[object Function]",b="[object Date]",k="[object Number]",P="[object String]",D="[object Array]",C="[object Boolean]",N=s("bug-string-char-index"),$=function(e,r){var i,n,a,s=0;(i=function(){this.valueOf=0}).prototype.valueOf=0,n=new i;for(a in n)y.call(n,a)&&s++;return i=n=null,s?$=function(e,t){var r,i,n=v.call(e)==S;for(r in e)n&&"prototype"==r||!y.call(e,r)||(i="constructor"===r)||t(r);(i||y.call(e,r="constructor"))&&t(r)}:(n=["valueOf","toString","toLocaleString","propertyIsEnumerable","isPrototypeOf","hasOwnProperty","constructor"],$=function(e,r){var i,a,s=v.call(e)==S,o=!s&&"function"!=typeof e.constructor&&t[typeof e.hasOwnProperty]&&e.hasOwnProperty||y;for(i in e)s&&"prototype"==i||!o.call(e,i)||r(i);for(a=n.length;i=n[--a];)o.call(e,i)&&r(i)}),$(e,r)};if(!s("json-stringify")&&!s("date-serialization")){var j={92:"\\\\",34:'\\"',8:"\\b",12:"\\f",10:"\\n",13:"\\r",9:"\\t"},A="000000",O=function(e,t){return(A+(t||0)).slice(-e)},I=function(e){var t,r,i,n,a,s,o,l,c;if(w)t=function(e){r=e.getUTCFullYear(),i=e.getUTCMonth(),n=e.getUTCDate(),s=e.getUTCHours(),o=e.getUTCMinutes(),l=e.getUTCSeconds(),c=e.getUTCMilliseconds()};else{var u=f.floor,d=[0,31,59,90,120,151,181,212,243,273,304,334],p=function(e,t){return d[t]+365*(e-1970)+u((e-1969+(t=+(t>1)))/4)-u((e-1901+t)/100)+u((e-1601+t)/400)};t=function(e){for(n=u(e/864e5),r=u(n/365.2425)+1970-1;p(r+1,0)<=n;r++);for(i=u((n-p(r,0))/30.42);p(r,i+1)<=n;i++);n=1+n-p(r,i),a=(e%864e5+864e5)%864e5,s=u(a/36e5)%24,o=u(a/6e4)%60,l=u(a/1e3)%60,c=a%1e3}}return(I=function(e){return e>-1/0&&e<1/0?(t(e),e=(r<=0||r>=1e4?(r<0?"-":"+")+O(6,r<0?-r:r):O(4,r))+"-"+O(2,i+1)+"-"+O(2,n)+"T"+O(2,s)+":"+O(2,o)+":"+O(2,l)+"."+O(3,c)+"Z",r=i=n=s=o=l=c=null):e=null,e})(e)};if(s("json-stringify")&&!s("date-serialization")){var T=n.stringify;n.stringify=function(e,t,r){var i=d.prototype.toJSON;d.prototype.toJSON=o;var n=T(e,t,r);return d.prototype.toJSON=i,n}}else{var x="\\u00",L=function(e){var t=e.charCodeAt(0),r=j[t];return r?r:x+O(2,t.toString(16))},E=/[\x00-\x1f\x22\x5c]/g,H=function(e){return E.lastIndex=0,'"'+(E.test(e)?e.replace(E,L):e)+'"'},J=function(e,t,r,i,n,s,o){var l,c,u,p,f,g,m,y,w;if(a(function(){l=t[e]}),"object"==typeof l&&l&&(l.getUTCFullYear&&v.call(l)==b&&l.toJSON===d.prototype.toJSON?l=I(l):"function"==typeof l.toJSON&&(l=l.toJSON(e))),r&&(l=r.call(t,e,l)),l==h)return l===h?l:"null";switch(c=typeof l,"object"==c&&(u=v.call(l)),u||c){case"boolean":case C:return""+l;case"number":case k:return l>-1/0&&l<1/0?""+l:"null";case"string":case P:return H(""+l)}if("object"==typeof l){for(m=o.length;m--;)if(o[m]===l)throw _();if(o.push(l),p=[],y=s,s+=n,u==D){for(g=0,m=l.length;g<m;g++)f=J(g,l,r,i,n,s,o),p.push(f===h?"null":f);w=p.length?n?"[\n"+s+p.join(",\n"+s)+"\n"+y+"]":"["+p.join(",")+"]":"[]"}else $(i||l,function(e){var t=J(e,l,r,i,n,s,o);t!==h&&p.push(H(e)+":"+(n?" ":"")+t)}),w=p.length?n?"{\n"+s+p.join(",\n"+s)+"\n"+y+"}":"{"+p.join(",")+"}":"{}";return o.pop(),w}};n.stringify=function(e,r,i){var n,a,s,o;if(t[typeof r]&&r)if(o=v.call(r),o==S)a=r;else if(o==D){s={};for(var l,c=0,u=r.length;c<u;)l=r[c++],o=v.call(l),"[object String]"!=o&&"[object Number]"!=o||(s[l]=1)}if(i)if(o=v.call(i),o==k){if((i-=i%1)>0)for(i>10&&(i=10),n="";n.length<i;)n+=" "}else o==P&&(n=i.length<=10?i:i.slice(0,10));return J("",(l={},l[""]=e,l),a,s,n,"",[])}}}if(!s("json-parse")){var B,U,R=c.fromCharCode,M={92:"\\",34:'"',47:"/",98:"\b",116:"\t",110:"\n",102:"\f",114:"\r"},K=function(){throw B=U=null,p()},W=function(){for(var e,t,r,i,n,a=U,s=a.length;B<s;)switch(n=a.charCodeAt(B)){case 9:case 10:case 13:case 32:B++;break;case 123:case 125:case 91:case 93:case 58:case 44:return e=N?a.charAt(B):a[B],B++,e;case 34:for(e="@",B++;B<s;)if(n=a.charCodeAt(B),n<32)K();else if(92==n)switch(n=a.charCodeAt(++B)){case 92:case 34:case 47:case 98:case 116:case 110:case 102:case 114:e+=M[n],B++;break;case 117:for(t=++B,r=B+4;B<r;B++)n=a.charCodeAt(B),n>=48&&n<=57||n>=97&&n<=102||n>=65&&n<=70||K();e+=R("0x"+a.slice(t,B));break;default:K()}else{if(34==n)break;for(n=a.charCodeAt(B),t=B;n>=32&&92!=n&&34!=n;)n=a.charCodeAt(++B);e+=a.slice(t,B)}if(34==a.charCodeAt(B))return B++,e;K();default:if(t=B,45==n&&(i=!0,n=a.charCodeAt(++B)),n>=48&&n<=57){for(48==n&&(n=a.charCodeAt(B+1),n>=48&&n<=57)&&K(),i=!1;B<s&&(n=a.charCodeAt(B),n>=48&&n<=57);B++);if(46==a.charCodeAt(B)){for(r=++B;r<s&&(n=a.charCodeAt(r),!(n<48||n>57));r++);r==B&&K(),B=r}if(n=a.charCodeAt(B),101==n||69==n){for(n=a.charCodeAt(++B),43!=n&&45!=n||B++,r=B;r<s&&(n=a.charCodeAt(r),!(n<48||n>57));r++);r==B&&K(),B=r}return+a.slice(t,B)}i&&K();var o=a.slice(B,B+4);if("true"==o)return B+=4,!0;if("fals"==o&&101==a.charCodeAt(B+4))return B+=5,!1;if("null"==o)return B+=4,null;K()}return"$"},V=function(e){var t,r;if("$"==e&&K(),"string"==typeof e){if("@"==(N?e.charAt(0):e[0]))return e.slice(1);if("["==e){for(t=[];e=W(),"]"!=e;)r?","==e?(e=W(),"]"==e&&K()):K():r=!0,","==e&&K(),t.push(V(e));return t}if("{"==e){for(t={};e=W(),"}"!=e;)r?","==e?(e=W(),"}"==e&&K()):K():r=!0,","!=e&&"string"==typeof e&&"@"==(N?e.charAt(0):e[0])&&":"==W()||K(),t[e.slice(1)]=V(W());return t}K()}return e},q=function(e,t,r){var i=F(e,t,r);i===h?delete e[t]:e[t]=i},F=function(e,t,r){var i,n=e[t];if("object"==typeof n&&n)if(v.call(n)==D)for(i=n.length;i--;)q(v,$,n,i,r);else $(n,function(e){q(n,e,r)});return r.call(e,t,n)};n.parse=function(e,t){var r,i;return B=0,U=""+e,r=V(W()),"$"!=W()&&K(),B=U=null,t&&v.call(t)==S?F((i={},i[""]=r,i),"",t):r}}}return n.runInContext=e,n}var t={"function":!0,object:!0},r=t[typeof window]&&window||this,i=r.JSON,n=r.JSON3,a=!1,s=e(r,r.JSON3={noConflict:function(){return a||(a=!0,r.JSON=i,r.JSON3=n,i=n=null),s}});r.JSON={parse:s.parse,stringify:s.stringify}}).call(window),function(e,t){t(e)}(window,function(e){if(e.atob)try{e.atob(" ")}catch(t){e.atob=function(e){var t=function(t){return e(String(t).replace(/[\t\n\f\r ]+/g,""))};return t.original=e,t}(e.atob)}else{var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",i=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/;e.btoa=function(e){e=String(e);for(var t,i,n,a,s="",o=0,l=e.length%3;o<e.length;){if((i=e.charCodeAt(o++))>255||(n=e.charCodeAt(o++))>255||(a=e.charCodeAt(o++))>255)return"";t=i<<16|n<<8|a,s+=r.charAt(t>>18&63)+r.charAt(t>>12&63)+r.charAt(t>>6&63)+r.charAt(63&t)}return l?s.slice(0,l-3)+"===".substring(l):s},e.atob=function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!i.test(e))return"";e+="==".slice(2-(3&e.length));for(var t,n,a,s="",o=0;o<e.length;)t=r.indexOf(e.charAt(o++))<<18|r.indexOf(e.charAt(o++))<<12|(n=r.indexOf(e.charAt(o++)))<<6|(a=r.indexOf(e.charAt(o++))),s+=64===n?String.fromCharCode(t>>16&255):64===a?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return s}}}),function(){String.prototype.replaceAll||(String.prototype.replaceAll=function(e,t){return"[object regexp]"===Object.prototype.toString.call(e).toLowerCase()?this.replace(e,t):this.replace(new RegExp(e,"g"),t)})}();var Zt=Array.prototype,Qt=Zt.forEach,Yt=Zt.slice,Gt=Array.isArray,er=Object.prototype,tr=er.toString,rr=er.hasOwnProperty,ir={},nr=Gt||function(e){return"[object Array]"===tr.call(e)},ar=function(){function e(){return r=(9301*r+49297)%233280,r/233280}var t=new Date,r=t.getTime();return function(t){return Math.ceil(e()*t)}}(),sr=Date.now||function(){return(new Date).getTime()},or={},lr={preset_properties:{search_keyword_baidu:!1,latest_utm:!0,latest_traffic_source_type:!0,latest_search_keyword:!0,latest_referrer:!0,latest_referrer_host:!1,latest_landing_page:!1,latest_wx_ad_click_id:void 0,url:!0,title:!0},encrypt_cookie:!1,enc_cookie:!1,login_id_key:"$identity_login_id",img_use_crossorigin:!1,name:"sa",max_referrer_string_length:200,max_string_length:500,max_id_length:255,max_key_length:100,cross_subdomain:!0,show_log:!1,is_debug:!1,debug_mode:!1,debug_mode_upload:!1,source_channel:[],sdk_id:"",send_type:"image",vtrack_ignore:{},auto_init:!0,is_track_single_page:!1,is_single_page:!1,batch_send:!1,source_type:{},callback_timeout:200,datasend_timeout:8e3,is_track_device_id:!1,ignore_oom:!0,app_js_bridge:!1};M.init=function(e){this.ele=e},M.init.prototype={addClass:function(e){var t=" "+this.ele.className+" ";return t.indexOf(" "+e+" ")===-1&&(this.ele.className=this.ele.className+(""===this.ele.className?"":" ")+e),this},removeClass:function(e){var t=" "+this.ele.className+" ";return t.indexOf(" "+e+" ")!==-1&&(this.ele.className=t.replace(" "+e+" "," ").slice(1,-1)),this},hasClass:function(e){var t=" "+this.ele.className+" ";return t.indexOf(" "+e+" ")!==-1},attr:function(e,t){return"string"==typeof e&&g(t)?this.ele.getAttribute(e):("string"==typeof e&&(t=String(t),this.ele.setAttribute(e,t)),this)},offset:function(){var e=this.ele.getBoundingClientRect();if(e.width||e.height){var t=this.ele.ownerDocument,r=t.documentElement;return{top:e.top+window.pageYOffset-r.clientTop,left:e.left+window.pageXOffset-r.clientLeft}}return{top:0,left:0}},getSize:function(){if(!window.getComputedStyle)return{width:this.ele.offsetWidth,height:this.ele.offsetHeight};try{var e=this.ele.getBoundingClientRect();return{width:e.width,height:e.height}}catch(t){return{width:0,height:0}}},getStyle:function(e){return this.ele.currentStyle?this.ele.currentStyle[e]:this.ele.ownerDocument.defaultView.getComputedStyle(this.ele,null).getPropertyValue(e)},wrap:function(e){var t=document.createElement(e);return this.ele.parentNode.insertBefore(t,this.ele),t.appendChild(this.ele),M(t)},getCssStyle:function(e){var t=this.ele.style.getPropertyValue(e);if(t)return t;var r=null;if("function"==typeof window.getMatchedCSSRules&&(r=window.getMatchedCSSRules(this.ele)),!r||!nr(r))return null;for(var i=r.length-1;i>=0;i--){var n=r[i];if(t=n.style.getPropertyValue(e))return t}},sibling:function(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e},next:function(){return this.sibling(this.ele,"nextSibling")},prev:function(){return this.sibling(this.ele,"previousSibling")},siblings:function(){return this.siblings((this.ele.parentNode||{}).firstChild,this.ele)},children:function(){return this.siblings(this.ele.firstChild)},parent:function(){var e=this.ele.parentNode;return e=e&&11!==e.nodeType?e:null,M(e)},previousElementSibling:function(){var e=this.ele;if("previousElementSibling"in document.documentElement)return M(e.previousElementSibling);for(;e=e.previousSibling;)if(1===e.nodeType)return M(e);return M(null)},getSameTypeSiblings:function(){for(var e=this.ele,t=e.parentNode,r=e.tagName.toLowerCase(),i=[],n=0;n<t.children.length;n++){var a=t.children[n];1===a.nodeType&&a.tagName.toLowerCase()===r&&i.push(t.children[n])}return i},getParents:function(){try{var e=this.ele;if(!S(e))return[];var t=[e];if(null===e||null===e.parentElement)return[];for(;null!==e.parentElement;)e=e.parentElement,t.push(e);return t}catch(r){return[]}}};var cr={isHttpUrl:function(e){if("string"!=typeof e)return!1;var t=/^https?:\/\/.+/;return t.test(e)!==!1||(H("Invalid URL"),!1)},removeScriptProtocol:function(e){if("string"!=typeof e)return"";for(var t=/^\s*javascript/i;t.test(e);)e=e.replace(t,"");return e}},ur=function(){var e={"+":"-","/":"_","=":"."},t={"-":"+",_:"/",".":"="},r=function(t){return t.replace(/[+\/=]/g,function(t){return e[t]})},i=function(e){return e.replace(/[-_.]/g,function(e){return t[e]})},n=function(e){return e.replace(/[.=]{1,2}$/,"")},a=function(e){return/^[A-Za-z0-9+\/]*[=]{0,2}$/.test(e)},s=function(e){return/^[A-Za-z0-9_-]*[.]{0,2}$/.test(e)};return{encode:r,decode:i,trim:n,isBase64:a,isUrlSafeBase64:s}}(),dr={distinct_id:function(){},jssdkDebug:function(){},_sendDebug:function(e){},apph5:function(e){var t="app_h5\u6253\u901a\u5931\u8d25-",r={1:t+"use_app_track\u4e3afalse",2:t+"Android\u6216\u8005iOS\uff0c\u6ca1\u6709\u66b4\u9732\u76f8\u5e94\u65b9\u6cd5",3.1:t+"Android\u6821\u9a8cserver_url\u5931\u8d25",3.2:t+"iOS\u6821\u9a8cserver_url\u5931\u8d25",4.1:t+"H5 \u6821\u9a8c iOS server_url \u5931\u8d25",4.2:t+"H5 \u6821\u9a8c Android server_url \u5931\u8d25"},i=e.output,n=e.step,a=e.data||"";"all"!==i&&"console"!==i||H(r[n]),("all"===i||"code"===i)&&_(or.is_debug)&&or.is_debug.apph5&&(a.type&&"profile"===a.type.slice(0,7)||(a.properties._jssdk_debug_info="apph5-"+String(n)))},defineMode:function(e){var t={1:{title:"\u5f53\u524d\u9875\u9762\u65e0\u6cd5\u8fdb\u884c\u53ef\u89c6\u5316\u5168\u57cb\u70b9",message:"App SDK \u4e0e Web JS SDK \u6ca1\u6709\u8fdb\u884c\u6253\u901a\uff0c\u8bf7\u8054\u7cfb\u8d35\u65b9\u6280\u672f\u4eba\u5458\u4fee\u6b63 App SDK \u7684\u914d\u7f6e\uff0c\u8be6\u7ec6\u4fe1\u606f\u8bf7\u67e5\u770b\u6587\u6863\u3002",link_text:"\u914d\u7f6e\u6587\u6863",link_url:"https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_link-1573913.html"},2:{title:"\u5f53\u524d\u9875\u9762\u65e0\u6cd5\u8fdb\u884c\u53ef\u89c6\u5316\u5168\u57cb\u70b9",message:"App SDK \u4e0e Web JS SDK \u6ca1\u6709\u8fdb\u884c\u6253\u901a\uff0c\u8bf7\u8054\u7cfb\u8d35\u65b9\u6280\u672f\u4eba\u5458\u4fee\u6b63 Web JS SDK \u7684\u914d\u7f6e\uff0c\u8be6\u7ec6\u4fe1\u606f\u8bf7\u67e5\u770b\u6587\u6863\u3002",link_text:"\u914d\u7f6e\u6587\u6863",link_url:"https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_link-1573913.html"},3:{title:"\u5f53\u524d\u9875\u9762\u65e0\u6cd5\u8fdb\u884c\u53ef\u89c6\u5316\u5168\u57cb\u70b9",message:"Web JS SDK \u6ca1\u6709\u5f00\u542f\u5168\u57cb\u70b9\u914d\u7f6e\uff0c\u8bf7\u8054\u7cfb\u8d35\u65b9\u5de5\u4f5c\u4eba\u5458\u4fee\u6b63 SDK \u7684\u914d\u7f6e\uff0c\u8be6\u7ec6\u4fe1\u606f\u8bf7\u67e5\u770b\u6587\u6863\u3002",link_text:"\u914d\u7f6e\u6587\u6863",link_url:"https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_web_all-1573964.html"},4:{title:"\u5f53\u524d\u9875\u9762\u65e0\u6cd5\u8fdb\u884c\u53ef\u89c6\u5316\u5168\u57cb\u70b9",message:"Web JS SDK \u914d\u7f6e\u7684\u6570\u636e\u6821\u9a8c\u5730\u5740\u4e0e App SDK \u914d\u7f6e\u7684\u6570\u636e\u6821\u9a8c\u5730\u5740\u4e0d\u4e00\u81f4\uff0c\u8bf7\u8054\u7cfb\u8d35\u65b9\u5de5\u4f5c\u4eba\u5458\u4fee\u6b63 SDK \u7684\u914d\u7f6e\uff0c\u8be6\u7ec6\u4fe1\u606f\u8bf7\u67e5\u770b\u6587\u6863\u3002",link_text:"\u914d\u7f6e\u6587\u6863",link_url:"https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_link-1573913.html"}};return!(!e||!t[e])&&t[e]},protocol:{protocolIsSame:function(e,t){try{if(Z(e).protocol!==Z(t).protocol)return!1}catch(r){return H("\u4e0d\u652f\u6301 _.URL \u65b9\u6cd5"),!1}return!0},serverUrl:function(){h(or.server_url)&&""!==or.server_url&&!this.protocolIsSame(or.server_url,location.href)&&H("SDK \u68c0\u6d4b\u5230\u60a8\u7684\u6570\u636e\u53d1\u9001\u5730\u5740\u548c\u5f53\u524d\u9875\u9762\u5730\u5740\u7684\u534f\u8bae\u4e0d\u4e00\u81f4\uff0c\u5efa\u8bae\u60a8\u4fee\u6539\u6210\u4e00\u81f4\u7684\u534f\u8bae\u3002\n\u56e0\u4e3a\uff1a1\u3001https \u4e0b\u9762\u53d1\u9001 http \u7684\u56fe\u7247\u8bf7\u6c42\u4f1a\u5931\u8d25\u30022\u3001http \u9875\u9762\u4f7f\u7528 https + ajax \u65b9\u5f0f\u53d1\u6570\u636e\uff0c\u5728 ie9 \u53ca\u4ee5\u4e0b\u4f1a\u4e22\u5931\u6570\u636e\u3002")},ajax:function(e){return e!==or.server_url&&void(h(e)&&""!==e&&!this.protocolIsSame(e,location.href)&&H("SDK \u68c0\u6d4b\u5230\u60a8\u7684\u6570\u636e\u53d1\u9001\u5730\u5740\u548c\u5f53\u524d\u9875\u9762\u5730\u5740\u7684\u534f\u8bae\u4e0d\u4e00\u81f4\uff0c\u5efa\u8bae\u60a8\u4fee\u6539\u6210\u4e00\u81f4\u7684\u534f\u8bae\u3002\u56e0\u4e3a http \u9875\u9762\u4f7f\u7528 https + ajax \u65b9\u5f0f\u53d1\u6570\u636e\uff0c\u5728 ie9 \u53ca\u4ee5\u4e0b\u4f1a\u4e22\u5931\u6570\u636e\u3002"))}}},pr="utm_source utm_medium utm_campaign utm_content utm_term",_r="1.21.12",fr=function(){var e=function(){for(var e=1*new Date,t=0;e==1*new Date;)t++;return e.toString(16)+t.toString(16)},t=function(){return N().toString(16).replace(".","")},r=function(){function e(e,t){var r,i=0;for(r=0;r<t.length;r++)i|=n[r]<<8*r;return e^i}var t,r,i=navigator.userAgent,n=[],a=0;for(t=0;t<i.length;t++)r=i.charCodeAt(t),n.unshift(255&r),n.length>=4&&(a=e(a,n),n=[]);return n.length>0&&(a=e(a,n)),a.toString(16)};return function(){var i=String(screen.height*screen.width);i=i&&/\d{5,}/.test(i)?i.toString(16):String(31242*N()).replace(".","").slice(0,8);var n=e()+"-"+t()+"-"+r()+"-"+i+"-"+e();return n?n:(String(N())+String(N())+String(N())).slice(2,15)}}(),gr={data:{},id:function(){return this.data.id?this.data.id:(this.data.id=ce(),this.data.id)},type:function(){return this.data.type?this.data.type:(this.data.type=ue(),this.data.type)}},hr={initPage:function(){var e=_e(),t=G(),r=se(t);r||dr.jssdkDebug("url_domain\u5f02\u5e38_"+t+"_"+r),this.pageProp={referrer:e,referrer_host:e?Q(e):"",url:t,url_host:Q(t,"url_host\u53d6\u503c\u5f02\u5e38"),url_domain:r}},pageProp:{},campaignParams:function(){var t=pr.split(" "),r="",i={};return nr(or.source_channel)&&or.source_channel.length>0&&(t=t.concat(or.source_channel),t=j(t)),e(t,function(e){r=F(location.href,e),r.length&&(i[e]=r)}),i},campaignParamsStandard:function(t,r){t=t||"",r=r||"";var i=hr.campaignParams(),n={},a={};return e(i,function(e,i,s){(" "+pr+" ").indexOf(" "+i+" ")!==-1?n[t+i]=s[i]:a[r+i]=s[i]}),{$utms:n,otherUtms:a}},properties:function(){var e=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight||0,t=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth||0,r={$timezone_offset:(new Date).getTimezoneOffset(),$screen_height:Number(screen.height)||0,$screen_width:Number(screen.width)||0,$viewport_height:e,$viewport_width:t,$lib:"js",$lib_version:_r};return r},currentProps:{},register:function(e){r(hr.currentProps,e)}},mr={get:function(e){for(var t=e+"=",r=document.cookie.split(";"),i=0;i<r.length;i++){for(var n=r[i];" "==n.charAt(0);)n=n.substring(1,n.length);if(0==n.indexOf(t))return V(n.substring(t.length,n.length))}return null},set:function(e,t,r,i){function n(e){return!!e&&e.replaceAll(/\r\n/g,"")}i="undefined"==typeof i?or.cross_subdomain:i;var a="",s="",o="",l="";if(r=null==r?73e3:r,i){var c=se(location.href);"url\u89e3\u6790\u5931\u8d25"===c&&(c=""),a=c?"; domain="+c:""}if(0!==r){var u=new Date;"s"===String(r).slice(-1)?u.setTime(u.getTime()+1e3*Number(String(r).slice(0,-1))):u.setTime(u.getTime()+24*r*60*60*1e3),s="; expires="+u.toGMTString()}h(or.set_cookie_samesite)&&""!==or.set_cookie_samesite&&(l="; SameSite="+or.set_cookie_samesite),or.is_secure_cookie&&(o="; secure");var d="",p="",_="";e&&(d=n(e)),t&&(p=n(t)),a&&(_=n(a)),d&&p&&(document.cookie=d+"="+encodeURIComponent(p)+s+"; path=/"+_+l+o)},remove:function(e,t){t="undefined"==typeof t?or.cross_subdomain:t,mr.set(e,"1",-1,t)},getCookieName:function(e,t){var r="";if(t=t||location.href,or.cross_subdomain===!1){try{r=Z(t).hostname}catch(i){H(i)}r="string"==typeof r&&""!==r?"sajssdk_2015_"+or.sdk_id+e+"_"+r.replace(/\./g,"_"):"sajssdk_2015_root_"+or.sdk_id+e}else r="sajssdk_2015_cross_"+or.sdk_id+e;return r},getNewUser:function(){var e="new_user";return this.isSupport()?null!==this.get("sensorsdata_is_new_user")||null!==this.get(this.getCookieName(e)):null!==yr.get(yr.getMemoryName(e))},isSupport:function(e,t){function r(){i.set(e,t);var r=i.get(e);return r===t&&(i.remove(e),!0)}e=e||"sajssdk_2015_cookie_access_test",t=t||"1";var i=this;return navigator.cookieEnabled&&r()}},vr={get:function(e){return window.localStorage.getItem(e)},parse:function(e){var t;try{t=JSON.parse(vr.get(e))||null}catch(r){H(r)}return t},set:function(e,t){window.localStorage.setItem(e,t)},remove:function(e){window.localStorage.removeItem(e)},isSupport:function(){var e=!0;try{var t="__sensorsdatasupport__",r="testIsSupportStorage";vr.set(t,r),vr.get(t)!==r&&(e=!1),vr.remove(t)}catch(i){e=!1}return e}},yr={data:{},get:function(e){var t=this.data[e];return void 0===t?null:void 0!==t._expirationTimestamp_?(new Date).getTime()>t._expirationTimestamp_?null:t.value:t},set:function(e,t,r){if(r){var i,n=new Date;i="s"===String(r).slice(-1)?n.getTime()+1e3*Number(String(r).slice(0,-1)):n.getTime()+24*r*60*60*1e3,t={value:t,_expirationTimestamp_:i}}this.data[e]=t},getMemoryName:function(e){return"sajssdk_2015_"+or.sdk_id+e}},wr={isSupport:function(){var e=!0,t="__sensorsdatasupport__",r="testIsSupportStorage";try{sessionStorage&&sessionStorage.setItem?(sessionStorage.setItem(t,r),sessionStorage.removeItem(t,r),e=!0):e=!1}catch(i){e=!1}return e}},Sr=xe.prototype;Sr.on=function(e,t){if(!e||!t)return!1;if(!Te(t))throw new Error("listener must be a function");this._events[e]=this._events[e]||[];var r="object"==typeof t;return this._events[e].push(r?t:{listener:t,once:!1}),this},Sr.prepend=function(e,t){if(!e||!t)return!1;if(!Te(t))throw new Error("listener must be a function");this._events[e]=this._events[e]||[];var r="object"==typeof t;return this._events[e].unshift(r?t:{listener:t,once:!1}),this},Sr.prependOnce=function(e,t){return this.prepend(e,{listener:t,once:!0})},Sr.once=function(e,t){return this.on(e,{listener:t,once:!0})},Sr.off=function(e,t){var r=this._events[e];if(!r)return!1;if("number"==typeof t)r.splice(t,1);else if("function"==typeof t)for(var i=0,n=r.length;i<n;i++)r[i]&&r[i].listener===t&&r.splice(i,1);return this},Sr.emit=function(e,t){var r=this._events[e];if(!r)return!1;for(var i=0;i<r.length;i++){var n=r[i];n&&(n.listener.call(this,t||{}),n.once&&this.off(e,i))}return this},Sr.removeAllListeners=function(e){
e&&this._events[e]?this._events[e]=[]:this._events={}},Sr.listeners=function(e){return e&&"string"==typeof e?this._events[e]:this._events};var br=function(){this._events=[],this.pendingEvents=[]};br.prototype={emit:function(t){var r=[].slice.call(arguments,1);e(this._events,function(e){e.type===t&&e.callback.apply(e.context,r)}),this.pendingEvents.push({type:t,data:r}),this.pendingEvents.length>20?this.pendingEvents.shift():null},on:function(t,r,i,n){"function"==typeof r&&(this._events.push({type:t,callback:r,context:i||this}),n=n!==!1,this.pendingEvents.length>0&&n&&e(this.pendingEvents,function(e){e.type===t&&r.apply(i,e.data)}))},tempAdd:function(e,t){if(t&&e)return this.emit(e,t)},isReady:function(){}};var kr="data:enc;",Pr="dfm-enc-",Dr={__proto__:null,each:e,map:t,extend:r,extend2Lev:i,coverExtend:n,isArray:nr,isFunction:a,isArguments:s,toArray:l,values:c,indexOf:u,filter:d,trim:p,isObject:_,isEmptyObject:f,isUndefined:g,isString:h,isDate:v,isBoolean:y,isNumber:w,isElement:S,isJSONString:b,safeJSONParse:k,throttle:P,hashCode:D,getRandomBasic:ar,getRandom:N,formatJsonString:$,unique:j,base64Decode:O,base64Encode:A,now:sr,rot13obfs:I,rot13defs:T,dfmapping:x,strToUnicode:L,hashCode53:C,hasAttributes:J,hasAttribute:B,getElementContent:U,loadScript:R,ry:M,setCssStyle:K,getDomBySelector:W,decodeURIComponent:V,decodeURI:q,getQueryParam:F,urlParse:z,getURLSearchParams:X,URL:Z,getHostname:Q,getQueryParamsFromUrl:Y,urlSafeBase64:ur,secCheck:cr,getURL:G,encodeDates:ee,formatDate:te,searchObjDate:re,mediaQueriesSupported:ye,getScreenOrientation:we,cookie:mr,localStorage:vr,sessionStorage:wr,isSupportCors:Se,isIOS:be,getUA:Pe,getIOSVersion:ke,isSupportBeaconSend:De,memory:yr,parseSuperProperties:ie,searchConfigData:ne,strip_empty_properties:ae,UUID:fr,getCurrentDomain:se,getEleInfo:oe,isBaiduTraffic:le,getReferrerEqid:ce,getReferrerEqidType:ue,getBaiduKeyword:gr,getCookieTopLevelDomain:de,isReferralTraffic:pe,getReferrer:_e,getKeywordFromReferrer:ge,getWxAdIdFromUrl:he,getReferSearchEngine:fe,getSourceFromReferrer:me,info:hr,autoExeQueue:ve,addEvent:Ce,addHashEvent:Ne,listenPageState:$e,bindReady:je,xhr:Ae,ajax:Oe,jsonp:Ie,EventEmitter:xe,EventEmitterSa:br,encrypt:He,decryptIfNeeded:Ee},Cr={checkIsAddSign:function(e){"track"===e.type&&(mr.getNewUser()?e.properties.$is_first_day=!0:e.properties.$is_first_day=!1)},is_first_visit_time:!1,is_page_first_visited:!1,checkIsFirstTime:function(e){"track"===e.type&&"$pageview"===e.event&&(this.is_first_visit_time?(e.properties.$is_first_time=!0,this.is_first_visit_time=!1):e.properties.$is_first_time=!1)},setDeviceId:function(e){var t=null,r=mr.get("sensorsdata2015jssdkcross"+Xt.para.sdk_id);r=Ee(r);var i={};null!=r&&b(r)&&(i=JSON.parse(r),i.$device_id&&(t=i.$device_id)),t=t||e,Xt.para.cross_subdomain===!0?Xt.store.set("$device_id",t):(i.$device_id=t,i=JSON.stringify(i),Xt.para.encrypt_cookie&&(i=He(i)),mr.set("sensorsdata2015jssdkcross"+Xt.para.sdk_id,i,null,!0)),Xt.para.is_track_device_id&&(hr.currentProps.$device_id=t)},storeInitCheck:function(){if(Xt.is_first_visitor){var e=new Date,t={h:23-e.getHours(),m:59-e.getMinutes(),s:59-e.getSeconds()};mr.isSupport()?mr.set(mr.getCookieName("new_user"),"1",3600*t.h+60*t.m+t.s+"s"):yr.set(yr.getMemoryName("new_user"),"1",3600*t.h+60*t.m+t.s+"s"),this.is_first_visit_time=!0,this.is_page_first_visited=!0}else mr.getNewUser()||(this.checkIsAddSign=function(e){"track"===e.type&&(e.properties.$is_first_day=!1)}),this.checkIsFirstTime=function(e){"track"===e.type&&"$pageview"===e.event&&(e.properties.$is_first_time=!1)}},checkIsFirstLatest:function(){var t=hr.pageProp.url_domain,r={};""===t&&(t="url\u89e3\u6790\u5931\u8d25");var i=ge(document.referrer,!0);if(Xt.para.preset_properties.search_keyword_baidu?pe(document.referrer)&&(!le()||_(i)&&i.active?Xt.store._state&&Xt.store._state.props&&(Xt.store._state.props.$search_keyword_id&&delete Xt.store._state.props.$search_keyword_id,Xt.store._state.props.$search_keyword_id_type&&delete Xt.store._state.props.$search_keyword_id_type,Xt.store._state.props.$search_keyword_id_hash&&delete Xt.store._state.props.$search_keyword_id_hash):(r.$search_keyword_id=gr.id(),r.$search_keyword_id_type=gr.type(),r.$search_keyword_id_hash=C(r.$search_keyword_id))):Xt.store._state&&Xt.store._state.props&&(Xt.store._state.props.$search_keyword_id&&delete Xt.store._state.props.$search_keyword_id,Xt.store._state.props.$search_keyword_id_type&&delete Xt.store._state.props.$search_keyword_id_type,Xt.store._state.props.$search_keyword_id_hash&&delete Xt.store._state.props.$search_keyword_id_hash),Xt.store.save(),e(Xt.para.preset_properties,function(i,n){if(n.indexOf("latest_")===-1)return!1;if(n=n.slice(7),i){if("wx_ad_click_id"===n&&"not_collect"===i)return!1;if("utm"!==n&&"url\u89e3\u6790\u5931\u8d25"===t)"wx_ad_click_id"===n?(r._latest_wx_ad_click_id="url\u7684domain\u89e3\u6790\u5931\u8d25",r._latest_wx_ad_hash_key="url\u7684domain\u89e3\u6790\u5931\u8d25",r._latest_wx_ad_callbacks="url\u7684domain\u89e3\u6790\u5931\u8d25"):r["$latest_"+n]="url\u7684domain\u89e3\u6790\u5931\u8d25";else if(pe(document.referrer))switch(n){case"traffic_source_type":r.$latest_traffic_source_type=me();break;case"referrer":r.$latest_referrer=hr.pageProp.referrer;break;case"search_keyword":ge()?r.$latest_search_keyword=ge():_(Xt.store._state)&&_(Xt.store._state.props)&&Xt.store._state.props.$latest_search_keyword&&delete Xt.store._state.props.$latest_search_keyword;break;case"landing_page":r.$latest_landing_page=G();break;case"wx_ad_click_id":var a=he(location.href);r._latest_wx_ad_click_id=a.click_id,r._latest_wx_ad_hash_key=a.hash_key,r._latest_wx_ad_callbacks=a.callbacks}}else if("utm"===n&&Xt.store._state&&Xt.store._state.props)for(var s in Xt.store._state.props)(0===s.indexOf("$latest_utm")||0===s.indexOf("_latest_")&&s.indexOf("_latest_wx_ad_")<0)&&delete Xt.store._state.props[s];else if(Xt.store._state&&Xt.store._state.props&&"$latest_"+n in Xt.store._state.props)delete Xt.store._state.props["$latest_"+n];else if("wx_ad_click_id"==n&&Xt.store._state&&Xt.store._state.props&&i===!1){var o=["_latest_wx_ad_click_id","_latest_wx_ad_hash_key","_latest_wx_ad_callbacks"];e(o,function(e){e in Xt.store._state.props&&delete Xt.store._state.props[e]})}}),Xt.register(r),Xt.para.preset_properties.latest_utm){var n=hr.campaignParamsStandard("$latest_","_latest_"),a=n.$utms,s=n.otherUtms;f(a)||Xt.register(a),f(s)||Xt.register(s)}}},Nr={identities:{set:function(e,t){var r={};switch(e){case"login":r[Xt.para.login_id_key]=t,r.$identity_cookie_id=Xt.store._state.identities.$identity_cookie_id;break;case"logout":r.$identity_cookie_id=Xt.store._state.identities.$identity_cookie_id;break;case"identify":r=JSON.parse(JSON.stringify(Xt.store._state.identities)),r.$identity_anonymous_id=t}Xt.store._state.identities=r,Xt.store.save()}},requests:[],_sessionState:{},_state:{distinct_id:"",first_id:"",props:{},identities:{}},getProps:function(){return this._state.props||{}},getSessionProps:function(){return this._sessionState},getDistinctId:function(){return this._state._distinct_id||this._state.distinct_id},getUnionId:function(e){var t={};e=e||this._state;var r=e._first_id||e.first_id,i=e._distinct_id||e.distinct_id;return r&&i?(t.login_id=i,t.anonymous_id=r):t.anonymous_id=i,t},getFirstId:function(){return this._state._first_id||this._state.first_id},initSessionState:function(){var e=mr.get("sensorsdata2015session");e=Ee(e);var t=null;null!==e&&"object"==typeof(t=k(e))&&(this._sessionState=t||{})},setOnce:function(e,t){e in this._state||this.set(e,t)},set:function(e,t){this._state=this._state||{};var r=this._state.distinct_id;this._state[e]=t,"first_id"===e?delete this._state._first_id:"distinct_id"===e&&delete this._state._distinct_id,this.save(),"distinct_id"===e&&r&&Xt.events.tempAdd("changeDistinctId",t)},change:function(e,t){this._state["_"+e]=t},setSessionProps:function(e){var t=this._sessionState;r(t,e),this.sessionSave(t)},setSessionPropsOnce:function(e){var t=this._sessionState;n(t,e),this.sessionSave(t)},setProps:function(e,t){var i={};i=t?e:r(this._state.props||{},e);for(var n in i)"string"==typeof i[n]&&(i[n]=i[n].slice(0,Xt.para.max_referrer_string_length));this.set("props",i)},setPropsOnce:function(e){var t=this._state.props||{};n(t,e),this.set("props",t)},clearAllProps:function(e){this._sessionState={};var t;if(nr(e)&&e.length>0)for(t=0;t<e.length;t++)h(e[t])&&e[t].indexOf("latest_")===-1&&_(this._state.props)&&e[t]in this._state.props&&delete this._state.props[e[t]];else if(_(this._state.props))for(t in this._state.props)1!==t.indexOf("latest_")&&delete this._state.props[t];this.sessionSave({}),this.save()},sessionSave:function(e){this._sessionState=e;var t=JSON.stringify(this._sessionState);Xt.para.encrypt_cookie&&(t=He(t)),mr.set("sensorsdata2015session",t,0)},save:function(){var e=JSON.parse(JSON.stringify(this._state));delete e._first_id,delete e._distinct_id,e.identities&&(e.identities=A(JSON.stringify(e.identities)));var t=JSON.stringify(e);Xt.para.encrypt_cookie&&(t=He(t)),mr.set(this.getCookieName(),t,73e3,Xt.para.cross_subdomain)},getCookieName:function(){var e="";if(Xt.para.cross_subdomain===!1){try{e=Z(location.href).hostname}catch(t){Xt.log(t)}e="string"==typeof e&&""!==e?"sa_jssdk_2015_"+Xt.para.sdk_id+e.replace(/\./g,"_"):"sa_jssdk_2015_root"+Xt.para.sdk_id}else e="sensorsdata2015jssdkcross"+Xt.para.sdk_id;return e},init:function(){function e(e){var t;e.identities&&(0===e.identities.indexOf("\n/")?e.identities=k(T(e.identities)):e.identities=k(O(e.identities)));var r=Nr.getUnionId(e);e.identities&&_(e.identities)&&!f(e.identities)?e.identities.$identity_anonymous_id&&e.identities.$identity_anonymous_id!==r.anonymous_id&&(e.identities.$identity_anonymous_id=r.anonymous_id):(e.identities={},e.identities.$identity_anonymous_id=r.anonymous_id,e.identities.$identity_cookie_id=fr()),e.history_login_id=e.history_login_id||{};var i=e.history_login_id,n=i.name;if(r.login_id)if(n&&_(e.identities)&&e.identities.hasOwnProperty(n)){if(e.identities[n]!==r.login_id){e.identities[n]=r.login_id;for(t in e.identities)_(e.identities)&&e.identities.hasOwnProperty(t)&&"$identity_cookie_id"!==t&&t!==n&&delete e.identities[t];e.history_login_id.value=r.login_id}}else{e.identities[Xt.para.login_id_key]=r.login_id;for(t in e.identities)_(e.identities)&&e.identities.hasOwnProperty(t)&&"$identity_cookie_id"!==t&&t!==Xt.para.login_id_key&&delete e.identities[t];e.history_login_id={name:Xt.para.login_id_key,value:r.login_id}}else{if(_(e.identities)&&e.identities.hasOwnProperty("$identity_login_id")||e.identities.hasOwnProperty(n))for(t in e.identities)_(e.identities)&&e.identities.hasOwnProperty(t)&&"$identity_cookie_id"!==t&&"$identity_anonymous_id"!==t&&delete e.identities[t];e.history_login_id={name:"",value:""}}return e}function t(e){Xt.store.set("distinct_id",e),Xt.store.set("identities",{$identity_cookie_id:e}),Xt.store.set("history_login_id",{name:"",value:""})}this.initSessionState();var i,n,a=fr();mr.isSupport()&&(i=mr.get(this.getCookieName()),i=Ee(i),n=k(i)),mr.isSupport()&&null!==i&&b(i)&&_(n)&&(!_(n)||n.distinct_id)?(Xt.store._state=r(e(n)),Xt.store.save()):(Xt.is_first_visitor=!0,t(a)),Cr.setDeviceId(a),Cr.storeInitCheck(),Cr.checkIsFirstLatest()},saveObjectVal:function(e,t){h(t)||(t=JSON.stringify(t)),1==Xt.para.encrypt_cookie&&(t=He(t)),vr.set(e,t)},readObjectVal:function(e){var t=vr.get(e);return t?(t=Ee(t),k(t)):null}},$r={string:function(e){H(e+" must be string")},emptyString:function(e){H(e+"'s is empty")},regexTest:function(e){H(e+" is invalid")},idLength:function(e){H(e+" length is longer than "+or.max_id_length)},keyLength:function(e){H(e+" length is longer than "+or.max_key_length)},stringLength:function(e){H(e+" length is longer than "+or.max_string_length)},voidZero:function(e){H(e+"'s is undefined")},reservedLoginId:function(e){H(e+" is invalid")},reservedBind:function(e){H(e+" is invalid")}},jr={regName:/^((?!^distinct_id$|^original_id$|^time$|^properties$|^id$|^first_id$|^second_id$|^users$|^events$|^event$|^user_id$|^date$|^datetime$|^user_tag.*|^user_group.*)[a-zA-Z_$][a-zA-Z\d_$]*)$/i,loginIDReservedNames:["$identity_anonymous_id","$identity_cookie_id"],bindReservedNames:["$identity_login_id","$identity_anonymous_id","$identity_cookie_id"],string:function(e){return!!h(e)},emptyString:function(e){return!(!h(e)||0===p(e).length)},regexTest:function(e){return!(!h(e)||!this.regName.test(e))},idLength:function(e){return!(!h(e)||e.length>or.max_id_length)},keyLength:function(e){return!(!h(e)||e.length>or.max_key_length)},stringLength:function(e){return!(!h(e)||e.length>or.max_string_length)},voidZero:function(e){return void 0!==e},reservedLoginId:function(e){return!(u(this.loginIDReservedNames,e)>-1)},reservedBind:function(e){return or.login_id_key&&this.bindReservedNames.indexOf(or.login_id_key)===-1&&this.bindReservedNames.push(or.login_id_key),!(u(this.bindReservedNames,e)>-1)}},Ar={distinct_id:{rules:["string","emptyString","idLength"],onComplete:function(e,t,r){return!e&&("emptyString"===r&&(t="Id"),a($r[r])&&$r[r](t),"idLength"===r)||e}},event:{rules:["string","emptyString","keyLength","regexTest"],onComplete:function(e,t,r){return e||("emptyString"===r&&(t="eventName"),a($r[r])&&$r[r](t)),!0}},propertyKey:{rules:["string","emptyString","keyLength","regexTest"],onComplete:function(e,t,r){return e||("emptyString"===r&&(t="Property key"),a($r[r])&&$r[r](t)),!0}},propertyValue:{rules:["voidZero"],onComplete:function(e,t,r){return e||(t="Property Value",a($r[r])&&$r[r](t)),!0}},properties:function(t){return _(t)?e(t,function(e,t){Je({propertyKey:t});var r=function(e,r,i){return e||(r=t+"'s Value",a($r[i])&&$r[i](r)),!0};Je({propertyValue:e},r)}):jr.voidZero(t)&&H("properties\u53ef\u4ee5\u6ca1\u6709\uff0c\u4f46\u6709\u7684\u8bdd\u5fc5\u987b\u662f\u5bf9\u8c61"),!0},propertiesMust:function(e){return void 0!==e&&_(e)&&!f(e)?this.properties.call(this,e):H("properties\u5fc5\u987b\u662f\u5bf9\u8c61"),!0},item_type:{rules:["string","emptyString","keyLength","regexTest"],onComplete:function(e,t,r){return e||("emptyString"===r&&(t="item_type"),a($r[r])&&$r[r](t)),!0}},item_id:{rules:["string","emptyString","stringLength"],onComplete:function(e,t,r){return e||("emptyString"===r&&(t="item_id"),a($r[r])&&$r[r](t)),!0}},loginIdKey:{rules:["string","emptyString","keyLength","regexTest","reservedLoginId"],onComplete:function(e,t,r){return!e&&("emptyString"===r&&(t="login_id_key"),a($r[r])&&$r[r](t),"keyLength"===r)||e}},bindKey:{rules:["string","emptyString","keyLength","regexTest","reservedBind"],onComplete:function(e,t,r){return!e&&("emptyString"===r&&(t="Key"),a($r[r])&&$r[r](t),"keyLength"===r)||e}},bindValue:{rules:["string","emptyString","idLength"],onComplete:function(e,t,r){return!e&&("emptyString"===r&&(t="Value"),a($r[r])&&$r[r](t),"idLength"===r)||e}},check:function(e,t,r){var i=this[e];if(a(i))return i.call(this,t);if(!i)return!1;for(var n=0;n<i.rules.length;n++){var s=i.rules[n],o=jr[s](t),l=a(r)?r(o,t,s):i.onComplete(o,t,s);if(!o)return l}return!0}},Or={stage:null,init:function(e){this.stage=e}},Ir={};Ir.check=Je,Ir.sendItem=function(e){var t={lib:{$lib:"js",$lib_method:"code",$lib_version:String(Xt.lib_version)},time:1*new Date};r(t,e),Or.stage.process("formatData",t),Xt.sendState.getSendCall(t)},Ir.send=function(e,t){var r=Xt.kit.buildData(e);Xt.kit.sendData(r,t)},Ir.debugPath=function(e){var t=e,r="";r=Xt.para.debug_mode_url.indexOf("?")!==-1?Xt.para.debug_mode_url+"&"+Xt.kit.encodeTrackData(e):Xt.para.debug_mode_url+"?"+Xt.kit.encodeTrackData(e),Oe({url:r,type:"GET",cors:!0,header:{"Dry-Run":String(Xt.para.debug_mode_upload)},success:function(e){f(e)===!0?alert("debug\u6570\u636e\u53d1\u9001\u6210\u529f"+t):alert("debug\u5931\u8d25 \u9519\u8bef\u539f\u56e0"+JSON.stringify(e))}})};var Tr={otherTags:[],getTargetElement:function(e,t){var r=this,i=e;if("object"!=typeof i)return null;if("string"!=typeof i.tagName)return null;var n=i.tagName.toLowerCase();if("body"===n.toLowerCase()||"html"===n.toLowerCase())return null;if(!i||!i.parentNode||!i.parentNode.children)return null;var a=i.parentNode,s=r.hasElement({event:t&&t.originalEvent||t,element:e},function(e){return"a"===e.tagName.toLowerCase()||J(e,Xt.para.heatmap.track_attr)}),o=r.otherTags;if("a"===n||"button"===n||"input"===n||"textarea"===n)return i;if(u(o,n)>-1)return i;if("button"===a.tagName.toLowerCase()||"a"===a.tagName.toLowerCase())return a;if("area"===n&&"map"===a.tagName.toLowerCase()&&M(a).prev().tagName&&"img"===M(a).prev().tagName.toLowerCase())return M(a).prev();if(s)return s;if("div"===n&&Xt.para.heatmap.collect_tags.div&&r.isDivLevelValid(i)){var l=Xt.para.heatmap&&Xt.para.heatmap.collect_tags&&Xt.para.heatmap.collect_tags.div&&Xt.para.heatmap.collect_tags.div.max_level||1;return l>1||r.isCollectableDiv(i)?i:null}if(r.isStyleTag(n)&&Xt.para.heatmap.collect_tags.div){var c=r.getCollectableParent(i);if(c&&r.isDivLevelValid(c))return c}return null},getDivLevels:function(t,r){var i=Tr.getElementPath(t,!0,r),n=i.split(" > "),a=0;return e(n,function(e){"div"===e&&a++}),a},isDivLevelValid:function(e){for(var t=Xt.para.heatmap&&Xt.para.heatmap.collect_tags&&Xt.para.heatmap.collect_tags.div&&Xt.para.heatmap.collect_tags.div.max_level||1,r=e.getElementsByTagName("div"),i=r.length-1;i>=0;i--)if(Tr.getDivLevels(r[i],e)>t)return!1;return!0},getElementPath:function(e,t,r){for(var i=[];e.parentNode;){if(e.id&&!t&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.id)){i.unshift(e.tagName.toLowerCase()+"#"+e.id);break}if(r&&e===r){i.unshift(e.tagName.toLowerCase());break}if(e===document.body){i.unshift("body");break}i.unshift(e.tagName.toLowerCase()),e=e.parentNode}return i.join(" > ")},getClosestLi:function(e){var t=function(e,t){for(;e&&e!==document&&1===e.nodeType;e=e.parentNode)if(e.tagName.toLowerCase()===t)return e;return null};return t(e,"li")},getElementPosition:function(e,t,r){function i(e){var t=e.parentNode;if(!t)return"";var r=M(e).getSameTypeSiblings(),i=r.length;if(1===i)return 0;for(var n=0,a=e;M(a).previousElementSibling().ele;a=M(a).previousElementSibling().ele,n++);return n}var n=Xt.heatmap.getClosestLi(e);if(!n)return null;var a=e.tagName.toLowerCase(),s=n.getElementsByTagName(a),o=s.length,l=[];if(o>1){for(var c=0;c<o;c++){var d=Xt.heatmap.getElementPath(s[c],r);d===t&&l.push(s[c])}if(l.length>1)return u(l,e)}return i(n)},setNotice:function(e){Xt.is_heatmap_render_mode=!0,Xt.para.heatmap||(Xt.errorMsg="\u60a8SDK\u6ca1\u6709\u914d\u7f6e\u5f00\u542f\u70b9\u51fb\u56fe\uff0c\u53ef\u80fd\u6ca1\u6709\u6570\u636e\uff01"),e&&"http:"===e.slice(0,5)&&"https:"===location.protocol&&(Xt.errorMsg="\u60a8\u7684\u5f53\u524d\u9875\u9762\u662fhttps\u7684\u5730\u5740\uff0c\u795e\u7b56\u5206\u6790\u73af\u5883\u4e5f\u5fc5\u987b\u662fhttps\uff01"),Xt.para.heatmap_url||(Xt.para.heatmap_url=location.protocol+"//static.sensorsdata.cn/sdk/"+Xt.lib_version+"/heatmap.min.js")},getDomIndex:function(e){if(!e.parentNode)return-1;for(var t=0,r=e.tagName,i=e.parentNode.children,n=0;n<i.length;n++)if(i[n].tagName===r){if(e===i[n])return t;t++}return-1},selector:function(e,t){var r=e.parentNode&&9==e.parentNode.nodeType?-1:this.getDomIndex(e);return e.getAttribute&&e.getAttribute("id")&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.getAttribute("id"))&&(!Xt.para.heatmap||Xt.para.heatmap&&"not_use_id"!==Xt.para.heatmap.element_selector)&&!t?"#"+e.getAttribute("id"):e.tagName.toLowerCase()+(~r?":nth-of-type("+(r+1)+")":"")},getDomSelector:function(e,t,r){if(!e||!e.parentNode||!e.parentNode.children)return!1;t=t&&t.join?t:[];var i=e.nodeName.toLowerCase();return e&&"body"!==i&&1==e.nodeType?(t.unshift(this.selector(e,r)),e.getAttribute&&e.getAttribute("id")&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.getAttribute("id"))&&Xt.para.heatmap&&"not_use_id"!==Xt.para.heatmap.element_selector&&!r?t.join(" > "):this.getDomSelector(e.parentNode,t,r)):(t.unshift("body"),t.join(" > "))},na:function(){var e=document.documentElement.scrollLeft||window.pageXOffset;return parseInt(isNaN(e)?0:e,10)},i:function(){var e=0;try{e=o.documentElement&&o.documentElement.scrollTop||m.pageYOffset,e=isNaN(e)?0:e}catch(t){e=0}return parseInt(e,10)},getBrowserWidth:function(){var e=window.innerWidth||document.body.clientWidth;return isNaN(e)?0:parseInt(e,10)},getBrowserHeight:function(){var e=window.innerHeight||document.body.clientHeight;return isNaN(e)?0:parseInt(e,10)},getScrollWidth:function(){var e=parseInt(document.body.scrollWidth,10);return isNaN(e)?0:e},getEleDetail:function(e){var t=this.getDomSelector(e),r=oe({target:e});r.$element_selector=t?t:"",r.$element_path=Xt.heatmap.getElementPath(e,Xt.para.heatmap&&"not_use_id"===Xt.para.heatmap.element_selector);var i=Xt.heatmap.getElementPosition(e,r.$element_path,Xt.para.heatmap&&"not_use_id"===Xt.para.heatmap.element_selector);return w(i)&&(r.$element_position=i),r},getPointerEventProp:function(e,t){function r(){var e=document.body.scrollLeft||document.documentElement.scrollLeft||0,t=document.body.scrollTop||document.documentElement.scrollTop||0;return{scrollLeft:e,scrollTop:t}}function i(e){if(document.documentElement.getBoundingClientRect){var t=e.getBoundingClientRect();return{targetEleX:t.left+r().scrollLeft||0,targetEleY:t.top+r().scrollTop||0}}}function n(e){return Number(Number(e).toFixed(3))}function a(e){var a=e.pageX||e.clientX+r().scrollLeft||e.offsetX+i(t).targetEleX||0,s=e.pageY||e.clientY+r().scrollTop||e.offsetY+i(t).targetEleY||0;return{$page_x:n(a),$page_y:n(s)}}return e?a(e):{}},start:function(e,t,i,n,s){var o=_(n)?n:{},l=a(s)?s:a(n)?n:void 0;if(Xt.para.heatmap&&Xt.para.heatmap.collect_element&&!Xt.para.heatmap.collect_element(t))return!1;var c=this.getEleDetail(t);if(Xt.para.heatmap&&Xt.para.heatmap.custom_property){var u=Xt.para.heatmap.custom_property(t);_(u)&&(c=r(c,u))}c=r(c,this.getPointerEventProp(e,t),o),"a"===i&&Xt.para.heatmap&&Xt.para.heatmap.isTrackLink===!0?Xt.trackLink({event:e,target:t},"$WebClick",c):Xt.track("$WebClick",c,l)},hasElement:function(e,t){var r;if(e.event){var i=e.event;r=i.path||i._getPath&&i._getPath()}else e.element&&(r=M(e.element).getParents());if(r&&nr(r)&&r.length>0)for(var n=0;n<r.length;n++)if("object"==typeof r[n]&&1===r[n].nodeType&&t(r[n]))return r[n]},isStyleTag:function(e,t){var r=["a","div","input","button","textarea"],i=["mark","/mark","strong","b","em","i","u","abbr","ins","del","s","sup"];return!(u(r,e)>-1)&&(!t||Xt.para.heatmap&&Xt.para.heatmap.collect_tags&&Xt.para.heatmap.collect_tags.div?!!(_(Xt.para.heatmap)&&_(Xt.para.heatmap.collect_tags)&&_(Xt.para.heatmap.collect_tags.div)&&nr(Xt.para.heatmap.collect_tags.div.ignore_tags)&&u(Xt.para.heatmap.collect_tags.div.ignore_tags,e)>-1):u(i,e)>-1)},isCollectableDiv:function(e,t){try{if(0===e.children.length)return!0;for(var r=0;r<e.children.length;r++)if(1===e.children[r].nodeType){var i=e.children[r].tagName.toLowerCase(),n=Xt.para&&Xt.para.heatmap&&Xt.para.heatmap.collect_tags&&Xt.para.heatmap.collect_tags.div&&Xt.para.heatmap.collect_tags.div.max_level;if(!("div"===i&&n>1||this.isStyleTag(i,t)))return!1;if(!this.isCollectableDiv(e.children[r],t))return!1}return!0}catch(a){Xt.log(a)}return!1},getCollectableParent:function(e,t){try{var r=e.parentNode,i=r?r.tagName.toLowerCase():"";if("body"===i)return!1;var n=Xt.para&&Xt.para.heatmap&&Xt.para.heatmap.collect_tags&&Xt.para.heatmap.collect_tags.div&&Xt.para.heatmap.collect_tags.div.max_level;if(i&&"div"===i&&(n>1||this.isCollectableDiv(r,t)))return r;if(r&&this.isStyleTag(i,t))return this.getCollectableParent(r,t)}catch(a){Xt.log(a)}return!1},initScrollmap:function(){if(!_(Xt.para.heatmap)||"default"!==Xt.para.heatmap.scroll_notice_map)return!1;var e=function(){return!(Xt.para.scrollmap&&a(Xt.para.scrollmap.collect_url)&&!Xt.para.scrollmap.collect_url())},t=function(e){var t={};return t.timeout=e.timeout||1e3,t.func=e.func,t.hasInit=!1,t.inter=null,t.main=function(e,t){this.func(e,t),this.inter=null},t.go=function(e){var r={};this.inter||(r.$viewport_position=document.documentElement&&document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop||0,r.$viewport_position=Math.round(r.$viewport_position)||0,e?t.main(r,!0):this.inter=setTimeout(function(){t.main(r)},this.timeout))},t},r=t({timeout:1e3,func:function(e,t){var r=document.documentElement&&document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop||0,i=new Date,n=i-this.current_time;(n>Xt.para.heatmap.scroll_delay_time&&r-e.$viewport_position!==0||t)&&(e.$url=G(),e.$title=document.title,e.$url_path=location.pathname,e.event_duration=Math.min(Xt.para.heatmap.scroll_event_duration,parseInt(n)/1e3),e.event_duration=e.event_duration<0?0:e.event_duration,Xt.track("$WebStay",e)),this.current_time=i}});r.current_time=new Date,Ce(window,"scroll",function(){return!!e()&&void r.go()}),Ce(window,"unload",function(){return!!e()&&void r.go("notime")})},initHeatmap:function(){var e=this;return!(!_(Xt.para.heatmap)||"default"!==Xt.para.heatmap.clickmap)&&(!(a(Xt.para.heatmap.collect_url)&&!Xt.para.heatmap.collect_url())&&("all"===Xt.para.heatmap.collect_elements?Xt.para.heatmap.collect_elements="all":Xt.para.heatmap.collect_elements="interact",void("all"===Xt.para.heatmap.collect_elements?Ce(document,"click",function(t){var r=t||window.event;if(!r)return!1;var i=r.target||r.srcElement;if("object"!=typeof i)return!1;if("string"!=typeof i.tagName)return!1;var n=i.tagName.toLowerCase();if("body"===n||"html"===n)return!1;if(!i||!i.parentNode||!i.parentNode.children)return!1;var a=i.parentNode.tagName.toLowerCase();"a"===a||"button"===a?e.start(r,i.parentNode,a):e.start(r,i,n)}):Ce(document,"click",function(t){var r=t||window.event;if(!r)return!1;var i=r.target||r.srcElement,n=Xt.heatmap.getTargetElement(i,t);n?e.start(r,n,n.tagName.toLowerCase()):S(i)&&"div"===i.tagName.toLowerCase()&&_(Xt.para.heatmap)&&Xt.para.heatmap.get_vtrack_config&&Xt.unlimitedDiv.events.length>0&&Xt.unlimitedDiv.isTargetEle(i)&&e.start(r,i,i.tagName.toLowerCase(),{$lib_method:"vtrack"})}))))}},xr={setOnlineState:function(t){if(t===!0&&_(Xt.para.jsapp)&&"function"==typeof Xt.para.jsapp.getData){Xt.para.jsapp.isOnline=!0;var r=Xt.para.jsapp.getData();nr(r)&&r.length>0&&e(r,function(e){b(e)&&Xt.sendState.realtimeSend(JSON.parse(e))})}else Xt.para.jsapp.isOnline=!1},autoTrackIsUsed:!1,isReady:function(e){e()},getUtm:function(){return hr.campaignParams()},getStayTime:function(){return(new Date-Xt._t)/1e3},setProfileLocal:function(e){if(!vr.isSupport())return Xt.setProfile(e),!1;if(!_(e)||f(e))return!1;var t=Xt.store.readObjectVal("sensorsdata_2015_jssdk_profile"),r=!1;if(_(t)&&!f(t)){for(var i in e)!(i in t&&t[i]!==e[i])&&i in t||(t[i]=e[i],r=!0);r&&(Xt.store.saveObjectVal("sensorsdata_2015_jssdk_profile",t),Xt.setProfile(e))}else Xt.store.saveObjectVal("sensorsdata_2015_jssdk_profile",e),Xt.setProfile(e)},setInitReferrer:function(){var e=_e();Xt.setOnceProfile({_init_referrer:e,_init_referrer_host:hr.pageProp.referrer_host})},setSessionReferrer:function(){var e=_e();Xt.store.setSessionPropsOnce({_session_referrer:e,_session_referrer_host:hr.pageProp.referrer_host})},setDefaultAttr:function(){hr.register({_current_url:location.href,_referrer:_e(),_referring_host:hr.pageProp.referrer_host})},trackHeatMap:function(e,t,r){if("object"==typeof e&&e.tagName){var i=e.tagName.toLowerCase(),n=e.parentNode.tagName.toLowerCase(),a=Xt.para.heatmap&&Xt.para.heatmap.track_attr?Xt.para.heatmap.track_attr:["data-sensors-click"];"button"===i||"a"===i||"a"===n||"button"===n||"input"===i||"textarea"===i||J(e,a)||Tr.start(null,e,i,t,r)}},trackAllHeatMap:function(e,t,r){if("object"==typeof e&&e.tagName){var i=e.tagName.toLowerCase();Tr.start(null,e,i,t,r)}},autoTrackSinglePage:function(t,i){function n(){var t=hr.campaignParams(),r={};return e(t,function(e,t,i){(" "+Xt.source_channel_standard+" ").indexOf(" "+t+" ")!==-1?r["$"+t]=i[t]:r[t]=i[t]}),r}function a(e,t){Xt.track("$pageview",r({$referrer:s,$url:G(),$url_path:location.pathname,$title:document.title},e,n()),t),s=G()}var s;s=this.autoTrackIsUsed?hr.pageProp.url:hr.pageProp.referrer,t=_(t)?t:{};var o=!t.not_set_profile;if(t.not_set_profile&&delete t.not_set_profile,a(t,i),this.autoTrackSinglePage=a,Xt.is_first_visitor&&o){var l={};Xt.para.preset_properties.search_keyword_baidu&&pe(document.referrer)&&le()&&(l.$search_keyword_id=gr.id(),l.$search_keyword_id_type=gr.type(),l.$search_keyword_id_hash=C(l.$search_keyword_id)),Xt.setOnceProfile(r({$first_visit_time:new Date,$first_referrer:_e(),$first_browser_language:navigator.language||"\u53d6\u503c\u5f02\u5e38",$first_browser_charset:"string"==typeof document.charset?document.charset.toUpperCase():"\u53d6\u503c\u5f02\u5e38",$first_traffic_source_type:me(),$first_search_keyword:ge()},n(),l)),Xt.is_first_visitor=!1}},autoTrackWithoutProfile:function(e,t){e=_(e)?e:{},this.autoTrack(r(e,{not_set_profile:!0}),t)},autoTrack:function(t,i){t=_(t)?t:{};var n=hr.campaignParams(),a={};e(n,function(e,t,r){(" "+Xt.source_channel_standard+" ").indexOf(" "+t+" ")!==-1?a["$"+t]=r[t]:a[t]=r[t]});var s=!t.not_set_profile;t.not_set_profile&&delete t.not_set_profile;var o=location.href;if(Xt.para.is_single_page&&Ne(function(){var e=_e(o,!0);Xt.track("$pageview",r({$referrer:e,$url:G(),$url_path:location.pathname,$title:document.title},a,t),i),o=G()}),Xt.track("$pageview",r({$referrer:_e(null,!0),$url:G(),$url_path:location.pathname,$title:document.title},a,t),i),Xt.is_first_visitor&&s){var l={};Xt.para.preset_properties.search_keyword_baidu&&pe(document.referrer)&&le()&&(l.$search_keyword_id=gr.id(),l.$search_keyword_id_type=gr.type(),l.$search_keyword_id_hash=C(l.$search_keyword_id)),Xt.setOnceProfile(r({$first_visit_time:new Date,$first_referrer:_e(null,!0),$first_browser_language:navigator.language||"\u53d6\u503c\u5f02\u5e38",$first_browser_charset:"string"==typeof document.charset?document.charset.toUpperCase():"\u53d6\u503c\u5f02\u5e38",$first_traffic_source_type:me(),$first_search_keyword:ge()},a,l)),Xt.is_first_visitor=!1}this.autoTrackIsUsed=!0},getAnonymousID:function(){return f(Xt.store._state)?"\u8bf7\u5148\u521d\u59cb\u5316SDK":Xt.store._state._first_id||Xt.store._state.first_id||Xt.store._state._distinct_id||Xt.store._state.distinct_id},setPlugin:function(t){return!!_(t)&&void e(t,function(e,t){a(e)&&(_(window.SensorsDataWebJSSDKPlugin)&&window.SensorsDataWebJSSDKPlugin[t]?e(window.SensorsDataWebJSSDKPlugin[t]):Xt.log(t+"\u6ca1\u6709\u83b7\u53d6\u5230,\u8bf7\u67e5\u9605\u6587\u6863\uff0c\u8c03\u6574"+t+"\u7684\u5f15\u5165\u987a\u5e8f\uff01"))})},useModulePlugin:function(){Xt.use.apply(Xt,arguments)},useAppPlugin:function(){this.setPlugin.apply(this,arguments)}},Lr={state:0,historyState:[],stateType:{1:"1-init\u672a\u5f00\u59cb",2:"2-init\u5f00\u59cb",3:"3-store\u5b8c\u6210"},getState:function(){return this.historyState.join("\n")},setState:function(e){String(e)in this.stateType&&(this.state=e),this.historyState.push(this.stateType[e])}},Er={EMAIL:"$identity_email",MOBILE:"$identity_mobile"},Hr={__proto__:null,addReferrerHost:Be,addPropsHook:Ue,initPara:Re,setInitVar:Me,enableLocalLog:Ke,disableLocalLog:We,quick:Ve,use:qe,track:Fe,bind:ze,unbind:Xe,trackLink:Ze,trackLinks:Qe,setItem:Ye,deleteItem:Ge,setProfile:et,setOnceProfile:tt,appendProfile:rt,incrementProfile:it,deleteProfile:nt,unsetProfile:at,identify:st,trackSignup:lt,registerPage:ct,clearAllRegister:ut,clearPageRegister:dt,register:pt,registerOnce:_t,registerSession:ft,registerSessionOnce:gt,login:ht,logout:mt,getPresetProperties:vt,iOSWebClickPolyfill:yt,readyState:Lr,para_default:lr,log:H,debug:dr,IDENTITY_KEY:Er},Jr={};Jr.buildData=function(e){var t={identities:{},distinct_id:Xt.store.getDistinctId(),lib:{$lib:"js",$lib_method:"code",$lib_version:String(Xt.lib_version)},properties:{}};return _(e)&&_(e.identities)&&!f(e.identities)?r(t.identities,e.identities):r(t.identities,Nr._state.identities),_(e)&&_(e.properties)&&!f(e.properties)&&(e.properties.$lib_detail&&(t.lib.$lib_detail=e.properties.$lib_detail,delete e.properties.$lib_detail),e.properties.$lib_method&&(t.lib.$lib_method=e.properties.$lib_method,delete e.properties.$lib_method)),r(t,Xt.store.getUnionId(),e),_(e.properties)&&!f(e.properties)&&r(t.properties,e.properties),e.type&&"profile"===e.type.slice(0,7)||(t.properties=r({},hr.properties(),Nr.getProps(),Nr.getSessionProps(),hr.currentProps,t.properties),Xt.para.preset_properties.latest_referrer&&!h(t.properties.$latest_referrer)&&(t.properties.$latest_referrer="\u53d6\u503c\u5f02\u5e38"),Xt.para.preset_properties.latest_search_keyword&&!h(t.properties.$latest_search_keyword)&&(Xt.para.preset_properties.search_keyword_baidu&&h(t.properties.$search_keyword_id)&&w(t.properties.$search_keyword_id_hash)&&h(t.properties.$search_keyword_id_type)||(t.properties.$latest_search_keyword="\u53d6\u503c\u5f02\u5e38")),Xt.para.preset_properties.latest_traffic_source_type&&!h(t.properties.$latest_traffic_source_type)&&(t.properties.$latest_traffic_source_type="\u53d6\u503c\u5f02\u5e38"),
Xt.para.preset_properties.latest_landing_page&&!h(t.properties.$latest_landing_page)&&(t.properties.$latest_landing_page="\u53d6\u503c\u5f02\u5e38"),"not_collect"===Xt.para.preset_properties.latest_wx_ad_click_id?(delete t.properties._latest_wx_ad_click_id,delete t.properties._latest_wx_ad_hash_key,delete t.properties._latest_wx_ad_callbacks):Xt.para.preset_properties.latest_wx_ad_click_id&&!h(t.properties._latest_wx_ad_click_id)&&(t.properties._latest_wx_ad_click_id="\u53d6\u503c\u5f02\u5e38",t.properties._latest_wx_ad_hash_key="\u53d6\u503c\u5f02\u5e38",t.properties._latest_wx_ad_callbacks="\u53d6\u503c\u5f02\u5e38"),h(t.properties._latest_wx_ad_click_id)&&(t.properties.$url=G())),t.properties.$time&&v(t.properties.$time)?(t.time=1*t.properties.$time,delete t.properties.$time):t.time=1*new Date,Xt.vtrackBase.addCustomProps(t),ie(t),Cr.checkIsAddSign(t),Cr.checkIsFirstTime(t),Xt.addReferrerHost(t),Xt.addPropsHook(t),Or.stage.process("formatData",t),t},Jr.sendData=function(e,t){var r=ne(e.properties);Xt.para.debug_mode===!0?(Xt.log(e),Xt.saEvent.debugPath(JSON.stringify(e),t)):Xt.sendState.getSendCall(e,r,t)},Jr.encodeTrackData=function(e){var t=A(e),r="crc="+D(t);return"data="+encodeURIComponent(t)+"&ext="+encodeURIComponent(r)};var Br=function(e){this.callback=e.callback,this.img=document.createElement("img"),this.img.width=1,this.img.height=1,Xt.para.img_use_crossorigin&&(this.img.crossOrigin="anonymous"),this.data=e.data,this.server_url=wt(e.server_url,e.data)};Br.prototype.start=function(){var e=this;Xt.para.ignore_oom&&(this.img.onload=function(){this.onload=null,this.onerror=null,this.onabort=null,e.isEnd()},this.img.onerror=function(){this.onload=null,this.onerror=null,this.onabort=null,e.isEnd()},this.img.onabort=function(){this.onload=null,this.onerror=null,this.onabort=null,e.isEnd()}),this.img.src=this.server_url},Br.prototype.lastClear=function(){var e=Pe();void 0!==e.ie?this.img.src="about:blank":this.img.src=""};var Ur=function(e){this.callback=e.callback,this.server_url=e.server_url,this.data=St(e.data)};Ur.prototype.start=function(){var e=this;Oe({url:this.server_url,type:"POST",data:this.data,credentials:!1,timeout:Xt.para.datasend_timeout,cors:!0,success:function(){e.isEnd()},error:function(){e.isEnd()}})};var Rr=function(e){this.callback=e.callback,this.server_url=e.server_url,this.data=St(e.data)};Rr.prototype.start=function(){var e=this;"object"==typeof navigator&&"function"==typeof navigator.sendBeacon&&navigator.sendBeacon(this.server_url,this.data),setTimeout(function(){e.isEnd()},40)};var Mr={};Mr.queue=ve(),Mr.getSendCall=function(e,t,r){if(Xt.is_heatmap_render_mode)return!1;if(Xt.readyState.state<3)return Xt.log("\u521d\u59cb\u5316\u6ca1\u6709\u5b8c\u6210"),!1;e._track_id=Number(String(N()).slice(2,5)+String(N()).slice(2,4)+String((new Date).getTime()).slice(-4)),e._flush_time=(new Date).getTime();var i=e;e=JSON.stringify(e);var n={data:i,config:t,callback:r};return Xt.events.tempAdd("send",i),!Xt.para.app_js_bridge&&Xt.para.batch_send&&localStorage.length<200?(Xt.log(i),Xt.batchSend.add(n.data),!1):("item_set"===i.type||"item_delete"===i.type?this.prepareServerUrl(n):Xt.bridge.dataSend(n,this,r),void Xt.log(i))},Mr.prepareServerUrl=function(e){if("object"==typeof e.config&&e.config.server_url)this.sendCall(e,e.config.server_url,e.callback);else if(nr(Xt.para.server_url)&&Xt.para.server_url.length)for(var t=0;t<Xt.para.server_url.length;t++)this.sendCall(e,Xt.para.server_url[t]);else"string"==typeof Xt.para.server_url&&""!==Xt.para.server_url?this.sendCall(e,Xt.para.server_url,e.callback):Xt.log("\u5f53\u524d server_url \u4e3a\u7a7a\u6216\u4e0d\u6b63\u786e\uff0c\u53ea\u5728\u63a7\u5236\u53f0\u6253\u5370\u65e5\u5fd7\uff0cnetwork \u4e2d\u4e0d\u4f1a\u53d1\u6570\u636e\uff0c\u8bf7\u914d\u7f6e\u6b63\u786e\u7684 server_url\uff01")},Mr.sendCall=function(e,t,r){var i={server_url:t,data:JSON.stringify(e.data),callback:r,config:e.config};_(Xt.para.jsapp)&&!Xt.para.jsapp.isOnline&&"function"==typeof Xt.para.jsapp.setData?(delete i.callback,i=JSON.stringify(i),Xt.para.jsapp.setData(i)):this.realtimeSend(i)},Mr.realtimeSend=function(e){var t=Pt(e);t.start()},Dt.prototype={add:function(e){_(e)&&(this.writeStore(e),"track_signup"!==e.type&&"$pageview"!==e.event||this.sendStrategy())},clearPendingStatus:function(){this.sendingItemKeys.length&&this.removePendingItems(this.sendingItemKeys)},remove:function(t){this.sendingData>0&&--this.sendingData,nr(t)&&t.length>0&&e(t,function(e){vr.remove(e)})},send:function(e){var t,r=this;return h(Xt.para.server_url)&&""!==Xt.para.server_url||nr(Xt.para.server_url)&&Xt.para.server_url.length?(t=nr(Xt.para.server_url)?Xt.para.server_url[0]:Xt.para.server_url,void Oe({url:t,type:"POST",data:"data_list="+encodeURIComponent(A(JSON.stringify(e.vals))),credentials:!1,timeout:Xt.para.batch_send.datasend_timeout,cors:!0,success:function(){r.remove(e.keys),r.removePendingItems(e.keys)},error:function(){r.sendingData>0&&--r.sendingData,r.removePendingItems(e.keys)}})):void Xt.log("\u5f53\u524d server_url \u4e3a\u7a7a\u6216\u4e0d\u6b63\u786e\uff0c\u53ea\u5728\u63a7\u5236\u53f0\u6253\u5370\u65e5\u5fd7\uff0cnetwork \u4e2d\u4e0d\u4f1a\u53d1\u6570\u636e\uff0c\u8bf7\u914d\u7f6e\u6b63\u786e\u7684 server_url\uff01")},appendPendingItems:function(e){if(nr(e)!==!1){this.sendingItemKeys=j(this.sendingItemKeys.concat(e));try{var t=this.getPendingItems(),r=j(t.concat(e));Xt.store.saveObjectVal("sawebjssdk-sendingitems",r)}catch(i){}}},removePendingItems:function(e){if(nr(e)!==!1){this.sendingItemKeys.length&&(this.sendingItemKeys=d(this.sendingItemKeys,function(t){return u(e,t)===-1}));try{var t=this.getPendingItems(),r=d(t,function(t){return u(e,t)===-1});Xt.store.saveObjectVal("sawebjssdk-sendingitems",r)}catch(i){}}},getPendingItems:function(){return Xt.store.readObjectVal("sawebjssdk-sendingitems")||[]},sendPrepare:function(e){this.appendPendingItems(e.keys);var t=e.vals,r=t.length;r>0&&this.send({keys:e.keys,vals:t})},sendStrategy:function(){if(document.hasFocus()===!1)return!1;var e=this.readStore();e.keys.length>0&&0===this.sendingData&&(this.sendingData=1,this.sendPrepare(e))},batchInterval:function(){var e=this;setInterval(function(){e.sendStrategy()},Xt.para.batch_send.send_interval)},readStore:function(){for(var e=[],t=[],r=null,i=(new Date).getTime(),n=localStorage.length,a=this.getPendingItems(),s=0;s<n;s++){var o=localStorage.key(s);if(0===o.indexOf("sawebjssdk-")&&/^sawebjssdk\-\d+$/.test(o)){if(a.length&&u(a,o)>-1)continue;r=Xt.store.readObjectVal(o),r?r&&_(r)?(r._flush_time=i,e.push(o),t.push(r)):(localStorage.removeItem(o),Xt.log("localStorage-\u6570\u636eparse\u5f02\u5e38"+r)):(localStorage.removeItem(o),Xt.log("localStorage-\u6570\u636e\u53d6\u503c\u5f02\u5e38"+r))}}return{keys:e,vals:t}},writeStore:function(e){var t=String(N()).slice(2,5)+String(N()).slice(2,5)+String((new Date).getTime()).slice(3);Xt.store.saveObjectVal("sawebjssdk-"+t,e)}};var Kr=new Dt,Wr={bridge_info:{touch_app_bridge:!1,verify_success:!1,platform:""},is_verify_success:!1,initPara:function(){var e={is_send:!0,white_list:[],is_mui:!1};"object"==typeof Xt.para.app_js_bridge?Xt.para.app_js_bridge=r({},e,Xt.para.app_js_bridge):Xt.para.use_app_track===!0||Xt.para.app_js_bridge===!0||"only"===Xt.para.use_app_track?(Xt.para.use_app_track_is_send!==!1&&"only"!==Xt.para.use_app_track||(e.is_send=!1),Xt.para.app_js_bridge=r({},e)):"mui"===Xt.para.use_app_track&&(e.is_mui=!0,Xt.para.app_js_bridge=r({},e)),Xt.para.app_js_bridge.is_send===!1&&Xt.log("\u8bbe\u7f6e\u4e86 is_send:false,\u5982\u679c\u6253\u901a\u5931\u8d25\uff0c\u6570\u636e\u5c06\u88ab\u4e22\u5f03\uff01")},initState:function(){function e(e){function t(e){var t={hostname:"",project:""};try{t.hostname=Z(e).hostname,t.project=Z(e).searchParams.get("project")||"default"}catch(r){Xt.log(r)}return t}var r=t(e),i=t(Xt.para.server_url);if(r.hostname===i.hostname&&r.project===i.project)return!0;if(Xt.para.app_js_bridge.white_list.length>0)for(var n=0;n<Xt.para.app_js_bridge.white_list.length;n++){var a=t(Xt.para.app_js_bridge.white_list[n]);if(a.hostname===r.hostname&&a.project===r.project)return!0}return!1}if(_(Xt.para.app_js_bridge)&&!Xt.para.app_js_bridge.is_mui)if(window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&_(window.SensorsData_iOS_JS_Bridge)&&window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url)e(window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url)&&(Xt.bridge.is_verify_success=!0);else if(_(window.SensorsData_APP_New_H5_Bridge)&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_track){var t=window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url();t&&e(t)&&(Xt.bridge.is_verify_success=!0)}this.bridge_info=this.initDefineBridgeInfo()},initDefineBridgeInfo:function(){var e={touch_app_bridge:!0,verify_success:!1,platform:""};return window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage&&_(window.SensorsData_iOS_JS_Bridge)&&window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url?(e.platform="ios",Xt.bridge.is_verify_success?e.verify_success="success":e.verify_success="fail"):_(window.SensorsData_APP_New_H5_Bridge)&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_track?(e.platform="android",Xt.bridge.is_verify_success?e.verify_success="success":e.verify_success="fail"):"object"==typeof SensorsData_APP_JS_Bridge&&(SensorsData_APP_JS_Bridge.sensorsdata_verify&&SensorsData_APP_JS_Bridge.sensorsdata_visual_verify||SensorsData_APP_JS_Bridge.sensorsdata_track)?(e.platform="android",SensorsData_APP_JS_Bridge.sensorsdata_verify&&SensorsData_APP_JS_Bridge.sensorsdata_visual_verify?SensorsData_APP_JS_Bridge.sensorsdata_visual_verify(JSON.stringify({server_url:Xt.para.server_url}))?e.verify_success="success":e.verify_success="fail":e.verify_success="success"):!/sensors-verify/.test(navigator.userAgent)&&!/sa-sdk-ios/.test(navigator.userAgent)||window.MSStream?e.touch_app_bridge=!1:(e.platform="ios",Xt.bridge.iOS_UA_bridge()?e.verify_success="success":e.verify_success="fail"),e},iOS_UA_bridge:function(){if(/sensors-verify/.test(navigator.userAgent)){var e=navigator.userAgent.match(/sensors-verify\/([^\s]+)/);if(e&&e[0]&&"string"==typeof e[1]&&2===e[1].split("?").length){e=e[1].split("?");var t=null,r=null;try{t=Z(Xt.para.server_url).hostname,r=Z(Xt.para.server_url).searchParams.get("project")||"default"}catch(i){Xt.log(i)}return!(!t||t!==e[0]||!r||r!==e[1])}return!1}return!!/sa-sdk-ios/.test(navigator.userAgent)},dataSend:function(e,t,i){function n(e){var t=JSON.stringify(r({server_url:Xt.para.server_url},e));return t=t.replaceAll(/\r\n/g,""),t=encodeURIComponent(t),"sensorsanalytics://trackEvent?event="+t}var a=e.data;if(_(Xt.para.app_js_bridge)&&!Xt.para.app_js_bridge.is_mui)if(window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage&&_(window.SensorsData_iOS_JS_Bridge)&&window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url)Xt.bridge.is_verify_success?(window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(JSON.stringify({callType:"app_h5_track",data:r({server_url:Xt.para.server_url},a)})),"function"==typeof i&&i()):Xt.para.app_js_bridge.is_send?(Xt.debug.apph5({data:a,step:"4.1",output:"all"}),t.prepareServerUrl(e)):"function"==typeof i&&i();else if(_(window.SensorsData_APP_New_H5_Bridge)&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_track)Xt.bridge.is_verify_success?(SensorsData_APP_New_H5_Bridge.sensorsdata_track(JSON.stringify(r({server_url:Xt.para.server_url},a))),"function"==typeof i&&i()):Xt.para.app_js_bridge.is_send?(Xt.debug.apph5({data:a,step:"4.2",output:"all"}),t.prepareServerUrl(e)):"function"==typeof i&&i();else if("object"==typeof SensorsData_APP_JS_Bridge&&(SensorsData_APP_JS_Bridge.sensorsdata_verify||SensorsData_APP_JS_Bridge.sensorsdata_track))SensorsData_APP_JS_Bridge.sensorsdata_verify?SensorsData_APP_JS_Bridge.sensorsdata_verify(JSON.stringify(r({server_url:Xt.para.server_url},a)))?"function"==typeof i&&i():Xt.para.app_js_bridge.is_send?(Xt.debug.apph5({data:a,step:"3.1",output:"all"}),t.prepareServerUrl(e)):"function"==typeof i&&i():(SensorsData_APP_JS_Bridge.sensorsdata_track(JSON.stringify(r({server_url:Xt.para.server_url},a))),"function"==typeof i&&i());else if(!/sensors-verify/.test(navigator.userAgent)&&!/sa-sdk-ios/.test(navigator.userAgent)||window.MSStream)_(Xt.para.app_js_bridge)&&Xt.para.app_js_bridge.is_send===!0?(Xt.debug.apph5({data:a,step:"2",output:"all"}),t.prepareServerUrl(e)):"function"==typeof i&&i();else{var s=null;if(Xt.bridge.iOS_UA_bridge()){s=document.createElement("iframe");var o=n(a);s.setAttribute("src",o),document.documentElement.appendChild(s),s.parentNode.removeChild(s),s=null,"function"==typeof i&&i()}else Xt.para.app_js_bridge.is_send?(Xt.debug.apph5({data:a,step:"3.2",output:"all"}),t.prepareServerUrl(e)):"function"==typeof i&&i()}else _(Xt.para.app_js_bridge)&&Xt.para.app_js_bridge.is_mui?_(window.plus)&&window.plus.SDAnalytics&&window.plus.SDAnalytics.trackH5Event?(window.plus.SDAnalytics.trackH5Event(e),"function"==typeof i&&i()):_(Xt.para.app_js_bridge)&&Xt.para.app_js_bridge.is_send===!0?t.prepareServerUrl(e):"function"==typeof i&&i():(Xt.debug.apph5({data:a,step:"1",output:"code"}),t.prepareServerUrl(e))},app_js_bridge_v1:function(){function e(e){i=e,b(i)&&(i=JSON.parse(i)),n&&(n(i),n=null,i=null)}function t(){"object"==typeof window.SensorsData_APP_JS_Bridge&&window.SensorsData_APP_JS_Bridge.sensorsdata_call_app&&(i=SensorsData_APP_JS_Bridge.sensorsdata_call_app(),b(i)&&(i=JSON.parse(i)))}function r(){if(/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream){var e=document.createElement("iframe");e.setAttribute("src","sensorsanalytics://getAppInfo"),document.documentElement.appendChild(e),e.parentNode.removeChild(e),e=null}}var i=null,n=null;window.sensorsdata_app_js_bridge_call_js=function(t){e(t)},Xt.getAppStatus=function(e){return r(),t(),e?void(null===i?n=e:(e(i),i=null)):i}},supportAppCallJs:function(){window.sensorsdata_app_call_js=function(e,t){if(e in window.sensorsdata_app_call_js.modules)return window.sensorsdata_app_call_js.modules[e](t)},window.sensorsdata_app_call_js.modules={}}},Vr=function(e){this.list={},this.type=e.type,this.app_call_js=a(e.app_call_js)?e.app_call_js:function(){},this.init()};Vr.prototype.init=function(){var e=this;window.sensorsdata_app_call_js.modules[this.type]||(window.sensorsdata_app_call_js.modules[this.type]=function(t){return e.app_call_js(t)})},Vr.prototype.jsCallApp=function(e){var t={callType:this.type,data:e};if(window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage)window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(JSON.stringify(t));else{if(!_(window.SensorsData_APP_New_H5_Bridge)||!window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app)return Xt.log("\u6570\u636e\u53d1\u5f80App\u5931\u8d25\uff0cApp\u6ca1\u6709\u66b4\u9732bridge"),!1;window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app(JSON.stringify(t))}},Vr.prototype.getAppData=function(){return _(window.SensorsData_APP_New_H5_Bridge)?a(window.SensorsData_APP_New_H5_Bridge[this.type])?window.SensorsData_APP_New_H5_Bridge[this.type]():window.SensorsData_APP_New_H5_Bridge[this.type]:_(window.SensorsData_APP_JS_Bridge)&&a(window.SensorsData_APP_JS_Bridge[this.type])?window.SensorsData_APP_JS_Bridge[this.type]():void 0},Vr.prototype.hasAppBridge=function(){return window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage?"ios":_(window.SensorsData_APP_New_H5_Bridge)&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app?"android":(Xt.log("App\u7aefbridge\u672a\u66b4\u9732"),!1)},Vr.prototype.requestToApp=function(e){function t(){var e=(new Date).getTime().toString(16),t=String(N()).replace(".","").slice(1,8);return e+"-"+t}var r=this,i=_(e.data)?e.data:{};a(e.callback)||(e.callback=function(){}),_(e.timeout)&&w(e.timeout.time)&&(a(e.timeout.callback)||(e.timeout.callback=function(){}),e.timer=setTimeout(function(){e.timeout.callback(),delete r.list[n]},e.timeout.time));var n=t();this.list[n]=e;var s={callType:this.type,data:i};if(s.data.message_id=n,window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage)window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(JSON.stringify(s));else{if(!_(window.SensorsData_APP_New_H5_Bridge)||!window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app)return Xt.log("\u6570\u636e\u53d1\u5f80App\u5931\u8d25\uff0cApp\u6ca1\u6709\u66b4\u9732bridge"),!1;window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app(JSON.stringify(s))}},Vr.prototype["double"]=function(e){if(e.message_id){var t=this.list[e.message_id];t&&(t.timer&&clearTimeout(t.timer),t.callback(e),delete this.list[e.message_id])}};var qr={};qr.initUrl=function(){var e,t={server_url:{project:"",host:""},page_url:{host:"",pathname:""}};if(!h(Xt.para.server_url))return Xt.log("----vcollect---server_url\u5fc5\u987b\u4e3a\u5b57\u7b26\u4e32"),!1;try{e=Z(Xt.para.server_url),t.server_url.project=e.searchParams.get("project")||"default",t.server_url.host=e.host}catch(r){return Xt.log("----vcollect---server_url\u89e3\u6790\u5f02\u5e38",r),!1}var i;try{i=Z(location.href),t.page_url.host=i.hostname,t.page_url.pathname=i.pathname}catch(r){return Xt.log("----vcollect---\u9875\u9762\u5730\u5740\u89e3\u6790\u5f02\u5e38",r),!1}return t},qr.isDiv=function(e){if(e.element_path){var t=e.element_path.split(">"),r=p(t.pop());if("div"!==r.slice(0,3))return!1}return!0},qr.configIsMatchNew=function(e,t){if(h(e.$element_selector)&&h(t.element_selector)){if("element_selector"===t.element_field&&"equal"===t["function"])return e.$element_selector===t.element_selector;if("element_selector"===t.element_field&&"contain"===t["function"])return e.$element_selector.indexOf(t.element_selector)>-1}if(h(e.$element_path)&&h(t.element_path)){if("element_path"===t.element_field&&"equal"===t["function"])return e.$element_path===t.element_path;if("element_path"===t.element_field&&"contain"===t["function"])return e.$element_path.indexOf(t.element_path)>-1}return!1},qr.configIsMatch=function(e,t){return(!t.limit_element_content||t.element_content===e.$element_content)&&((!t.limit_element_position||t.element_position===String(e.$element_position))&&(t.element_field&&t["function"]?qr.configIsMatchNew(e,t):qr.configIsMatchOldVersion(e,t)))},qr.configIsMatchOldVersion=function(e,t){if(!t.element_path)return!1;if(void 0!==e.$element_position){if(t.element_path!==e.$element_path)return!1}else if(Xt.vtrackBase.isDiv({element_path:t.element_path})){if(e.$element_path.indexOf(t.element_path)<0)return!1}else if(t.element_path!==e.$element_path)return!1;return!0},qr.filterConfig=function(t,r,i){var n=[];if(!i){var a=qr.initUrl();if(!a)return[];i=a.page_url}return"$WebClick"===t.event&&e(r,function(e){_(e)&&("webclick"===e.event_type||"appclick"===e.event_type)&&_(e.event)&&e.event.url_host===i.host&&e.event.url_path===i.pathname&&qr.configIsMatch(t.properties,e.event)&&n.push(e)}),n},qr.getPropElInLi=function(e,t){if(!(e&&S(e)&&h(t)))return null;if("li"!==e.tagName.toLowerCase())return null;var r,i=Xt.heatmap.getDomSelector(e);if(i){r=i+t;var n=W(r);return n?n:null}return Xt.log("----custom---\u83b7\u53d6\u540c\u7ea7\u5c5e\u6027\u5143\u7d20\u5931\u8d25\uff0cselector\u4fe1\u606f\u5f02\u5e38",i,t),null},qr.getProp=function(e,t){if(!_(e))return!1;if(!(h(e.name)&&e.name.length>0))return Xt.log("----vcustom----\u5c5e\u6027\u540d\u4e0d\u5408\u6cd5,\u5c5e\u6027\u629b\u5f03",e.name),!1;var r,i,n={};if("content"===e.method){var a;if(h(e.element_selector)&&e.element_selector.length>0)a=W(e.element_selector);else{if(!t||!h(e.list_selector))return Xt.log("----vcustom----\u5c5e\u6027\u914d\u7f6e\u5f02\u5e38\uff0c\u5c5e\u6027\u629b\u5f03",e.name),!1;var s=W(t.properties.$element_selector);if(!s)return Xt.log("----vcustom----\u70b9\u51fb\u5143\u7d20\u83b7\u53d6\u5f02\u5e38\uff0c\u5c5e\u6027\u629b\u5f03",e.name),!1;var o=Xt.heatmap.getClosestLi(s);a=qr.getPropElInLi(o,e.list_selector)}if(!a||!S(a))return Xt.log("----vcustom----\u5c5e\u6027\u5143\u7d20\u83b7\u53d6\u5931\u8d25\uff0c\u5c5e\u6027\u629b\u5f03",e.name),!1;if("input"===a.tagName.toLowerCase())r=a.value||"";else if("select"===a.tagName.toLowerCase()){var l=a.selectedIndex;w(l)&&S(a[l])&&(r=U(a[l],"select"))}else r=U(a,a.tagName.toLowerCase());if(e.regular){try{i=new RegExp(e.regular).exec(r)}catch(c){return Xt.log("----vcustom----\u6b63\u5219\u5904\u7406\u5931\u8d25\uff0c\u5c5e\u6027\u629b\u5f03",e.name),!1}if(null===i)return Xt.log("----vcustom----\u5c5e\u6027\u89c4\u5219\u5904\u7406\uff0c\u672a\u5339\u914d\u5230\u7ed3\u679c,\u5c5e\u6027\u629b\u5f03",e.name),!1;if(!nr(i)||!h(i[0]))return Xt.log("----vcustom----\u6b63\u5219\u5904\u7406\u5f02\u5e38\uff0c\u5c5e\u6027\u629b\u5f03",e.name,i),!1;r=i[0]}if("STRING"===e.type)n[e.name]=r;else if("NUMBER"===e.type){if(r.length<1)return Xt.log("----vcustom----\u672a\u83b7\u53d6\u5230\u6570\u5b57\u5185\u5bb9\uff0c\u5c5e\u6027\u629b\u5f03",e.name,r),!1;if(isNaN(Number(r)))return Xt.log("----vcustom----\u6570\u5b57\u7c7b\u578b\u5c5e\u6027\u8f6c\u6362\u5931\u8d25\uff0c\u5c5e\u6027\u629b\u5f03",e.name,r),!1;n[e.name]=Number(r)}return n}return Xt.log("----vcustom----\u5c5e\u6027\u4e0d\u652f\u6301\u6b64\u83b7\u53d6\u65b9\u5f0f",e.name,e.method),!1},qr.getAssignConfigs=function(t,r){var i=qr.initUrl();if(!i||!i.page_url)return[];if(!_(r))return[];var n=[];return r.events=r.events||r.eventList,nr(r.events)&&r.events.length>0?(e(r.events,function(e){_(e)&&_(e.event)&&e.event.url_host===i.page_url.host&&e.event.url_path===i.page_url.pathname&&t(e)&&n.push(e)}),n):[]},qr.addCustomProps=function(e){if("success"===Xt.bridge.bridge_info.verify_success){var t=Xt.vapph5collect.customProp.geth5Props(JSON.parse(JSON.stringify(e)));_(t)&&!f(t)&&(e.properties=r(e.properties,t))}var i=Xt.vtrackcollect.customProp.getVtrackProps(JSON.parse(JSON.stringify(e)));return _(i)&&!f(i)&&(e.properties=r(e.properties,i)),e},qr.init=function(){Xt.vtrackcollect.init(),"success"===Xt.bridge.bridge_info.verify_success&&Xt.vapph5collect.init()};var Fr={events:[],init:function(e){this.filterWebClickEvents(e)},filterWebClickEvents:function(e){this.events=Xt.vtrackcollect.getAssignConfigs(function(e){return!(!_(e)||e.event.unlimited_div!==!0||"webclick"!==e.event_type)},e)},isTargetEle:function(e){var t=Xt.heatmap.getEleDetail(e);if(!_(t)||!h(t.$element_path))return!1;for(var r=0;r<this.events.length;r++)if(_(this.events[r])&&_(this.events[r].event)&&Xt.vtrackcollect.configIsMatch(t,this.events[r].event))return!0;return!1}},zr={events:[],configSwitch:!1,collectAble:function(){return this.configSwitch&&_(Xt.para.heatmap)&&Xt.para.heatmap.get_vtrack_config},updateEvents:function(e){this.events=Xt.vtrackcollect.getAssignConfigs(function(e){return!!(_(e)&&nr(e.properties)&&e.properties.length>0)},e),this.events.length?this.configSwitch=!0:this.configSwitch=!1},getVtrackProps:function(e){var t={};return this.collectAble()?("$WebClick"===e.event&&(t=this.clickCustomPropMaker(e,this.events)),t):{}},clickCustomPropMaker:function(t,i,n){var a=this;n=n||this.filterConfig(t,i,Xt.vtrackcollect.url_info.page_url);var s={};return n.length?(e(n,function(i){nr(i.properties)&&i.properties.length>0&&e(i.properties,function(e){var i=a.getProp(e,t);_(i)&&r(s,i)})}),s):{}},getProp:qr.getProp,getPropElInLi:qr.getPropElInLi,filterConfig:qr.filterConfig},Xr={unlimitedDiv:Fr,config:{},storageEnable:!0,storage_name:"webjssdkvtrackcollect",para:{session_time:18e5,timeout:5e3,update_interval:18e5},url_info:{},timer:null,update_time:null,customProp:zr,initUrl:function(){var e=qr.initUrl();if(e){var t;try{t=new z(Xt.para.server_url),t._values.Path="/config/visualized/Web.conf",e.api_url=t.getUrl()}catch(r){return Xt.log("----vtrackcollect---API\u5730\u5740\u89e3\u6790\u5f02\u5e38",r),!1}this.url_info=e}return e},init:function(){if(!_(Xt.para.heatmap)||!Xt.para.heatmap.get_vtrack_config)return!1;if(vr.isSupport()||(this.storageEnable=!1),!this.initUrl())return Xt.log("----vtrackcustom----\u521d\u59cb\u5316\u5931\u8d25\uff0curl\u4fe1\u606f\u89e3\u6790\u5931\u8d25"),!1;if(this.storageEnable){var e=Xt.store.readObjectVal(this.storage_name);if(_(e)&&_(e.data))if(this.serverUrlIsSame(e.serverUrl)){this.config=e.data,this.update_time=e.updateTime,this.updateConfig(e.data);var t=(new Date).getTime(),r=t-this.update_time;if(w(r)&&r>0&&r<this.para.session_time){var i=this.para.update_interval-r;this.setNextFetch(i)}else this.getConfigFromServer()}else this.getConfigFromServer();else this.getConfigFromServer()}else this.getConfigFromServer();this.pageStateListenner()},serverUrlIsSame:function(e){return!!_(e)&&(e.host===this.url_info.server_url.host&&e.project===this.url_info.server_url.project)},getConfigFromServer:function(){var e=this,t=function(t,r){e.update_time=(new Date).getTime();var i={};200===t?r&&_(r)&&"Web"===r.os&&(i=r,e.updateConfig(i)):205===t?e.updateConfig(i):304===t?i=e.config:(Xt.log("----vtrackcustom----\u6570\u636e\u5f02\u5e38",t),e.updateConfig(i)),e.updateStorage(i),e.setNextFetch()},r=function(t){e.update_time=(new Date).getTime(),Xt.log("----vtrackcustom----\u914d\u7f6e\u62c9\u53d6\u5931\u8d25",t),e.setNextFetch()};this.sendRequest(t,r)},setNextFetch:function(e){var t=this;this.timer&&(clearTimeout(this.timer),this.timer=null),e=e||this.para.update_interval,this.timer=setTimeout(function(){t.getConfigFromServer()},e)},pageStateListenner:function(){var e=this;$e({visible:function(){var t=(new Date).getTime(),r=t-e.update_time;if(w(r)&&r>0&&r<e.para.update_interval){var i=e.para.update_interval-r;e.setNextFetch(i)}else e.getConfigFromServer()},hidden:function(){e.timer&&(clearTimeout(e.timer),e.timer=null)}})},updateConfig:function(e){return!!_(e)&&(this.config=e,this.customProp.updateEvents(e),void this.unlimitedDiv.init(e))},updateStorage:function(e){if(!this.storageEnable)return!1;if(!_(e))return!1;var t;if(this.url_info.server_url)t=this.url_info.server_url;else{var r=Xt.vtrackcollect.initUrl();if(!r)return!1;t=r.server_url}var i={updateTime:(new Date).getTime(),data:e,serverUrl:t};Xt.store.saveObjectVal(this.storage_name,i)},sendRequest:function(e,t){var r=this,i={app_id:this.url_info.page_url.host};this.config.version&&(i.v=this.config.version),Ie({url:r.url_info.api_url,callbackName:"saJSSDKVtrackCollectConfig",data:i,timeout:r.para.timeout,success:function(t,r){e(t,r)},error:function(e){t(e)}})},getAssignConfigs:qr.getAssignConfigs,configIsMatch:qr.configIsMatch},Zr={events:[],getAssignConfigs:qr.getAssignConfigs,filterConfig:qr.filterConfig,getProp:qr.getProp,initUrl:qr.initUrl,updateEvents:function(e){nr(e)&&(this.events=e)},init:function(){this.initAppGetPropsBridge()},geth5Props:function(t){var i={},n=[],a=this;if(!this.events.length)return{};if("$WebClick"===t.event){var s=this.filterConfig(t,this.events);if(!s.length)return{};e(s,function(s){_(s)&&(nr(s.properties)&&s.properties.length>0&&e(s.properties,function(e){if(_(e))if(e.h5===!1)nr(i.sensorsdata_app_visual_properties)||(i.sensorsdata_app_visual_properties=[]),i.sensorsdata_app_visual_properties.push(e);else{var n=a.getProp(e,t);_(n)&&(i=r(i,n))}}),h(s.event_name)&&n.push(s.event_name))}),_(window.SensorsData_App_Visual_Bridge)&&window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode&&(window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode===!0||window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode())&&(i.sensorsdata_web_visual_eventName=n)}return i.sensorsdata_app_visual_properties&&(i.sensorsdata_app_visual_properties=A(JSON.stringify(i.sensorsdata_app_visual_properties))),i},initAppGetPropsBridge:function(){var t=this;return new Xt.JSBridge({type:"getJSVisualProperties",app_call_js:function(i){var n={};try{i=JSON.parse(O(i))}catch(s){Xt.log("getJSVisualProperties data parse error!")}if(_(i)){var o=i.sensorsdata_js_visual_properties,l=t.initUrl();l&&(l=l.page_url,nr(o)&&o.length>0&&e(o,function(e){if(_(e)&&e.url_host===l.host&&e.url_path===l.pathname&&e.h5){var i=t.getProp(e);_(i)&&(n=r(n,i))}}))}var c=Xt.bridge.bridge_info.platform;if("android"===c){var u={callType:"getJSVisualProperties",data:n};_(i)&&i.message_id&&(u.message_id=i.message_id),_(window.SensorsData_APP_New_H5_Bridge)&&a(SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app)?SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app(JSON.stringify(u)):_(window.SensorsData_APP_JS_Bridge)&&a(SensorsData_APP_JS_Bridge.sensorsdata_js_call_app)&&SensorsData_APP_JS_Bridge.sensorsdata_js_call_app(JSON.stringify(u))}return n}})}},Qr={events:[],customProp:Zr,getAssignConfigs:qr.getAssignConfigs,initUrl:qr.initUrl,init:function(){if(this.initUrl()){var e=this.getConfigFromApp();e&&this.updateConfigs(e),this.customProp.init(),this.initAppUpdateConfigBridge()}},initAppUpdateConfigBridge:function(){var e=this;return new Xt.JSBridge({type:"updateH5VisualConfig",app_call_js:function(t){if(t){try{t=JSON.parse(O(t))}catch(r){return void Xt.log("updateH5VisualConfig result parse error\uff01")}e.updateConfigs(t)}}})},getConfigFromApp:function(){var e=new Xt.JSBridge({type:"sensorsdata_get_app_visual_config"}),t=e.getAppData();if(t)try{t=JSON.parse(O(t))}catch(r){t=null,Xt.log("getAppVisualConfig result parse error\uff01")}return t},updateConfigs:function(e){this.events=this.filterConfigs(e),this.customProp.updateEvents(this.events)},filterConfigs:function(e){return this.getAssignConfigs(function(e){return!(!_(e)||e.h5===!1)},e)}},Yr=new xe,Gr={};Gr.spa=Yr,Gr.initSystemEvent=function(){Ct(function(e){Yr.emit("switch",e)})};var ei={isSeachHasKeyword:function(){return null!==Nt("sa-request-id")&&("string"==typeof sessionStorage.getItem("sensors-visual-mode")&&sessionStorage.removeItem("sensors-visual-mode"),!0)},hasKeywordHandle:function(){var e=Nt("sa-request-id"),t=Nt("sa-request-type"),r=Nt("sa-request-url");Tr.setNotice(r),wr.isSupport()&&(null!==r&&sessionStorage.setItem("sensors_heatmap_url",r),sessionStorage.setItem("sensors_heatmap_id",e),null!==t?"1"===t||"2"===t||"3"===t?sessionStorage.setItem("sensors_heatmap_type",t):t=null:t=null!==sessionStorage.getItem("sensors_heatmap_type")?sessionStorage.getItem("sensors_heatmap_type"):null),this.isReady(e,t)},isReady:function(e,t,r){Xt.para.heatmap_url?R({success:function(){setTimeout(function(){"undefined"!=typeof sa_jssdk_heatmap_render&&(sa_jssdk_heatmap_render(Xt,e,t,r),"object"==typeof console&&"function"==typeof console.log&&(Xt.heatmap_version&&Xt.heatmap_version===Xt.lib_version||console.log("heatmap.js\u4e0esensorsdata.js\u7248\u672c\u53f7\u4e0d\u4e00\u81f4\uff0c\u53ef\u80fd\u5b58\u5728\u98ce\u9669!")))},0)},error:function(){},type:"js",url:Xt.para.heatmap_url}):Xt.log("\u6ca1\u6709\u6307\u5b9aheatmap_url\u7684\u8def\u5f84")},isStorageHasKeyword:function(){return wr.isSupport()&&"string"==typeof sessionStorage.getItem("sensors_heatmap_id")},storageHasKeywordHandle:function(){Tr.setNotice(),ei.isReady(sessionStorage.getItem("sensors_heatmap_id"),sessionStorage.getItem("sensors_heatmap_type"),location.href)}},ti={isStorageHasKeyword:function(){return wr.isSupport()&&"string"==typeof sessionStorage.getItem("sensors-visual-mode")},isSearchHasKeyword:function(){return(Nt("sa-visual-mode")===!0||"true"===Nt("sa-visual-mode"))&&("string"==typeof sessionStorage.getItem("sensors_heatmap_id")&&sessionStorage.removeItem("sensors_heatmap_id"),!0)},loadVtrack:function(){R({success:function(){},error:function(){},type:"js",url:Xt.para.vtrack_url?Xt.para.vtrack_url:location.protocol+"//static.sensorsdata.cn/sdk/"+Xt.lib_version+"/vtrack.min.js"})},messageListener:function(e){function t(e){return cr.isHttpUrl(e)?cr.removeScriptProtocol(e):(Xt.log("\u53ef\u89c6\u5316\u6a21\u5f0f\u68c0\u6d4b URL \u5931\u8d25"),!1)}if("sa-fe"!==e.data.source)return!1;if("v-track-mode"===e.data.type){if(e.data.data&&e.data.data.isVtrack)if(wr.isSupport()&&sessionStorage.setItem("sensors-visual-mode","true"),e.data.data.userURL&&location.href.match(/sa-visual-mode=true/)){var r=t(e.data.data.userURL);r&&(window.location.href=r)}else ti.loadVtrack();window.removeEventListener("message",ti.messageListener,!1)}},removeMessageHandle:function(){window.removeEventListener&&window.removeEventListener("message",ti.messageListener,!1)},verifyVtrackMode:function(){window.addEventListener&&window.addEventListener("message",ti.messageListener,!1),ti.postMessage()},postMessage:function(){window.parent&&window.parent.postMessage&&window.parent.postMessage({source:"sa-web-sdk",type:"v-is-vtrack",data:{sdkversion:"1.21.12"}},"*")},notifyUser:function(){var e=function(t){return"sa-fe"===t.data.source&&void("v-track-mode"===t.data.type&&(t.data.data&&t.data.data.isVtrack&&alert("\u5f53\u524d\u7248\u672c\u4e0d\u652f\u6301\uff0c\u8bf7\u5347\u7ea7\u90e8\u7f72\u795e\u7b56\u6570\u636e\u6cbb\u7406"),window.removeEventListener("message",e,!1)))};window.addEventListener&&window.addEventListener("message",e,!1),ti.postMessage()}},ri=["setItem","deleteItem","getAppStatus","track","quick","register","registerPage","registerOnce","trackSignup","setProfile","setOnceProfile","appendProfile","incrementProfile","deleteProfile","unsetProfile","identify","login","logout","trackLink","clearAllRegister","clearPageRegister"],ii={track:function(e,t,r){},quick:function(e,t,r,i){},register:function(e){},registerPage:function(e){},registerOnce:function(e){},
clearAllRegister:function(e){},trackSignup:function(e,t,r,i){},setProfile:function(e,t){},setOnceProfile:function(e,t){},appendProfile:function(e,t){},incrementProfile:function(e,t){},deleteProfile:function(e){},unsetProfile:function(e,t){},identify:function(e,t){},login:function(e,t){},logout:function(e){},trackLink:function(e,t,r){},deleteItem:function(e,t){},setItem:function(e,t,r){},getAppStatus:function(e){},clearPageRegister:function(e){}};Et.prototype.process=function(e,t){if(!(e&&e in this.processDef))return void H("process ["+e+"] is not supported");var r=this.registeredInterceptors[e];if(r&&nr(r)&&r.length>0)for(var i={current:0,total:r.length},n=new Lt(t,i,Xt),a=0;a<r.length;a++)try{if(i.current=a+1,t=r[a].call(null,t,n)||t,n.cancelationToken.getCanceled()){H("process ["+e+"] has been canceled.");break}}catch(s){H("interceptor error:"+s)}return this.processDef[e]&&this.processDef[e]in this.processDef&&(t=this.process(this.processDef[e],t)),t},Et.prototype.registerStageImplementation=function(e){e&&e.init&&a(e.init)&&(e.init(this),e.interceptor&&this.registerInterceptor(e.interceptor))},Et.prototype.registerInterceptor=function(e){if(e)for(var t in e){var r=e[t];if(r&&_(r)&&a(r.entry)){w(r.priority)||(r.priority=1e7),this.registeredInterceptors[t]||(this.registeredInterceptors[t]=[]);var i=this.registeredInterceptors[t],n=r.priority,s=r.entry;switch(!0){case n<=0:i.unshift(s);break;case n>=i.length:i.push(s);break;default:i.splice(n,0,s)}}}};var ni={addCustomProps:"formatData",formatData:null},ai=new Et(ni),si={init:function(){},interceptor:{formatData:{priority:0,entry:function(e){return qt(e),e}}}},oi=window.sensors_data_pre_config,li=!!_(oi)&&oi.is_compliance_enabled;Xt.init=function(e){return!(Xt.readyState&&Xt.readyState.state&&Xt.readyState.state>=2)&&(li&&zt(!0),Xt.ee.initSystemEvent(),Xt.setInitVar(),Xt.readyState.setState(2),Xt.initPara(e),Xt.bridge.supportAppCallJs(),Xt.detectMode(),void Xt.iOSWebClickPolyfill())},li?zt(!1):(zt(!0),Tt());var ci=Xt;try{Xt.modules={},Xt.modules.Amp=function(){"use strict";var e={sd:null,init:function(e){if(this.sd)return!1;if(this.sd=e,!this.sd||!this.sd._)return!1;var t=this.sd._.cookie.get("sensors_amp_id"),r=this.sd.store._state.distinct_id;if(t&&t.length>0){var i="amp-"===t.slice(0,4);if(t!==r){if(!i)return!1;this.sd.store._state.first_id?(this.sd.identify(t,!0),this.sd.saEvent.send({original_id:t,distinct_id:r,type:"track_signup",event:"$SignUp",properties:{}},null),this.setAmpId(r)):this.sd.identify(t,!0)}}else this.setAmpId(r);this.addListener()},addListener:function(){var e=this;this.sd.events.on("changeDistinctId",function(t){e.setAmpId(t)}),this.sd.events.isReady()},setAmpId:function(e){this.sd._.cookie.set("sensors_amp_id",e)}};return window.SensorsDataWebJSSDKPlugin&&"[object Object]"===Object.prototype.toString.call(window.SensorsDataWebJSSDKPlugin)?window.SensorsDataWebJSSDKPlugin.Amp=window.SensorsDataWebJSSDKPlugin.Amp||e:window.SensorsDataWebJSSDKPlugin={Amp:e},e}(),Xt.modules.Channel=function(){"use strict";var e,t,r,i={event_list:[],latest_event_initial_time:null,max_save_time:2592e6,init:function(i){return!t&&(!!(t=i)&&(e=t._,r=t.store,!!e.localStorage.isSupport()&&(t.para.max_string_length=1024,this.eventList.init(),this.addLatestChannelUrl(),void this.addIsChannelCallbackEvent())))},addIsChannelCallbackEvent:function(){t.registerPage({$is_channel_callback_event:function(e){if(e.event&&"$WebClick"!==e.event&&"$pageview"!==e.event&&"$WebStay"!==e.event&&"$SignUp"!==e.event)return!i.eventList.hasEvent(e.event)&&(i.eventList.add(e.event),!0)}})},addLatestChannelUrl:function(){var r=this.getUrlDomain(),n=this.cookie.getChannel();if("url\u89e3\u6790\u5931\u8d25"===r)this.registerAndSave({_sa_channel_landing_url:"",_sa_channel_landing_url_error:"url\u7684domain\u89e3\u6790\u5931\u8d25"});else if(e.isReferralTraffic(document.referrer)){var a=e.getQueryParam(location.href,"sat_cf");e.isString(a)&&a.length>0?(this.registerAndSave({_sa_channel_landing_url:location.href}),i.channelLinkHandler()):this.registerAndSave({_sa_channel_landing_url:""})}else n?t.registerPage(n):t.registerPage({_sa_channel_landing_url:"",_sa_channel_landing_url_error:"\u53d6\u503c\u5f02\u5e38"})},registerAndSave:function(e){t.registerPage(e),this.cookie.saveChannel(e)},cookie:{getChannel:function(){var t=e.decryptIfNeeded(e.cookie.get("sensorsdata2015jssdkchannel"));return t=e.safeJSONParse(t),!(!e.isObject(t)||!t.prop)&&t.prop},saveChannel:function(r){var i={prop:r},n=JSON.stringify(i);t.para.encrypt_cookie&&(n=e.encrypt(n)),e.cookie.set("sensorsdata2015jssdkchannel",n)}},channelLinkHandler:function(){this.eventList.reset(),t.track("$ChannelLinkReaching")},getUrlDomain:function(){var t=e.info.pageProp.url_domain;return""===t&&(t="url\u89e3\u6790\u5931\u8d25"),t},eventList:{init:function(){var t=this.get(),r=(new Date).getTime();if(t&&e.isNumber(t.latest_event_initial_time)&&e.isArray(t.eventList)){var n=r-t.latest_event_initial_time;n>0&&n<i.max_save_time?(i.event_list=t.eventList,i.latest_event_initial_time=t.latest_event_initial_time):this.reset()}else this.reset()},get:function(){var e={};try{e=r.readObjectVal("sawebjssdkchannel")}catch(i){t.log(i)}return e},add:function(e){i.event_list.push(e),this.save()},save:function(){var e={latest_event_initial_time:i.latest_event_initial_time,eventList:i.event_list};r.saveObjectVal("sawebjssdkchannel",e)},reset:function(){i.event_list=[],i.latest_event_initial_time=(new Date).getTime(),this.save()},hasEvent:function(t){var r=!1;return e.each(i.event_list,function(e){e===t&&(r=!0)}),r}}};return window.SensorsDataWebJSSDKPlugin&&"[object Object]"===Object.prototype.toString.call(window.SensorsDataWebJSSDKPlugin)?window.SensorsDataWebJSSDKPlugin.SensorsChannel=window.SensorsDataWebJSSDKPlugin.SensorsChannel||i:window.SensorsDataWebJSSDKPlugin={SensorsChannel:i},i}(),Xt.modules.Deeplink=function(){"use strict";function e(){return"undefined"!=typeof t&&document[t]}var t,r=(/micromessenger\/([\d.]+)/i.test(navigator.userAgent||""),function(){var e={};return"undefined"!=typeof document.hidden?(e.hidden="hidden",e.visibilityChange="visibilitychange"):"undefined"!=typeof document.msHidden?(e.hidden="msHidden",e.visibilityChange="msvisibilitychange"):"undefined"!=typeof document.webkitHidden&&(e.hidden="webkitHidden",e.visibilityChange="webkitvisibilitychange"),e});t=r().hidden;var i={android:/Android/i,iOS:/iPhone|iPad|iPod/i},n=function(){for(var e in i)if(navigator.userAgent.match(i[e]))return e;return""},a=n(),s=function(){return i.hasOwnProperty(a)},o=function(e){return null!=e&&"[object Object]"==Object.prototype.toString.call(e)},l=function(e){var t=/\/sd\/(\w+)\/(\w+)$/;return e.match(t)},c=function(e){var t=e._.URL(e.para.server_url);return{origin:t.origin,project:t.searchParams.get("project")||"default"}},u=function(r,i,n){r.log("\u5c1d\u8bd5\u5524\u8d77 android app");var a=i;r.log("\u5524\u8d77APP\u7684\u5730\u5740\uff1a"+a),window.location=a,r.timer=setTimeout(function(){var i=e();return r.log("hide:"+t+":"+document[t]),i?(r.log("The page is hidden, stop navigating to download page"),!1):(r.log("App\u53ef\u80fd\u672a\u5b89\u88c5\uff0c\u8df3\u8f6c\u5230\u4e0b\u8f7d\u5730\u5740"),void(window.location=n))},r.timeout)},d=function(t,r,i){t.log("\u5c1d\u8bd5\u5524\u8d77 iOS app:"+r),window.location.href=r,t.timer=setTimeout(function(){var r=e();return r?(t.log("The page is hidden, stop navigating to download page"),!1):(t.log("App\u53ef\u80fd\u672a\u5b89\u88c5\uff0c\u8df3\u8f6c\u5230\u4e0b\u8f7d\u5730\u5740"),void(window.location.href=i))},t.timeout),t.log("new timer:"+t.timer)},p={key:null,timer:null,sd:null,data:null,timeout:2500,apiURL:"{origin}/sdk/deeplink/param?key={key}&system_type=JS&project={project}",init:function(){if(this.sd)return this.log("deeplink\u5df2\u7ecf\u521d\u59cb\u5316"),!1;if(o(sensorsDataAnalytic201505)&&(this.sd=sensorsDataAnalytic201505),this.log("init()"),null===this.sd)return this.log("\u795e\u7b56JS SDK\u672a\u6210\u529f\u5f15\u5165"),!1;var e={};if(arguments.length>0&&(1===arguments.length&&o(arguments[0])?e=arguments[0]:arguments.length>=2&&o(arguments[1])&&(e=arguments[1])),!s())return this.log("\u4e0d\u652f\u6301\u5f53\u524d\u7cfb\u7edf\uff0c\u76ee\u524d\u53ea\u652f\u6301Android\u548ciOS"),!1;if(o(e)&&this.sd._.isNumber(e.timeout)&&e.timeout>=2500&&(this.timeout=e.timeout),!this.sd.para.server_url)return this.log("\u795e\u7b56JS SDK\u914d\u7f6e\u9879server_url\u672a\u6b63\u786e\u914d\u7f6e"),!1;var t=c(this.sd);this.apiURL=this.apiURL.replace("{origin}",t.origin).replace("{project}",t.project);var r=this.sd._.URL(window.location.href).searchParams.get("deeplink");if(!r)return this.log("\u5f53\u524d\u9875\u9762\u7f3a\u5c11deeplink\u53c2\u6570"),!1;r=window.decodeURIComponent(r);var i=l(r);return i?(this.key=i[2],this.apiURL=this.apiURL.replace("{key}",window.encodeURIComponent(i[2])),this.sd._.ajax({url:this.apiURL,type:"GET",cors:!0,credentials:!1,success:function(e){return e.errorMsg?(p.log("API\u62a5\u9519\uff1a"+e.errorMsg),!1):(p.data=e,p.log("API\u67e5\u8be2\u6210\u529f\uff0c\u6570\u636e\uff1a"+JSON.stringify(e,null,"  ")),void(this.data.app_key&&(this.data.android_info&&this.data.android_info.url_schemes&&(this.data.android_info.url_schemes+="://sensorsdata/sd/"+this.data.app_key+"/"+this.key),this.data.ios_info&&this.data.ios_info.url_schemes&&(this.data.ios_info.url_schemes+="://sensorsdata/sd/"+this.data.app_key+"/"+this.key))))}.bind(this),error:function(){p.log("API\u67e5\u8be2\u51fa\u9519")}}),void this.addListeners()):(this.log("\u5f53\u524d\u9875\u9762\u7684deeplink\u53c2\u6570\u65e0\u6548"),!1)},openDeepLink:function(){if(this.log("openDeeplink()"),!this.data)return this.log("\u6ca1\u6709Deep link\u6570\u636e!"),!1;if("iOS"===a){this.log("\u5f53\u524d\u7cfb\u7edf\u662fiOS");var e=this.sd&&this.sd._&&this.sd._.getIOSVersion()>=9&&this.data.ios_info.ios_wake_url?this.data.ios_info.ios_wake_url:this.data.ios_info.url_schemes;this.log("\u5524\u8d77APP\u7684\u5730\u5740\uff1a"+e),d(this,e,this.data.ios_info.download_url)}else this.log("\u5f53\u524d\u7cfb\u7edf\u662f android"),u(this,this.data.android_info.url_schemes,this.data.android_info.download_url)},log:function(e){this.sd&&this.sd.log(e)},addListeners:function(){var e=r().visibilityChange;e&&document.addEventListener(e,function(){clearTimeout(this.timer),this.log("visibilitychange, clear timeout:"+this.timer)}.bind(this),!1),window.addEventListener("pagehide",function(){this.log("page hide, clear timeout:"+this.timer),clearTimeout(this.timer)}.bind(this),!1)}};return o(window.SensorsDataWebJSSDKPlugin)?(window.SensorsDataWebJSSDKPlugin.Deeplink=window.SensorsDataWebJSSDKPlugin.Deeplink||p,window.SensorsDataWebJSSDKPlugin.deeplink=window.SensorsDataWebJSSDKPlugin.deeplink||p):window.SensorsDataWebJSSDKPlugin={Deeplink:p,deeplink:p},p}(),Xt.modules.Pageleave=function(){"use strict";function e(){this.sd=null,this.start_time=+new Date,this.page_show_status=!0,this.page_hidden_status=!1,this._={},this.timer=null,this.current_page_url=document.referrer,this.url=location.href,this.option={},this.heartbeat_interval_time=5e3,this.heartbeat_interval_timer=null,this.page_id=null,this.storage_name="sawebjssdkpageleave"}var t=5e3;e.prototype.init=function(e,t){if(e){this.sd=e,this._=this.sd._;var r=this;if(t){this.option=t;var i=t.heartbeat_interval_time;i&&(this._.isNumber(i)||this._.isNumber(1*i))&&1*i>0&&(this.heartbeat_interval_time=1e3*i)}this.page_id=Number(String(this._.getRandom()).slice(2,5)+String(this._.getRandom()).slice(2,4)+String((new Date).getTime()).slice(-4)),r.addEventListener(),document.hidden===!0?this.page_show_status=!1:r.addHeartBeatInterval(),this.log("PageLeave\u521d\u59cb\u5316\u5b8c\u6bd5")}else this.log("\u795e\u7b56JS SDK\u672a\u6210\u529f\u5f15\u5165")},e.prototype.log=function(e){this.sd&&this.sd.log(e)},e.prototype.refreshPageEndTimer=function(){var e=this;this.timer&&(clearTimeout(this.timer),this.timer=null),this.timer=setTimeout(function(){e.page_hidden_status=!1},t)},e.prototype.hiddenStatusHandler=function(){clearTimeout(this.timer),this.timer=null,this.page_hidden_status=!1},e.prototype.pageStartHandler=function(){this.start_time=+new Date,!document.hidden==!0?this.page_show_status=!0:this.page_show_status=!1,this.url=location.href},e.prototype.pageEndHandler=function(){if(this.page_hidden_status!==!0){var e=this.getPageLeaveProperties();this.page_show_status===!1&&delete e.event_duration,this.page_show_status=!1,this.page_hidden_status=!0,this.isCollectUrl(this.url)&&this.sd.track("$WebPageLeave",e),this.refreshPageEndTimer(),this.delHeartBeatData()}},e.prototype.addEventListener=function(){this.addPageStartListener(),this.addPageSwitchListener(),this.addSinglePageListener(),this.addPageEndListener()},e.prototype.addPageStartListener=function(){var e=this;"onpageshow"in window&&this._.addEvent(window,"pageshow",function(){e.pageStartHandler(),e.hiddenStatusHandler()})},e.prototype.isCollectUrl=function(e){return"function"!=typeof this.option.isCollectUrl||("string"!=typeof e||""===e||this.option.isCollectUrl(e))},e.prototype.addSinglePageListener=function(){var e=this;this.sd.ee&&this.sd.ee.spa.prepend("switch",function(t){t!==location.href&&(e.url=t,e.pageEndHandler(),e.stopHeartBeatInterval(),e.current_page_url=e.url,e.pageStartHandler(),e.hiddenStatusHandler(),e.startHeartBeatInterval())})},e.prototype.addPageEndListener=function(){var e=this;this._.each(["pagehide","beforeunload","unload"],function(t){"on"+t in window&&e._.addEvent(window,t,function(){e.pageEndHandler(),e.stopHeartBeatInterval()})})},e.prototype.addPageSwitchListener=function(){var e=this;this._.listenPageState({visible:function(){e.pageStartHandler(),e.hiddenStatusHandler(),e.startHeartBeatInterval()},hidden:function(){e.url=location.href,e.pageEndHandler(),e.stopHeartBeatInterval()}})},e.prototype.addHeartBeatInterval=function(){this._.localStorage.isSupport()&&this.startHeartBeatInterval()},e.prototype.startHeartBeatInterval=function(){var e=this;this.heartbeat_interval_timer&&this.stopHeartBeatInterval();var t=!0;this.isCollectUrl(this.url)||(t=!1),this.heartbeat_interval_timer=setInterval(function(){t&&e.saveHeartBeatData()},this.heartbeat_interval_time),t&&this.saveHeartBeatData("is_first_heartbeat"),this.reissueHeartBeatData()},e.prototype.stopHeartBeatInterval=function(){clearInterval(this.heartbeat_interval_timer),this.heartbeat_interval_timer=null},e.prototype.saveHeartBeatData=function(e){var t=this.getPageLeaveProperties();t.$time=new Date,"is_first_heartbeat"===e&&(t.event_duration=3.14);var r=this.sd.kit.buildData({type:"track",event:"$WebPageLeave",properties:t});r.heartbeat_interval_time=this.heartbeat_interval_time,this.sd.store.saveObjectVal(this.storage_name+"-"+this.page_id,r)},e.prototype.delHeartBeatData=function(e){this._.localStorage.remove(e||this.storage_name+"-"+this.page_id)},e.prototype.reissueHeartBeatData=function(){for(var e=window.localStorage.length,t=e-1;t>=0;t--){var r=window.localStorage.key(t);if(r&&r!==this.storage_name+"-"+this.page_id&&0===r.indexOf(this.storage_name+"-")){var i=this.sd.store.readObjectVal(r);this._.isObject(i)&&1*new Date-i.time>i.heartbeat_interval_time+5e3&&(delete i.heartbeat_interval_time,this.sd.kit.sendData(i),this.delHeartBeatData(r))}}},e.prototype.getPageLeaveProperties=function(){var e=(+new Date-this.start_time)/1e3;(isNaN(e)||e<0)&&(e=0),e=Number(e.toFixed(3));var t=this._.getReferrer(this.current_page_url),r=document.documentElement&&document.documentElement.scrollTop||window.pageYOffset||document.body&&document.body.scrollTop||0;r=Math.round(r)||0;var i={$title:document.title,$url:this._.getURL(this.url),$url_path:location.pathname,$referrer_host:t?this._.getHostname(t):"",$referrer:t,$viewport_position:r};return 0!==e&&(i.event_duration=e),i=this._.extend(i,this.option.custom_props)};var r=new e;return window.SensorsDataWebJSSDKPlugin&&"[object Object]"===Object.prototype.toString.call(window.SensorsDataWebJSSDKPlugin)?window.SensorsDataWebJSSDKPlugin.PageLeave=window.SensorsDataWebJSSDKPlugin.PageLeave||r:window.SensorsDataWebJSSDKPlugin={PageLeave:r},r}(),Xt.modules.Pageload=function(){"use strict";var e={init:function(e){function t(e,t){if(e.getEntries&&"function"==typeof e.getEntries){for(var r=e.getEntries(),i=null,n=0;n<r.length;n++)"transferSize"in r[n]&&(i+=r[n].transferSize);Dr.isNumber(i)&&i>=0&&i<10737418240&&(t.$page_resource_size=Number((i/1024).toFixed(3)))}}function r(){var i=window.performance||window.webkitPerformance||window.msPerformance||window.mozPerformance,n=0,a={$url_path:location.pathname,$referrer:e._.getReferrer(null,!0)};if(i&&i.timing){var s=i.timing;0===s.fetchStart||0===s.domContentLoadedEventEnd?e.log("performance \u6570\u636e\u83b7\u53d6\u5f02\u5e38"):n=s.domContentLoadedEventEnd-s.fetchStart,t(i,a)}else e.log("\u6d4f\u89c8\u5668\u672a\u652f\u6301 performance API.");n>0&&(a.event_duration=Number((n/1e3).toFixed(3))),e.track("$WebPageLoad",a),window.removeEventListener?window.removeEventListener("load",r):window.detachEvent&&window.detachEvent("onload",r)}"complete"==document.readyState?r():window.addEventListener?window.addEventListener("load",r):window.attachEvent&&window.attachEvent("onload",r)}};return window.SensorsDataWebJSSDKPlugin&&"[object Object]"===Object.prototype.toString.call(window.SensorsDataWebJSSDKPlugin)?window.SensorsDataWebJSSDKPlugin.PageLoad=window.SensorsDataWebJSSDKPlugin.PageLoad||e:window.SensorsDataWebJSSDKPlugin={PageLoad:e},e}(),Xt.modules.RegisterPropertyPageHeight=function(){"use strict";function e(e){try{if("$pageview"!==e.event&&(!e.type||"profile"!==e.type.slice(0,7))){var n=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight||0,a=document.documentElement.scrollHeight||0,s={$page_height:Math.max(n,a)||0};e.properties=t._.extend(e.properties||{},s)}}catch(o){i("\u9875\u9762\u9ad8\u5ea6\u83b7\u53d6\u5f02\u5e38\u3002")}return r.call(t.kit,e)}var t,r,i=window.console&&window.console.log||function(){},n={init:function(n){return t=n,i=t&&t.log||i,n&&n.kit&&n.kit.buildData?(r=t.kit.buildData,t.kit.buildData=e,void i("RegisterPropertyPageHeight \u63d2\u4ef6\u521d\u59cb\u5316\u5b8c\u6210")):void i("RegisterPropertyPageHeight \u63d2\u4ef6\u521d\u59cb\u5316\u5931\u8d25,\u5f53\u524d\u4e3bsdk\u4e0d\u652f\u6301 RegisterPropertyPageHeight \u63d2\u4ef6\uff0c\u8bf7\u5347\u7ea7\u4e3bsdk")}};return window.SensorsDataWebJSSDKPlugin&&"[object Object]"===Object.prototype.toString.call(window.SensorsDataWebJSSDKPlugin)?window.SensorsDataWebJSSDKPlugin.RegisterPropertyPageHeight=window.SensorsDataWebJSSDKPlugin.RegisterPropertyPageHeight||n:window.SensorsDataWebJSSDKPlugin={RegisterPropertyPageHeight:n},n}(),function(){"use strict";var e={};e.getPart=function(e){var t=!1,r=this.option.length;if(r)for(var i=0;i<r;i++)if(e.indexOf(this.option[i].part_url)>-1)return!0;return t},e.getPartHash=function(e){var t=this.option.length,r=!1;if(t)for(var i=0;i<t;i++)if(e.indexOf(this.option[i].part_url)>-1)return this.option[i].after_hash;return!!r},e.getCurrenId=function(){var e=this.store.getDistinctId()||"",t=this.store.getFirstId()||"";this._.urlSafeBase64&&this._.urlSafeBase64.encode?e=e?this._.urlSafeBase64.trim(this._.urlSafeBase64.encode(this._.base64Encode(e))):"":this._.rot13obfs&&(e=e?this._.rot13obfs(e):"");var r=t?"f"+e:"d"+e;return encodeURIComponent(r)},e.rewriteUrl=function(e,t){var r=/([^?#]+)(\?[^#]*)?(#.*)?/,i=r.exec(e),n="";if(i){var a,s=i[1]||"",o=i[2]||"",l=i[3]||"";if(this.getPartHash(e)){a=l.indexOf("_sasdk");var c=l.indexOf("?");n=c>-1?a>-1?s+o+"#"+l.substring(1,a)+"_sasdk="+this.getCurrenId():s+o+"#"+l.substring(1)+"&_sasdk="+this.getCurrenId():s+o+"#"+l.substring(1)+"?_sasdk="+this.getCurrenId()}else{a=o.indexOf("_sasdk");var u=/^\?(\w)+/.test(o);n=u?a>-1?s+"?"+o.substring(1,a)+"_sasdk="+this.getCurrenId()+l:s+"?"+o.substring(1)+"&_sasdk="+this.getCurrenId()+l:s+"?"+o.substring(1)+"_sasdk="+this.getCurrenId()+l}return t&&(t.href=n),n}},e.getUrlId=function(){var e=location.href.match(/_sasdk=([aufd][^\?\#\&\=]+)/);if(this._.isArray(e)&&e[1]){var t=decodeURIComponent(e[1]);return!t||"f"!==t.substring(0,1)&&"d"!==t.substring(0,1)||(this._.urlSafeBase64&&this._.urlSafeBase64.isUrlSafeBase64&&this._.urlSafeBase64.isUrlSafeBase64(t)?t=t.substring(0,1)+this._.base64Decode(this._.urlSafeBase64.decode(t.substring(1))):this._.rot13defs&&(t=t.substring(0,1)+this._.rot13defs(t.substring(1)))),t}return""},e.setRefferId=function(){var e=this.store.getDistinctId(),t=this.getUrlId();if(""===t)return!1;var r="a"===t.substring(0,1)||"d"===t.substring(0,1);return t=t.substring(1),t!==e&&(t&&r&&this.store.getFirstId()&&(this.sd.identify(t,!0),this.sd.saEvent.send({original_id:t,distinct_id:e,type:"track_signup",event:"$SignUp",properties:{}},null)),t&&r&&!this.store.getFirstId()&&this.sd.identify(t,!0),void(!t||r||this.store.getFirstId()||this.sd.login(t)))},e.addListen=function(){var e=this,t=function(t){var r,i,n=t.target,a=n.tagName.toLowerCase(),s=n.parentNode;if("a"===a&&n.href||s&&s.tagName&&"a"===s.tagName.toLowerCase()&&s.href){"a"===a&&n.href?(r=n.href,i=n):(r=s.href,i=s);var o=e._.URL(r),l=o.protocol;"http:"!==l&&"https:"!==l||e.getPart(r)&&e.rewriteUrl(r,i)}};e._.addEvent(document,"mousedown",t),window.PointerEvent&&"maxTouchPoints"in window.navigator&&window.navigator.maxTouchPoints>=0&&e._.addEvent(document,"pointerdown",t)},e.init=function(e,t){function r(t){for(var r=t.length,i=[],n=0;n<r;n++)/[A-Za-z0-9]+\./.test(t[n].part_url)&&"[object Boolean]"==Object.prototype.toString.call(t[n].after_hash)?i.push(t[n]):e.log("linker \u914d\u7f6e\u7684\u7b2c "+(n+1)+" \u9879\u683c\u5f0f\u4e0d\u6b63\u786e\uff0c\u8bf7\u68c0\u67e5\u53c2\u6570\u683c\u5f0f\uff01");return i}return this.sd=e,this._=e._,this.store=e.store,this.para=e.para,this._.isObject(t)&&this._.isArray(t.linker)&&t.linker.length>0?(this.setRefferId(),this.addListen(),this.option=t.linker,void(this.option=r(this.option))):void e.log("\u8bf7\u914d\u7f6e\u6253\u901a\u57df\u540d\u53c2\u6570\uff01")},Dr.isObject(window.SensorsDataWebJSSDKPlugin)?window.SensorsDataWebJSSDKPlugin.SiteLinker=window.SensorsDataWebJSSDKPlugin.SiteLinker||e:window.SensorsDataWebJSSDKPlugin={SiteLinker:e}}(),"string"==typeof window.sensorsDataAnalytic201505?(Xt.para=window[sensorsDataAnalytic201505].para,Xt._q=window[sensorsDataAnalytic201505]._q,window[sensorsDataAnalytic201505]=Xt,window.sensorsDataAnalytic201505=Xt,Xt.init()):"undefined"==typeof window.sensorsDataAnalytic201505?window.sensorsDataAnalytic201505=Xt:ci=window.sensorsDataAnalytic201505}catch(ui){if("object"==typeof console&&console.log)try{console.log(ui)}catch(di){Xt.log(di)}}var pi=ci;return pi});