try{eval("var theRef = document.referrer")}catch(e){}window.wiseAPM||function(e){function t(){var t=e.location.hash;return t.indexOf("?")>0&&(t=t.substring(0,t.indexOf("?"))),e.location.pathname+t}function r(){this.__values__={}}function n(){function e(e){return e<0?NaN:e<=30?0|m()*(1<<e):e<=53?(0|m()*(1<<30))+(0|m()*(1<<e-30))*(1<<30):NaN}function t(e,t){for(var r=e.toString(16),n=t-r.length,i="0";n>0;n>>>=1,i+=i)1&n&&(r=i+r);return r}return t(e(32),8)+"-"+t(e(16),4)+"-"+t(16384|e(12),4)+"-"+t(32768|e(14),4)+"-"+t(e(48),12)}function i(e){switch(typeof e){case"object":if(!e)return"null";if(e instanceof Array){for(var t="[",r=0;r<e.length;r++)t+=(r>0?",":"")+i(e[r]);return t+"]"}for(var n in t="{",r=0,e)if("function"!=typeof e[n]){var a=i(e[n]);t+=(r>0?",":"")+i(n)+":"+a,r++}return t+"}";case"string":return'"'+e.replace(/([\"\\])/g,"\\$1").replace(/\r/g,"\\r").replace(/\n/g,"\\n")+'"';case"number":return e.toString();case"boolean":return e?"true":"false";case"function":return i(e.toString());case"undefined":default:return'"undefined"'}}function o(e,t,r,n){if(JSON&&JSON.stringify&&(Se.isType(e,"object")||Se.isType(e,"array")))return JSON.stringify(e,s(t,n),r);try{return Se.stringify(e)}catch(e){return""}}function s(e,t){var r=[],n=[];return null==t&&(t=function(e,t){return r[0]===t?"[Circular ~]":"[Circular ~."+n.slice(0,r.indexOf(t)).join(".")+"]"}),function(i,a){if(r.length>0){var o=r.indexOf(this);~o?r.splice(o+1):r.push(this),~o?n.splice(o,1/0,i):n.push(i),~r.indexOf(a)&&(a=t.call(this,i,a))}else r.push(a);return null==e?a:e.call(this,i,a)}}function c(e){return e.replace(/^\s+|\s+$/gm,"")}function u(e,t){if(e)for(var r=0;r<e.length;r++)e[r]&&t&&t(e[r],r,e)}function f(e,t){try{t=t.toLowerCase()}catch(e){}return Object.prototype.toString.call(e).toLowerCase()==="[object "+t+"]"}function l(e,t){return e&&Object.prototype.hasOwnProperty.call(e,t)}function d(t){return e.encodeURIComponent?e.encodeURIComponent(t):t}function h(e){var t=document.URL;return t}function g(e,t,r){e=e||"";try{e=e.length>t?e.substring(0,t)+(r?"":"..."):e}catch(t){e=""}return e}function p(){return(new Date).getTime()}function m(){var e=p();return e=(9301*e+49297)%233280,e/233280}function v(e){return"string"==typeof e?e.length:window.ArrayBuffer&&e instanceof ArrayBuffer?e.byteLength:window.Blob&&e instanceof Blob?e.size:e&&e.length?e.length:0}function y(e,t,r,n){return e+t+r+(n||"")}function b(e){return function(e,t){}}function w(e){if(Re.sended&&(this.ready()||e)&&this.errors.length){var t=[],r={};for(var n in _e.each(this.errors,function(e,t,n){t=y(e.e1,e.e2,e.e3,e.e6);var i=r[t];i?i.ep=i.ep+1:r[t]={o:e.e0-Re.st,e:e.e1,ec:n.length,ep:1,l:e.e2||0,c:e.e3||0,m:e.e6||"",r:e.e4,s:g(e.e5,5e3),f:e.e8||"",fv:e.e9||"",rvid:e.e10||"",rl:e.e11||"",route:e.e12}}),r)t.push(r[n]);var i=_e.mkurl(Re.server.beacon,"er",{}),a=this.errors.length;_e.POST(i,Se.stringify2({err:t,uid:F()}),{},function(e,t){e||De.errors.splice(0,a)})}if(Re.sended&&this.resErrors.length){var o=_e.mkurl(Re.server.beacon,"re",{}),s=this.resErrors.length;_e.POST(o,Se.stringify2({se:this.resErrors,uid:F()}),{},function(e,t){e||De.resErrors.splice(0,s)})}}function T(){De.initend()}function S(){"complete"===Oe.readyState&&De.initend()}function E(e){function t(){De.send()}return!!Re.load_time||(De.initend(),Re.load_time=p(),!0===e?t():setTimeout(t,0))}function _(e){De.flush||E(!0),_e.bind(w,De)(!0),De.sa(!0),De.sendTrail(),De.sendMarkData(),D(!0),De.flush=!0}function k(){De.touch||(De.touch=p())}function A(r){var n=arguments;if(0!=n.length){var i={};if(i.e0=p(),i.e1="",i.e2=0,i.e3=0,i.e4="","string"==typeof r)i.e1=n[0],n.length>2&&(i.e2=n[2]||0,i.e4=n[1]),n.length>3&&(i.e3=n[3]||0);else if(r instanceof Event||e.PromiseRejectionEvent&&r instanceof PromiseRejectionEvent){if(i.e5=null,i.e6=null,i.e7=0,e.ErrorEvent&&r instanceof ErrorEvent){if(i.e1=r.message||(r.error&&r.error.constructor.name)+" "+(r.error&&r.error.message)||"",i.e2=r.lineno||0,i.e3=r.colno||0,i.e4=r.filename||r.error&&r.error.fileName||r.target&&r.target.baseURI||"",r.error){i.e5=r.error.stack||"",i.e6=r.error.moduleName||"";var a=y(i.e1,i.e2,i.e3,i.e6);i.e7=Ue[a]?0:1}}else if(e.PromiseRejectionEvent&&r instanceof PromiseRejectionEvent){if(i.e1="",i.e2=1,i.e3=1,i.e4=r.filename||r.reason&&r.reason.fileName||r.target&&r.target.baseURI||h(!0),r.reason){i.e1=be(r.reason.message)?r.reason.message:"",i.e5=r.reason.stack||"",i.e6=r.reason.moduleName||"";a=y(i.e1,i.e2,i.e3,i.e6);i.e7=Ue[a]?0:1}if(!i.e1)return}if(i.e8=Pe.index("f"),i.e9=Pe.index("fv"),i.e10=Pe.index("rvid"),i.e11=Pe.index("rl"),"unknown"===i.e1&&"unknown"===i.e4)return}i.e12=t(),De.errors&&De.errors.push(i),de++,Z("jsError",i)}}function x(e){return function(){var r=arguments;if(!this._ignore){var n=_e.args.apply(this,r);this._runtime={method:n[0],url:n[1],start:p(),route:t()}}try{return e.apply(this,r)}catch(n){}}}function I(e){return function(){function t(e){var t,r,n,i=u._runtime;if(i){if(!i.start||i.start<=0)return void(u._runtime=null);if(4!==i.readyState&&(i.end=p()),i.endAll=p(),i.s=u.status,0==i.s)return;if(""==u.responseType||"text"==u.responseType)i.res=v(u.responseText);else if(u.response)i.res=v(u.response);else try{i.res=v(u.responseText)}catch(e){i.res=0}if(n=Me&&Me.timing?Me.timing.loadEventEnd:Re.load_time,t={n:i.url,m:i.method,st:i.s,o:i.start-Re.st,req:i.req,res:i.res,e:i.endAll-i.start,f:i.fb?i.fb-i.start:0,rd:i.fb?i.end-i.fb:0,cb:f||0,p:i.param,oe:0==n?1:i.start-n?0:1,route:i.route},r={u:i.url,m:i.method,st:i.s,s:i.start,req:i.req,res:i.res,e:i.endAll-i.start,fb:i.fb?i.fb-i.start:0,d:i.fb?i.end-i.fb:0,cb:f||0,a:i.param,oe:0===n?1:i.start-n?0:1},u.getAllResponseHeaders&&(t.h=(u.getAllResponseHeaders()||"").substring(0,2e3),r.h=(u.getAllResponseHeaders()||"").substring(0,2e3)),Re.aa&&Re.aa.push(t),Re.maa&&Re.maa.push(r),Z("xhr",t),Re.server.custom_urls&&Re.server.custom_urls.length&&!De.ct){if(!Re.pattern){Re.pattern=[];for(var o=0;o<Re.server.custom_urls.length;o++)Re.pattern.push(new RegExp(Re.server.custom_urls[o]))}for(o=0;o<Re.pattern.length;o++)if(a.url.match(Re.pattern[o])){De.ct=a.end+f;break}}De.sa(),u._runtime=null}}function r(){2==u.readyState&&u._runtime&&(u._runtime.fb=u._runtime.fb||p()),4==u.readyState&&t(0)}function n(e){return function(){var t,n;2==u.readyState&&u._runtime&&(u._runtime.fb=u._runtime.fb||p()),4==u.readyState&&u._runtime&&(u._runtime.end=t=p(),u._runtime.readyState=4);try{n=e&&e.apply(this,arguments)}catch(e){}return 4==u.readyState&&(f=p()-t),r(),n}}function i(e){return function(){var r=u._runtime;if(r)switch(e){case"abort":t(905);break;case"error":t(990);break;case"timeout":t(903);break;case"loadstart":r.start=p()}return!0}}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];_e.sh(e,n,i(n),!1)}}function s(e){var t="";return t=e.toString?e.toString():"",t.substring(0,2e3)}var c=arguments;if(!this._ignore){this._runtime.start=p(),this._runtime.req=c[0]?v(c[0]):0,this._runtime.param=c[0]?s(c[0]):"";var u=this,f=0,l=_e.wrap(!1,this,"onreadystatechange",n);l||_e.sh(this,"readystatechange",r,!1),o(this,["error","progress","abort","load","loadstart","loadend","timeout"]),l||setTimeout(function(){_e.wrap(!1,u,"onreadystatechange",n)},0)}try{return e.apply(this,c)}catch(e){}}}function O(){var e=Oe.getElementsByTagName("base")||[];return e.length&&(e[e.length-1]&&e[e.length-1].href||"")||""}function P(){if(Oe.querySelectorAll){var e=[],t=Oe.querySelectorAll("head>link,head>script");return t.length&&_e.each(t,function(t){var r;"LINK"===t.tagName?r=t.href:"SCRIPT"!==t.tagName||t.defer||t.async||(r=t.src),r&&e.push(r)}),e}}function L(){var t,r=0,n=Me&&Me.timing.navigationStart||Re.st;try{if(Me&&Me.getEntriesByType("paint").length)r=Me.getEntriesByType("paint")[0].startTime;else if((t=e.chrome)&&t.loadTimes){var i=t.loadTimes();i&&i.firstPaintTime&&(r=1e3*i.firstPaintTime-n)}else Me&&Me.timing.msFirstPaint&&(r=Me.timing.msFirstPaint-n)}catch(a){(t=e.chrome)&&t.loadTimes?(i=t.loadTimes(),i&&i.firstPaintTime&&(r=1e3*i.firstPaintTime-n)):Me&&Me.timing.msFirstPaint&&(r=Me.timing.msFirstPaint-n)}if(Me&&!r){var a=0,o=Me.timing,s=P();o&&(Me.getEntriesByName&&s.length&&(a=o.domLoading,_e.each(s,function(e){var t=Me.getEntriesByName(e);if(1===t.length){var r=t[0].responseEnd+o.navigationStart;r>a&&(a=r)}}),a-=o.navigationStart),r=a)}return Math.round(r)}function N(t,r,n){if(t){var i=e.pageYOffset;if(t.top+(0===i?0:i||Oe.scrollTop||0)-(Oe.clientTop||0)>=r)return!1;var a=t.left;return a>=0&&a<n}return!1}function C(e){return f(e.getBoundingClientRect,"Function")?e.getBoundingClientRect():void 0}function M(e){return e.offsetWidth<=0&&e.offsetHeight<=0&&e.getClientRects&&!e.getClientRects().length}function R(e){var t=e||0;if(!Oe.createElement("img").getBoundingClientRect)return t;if(Me&&Me.getEntriesByName){var r=window.innerHeight,n=window.innerWidth,i=[];_e.each(Oe.querySelectorAll("img"),function(e){e.src&&N(C(e),r,n)&&!M(e)&&i.push(e.src)});var a=(Me.timing.loadEventEnd||Re.load_time)-Me.timing.navigationStart;_e.each(i,function(e){var r=Me.getEntriesByName(e);if(r.length){var n=r[0],i=n.responseEnd;n.fetchStart<=a&&i>t&&(t=i)}})}return Math.round(t)}function D(e){var t=Pe.index("_webLog");if(t&&t.length){var r=e||!Pe.index("_webLogLastSend")||p()-Pe.index("_webLogLastSend")>Re.snd_du()||t.length>=Re.cc();if(r){var n=_e.mkurl(Re.server.beacon,"cp"),i=Se.stringify2({pd:t,uid:F()}),a=t.length;_e.POST(n,i,{},function(e,r){e||t.splice(0,a)}),Pe.index("_webLogLastSend",p())}}var o=Pe.index("_customError");o&&o.length&&(r=e||!Pe.index("_customErrorLastSend")||p()-Pe.index("_customErrorLastSend")>Re.snd_du()||o.length>=Re.cc(),r&&(n=_e.mkurl(Re.server.beacon,"de"),i=Se.stringify2({derr:o,uid:F()}),a=o.length,_e.POST(n,i,{},function(e,t){e||o.splice(0,a)}),Pe.index("_customErrorLastSend",p())))}function U(e,r,n,i){if(""!=e){e=g(e+"",200,!0),r=g(r+"",200,!0),i=g(i+"",100),n=g(n+"",100);var a={k:e,d:r,m:n||"",c:i||"",route:t()};Pe.index("_webLog")?Pe.index("_webLog").push(a):Pe.index("_webLog",[a])}}function j(e){try{if(e.$root===e)return"root";e._isVue?e.$options&&e.$options.name||e.$options&&e.$options._componentTag:e.name;var t=e.$options&&e.$options.__file;return t?"http://"+t:""}catch(e){}}function H(e,r){try{if(e){var n={};n.e0=p(),n.e1=e.message||"",n.e2=e.line||0,n.e3=e.column||0,n.e4=j(r);var i=e.stack||"";if(i){n.e5=i;var a=i.split(/\n/);if(n.e1=n.e1||a[0],a[1]){var o=c(a[1]);if(0===o.indexOf("at ")){var s,u=o.indexOf("("),f=o.indexOf(")");if(u>=0&&f>=0){var l=u+1;l>f&&(l=f),s=o.substring(l,f)}else s=o.substring(3);if(s){var d=s.split(":");d&&d.length>2&&(n.e4=n.e4||(d.slice(0,d.length-2)||[]).join(":"),n.e2=n.e2||+d[d.length-2],n.e3=n.e3||+d[d.length-1])}}}}n.e12=t(),De.errors&&De.errors.push(n),de++,Z("jsError",n)}}catch(e){}}function B(e){function t(){return(new Date).getTime()}e.config.errorHandler=function(e,t,r){try{H(e,t)}catch(e){}};var r=0,n=0;e.mixin({mounted:function(){try{this.$root&&this.$root.$router&&!this.$root.$router.hasSetByAgent&&(!0,this.$root.$router.hasSetByAgent=!0,this.$root.$router.beforeEach(function(e,n,i){r=t(),i()}),this.$root.$router.afterEach(function(){if(n=t(),n&&r){var e=n-r;parseInt(e)>0&&(G(e),r=0)}}))}catch(e){}}})}function q(e){if(e){var r={},n=e.message||"",i=e.stack||"",a=e.sourceURL||"",o=e.line||0,s=e.column||0,f=p();if(i){var l=i.split(/\n/);u(l,function(e,t){if(e&&(e=c(e)),e&&0===e.indexOf("at ")){var r,n=e.indexOf("("),i=e.indexOf(")");if(n>=0&&i>=0){var u=n+1;u>i&&(u=i),r=e.substring(u,i)}else r=e.substring(3);if(r){var f=r.split(":");f&&f.length>2&&(a=a||(f.slice(0,f.length-2)||[]).join(":"),o=o||+f[f.length-2],s=s||+f[f.length-1])}}})}r={e:n,s:i,r:a,l:o,c:s,o:f,route:t()},n&&i&&a&&(Pe.index("_customError")?Pe.index("_customError").push(r):Pe.index("_customError",[r]),Z("customError",r))}}function F(){var e=sessionStorage.getItem("wiseAPMUserId")||Se.localStorage.get(ae.defaultUid),t=e?d(e):"";return t}function $(e){Se.each(e,function(e,t){try{if(e.sbn){var r=Se.findDom(e.ep);r?(Se.setAttr(r,ae.dataSpm,e.sbn),ke.splice(t,1)):0===Ae&&ke.push(e)}}catch(e){}}),ke.length>0&&Ae<5?(Ae+=1,setTimeout(()=>{$(ke)},1e3)):(ke=[],Ae=0)}function J(){function t(){var e=h();if(e!==b[b.length-1]){var t=Se.now();x&&t-I>g&&n(!0),x||(x=!0,I=t,S.subscribe("unsafe_startChange",{time:t})),b.push(e),clearTimeout(T)}}function r(){var e=h(),t=Se.now();b.length=0,x||(x=!0,I=t,S.subscribe("unsafe_startChange",{time:t})),b.push(e),clearTimeout(T)}function n(e){function t(){x=!1,p=m,m=r,w=b.join(" -> "),S.subscribe("unsafe_endChange",{time:O}),b.length>1&&S.subscribe("change",P),b.length=0,b.push(r),T=null,v=O-I}if(x){var r=h();clearTimeout(T),O=E?Se.now():I,_&&b[b.length-1]!==r&&b.push(r),e?t():T=setTimeout(t,g)}}function i(e){return function(){var i=h(),a=i.indexOf("#")>=0;P=a?"hash":"history","user"===e?_?r():t():(t(),n())}}function a(e){S.on("change",function(t){var r=O-I;0!==r&&r<=20&&(r=20),e({type:t.args,prev:p,current:m,path:w,start:I,end:O,duration:r,framework:k+" "+A})})}function o(e,t){E=!0,k=e,A=t,_="vue"===e}function s(){return v}function c(){return y}function u(e){S.on("close",e)}function f(e){S.on("load",e)}function l(e){S.on("unsafe_startChange",e)}function d(e){S.on("unsafe_endChange",e)}var g=200,p="",m=h(),v=0,y=0,b=[m],w=m,T=null,S=new ge,E=!1,_=!1,k="",A="",x=!1,I=Se.now(),O=0,P="hash",L=function(e){if("pushState"===e||"replaceState"===e){var t=history[e];return function(){t.apply(history,arguments),!E&&i("historyChange")()}}};return history.pushState=L("pushState"),history.replaceState=L("replaceState"),_e.sh(e,"hashchange",function(){!E&&i("hashchange")()},!1),_e.sh(e,"load",function(){var t=e.config_param&&e.config_param.vb||[];$(t),O=Se.now(),v=O-I,setTimeout(function(){try{y=performance.timing.loadEventEnd-performance.timing.fetchStart}catch(e){y=O-I}}),S.subscribe("load")},!1),_e.sh(e,"beforeunload",function(){clearTimeout(T),T=null,S.subscribe("close")},!1),{onRouteChange:a,onClose:u,onLoad:f,setSinglePage:o,start:i("user"),end:n,onUnsafeStartChange:l,onUnsafeEndChange:d,getPageTime:s,getPageTime2:c}}function W(){var e=Se.localStorage.get(ae.defaultUid);e||Se.localStorage.set(ae.defaultUid,Se.generateId())}function V(e){return Be=e,qe=!0,e}function X(){function e(e){var t=Me.timing,r=t.navigationStart?t.navigationStart:t.fetchStart;return t[e]-r}var t=h(!0);ue=p(),G(0),Z("link",{route:t,type:"routeChange",from:se,to:t,le:e("loadEventEnd"),os:e("domContentLoadedEventStart"),jsErrorNum:de}),se=t,de=0}function z(e){var t=Be;"string"==typeof Be&&Be.indexOf("{")>-1&&(t=JSON.parse(Be)),t&&t.am&&(e.nt=t.am),t&&t.s&&(e.nq=t.s);var r=_e.mkurl(Re.server.beacon,"pf",e),n=Se.stringify2({uid:F()});_e.POST(r,n,{},b("POST"))}function G(r){if(fe&&le){var n=d(t()),i={route:n,sh:e.screen&&e.screen.height||1,sw:e.screen&&e.screen.width||1,sc:r};if(Be={},qe=!1,/(iPhone|iPad|iPod|iOS)/i.test(e.navigator.userAgent)){e.webkit&&e.webkit.messageHandlers&&e.webkit.messageHandlers.getWiseapmNetInfo&&e.webkit.messageHandlers.getWiseapmNetInfo.postMessage("");var a=0,o=setInterval(function(){a+=1,(qe||10===a)&&(clearInterval(o),o=null,a=0,z(i))},500)}else/(Android)/i.test(e.navigator.userAgent)?(Be=e.wiseapmJsBridge&&e.wiseapmJsBridge.getWiseapmNetInfo&&e.wiseapmJsBridge.getWiseapmNetInfo(),z(i)):z(i)}}function Y(t){var r,n=(r=t.target?t.target:t.srcElement)&&r.outerHTML;n&&200<n.length&&(n=n.slice(0,200));var i={type:"click",x:t.pageX||0,y:t.pageY||0,px:(e.screen.width||0)+"x"+(e.screen.height||0),detail:{outerHTML:n,tagName:r&&r.getAttribute("tagName")||"",id:r&&r.getAttribute("id")||"",className:r&&r.getAttribute("className")||"",name:r&&r.getAttribute("name")||""},time:p()};Z("click",i)}function Z(e,t){if(Re.checkTrailUpload()){var r,n=["jsError","sourceError","customError"],i=["click","xhr","link","console"];if(-1!==i.indexOf(e)&&Re.trailA.push(_e.extend({gjType:e,ref:h(),pvid:Ce,ti:p()},t)),-1!==n.indexOf(e))r="jsError"===e?{o:t.e0-Re.st,e:t.e1,ep:1,l:t.e2||0,c:t.e3||0,m:t.e6||"",r:t.e4,s:g(t.e5,5e3),f:t.e8||"",fv:t.e9||"",rvid:t.e10||"",rl:t.e11||"",route:t.e12}:t,Re.trailA.push(_e.extend({gjType:"error",errorType:e,ref:h(),pvid:Ce,ti:p()},r));Re.trailA.length>=10&&De.sendTrail()}}function K(){function t(e){try{return e=(e||"").replace(/\/+$/,""),Oe.URL.startsWith(e)}catch(e){return!1}}var r=[],n=[],i=null,a=function(e,t){var r=Se.parseUrl(Oe.URL),n={host:r.origin||"",pathname:r.pathname||"/",hash:r._hash||"",search:r._search||"",title:Oe.title};try{i&&i.postMessage({from:"wiseapm",type:e,payload:t,baseInfo:n},"*")}catch(e){}},o=function(e){return!0},s=function(s){s=s||{},i=s.source;var c=s.data||{},u=c.payload;if("wiseapm"===c.from){if("check"===c.type&&a("check-back",{url:document.URL,pathname:location.pathname,appId:Re.server.id,height:document.body.scrollHeight}),"init"===c.type){_e.wrap(!1,e.history,"pushState",function(e){return function(){return"function"!=typeof e||e.apply(this,arguments)}}),_e.wrap(!1,e.history,"replaceState",function(e){return function(){return"function"!=typeof e||e.apply(this,arguments)}}),_e.sh(e,"hashchange",function(){a("route-change",document.URL)},!1),xe.onRouteChange(function(){a("route-change",document.URL)}),a("route-change",document.URL);var f="[".concat(ae.clickable,"],[").concat(ae.hightlight,"] { outline: 2px solid #1890ff !important; outline-offset: -2px !important }[").concat(ae.hightlight,"] { background-color: rgb(24 144 255 / 15%) !important }");try{var l=Oe.createElement("style");l.innerText=f,Oe.head.appendChild(l)}catch(e){}function d(){var e=Se.findAllDom("["+ae.clickable+"]");Se.each(e,function(e){var t=Se.find(r,function(t){return t.targetDom===e});t||Se.removeAttr(e,ae.clickable)})}document.oncontextmenu=function(){return!1},window.addEventListener("mouseover",function(e){d();for(var t=e.target;t&&t.nodeType===Node.ELEMENT_NODE&&!o(t);)t=t.parentNode===document?null:t.parentNode;if(t&&o(t)){var n=Se.find(r,function(e){return e.targetDom===t});n||Se.setAttr(t,ae.clickable)}}),window.addEventListener("mouseout",function(e){var t=e.target;if(o(t)){var n=Se.find(r,function(e){return e.targetDom===t});n||Se.removeAttr(t,ae.clickable)}}),window.addEventListener("contextmenu",function(e){for(var t=e.target;t&&!Se.hasAttr(t,ae.clickable)&&!Se.hasAttr(t,ae.hightlight);)t=t.parentNode===document?null:t.parentNode;if(t){var n=Se.find(r,function(e){return e.targetDom===t});n?a("mark",{eventId:n.eventId,spmType:!(!n.blockName||""===n.blockName),nodeData:Se.parseNode(t)}):(Se.removeAttr(t,ae.clickable),Se.removeAttr(t,ae.hightlight),a("mark",{nodeData:Se.parseNode(t)}))}})}"update-config"===c.type&&(r.forEach(function(e){Se.removeAttr(e.targetDom,ae.hightlight)}),r.length=0,n.length=0,Se.each(u||[],function(e){if(!1!==t(e.enableUrl)){e.path=e.elementPath,e.innerText=e.elementInnerText;var n=Se.findDom(e.path);n&&(r.push(e),e.targetDom=n,Se.setAttr(n,ae.hightlight))}})),"upSpm-config"===c.type&&(console.log("---upSpm-config---"),r.forEach(function(e){Se.removeAttr(e.targetDom,ae.hightlight)}),r.length=0,n.length=0,Se.each(u||[],function(e){if(!1!==t(e.enableUrl)){e.path=e.elementPath,e.innerText=e.elementInnerText;var n=Se.findDom(e.path);n&&(r.push(e),e.targetDom=n,e.blockName&&Se.setAttr(n,ae.dataSpm,e.blockName),Se.setAttr(n,ae.hightlight))}})),"change-mode"===c.type&&u}};window.addEventListener("message",s)}function Q(){function t(e){try{return e=(e||"").replace(/\/+$/,""),Oe.URL.startsWith(e)}catch(e){return!1}}function r(e){if(e.path)return e.path;var t=e.target;for(e.path=[];null!==t.parentNode;)e.path.push(t),t=t.parentNode;return e.path.push(document,window),e.path}var n=e.config_param&&e.config_param.ve||[];Se.each(n,function(e){try{e.p=JSON.parse(e.p)}catch(e){}}),_e.sh(e,"click",function(e){if(0!==n.length){var i=r(e),a=[],o=["ep"],s=["ep","eit"];Se.each(i,function(e,r){if(e instanceof Element){var c=Se.parseNode(e,i.slice(r));Se.each(n,function(e){t(e.eu)&&Se.compareObject({ep:c.path,eit:c.innerText},e,1===e.propagation?s:o)&&a.push(e)})}}),a.length&&Se.each(a,function(e){var t={};e.p&&e.p.length&&Se.each(e.p,function(e){var r=Se.findDom(e.elementPath);r&&(t[e.name]=r[e.keyName||"innerText"])}),te(e.ei,"",t)})}},!1)}function ee(e,t,r){if(Se.isType(e,"String")&&""!==e){t=Se.isType(t,"Number")?t>=0?t:0:null;var n={ei:e,ev:t,kv:Se.filterTrackProp(r),t:1,ti:p()};Re.markA.push(n),Re.markA.length>10&&De.sendMarkData()}}function te(e,t,r){if(Se.isType(e,"String")&&""!==e){t=Se.isType(t,"Number")?t>=0?t:0:null;var n={ei:e,ev:t,kv:Se.filterTrackProp(r),t:2,ti:p()};Re.markA.push(n),Re.markA.length>10&&De.sendMarkData()}}try{re=this,ne=function(e){"use strict";var t,r,n,i,a=function(e,t){return{name:e,value:void 0===t?-1:t,delta:0,entries:[],id:"v2-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12)}},o=function(e,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){if("first-input"===e&&!("PerformanceEventTiming"in self))return;var r=new PerformanceObserver(function(e){return e.getEntries().map(t)});return r.observe({type:e,buffered:!0}),r}}catch(e){}},s=function(e,t){var r=function r(n){"pagehide"!==n.type&&"hidden"!==document.visibilityState||(e(n),t&&(removeEventListener("visibilitychange",r,!0),removeEventListener("pagehide",r,!0)))};addEventListener("visibilitychange",r,!0),addEventListener("pagehide",r,!0)},c=function(e){addEventListener("pageshow",function(t){t.persisted&&e(t)},!0)},u=function(e,t,r){var n;return function(i){t.value>=0&&(i||r)&&(t.delta=t.value-(n||0),(t.delta||void 0===n)&&(n=t.value,e(t)))}},f=-1,l=function(){return"hidden"===document.visibilityState?0:1/0},d=function(){s(function(e){var t=e.timeStamp;f=t},!0)},h=function(){return f<0&&(f=l(),d(),c(function(){setTimeout(function(){f=l(),d()},0)})),{get firstHiddenTime(){return f}}},g=function(e,t){var r,n=h(),i=a("FCP"),s=function(e){"first-contentful-paint"===e.name&&(l&&l.disconnect(),e.startTime<n.firstHiddenTime&&(i.value=e.startTime,i.entries.push(e),r(!0)))},f=window.performance&&performance.getEntriesByName&&performance.getEntriesByName("first-contentful-paint")[0],l=f?null:o("paint",s);(f||l)&&(r=u(e,i,t),f&&s(f),c(function(n){i=a("FCP"),r=u(e,i,t),requestAnimationFrame(function(){requestAnimationFrame(function(){i.value=performance.now()-n.timeStamp,r(!0)})})}))},p=!1,m=-1,v={passive:!0,capture:!0},y=new Date,b=function(e,i){t||(t=i,r=e,n=new Date,S(removeEventListener),w())},w=function(){if(r>=0&&r<n-y){var e={entryType:"first-input",name:t.type,target:t.target,cancelable:t.cancelable,startTime:t.timeStamp,processingStart:t.timeStamp+r};i.forEach(function(t){t(e)}),i=[]}},T=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var r=function(){b(e,t),i()},n=function(){i()},i=function(){removeEventListener("pointerup",r,v),removeEventListener("pointercancel",n,v)};addEventListener("pointerup",r,v),addEventListener("pointercancel",n,v)}(t,e):b(t,e)}},S=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach(function(t){return e(t,T,v)})},E={};e.getCLS=function(e,t){p||(g(function(e){m=e.value}),p=!0);var r,n=function(t){m>-1&&e(t)},i=a("CLS",0),f=0,l=[],d=function(e){if(!e.hadRecentInput){var t=l[0],n=l[l.length-1];f&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(f+=e.value,l.push(e)):(f=e.value,l=[e]),f>i.value&&(i.value=f,i.entries=l,r())}},h=o("layout-shift",d);h&&(r=u(n,i,t),s(function(){h.takeRecords().map(d),r(!0)}),c(function(){f=0,m=-1,i=a("CLS",0),r=u(n,i,t)}))},e.getFCP=g,e.getFID=function(e,n){var f,l=h(),d=a("FID"),g=function(e){e.startTime<l.firstHiddenTime&&(d.value=e.processingStart-e.startTime,d.entries.push(e),f(!0))},p=o("first-input",g);f=u(e,d,n),p&&s(function(){p.takeRecords().map(g),p.disconnect()},!0),p&&c(function(){var o;d=a("FID"),f=u(e,d,n),i=[],r=-1,t=null,S(addEventListener),o=g,i.push(o),w()})},e.getLCP=function(e,t){var r,n=h(),i=a("LCP"),f=function(e){var t=e.startTime;t<n.firstHiddenTime&&(i.value=t,i.entries.push(e),r())},l=o("largest-contentful-paint",f);if(l){r=u(e,i,t);var d=function(){E[i.id]||(l.takeRecords().map(f),l.disconnect(),E[i.id]=!0,r(!0))};["keydown","click"].forEach(function(e){addEventListener(e,d,{once:!0,capture:!0})}),s(d,!0),c(function(n){i=a("LCP"),r=u(e,i,t),requestAnimationFrame(function(){requestAnimationFrame(function(){i.value=performance.now()-n.timeStamp,E[i.id]=!0,r(!0)})})})}},e.getTTFB=function(e){var t,r=a("TTFB");t=function(){try{var t=performance.getEntriesByType("navigation")[0]||function(){var e=performance.timing,t={entryType:"navigation",startTime:0};for(var r in e)"navigationStart"!==r&&"toJSON"!==r&&(t[r]=Math.max(e[r]-e.navigationStart,0));return t}();if(r.value=r.delta=t.responseStart,r.value<0||r.value>performance.now())return;r.entries=[t],e(r)}catch(e){}},"complete"===document.readyState?setTimeout(t,0):addEventListener("load",function(){return setTimeout(t,0)})},e.getTTI=function(e){var t,r,n=a("TTI"),i=0,s=0,c=!1,f=function(e){var t=e.startTime,r=t-s;"beacon"===e.initiatorType&&(e.name||"").indexOf("")>=0||r>0&&r<=500&&(s=t,n.entries.push(e))};return(r=o("resource",f))&&(t=u(e,n,!1)),{startRecord:function(e){c||(c=!0,i=s=e?0:performance.now(),n.entries.length=0)},doReport:function(e){if(r&&c){c=!1,r.takeRecords().map(f);var a=0;n.entries.forEach(function(e){var t=e.startTime+(e.duration||0);t>a&&(a=t)}),0===n.entries.length?n.value=e:(n.value=a-i>=0?a-i:0,n.value=Math.max(e,n.value)),t(!0)}},config:function(e){}}},Object.defineProperty(e,"__esModule",{value:!0})},ne((re="undefined"!=typeof globalThis?globalThis:re||self).webVitals={})}catch(e){}var re,ne,ie=ie||"",ae={sid:"wiseapm-browser-sessionId",debug:"wiseapm-browser-debug",debugIgnore:"wiseapm-browser-debug-ignore",markConfig:"wiseamp-browser-mark-config",defaultUid:"wiseamp-browser-default-uid",clickable:"browser-mark-clickable",hightlight:"browser-mark-highligth",dataSpm:"data-spm"},oe=h(!0),se=oe,ce="",ue=p(),fe=!0,le=!1,de=0,he={sid:"",timmer:"",keepTime:18e5,getSidInfo:function(){var e=Se.getOrigin(),t=Se.localStorage.get(ae.sid)||{};if(e)return t[e]},setSidInfo:function(e){var t=Se.getOrigin(),r=Se.localStorage.get(ae.sid)||{};r[t]=e,Se.localStorage.set(ae.sid,r)}},ge=function(){var e={};return{on:function(t,r){void 0===e[t]?e[t]=[r]:e[t].push(r)},subscribe:function(t,r){if(e[t])for(var n={type:t,args:r||{}},i=0,a=e[t].length;i<a;i++)e[t][i]&&e[t][i].call(this,n)},off:function(t,r){if(e[t]instanceof Array)for(var n=e[t].length-1;n>=0;n--)e[t][n]===r&&e[t].splice(n,1)}}};if(navigator&&navigator.userAgent&&navigator.userAgent.indexOf("MSIE")>0){if(navigator.userAgent.indexOf("MSIE 6.0")>0)return;if(navigator.userAgent.indexOf("MSIE 7.0")>0)return;navigator.userAgent.indexOf("MSIE 8.0")>0&&(ce="eight"),navigator.userAgent.indexOf("MSIE 9.0")>0&&(ce="nine")}var pe,me,ve=function(e){return e=("[object "+e+"]").toUpperCase(),function(t){var r=Object.prototype.toString.call(t);return r.toUpperCase()===e}},ye=ve("object"),be=ve("string"),we=(ve("array"),ve("function"),ve("boolean")),Te=(ve("undefined"),ve("number")),Se={now:p,hasOwnPFun:l,each:u,isType:f,trim:c,generateId:n,stringify:i,stringify2:o,storage:{set:function(e,t,r,n){try{r=Se.stringify2(r)}catch(e){}try{e.setItem(t,r),n()}catch(e){}},get:function(e,t,r){var n=null;try{n=e.getItem(t)}catch(e){}try{n=JSON.parse(n)}catch(e){}return r&&r(n),n}},sessionStorage:{set:function(t,r,n){Se.storage.set(e.sessionStorage,t,r,n)},get:function(t,r){return Se.storage.get(e.sessionStorage,t,r)}},localStorage:{set:function(t,r,n){Se.storage.set(e.localStorage,t,r,n)},get:function(t,r){return Se.storage.get(e.localStorage,t,r)}},cookie:{get:function(e){for(var t=e+"=",r=document.cookie.split(";"),n=0;n<r.length;n++){for(var i=r[n];" "==i.charAt(0);)i=i.substring(1,i.length);if(0==i.indexOf(t))return Se.decodeURIComponent(i.substring(t.length,i.length))}return null},set:function(e,t,r){var n=new Date;n.setTime(n.getTime()+24*r*60*60*1e3);var i="expires="+n.toGMTString();document.cookie=e+"="+t+"; "+i}},debounce:function(e,t){var r=null;return t=t||500,function(n){var i=this;clearTimeout(r),r=setTimeout(function(){e.call(i,n)},t)}},throttle:function(e,t){var r=!1;return t=t||500,function(n){if(!r){r=!0;var i=this;setTimeout(function(){e.call(i,n),r=!1},t)}}},decodeURIComponent:function(t){var r=t;try{r=e.decodeURIComponent(t)}catch(e){r=t}return r},debug:function(e,t){var r=Se.localStorage.get(ae.debugIgnore)||[];if(!(1!==Se.localStorage.get(ae.debug)||r.indexOf(e)>-1)&&(Se.isType(t,"String"),Se.isType(t,"Function")))try{t()}catch(e){}},getTagName:function(e){return e instanceof Element?(e.nodeName||"").toLowerCase():""},getCssPath:function(e){if(e instanceof Element){for(var t=[];e&&e.nodeType===Node.ELEMENT_NODE;){var r=Se.getTagName(e);if(e.id){r+="[id='"+e.id+"']",t.unshift(r);break}for(var n=e,i=1;n=n.previousElementSibling;)Se.getTagName(n)===r&&i++;1!==i&&(r+=":nth-of-type("+i+")"),t.unshift(r),e=e.parentNode}return t.join(" > ")}},getCssPath2:function(e){var t=0,r=e[t];if(r instanceof Element){for(var n=[];r&&r instanceof Element;){var i=Se.getTagName(r);if(r.id){i+="[id='"+r.id+"']",n.unshift(i);break}for(var a=r,o=1;a=a.previousElementSibling;)Se.getTagName(a)===i&&o++;1!==o&&(i+=":nth-of-type("+o+")"),n.unshift(i),r=e[++t]}return n.join(" > ")}},getAttr:function(e,t){try{var r=e.getAttribute(t);return Se.isType(r,"String")?Se.trim(r):r}catch(e){return""}},removeAttr:function(e,t){try{e.removeAttribute(t)}catch(e){}},setAttr:function(e,t,r){try{e.setAttribute(t,r||"")}catch(e){}},hasAttr:function(e,t){try{return null!==Se.getAttr(e,t)}catch(e){return!1}},getStyle:function(e,t){return t=t.replace(/-([a-z])/g,function(e,t){return t.toUpperCase()}),e&&e.style&&""===e.style[t]?"":document.defaultView?document.defaultView.getComputedStyle(e,null)[t]:e.currentStyle[t]},parseNode:function(e,t){return{id:Se.getAttr(e,"id")||"",name:Se.getAttr(e,"name")||"",className:Se.getAttr(e,"class")||"",href:Se.getAttr(e,"href")||"",tagName:Se.getTagName(e)||"",path:(t?Se.getCssPath2(t):Se.getCssPath(e))||"",innerText:e.innerText||"",width:e.offsetWidth||0,height:e.offsetHeight||0}},getOffsetByBody:function(e){for(var t=e.offsetTop,r=e.offsetLeft;e&&"HTML"!==e.tagName;)e=e.parentNode,t-=e.scrollTop||0,r-=e.scrollLeft||0;return{offsetTop:t,offsetLeft:r}},elementIsHidden:function(e){function t(e){for(var t=0,r=0;e;)t+=e.offsetTop,r+=e.offsetLeft,e=e.offsetParent;return{top:t,left:r}}if(t(e).top+e.clientHeight>window.pageYOffset&&window.pageYOffset+window.innerHeight>t(e).top)return!0},compareObject:function(e,t,r){var n=[];return r=r||[],Se.each(r,function(r){e[r]===t[r]&&void 0!==e[r]&&void 0!==t[r]||n.push(r)}),0===n.length},combine:function(){var e=Array.prototype.slice.call(arguments,0),t={};return _e.each(e,function(e){for(var r in e)Se.hasOwnPFun(e,r)&&(t[r]=e[r])}),t},merge:function(e,t){if(e&&t&&Se.isType(e,"Object")&&Se.isType(t,"Object"))for(var r in t)Se.hasOwnPFun(t,r)&&(e[r]=t[r]);return e},find:function(e,t){if(Se.isType(e,"Array"))for(var r=e.length,n=0;n<r;n+=1)if(t(e[n]))return e[n]},includes:function(e,t){return!!Se.isType(e,"Array")&&-1!==e.indexOf(t)},findDom:function(e,t){try{return t?document.querySelectorAll(e):document.querySelector(e)}catch(e){return t?[]:null}},findAllDom:function(e){return Se.findDom(e,!0)},traval:function(e,t){if(Se.isType(e,"Object"))for(var r in e)Se.hasOwnPFun(e,r)&&t(r)},filterTrackProp:function(e){var t={};return Se.traval(e,function(r){var n=e[r];Se.isType(n,"String")?t[r]=n||null:Se.isType(n,"Number")&&(t[r]=n>=0?n:0)}),t},getOrigin:function(){var t="";try{t=e.location.origin?e.location.origin:e.location.protocol+"//"+e.location.hostname+(e.location.port?":"+e.location.port:"")}catch(e){}return t},parseUrl:function(t){var r=Se.getOrigin();try{var n=new e.URL(t),i=n.hash.indexOf("?");if(n._query={},n._search=n.search,n.hash&&(i>-1?(n._hash=n.hash.slice(1,i),n._search=n.hash.slice(i)):n._hash=n.hash.slice(2)),n._search){n._search=n._search.slice(1);var a=n._search.split("&");Se.each(a,function(e){var t=e.split("=");t[0]&&(n._query[t[0]]=t[1]||"")})}return n||{origin:r}}catch(e){return{origin:r}}}
},Ee=function(){var t={prefix:"https",id:"3414",beacon:"wpmjs.paic.com.cn/upload",beacon_cfg:"",ignorePromiseError:!1,useHttps:!0};if(e.wiseAPMBrowserConfig&&ye(e.wiseAPMBrowserConfig)){var r=e.wiseAPMBrowserConfig;be(r.url)&&r.url&&(t.beacon=r.url),(be(r.appid)||Te(r.appid))&&r.appid&&(t.id=r.appid),we(r.ignorePromiseError)&&(t.ignorePromiseError=r.ignorePromiseError)}return t.beacon_cfg=t.prefix+"://"+t.beacon+"/cfg?aid="+t.id+"&h="+encodeURI(Se.getOrigin()),t}();String.prototype.startsWith||(String.prototype.startsWith=function(e,t){return t=t||0,this.indexOf(e,t)===t}),r.prototype.index=function(e,t){return e||t?t?(this.__values__[e]=t,t):this.__values__[e]:this.__values__};var _e={filtered:function(){function t(e){var t={},r=e&&e.match?e.match(/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?/):null;return r&&(t.protocol=(r[1]||"http")+":",t.hostname=r[3],t.port=r[4]||""),t}return function(r){var n=e.location;if(r=_e.trim(r),r){if(r=r.toLowerCase(),"//"===r.substring(0,2)&&(r=n.protocol+r),"http"!==r.substring(0,4))return!0;var i=t(r),a=i.protocol===n.protocol&&i.hostname===n.hostname;return a&&(a=i.port===n.port||!n.port&&("http:"===n.protocol&&"80"===i.port||"https"===n.protocol&&"443"===i.port)),a}return!1}}(),wrap:function(e,t,r,n,i){try{var a=t[r]}catch(t){if(!e)return!1}if(!a&&!e)return!1;if(a&&a._wraped)return!1;try{t[r]=n(a,i)}catch(e){return!1}return t[r]._wraped=a,!0},unwrap:function(e,t){try{var r=e[t]._wraped;r&&(e[t]=r)}catch(e){}},each:function(e,t){if(e)for(var r=0;r<e.length;r++)e[r]&&t&&t(e[r],r,e)},mkurl:function(t,r,n){var i=Re.server.useHttps||/^https/i.test(oe)?"https":"http";for(var a in i+="://"+t+"/"+r,i+="?av=4.7.6",i+="&v=2.2.16",i+="&key="+d(Re.server.key),i+="&isp="+d(Re.server.isp),i+="&ref="+d(g(Oe.URL,1e3)),i+="&referer="+d(g(ie,1e3)),i+="&base="+d(O()),i+="&rand="+ue,i+="&pvid="+Ce,i+="&aid="+Re.server.id,i+="&if="+(e.top&&e.self&&e.top===e.self?0:1),Re.server.get_ip&&(i+="&cip="+Re.cip+"&ua="+Re.ua),"pf"!==r&&Re&&Re.agent&&(i+="&n="+d(Re.agent.n)),n||{})i+="&"+a+"="+n[a];return i},scriptGet:function(t,r){function n(){i(),r&&r.apply(this,arguments)}function i(){e.config_param&&(e.config_param.createTime=p(),sessionStorage.setItem("wiseAPMConfigParam",Se.stringify2(e.config_param)))}function a(){4==l.readyState&&200==l.status&&l.responseText&&l.responseText.indexOf("error:")<0&&(e.config_param=JSON.parse(l.responseText),n())}var o=sessionStorage.getItem("wiseAPMConfigParam");if(o){var s=JSON.parse(o),c=Number(Re.server.id),u=c===s.wpId||c===s.wtId,f=p();if(s.createTime&&f-s.createTime<=3e5&&u)return window.config_param=s,void(r&&r())}var l=null;if(window.XMLHttpRequest){l=new XMLHttpRequest;try{l._ignore=!0}catch(e){}l.onreadystatechange=a,l.open("GET",t,!0),l.send(null)}},POST:function(e,t,r,n,i){if(Re&&Re.server&&1==Re.server.swi){if(!window.XMLHttpRequest||!window.atob){var a=d(t);if(a.length>1200)return;var o=e+"&b="+a,s=new Image;return s.src=o,s=null,void n()}if(navigator&&navigator.userAgent){var c=/iPhone|iPad|iPod/i.test(navigator.userAgent);if(!i&&!c&&navigator.sendBeacon&&e.startsWith("http")){try{var u=navigator.sendBeacon(e,t);n(!u)}catch(e){}return u}}var f=null;if(!Ie)return!1;f=new Ie,f.overrideMimeType&&f.overrideMimeType("text/html");try{f._ignore=!0}catch(e){}var l=0;f.onreadystatechange=function(){0==l&&n&&n(null,f.responseText),l++},i&&this.sh(f,"error",function(){n&&n("post error",f.responseText)}),f.onerror&&this.wrap(!0,f,"onerror",function(e){return function(){return n&&n("post error",f.responseText),"function"!=typeof e||e.apply(this,arguments)}});try{f.open("POST",e,!0)}catch(e){}for(var h in r)f.setRequestHeader&&f.setRequestHeader(h,r[h]);return f.send(t),!0}},sh:function(e,t,r,n){return e.addEventListener?e.addEventListener(t,r,n):!!e.attachEvent&&e.attachEvent("on"+t,r)},args:function(){var e=[];return[].push.apply(e,arguments),e},stringify:o,parseJSON:function(t){if(t&&"string"==typeof t){var r=e.JSON?e.JSON.parse:function(e){return new Function("return "+e)()};return r(t)}},trim:String.prototype.trim?function(e){return null==e?"":String.prototype.trim.call(e)}:function(e){return null==e?"":e.toString().replace(/(^\s+)|(\s+$)/g,"")},extend:function(e,t){if(e&&t)for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r]);return e},bind:function(e,t){return function(){e.apply(t,arguments)}},selfConsole:function(){window.console._ignore=!0,console.log.apply(null,arguments)}},ke=[],Ae=0,xe=J(),Ie=e.XMLHttpRequest,Oe=document,Pe=new r;Pe.index("_random",m());var Le,Ne,Ce=n(),Me=e.performance?e.performance:e.Performance;W(),Le=function(){he.timmer=setTimeout(Le,1e3);var e=he.getSidInfo(),t=Se.now();(!e||e.endTime<=t)&&(Se.debug("sessionId","鏇存崲sessionID"),e={endTime:t+he.keepTime,sid:n()},he.setSidInfo(e)),he.sid!==e.sid&&(he.sid=e.sid)},Ne=Se.throttle(function(){var e=he.getSidInfo();e&&(e.endTime=Se.now()+he.keepTime,he.setSidInfo(e))}),Le(),_e.sh(e,"mousemove",Ne,!1);var Re=_e.extend({checkUpload:function(){return fe&&Re.server&&1==Re.server.swi&&(!Re.server.sr||100*Pe.index("_random")<=Re.server.sr)},checkTrailUpload:function(){return 1===Re.server.ct&&Re.checkUpload()},st:p(),ra:[],c_ra:[],aa:[],maa:[],clickA:[],trailA:[],markA:[],snd_du:function(){return this.server.adu?1e3*this.server.adu:1e4},cc:function(){return this.server.ac||10},cip:"",ua:e.navigator&&e.navigator.userAgent.replace(/;/g,""),server:{key:"iRiIZP9b7eQ",useHttps:Ee.useHttps,id:Ee.id,beacon:Ee.beacon,beacon_cfg:Ee.beacon_cfg},load_time:0,end_time:0},{});_e.sh(e,"load",function(){Re.beforeload=!0},!1),_e.scriptGet(Re.server.beacon_cfg,function(t){e.config_param&&_e.extend(Re.server,e.config_param);var r=e.location.hostname,n=e.location.protocol;if(r&&n&&Re.server.wp&&Re.server.wp.length>0&&Re.server.wpId){for(var i=!1,a=!1,o=Re.server.wp.length,s=Re.server.wp,c=0;c<o;c++)if(0==s[c].indexOf("http")){if(s[c]==n+"//"+r){i=!0;break}}else if(s[c]==r){i=!0;break}if(i)Re.server.id=Re.server.wpId;else{if(!(!i&&Re.server.wt&&Re.server.wt.length>0&&Re.server.wtId))return void(fe=!1);for(var u=Re.server.wt.length,f=Re.server.wt,l=0;l<u;l++)if(0==f[l].indexOf("http")){if(f[l]==n+"//"+r){a=!0;break}}else if(f[l]==r){a=!0;break}if(!a)return void(fe=!1);Re.server.id=Re.server.wtId}}if(le=!0,Re.checkUpload()){Re.beforeload?E():_e.sh(e,"load",function(){Re.beforeload=!0,E()},!1);var d=[["beforeunload",_],["pagehide",_],["unload",_]];for(c=0;c<d.length;c++)_e.sh(e,d[c][0],d[c][1],!1);var h=[["scroll",k],["keypress",k],["click",k],["DOMContentLoaded",T],["readystatechange",S]];for(c=0;c<h.length;c++)_e.sh(Oe,h[c][0],h[c][1],!1)}else De&&(De.errors.length=0,De.resErrors.length=0),Re.aa.length=0,Re.maa.length=0,Re.clickA.length=0,Re.trailA.length=0,Re.markA.length=0;Q()});var De=Re.metric={ready:function(){return Re.load_time},initend:function(){function t(){if(e.wiseAPMMobile&&e.wiseAPMMobile.mobileAjxData&&Re.maa.length>0){for(var t=0;t<Re.maa.length;t++)e.wiseAPMMobile.mobileAjxData(Re.maa[t]);Re.maa.length=0}}Re.end_time||(Re.end_time=p(),this._sm=setInterval(t,4e3),this._h=setInterval(De.sa,2e3),1===Re.server.ct&&(this._t=setInterval(De.sendTrail,5e3)),this._m=setInterval(De.sendMarkData,5e3))},send:function(){function r(){function t(e){return n[e]-i}var r={};if(Me&&Me.timing){var n=Me.timing,i=n.navigationStart?n.navigationStart:n.fetchStart,a=t("redirectStart"),o=t("redirectEnd"),s=t("fetchStart"),c=t("domainLookupStart"),u=t("domainLookupEnd"),f=t("connectStart"),l=t("connectEnd"),h=t("secureConnectionStart"),g=t("requestStart"),p=t("responseStart"),m=t("responseEnd"),v=t("domLoading"),y=t("domInteractive"),b=t("domContentLoadedEventStart"),w=t("domContentLoadedEventEnd"),T=t("domComplete"),S=t("loadEventStart"),E=t("loadEventEnd"),_=t("unloadEventStart"),k=t("unloadEventEnd");r.ns=i,o>=0&&a>=0&&a>o||(a>=0&&(r.es=a),o>=0&&(r.ee=o)),s>=0&&(r.f=s),u>=0&&c>=0&&c>u||(c>=0&&(r.ds=c),u>=0&&(r.de=u)),l>=0&&f>=0&&f>l||(f>=0&&(r.cs=f),l>=0&&(r.ce=l)),h>=0&&(r.sl=h),g>=0&&(r.qs=g),m>=0&&p>=0&&p>m||(p>=0&&(r.rs=p),m>=0&&(r.re=m)),v>=0&&(r.ol=v),y>=0&&(r.oi=y),w>=0&&b>=0&&b>w||(b>=0&&(r.os=b),w>=0&&(r.oe=w)),T>=0&&(r.oc=T),E>=0&&S>=0&&S>E||(E>=0?(S>=0&&(r.ls=S),r.le=E):(S>=0&&(r.ls=S),r.ue=Re.load_time-i,r.ue<r.ls&&(r.ue=r.ls))),k>=0&&_>=0&&_>k||(_>=0&&(r.tus=_),k>=0&&(r.tue=k))}else i=Re.st,r={t:i,os:Re.end_time-i,ls:Re.load_time-i};r.je=De.errors.length,r.uk=me||"",r.gid=pe||"";var A=Re.agent;return A&&(r.id=d(A.id),r.a=A.a,r.q=A.q,r.tid=d(A.tid),r.n=d(A.n)),Re.beforeload?r.bl=0:r.bl=1,r.sh=e.screen&&e.screen.height,r.sw=e.screen&&e.screen.width,r}function n(e){function t(e){return u[e]>0?Math.round(1e3*u[e])/1e3:0}function r(e,t){if(null==e||null==t)return!1;try{var r=e.match(t)}catch(e){r=null}return r?1:0}function n(e,t){if(null==e||null==t)return!1;var r,n,i,a=!1,o=!1,s=0,c=0;for(n=0,i=0;n<e.length;n++){if(t.length<=i){if(0!=s){o=!0,n=s,i=c,s=0,c=0;continue}break}if("*"!=(r=t.charAt(i)))if(o)e.charAt(n)==r&&(o=!1,s=n+1,c=i,i++);else{if("?"!=r&&r!=e.charAt(n)){if(a=!1,0!=s){o=!0,n=s,i=c,s=0,c=0;continue}break}i++}else{if(i==t.length-1){a=!0;break}o=!0,i++}}return n==e.length&&i==t.length&&(a=!0),a}function i(e){var t=Re.server.key_el,i=0;if(t&&t.length<1)return!1;for(var a,o=0;a=t[o],o<t.length;o++){switch(a.p){case 1:String(e)===a.r&&(i=a.c);break;case 2:i=n(e,a.r)&&(i=a.c);break;case 3:i=r(e,a.r)&&(i=a.c)}if(i)return i}return i}if(Me&&Me.getEntriesByType){var a={tr:!0,tt:d(Oe.title),charset:Oe.characterSet},o=Me.getEntriesByType("resource"),s=Re.ra;o&&(s=s.concat(o)),a.res=[];for(var c=0;c<s.length;c++){var u=s[c];if("xmlhttprequest"!==u.initiatorType){var f={o:t("startTime"),rt:u.initiatorType,n:u.name,f:t("fetchStart"),ds:t("domainLookupStart"),de:t("domainLookupEnd"),cs:t("connectStart"),ce:t("connectEnd"),sl:t("secureConnectionStart"),qs:t("requestStart"),re:t("responseStart"),rs:t("responseEnd"),es:t("redirectStart"),ee:t("redirectEnd")};_e.filtered(u.name)?f.cr=0:f.cr=1,i(u.name)?(f.kc=i(u.name),a.res.push(f)):e&&a.res.push(f)}}return a}return{tr:!0,tt:d(Oe.title),charset:Oe.characterSet,res:[]}}function i(e){var t=Be;"string"==typeof Be&&Be.indexOf("{")>-1&&(t=JSON.parse(Be)),t&&t.am&&(e.nt=t.am),t&&t.s&&(e.nq=t.s);var r=_e.mkurl(Re.server.beacon,"pf",e);c.length&&_e.POST(r,c,{},b("POST")),f.sa();var n=_e.bind(w,f);n(),De._k=setInterval(n,3e3),D(),De._l=setInterval(D,3e3)}if(this.sended||!this.ready())return!1;var a,o,s=Re.st,c={},u={};try{o=L(),u=r(),u.fs=R(o),a=(u.ls>0?u.ls:Re.load_time-s)-Re.server.trace_threshold>0?1:0,u.sp=a,c=n(a)}catch(e){}c.uid=F(),c=c?Se.stringify2(c):"";var f=this;Re.sended=this.sended;var l=function(r,n){if(r&&r.substr("error")?Re.cip=Re.server.sip:Re.cip=n||"",Re.sended=!0,u.route=d(t()),Be={},qe=!1,/(iPhone|iPad|iPod|iOS)/i.test(e.navigator.userAgent)){e.webkit&&e.webkit.messageHandlers&&e.webkit.messageHandlers.getWiseapmNetInfo&&e.webkit.messageHandlers.getWiseapmNetInfo.postMessage("");var a=0,o=setInterval(function(){a+=1,(qe||10===a)&&(clearInterval(o),o=null,a=0,i(u))},500)}else if(/(Android)/i.test(e.navigator.userAgent))Be=e.wiseapmJsBridge&&e.wiseapmJsBridge.getWiseapmNetInfo&&e.wiseapmJsBridge.getWiseapmNetInfo(),i(u);else{var s=_e.mkurl(Re.server.beacon,"pf",u);c.length&&_e.POST(s,c,{},b("POST")),f.sa();var l=_e.bind(w,f);l(),De._k=setInterval(l,3e3),D(),De._l=setInterval(D,3e3)}};return l(),!0},sa:function(e){if(Re.sended&&(De.ready()||e)&&Re.aa.length){var t=e||!De._last_send||p()-De._last_send>Re.snd_du()||Re.aa.length>=Re.cc();if(Re.server.ignoreRequestParams&&u(Re.aa,function(e,t){e.p=""}),t){var r=_e.mkurl(Re.server.beacon,"as"),n=Se.stringify2({xhr:Re.aa,uid:F()}),i=Re.aa.length;_e.POST(r,n,{},function(e,t){e||Re.aa.splice(0,i)}),De._last_send=p()}}},sendTrail:function(){if(Re.checkTrailUpload()&&0!==Re.trailA.length&&!0!==Re.trailA.uploading){var e=Re.trailA.length>10?10:Re.trailA.length;Re.trailA.uploading=!0;var t=Re.trailA.slice(0,e),r=_e.mkurl(Re.server.beacon,"uc"),n=Se.stringify2({gj:t,uid:F(),sessionId:he.sid});_e.POST(r,n,{},function(t){Re.trailA.uploading=!1,t||Re.trailA.splice(0,e)})}},sendMarkData:function(){if(Re.checkUpload()&&0!==Re.markA.length&&!0!==Re.markA.uploading){var e=Re.markA.length>10?10:Re.markA.length;Re.markA.uploading=!0;var t=Re.markA.slice(0,e),r=_e.mkurl(Re.server.beacon,"ua"),n=Se.stringify2({ua:t,uid:F(),sessionId:he.sid});_e.POST(r,n,{},function(t){Re.markA.uploading=!1,t||Re.markA.splice(0,e)})}},sendWebVitalsData:function(e){if(Re.checkUpload()){var t=_e.mkurl(Re.server.beacon,"uw"),r=Se.stringify2({uw:e,uid:F(),sessionId:he.sid});_e.POST(t,r,{})}},errors:[],resErrors:[]},Ue={},je=/MSIE 9.0/.test(Re.ua);if(e.addEventListener&&!je?(_e.sh(e,"error",A,!1),!Ee.ignorePromiseError&&_e.sh(e,"unhandledrejection",A,!1)):e.onerror=function(e,r,n,i,a){var o={};if(o.e0=p(),o.e1=e,o.e2=n,o.e3=i,o.e4=r,a){var s=y(e,n,i,a.moduleName);o.e5=a.stack||"",o.e6=a.moduleName||"",o.e7=Ue[s]?0:1,Ue[s]=!0}o.e8=Pe.index("f"),o.e9=Pe.index("fv"),o.e10=Pe.index("rvid"),o.e11=Pe.index("rl"),o.e12=t(),De.errors&&De.errors.push(o),de++,Z("jsError",o)},e.addEventListener&&e.addEventListener("error",function(t){try{if(t.message)return;var r,n=(r=t.target?t.target:t.srcElement)&&r.outerHTML;n&&200<n.length&&(n=n.slice(0,200));var i={type:"sourceError",outer:n,src:r&&r.src,tag:r&&r.tagName,time:p()};if(r.src===e.location.href)return;if(r.src&&r.src.match(/.*\/(.*)$/)&&!r.src.match(/.*\/(.*)$/)[1])return;if(i.src&&e.XMLHttpRequest){var a=new XMLHttpRequest;try{a._ignore=!0}catch(t){}a.open("HEAD",i.src),a.send(),a.onload=function(e){200!==e.target.status&&(i.status=e.target.status,i.statusText=e.target.statusText),De.resErrors&&De.resErrors.push(i),Z("sourceError",i)}}}catch(e){}},!0),Ie&&"eight"!=ce)if(Ie.prototype)_e.wrap(!1,Ie.prototype,"open",x),_e.wrap(!1,Ie.prototype,"send",I);else{_e.ie=7;var He=Ie;e.XMLHttpRequest=function(){var e=new He;return _e.wrap(!1,e,"open",x),_e.wrap(!1,e,"send",I),e}}else e.ActiveXObject&&(_e.ie=6);var Be={},qe=!1;if(_e.wrap(!1,e.history,"pushState",function(e){return function(){return X(),"function"!=typeof e||e.apply(this,arguments)}}),_e.wrap(!1,e.history,"replaceState",function(e){return function(){return X(),"function"!=typeof e||e.apply(this,arguments)}}),_e.sh(e,"hashchange",X,!1),_e.sh(e,"click",Y,!1),e&&e.console&&_e.each([],function(t){_e.wrap(!1,e.console,t,function(r){return function(){if("function"==typeof r){var n=Array.apply(null,arguments);return e.console._ignore||Z("console",{type:t,args:n,route:h()}),e.console._ignore=!1,r.apply(this,n)}return!0}})}),e.wiseAPM={log:U,customError:q,config:function(e){for(var t in e)l(e,t)&&Re&&f(Re.server,"Object")&&(Re.server[t]=e[t])},notifyError:B,setUserId:function(e){e&&be(e)?(e=g(e,100),sessionStorage.setItem("wiseAPMUserId",e)):sessionStorage.setItem("wiseAPMUserId","")},setFromMobile:function(e,t){},setRouterTime:function(e){G(e)},report:ee,wiseapmIOSNetInfo:V,getNetInfo:Be},window.parent!==window&&K(),e.webVitals){function Fe(){this.arr=[],this.map={},this.href=h(),this.start=Se.now(),this.hasReportTTI=!1}Fe.prototype.add=function(e){var t=e.name,r=this.arr;if(!this.map[t]){this.map[t]=!0,r.push(e);var n=Se.find(r,function(e){return"FID"===e.name});2!==r.length||n||r.push({name:"FID",value:0}),3===r.length&&this.report()}},Fe.prototype.report=function(){if(!(this.arr.length<3)){var e={h:this.href,p:xe.getPageTime2()};Se.each(this.arr,function(t){var r=t.name[0].toLowerCase();e[r]=t.value}),De.sendWebVitalsData(e)}};var $e=new Fe;webVitals.getCLS(function(e){$e.add(e)}),webVitals.getFID(function(e){$e.add(e)}),webVitals.getLCP(function(e){$e.add(e)})}}(window);
