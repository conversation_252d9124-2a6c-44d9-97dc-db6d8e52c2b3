var sensors = window['sensorsDataAnalytic201505'];
sensors.init({
  server_url: 'http://test1-ant.paic.com.cn/sa?project=SA_KJZXddljaabhownr',
        // 开启采集 device_id
        is_track_device_id: true,
        // 开启 App 打通 H5
        use_app_track: false,
  // 全埋点url
  heatmap_url: '',
  // 全埋点设置
  heatmap: {
    clickmap:'default',
    scroll_notice_map:'default',
    collect_tags: {
      div : true
    }
  },
  show_log: true
});

// 初始化后设置公共属性（重要！无此设置数据将无法入库）【根据配置邮件中的值填入】
sensors.registerPage({
  sourceprojectid: "KJZXddljaabhownr",
  sourcesiteid: "WEB7SsVTaOUakalt",
  siteid: "WEB7SsVTaOUakalt"
});
// 开启页面浏览事件pageview
sensors.quick('autoTrack');
// 开启页面停留时长
sensors.use('PageLeave');