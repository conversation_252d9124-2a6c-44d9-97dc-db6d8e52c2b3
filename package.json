{"name": "nuxt-app", "private": true, "type": "module", "packageManager": "pnpm@9.9.0", "scripts": {"prd": "nuxt build --dotenv .env.prd", "gray": "nuxt build --dotenv .env.prd", "dev": "nuxt dev --dotenv .env.dev", "test": "nuxt build --dotenv .env.prd", "test2": "nuxt build --dotenv .env.prd", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "serve": "pm2 start ./ecosystem.config.cjs", "stop": "pm2 delete all", "cleanup": "nuxt cleanup", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepare": "husky", "pre-commit": "lint-staged"}, "lint-staged": {"*.{ts,vue,tsx,js}": ["eslint --fix"]}, "dependencies": {"@pafe/icons-icore-agr-an": "^1.0.65", "@pag/icon-vue3": "^1.0.10", "@pag/nuxt-amfe-flexible": "^1.0.2", "@pag/nuxt-cancelAutoImport": "^1.0.0", "@pag/nuxt-icon-vue3": "^1.0.7", "@pag/nuxt-server-middleware": "^1.0.1", "@pag/nuxt-winston-log": "^1.0.10", "@turf/turf": "^7.1.0", "@visactor/vtable": "^1.15.0", "@visactor/vtable-editors": "^1.14.0", "ant-design-vue": "^4.2.6", "dayjs": "^1.11.13", "ecosmap": "1.1.3-beta.7", "ecosmap-leaflet": "1.0.4-beta.9", "idb": "^8.0.0", "lodash-es": "^4.17.21", "number-precision": "^1.6.0", "nuxt": "3.17.1", "overlayscrollbars": "^2.10.0", "overlayscrollbars-vue": "^0.5.9", "pinia": "^2.2.6", "uuid": "^10.0.0", "vue": "^3.5.12", "vue-draggable-resizable": "^3.0.0", "vue-router": "^4.4.5", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "leaflet": "^1.9.4", "leaflet-agr-utils": "1.0.1-beta.3", "leaflet-draw": "^1.0.4", "leaflet-markers-canvas": "^0.2.2", "leaflet.fullscreen": "^4.0.0", "leaflet.markercluster": "^1.5.3", "leaflet.vectorgrid": "^1.3.0", "leaflet.wmts": "^1.0.2", "tilelayer-canvas": "^1.1.5"}, "devDependencies": {"@ant-design-vue/nuxt": "^1.4.5", "@nuxt/eslint": "^0.5.7", "@nuxtjs/tailwindcss": "^6.13.1", "@opentelemetry/api": "^1.9.0", "@pag/nuxt-legacy": "^1.0.1", "@pag/nuxt-noprefetch": "^1.0.4", "@pinia/nuxt": "^0.5.5", "@types/lodash-es": "^4.17.12", "@unocss/reset": "^0.58.9", "@vitejs/plugin-legacy": "^5.4.3", "@vueuse/core": "^13.0.0", "@vueuse/nuxt": "^13.0.0", "eslint": "^9.14.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "floating-vue": "^5.2.2", "husky": "^9.1.6", "less": "^4.2.0", "lint-staged": "^15.2.10", "nuxt-vite-legacy": "^1.3.1", "postcss": "8.5.2", "postcss-pxtorem": "^6.1.0", "prettier": "^3.4.2", "terser": "^5.36.0", "typescript": "^5.6.3"}, "resolutions": {"lodash-es": "4.17.21"}}