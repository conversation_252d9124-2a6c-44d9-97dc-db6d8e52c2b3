// 设置请求头，依赖环境变量返回不同的js
export default defineNuxtPlugin(() => {
  const { trackerUrl, kanyunUrl } = useRuntimeConfig().public;
  useHead({
    link: [{ rel: 'icon', type: 'image/svg+xml', href: '/favicon.svg' }],
    script: [kanyunUrl ? { src: kanyunUrl + '?v=1', defer: true, tagPosition: 'bodyClose' } : {}, trackerUrl ? { src: trackerUrl + '?v=1', defer: true, tagPosition: 'bodyClose' } : {}],
  });
});
