export default defineNuxtPlugin({
  name: 'nuxtErr',
  hooks: {
    'vue:error'(err) {
      const nuxtApp = useNuxtApp();
      console.error(nuxtApp.ssrContext?.event, err);
    },
    'app:error'(err) {
      const nuxtApp = useNuxtApp();
      console.error(nuxtApp.ssrContext?.event, err);
    },
    'app:created'() {
      const nuxtApp = useNuxtApp();
      console.log(nuxtApp.ssrContext?.event, 'vueapp创建完成', new Date().getTime());
    },
    'app:rendered'() {
      const nuxtApp = useNuxtApp();
      console.log(nuxtApp.ssrContext?.event, '页面已渲染完成', new Date().getTime());
    },
    'vue:setup'() {
      const nuxtApp = useNuxtApp();
      console.log(nuxtApp.ssrContext?.event, 'vue:setup', new Date().getTime());
    },
  },
});
